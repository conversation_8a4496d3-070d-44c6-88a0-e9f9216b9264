package cn.hydee.middle.business.order.refundorder;

import cn.hydee.middle.business.order.entity.OrderInfo;
import cn.hydee.middle.business.order.entity.RefundOrder;
import cn.hydee.middle.business.order.mapper.OrderInfoMapper;
import cn.hydee.middle.business.order.mapper.RefundOrderMapper;
import cn.hydee.unified.model.order.MeituanRefundRecord;
import cn.hydee.middle.business.order.thirdbill.service.IMeituanRefundRecordService;
import cn.hydee.middle.business.order.thirdbill.service.IMeituanRefundService;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import lombok.extern.slf4j.Slf4j;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.junit4.SpringRunner;

@SpringBootTest(webEnvironment = SpringBootTest.WebEnvironment.RANDOM_PORT)
@RunWith(SpringRunner.class)
@Slf4j
public class MeituanRefundTest {

    @Autowired
    private IMeituanRefundService meituanRefundService;

    @Autowired
    private OrderInfoMapper orderInfoMapper;

    @Autowired
    private RefundOrderMapper refundOrderMapper;

    @Autowired
    private IMeituanRefundRecordService meituanRefundRecordService;

    @Test
    public void setWmBIllObtainService() {
        String orderNo = "1675895275198311169";
        String refundNo = "1675902342638303744";
        meituanRefundService.obtainMtRefundValue(orderNo, refundNo);
    }

    @Test
    public void obtainMeituanRefundRecordService() {
        String orderNo = "1677048533127001344";
        String refundNo = "1677049332429426176";
//        String orderNo = "1677440456219881729";
//        String refundNo = "1677440519121430784";
        OrderInfo orderInfo = orderInfoMapper.selectOne(new QueryWrapper<OrderInfo>().lambda().eq(OrderInfo::getOrderNo, orderNo));
        RefundOrder refundOrder = refundOrderMapper.selectByRefundNo(Long.valueOf(refundNo));
        MeituanRefundRecord record = meituanRefundRecordService.obtainRefundRecord(orderInfo, refundOrder);
        System.out.println(record.toString());
    }
}
