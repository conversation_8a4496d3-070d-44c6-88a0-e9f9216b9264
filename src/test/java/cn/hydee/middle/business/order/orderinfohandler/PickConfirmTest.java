package cn.hydee.middle.business.order.orderinfohandler;

import cn.hutool.json.JSONUtil;
import cn.hydee.middle.business.order.Enums.DsConstants;
import cn.hydee.middle.business.order.Enums.FactoryTagEnum;
import cn.hydee.middle.business.order.dto.req.OrderHandleDeliveryOutReqDto;
import cn.hydee.middle.business.order.dto.req.OrderHandlePickConfirmReqDto;
import cn.hydee.middle.business.order.v2.factory.OrderForwardOperateFactory;
import cn.hydee.middle.business.order.v2.service.PickConfirmService;
import lombok.extern.slf4j.Slf4j;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.junit4.SpringRunner;


@SpringBootTest(webEnvironment = SpringBootTest.WebEnvironment.RANDOM_PORT)
@RunWith(SpringRunner.class)
@Slf4j
public class PickConfirmTest {
    static final String MER_CODE = "500001";
    static final String USER_ID = "4088620772053377032";

    static final String REQ_JSON = "{\"deliveryType\":\"达达骑手\",\"orderNo\":\"1799109257125132544\",\"orderState\":20,\"pickDetailList\":[{\"commodityBatchNo\":\"***********\",\"count\":5,\"erpCode\":\"0000144\",\"orderDetailId\":1300,\"purchasePrice\":5},{\"commodityBatchNo\":\"2018080839\",\"count\":5,\"erpCode\":\"000039\",\"orderDetailId\":1299,\"purchasePrice\":2}],\"pickOperatorId\":\"0B3D9BB5D0C9427281BAB6A3F04854DB\",\"pickOperatorName\":\"熊芳\"}";

    static final String DELI_JSON = "{\"deliveryManId\":\"01be23ca5b654910a52275d034935bcc\",\"deliveryManName\":\"2002拣货员\",\"deliveryManPhone\":\"\",\"orderNo\":\"1660394430342889473\",\"orderState\":30}";

    OrderHandlePickConfirmReqDto reqDto;

    @Before
    public void setUp() {
        reqDto = JSONUtil.toBean(REQ_JSON, OrderHandlePickConfirmReqDto.class);
    }

    @Test
    public void pickConfirmTest() {
    	PickConfirmService pickConfirmService = OrderForwardOperateFactory.getPickConfirmService("default");
        pickConfirmService.pickConfirm(MER_CODE, USER_ID, reqDto, DsConstants.INTEGER_ONE, reqDto.getOrderNo().toString());
 
    }

    @Test
    public void orderDeliveryOutTest() {
        OrderHandleDeliveryOutReqDto dto = JSONUtil.toBean(DELI_JSON, OrderHandleDeliveryOutReqDto.class);
        OrderForwardOperateFactory.getOrderDeliveryService(FactoryTagEnum.DELIVERYOUT.getTag())
		.deliveryOut(MER_CODE,USER_ID, dto,dto.getOrderNo().toString());

    }


}
