package cn.hydee.middle.business.order.orderinfohandler;

import cn.hydee.middle.business.order.entity.OrderDeliveryRecord;
import cn.hydee.middle.business.order.entity.OrderInfo;
import cn.hydee.middle.business.order.mapper.OrderDeliveryRecordMapper;
import cn.hydee.middle.business.order.service.suport.OrderBasicService;
import com.alibaba.fastjson.JSON;
import lombok.extern.slf4j.Slf4j;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.junit4.SpringRunner;

/**
 * cn.hydee.middle.business.order.orderinfohandler
 *
 * <AUTHOR>
 * @version 1.0
 * @date 2020/5/15 11:38
 **/
@SpringBootTest(webEnvironment = SpringBootTest.WebEnvironment.RANDOM_PORT)
@RunWith(SpringRunner.class)
@Slf4j
public class OrderDeliveryRecordTest {

    final private static Long ORDER_NO=1665110488699109377L;

    @Autowired
    private OrderDeliveryRecordMapper orderDeliveryRecordMapper;

    @Autowired
    private OrderBasicService orderBasicService;

    @Test
    public void testOrderDeliveryRecordMapper(){
        Long orderNo = null;
        OrderDeliveryRecord orderDeliveryRecord = orderDeliveryRecordMapper.selectByOrderNo(orderNo);
        //log.info("OrderDeliveyRecordTest: order:{} data:{} flag:{}", orderNo,JSON.toJSON(orderDeliveryRecord),orderDeliveryRecord==null);


        orderNo = 23234243L;
        orderDeliveryRecord = orderDeliveryRecordMapper.selectByOrderNo(orderNo);
        //log.info("OrderDeliveyRecordTest: order:{} data:{} flag:{}", orderNo,JSON.toJSON(orderDeliveryRecord),orderDeliveryRecord==null);



        orderNo = 1661010913557217281L;
        orderDeliveryRecord = orderDeliveryRecordMapper.selectByOrderNo(orderNo);
        //log.info("OrderDeliveyRecordTest: order:{} data:{} flag:{}", orderNo,JSON.toJSON(orderDeliveryRecord),orderDeliveryRecord==null);



        orderNo = 1141010913557217281L;
        orderDeliveryRecord = orderDeliveryRecordMapper.selectByOrderNo(orderNo);
        //log.info("OrderDeliveyRecordTest: order:{} data:{} flag:{}", orderNo,JSON.toJSON(orderDeliveryRecord),orderDeliveryRecord==null);


    }

    @Test
    public void test(){
        OrderInfo orderInfo = orderBasicService.getOrderBaseInfo(OrderDeliveryRecordTest.ORDER_NO);
        orderBasicService.orderCancelUpdateDeliveryRecord(orderInfo,null);
    }

}
