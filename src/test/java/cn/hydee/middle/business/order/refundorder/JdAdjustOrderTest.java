package cn.hydee.middle.business.order.refundorder;

import cn.hydee.middle.business.order.dto.req.AddOrderInfoReqDto;
import cn.hydee.middle.business.order.v2.factory.OrderForwardOperateFactory;
import cn.hydee.middle.business.order.v2.service.OrderForwardOperateService;

import com.alibaba.fastjson.JSON;
import lombok.extern.slf4j.Slf4j;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.junit4.SpringRunner;

@SpringBootTest(webEnvironment = SpringBootTest.WebEnvironment.RANDOM_PORT)
@RunWith(SpringRunner.class)
@Slf4j
public class JdAdjustOrderTest {

    @Test
    public void createJdOrder() {
        AddOrderInfoReqDto addOrderInfoReqDto = JSON.parseObject(CREATE_MESSAGE, AddOrderInfoReqDto.class);
        OrderForwardOperateService forwardOperateService =
                OrderForwardOperateFactory.getForwardOperateServiceDefault();
        // forwardOperateService.saveOrder(addOrderInfoReqDto);
    }

    @Test
    public void adjustJdOrder() {
        AddOrderInfoReqDto addOrderInfoReqDto = JSON.parseObject(ADJUST_MESSAGE, AddOrderInfoReqDto.class);
        OrderForwardOperateService forwardOperateService =
                OrderForwardOperateFactory.getForwardOperateServiceDefault();
        // forwardOperateService.saveOrder(addOrderInfoReqDto);
    }

    private static final String CREATE_MESSAGE = "{\"isadjust\":0,\"olorderno\":\"1780716608695945216\",\"orderid\":\"1780716608695945216\",\"ectype\":\"43\",\"groupid\":\"500001\",\"clientid\":\"48e245e5f85a4f2b85a20e8a183f4ee1\",\"off_status\":1,\"status\":\"43_7\",\"discount_fee\":\"0.02\",\"total_fee\":\"4.53\",\"adjust_fee\":\"0\",\"buyer_nick\":\"\",\"receiver_address\":null,\"receiver_name\":\"杨飞\",\"receiver_mobile\":\"18190800161\",\"receiver_phone\":\"18190800161\",\"receiver_address_encrypt\":null,\"receiver_name_encrypt\":\"P6uvS/MNuPu1VdvdoBlgwg==\",\"receiver_phone_encrypt\":\"AR9a1ccSFEZfrLNQDo7idQ==\",\"receiver_mobile_encrypt\":\"AR9a1ccSFEZfrLNQDo7idQ==\",\"receiver_state\":null,\"receiver_city\":null,\"receiver_district\":null,\"receiver_town\":null,\"receiver_zip\":null,\"buyer_message\":null,\"post_fee\":\"0\",\"buyer_email\":null,\"trade_from\":\"\",\"expcmpname\":\"\",\"modified\":\"2023-10-25 16:48:18\",\"confirm_time\":\"2023-10-25 16:48:17\",\"created\":\"2023-10-25 16:48:17\",\"end_time\":\"2023-10-25 16:48:18\",\"buyer_memo\":\"\",\"seller_memo\":null,\"o2o_shop_id\":\"A002\",\"buyer_flag\":0,\"seller_flag\":0,\"delivery_type\":\"1\",\"daynum\":\"46\",\"applyCancel\":null,\"o2o_auto_confirm\":1,\"vatTaxpayerNumber\":null,\"invoice_contect\":null,\"invoice_name\":null,\"invoice_type\":null,\"rx_audit_status\":\"1\",\"havecfy\":\"0\",\"cfycheck\":null,\"type\":\"43_0\",\"paytype\":\"1\",\"seller_cod_fee\":\"0\",\"buyer_cod_fee\":\"0\",\"payment\":\"0\",\"discount_fee_dtl\":\"0.02\",\"yfx_fee\":\"0\",\"discount_fee_eccode\":0.0,\"discount_fee_sum\":\"0\",\"cod_payment\":0.0,\"settlement_amount\":\"0\",\"commission_fee\":\"0\",\"package_fee\":\"0\",\"sumdiscount\":\"0\",\"postfee_dis\":\"0\",\"postfee_nodis\":null,\"customerpayment\":\"4.51\",\"pay_time\":\"2023-10-25 16:48:17\",\"omsstatus\":\"3\",\"omsdeliverytype\":\"4\",\"pushtime\":\"2023-10-25 16:48:18\",\"receiver_lat\":null,\"receiver_lng\":null,\"delivery_time\":\"\",\"delivery_time_type\":0,\"riderverifycode\":null,\"selfverifycode\":\"862704\",\"stamp\":\"2023-10-25 16:48:18\",\"mark_desc\":null,\"membercard\":\"900046923024\",\"orderdetaillist\":[{\"olorderno\":\"1780716608695945216\",\"clientid\":\"48e245e5f85a4f2b85a20e8a183f4ee1\",\"ectype\":\"43\",\"oid\":\"1780716611764079104\",\"price\":\"2.53\",\"num_iid\":\"1777979409329304067\",\"item_meal_id\":null,\"num\":\"1\",\"total_fee\":\"2.53\",\"payment\":\"2.51\",\"discount_fee\":\"0.02\",\"adjust_fee\":\"0\",\"modified\":\"2023-10-25 16:48:17\",\"title\":\"来益牌叶黄素咀嚼片_22.5g(450mg*50片) 22.5G(450MG*50片)\",\"sku_properties_name\":null,\"sku_id\":\"849731\",\"outer_iid\":\"849731\",\"isdelete\":0,\"upc\":\"849731\",\"orderdetailid\":\"1780716611764079104\",\"isgift\":\"false\",\"order_store_source\":null,\"expectdeliverytime\":null,\"store_code\":null,\"extrainfo\":\"{\\\"isDirectDelivery\\\":null,\\\"totalHb\\\":null,\\\"healthValue\\\":null}\"},{\"olorderno\":\"1780716608695945216\",\"clientid\":\"48e245e5f85a4f2b85a20e8a183f4ee1\",\"ectype\":\"43\",\"oid\":\"1780716611786099456\",\"price\":\"2\",\"num_iid\":\"1777979409317522435\",\"item_meal_id\":null,\"num\":\"1\",\"total_fee\":\"2\",\"payment\":\"2\",\"discount_fee\":\"0\",\"adjust_fee\":\"0\",\"modified\":\"2023-10-25 16:48:17\",\"title\":\"陈香露白露片_飞云岭23_0.5g*100片 0.5G*100片\",\"sku_properties_name\":null,\"sku_id\":\"149856\",\"outer_iid\":\"149856\",\"isdelete\":0,\"upc\":\"149856\",\"orderdetailid\":\"1780716611786099456\",\"isgift\":\"false\",\"order_store_source\":null,\"expectdeliverytime\":null,\"store_code\":null,\"extrainfo\":\"{\\\"isDirectDelivery\\\":null,\\\"totalHb\\\":null,\\\"healthValue\\\":null}\"}],\"orderprescriptionlist\":[],\"ordergiftlist\":[],\"ordertype\":\"N\",\"isfirstorder\":0,\"extrainfo\":\"{\\\"totalActualHb\\\":\\\"0\\\",\\\"sendStoreId\\\":\\\"A002\\\",\\\"orderEmpCode\\\":null,\\\"orderEmpName\\\":null,\\\"payTypeList\\\":[{\\\"payCode\\\":\\\"809083\\\",\\\"payName\\\":\\\"微商城余额支付\\\",\\\"payPrice\\\":\\\"4.51\\\"}],\\\"medicalInsurance\\\":\\\"0\\\",\\\"couponList\\\":null,\\\"healthValue\\\":null,\\\"b2cOnlineStore\\\":null,\\\"extJson\\\":\\\"{\\\\\\\"centralWarehouseDeliveryFlag\\\\\\\":0,\\\\\\\"performingParty\\\\\\\":\\\\\\\"500001\\\\\\\"}\\\"}\"}";

    private static final String ADJUST_MESSAGE = "{\n" +
            "    \"isadjust\": 1,\n" +
            "    \"olorderno\": \"2021128611000161\",\n" +
            "    \"orderid\": \"2021128611000161\",\n" +
            "    \"ectype\": \"11\",\n" +
            "    \"groupid\": \"999999\",\n" +
            "    \"clientid\": \"933e06f5e05c46e9854875a66d4be965\",\n" +
            "    \"off_status\": 0,\n" +
            "    \"status\": \"32000\",\n" +
            "    \"discount_fee\": \"0\",\n" +
            "    \"total_fee\": \"204\",\n" +
            "    \"adjust_fee\": \"0\",\n" +
            "    \"buyer_nick\": null,\n" +
            "    \"receiver_name\": \"黄燕\",\n" +
            "    \"receiver_state\": null,\n" +
            "    \"receiver_city\": \"成都市\",\n" +
            "    \"receiver_district\": \"成华区\",\n" +
            "    \"receiver_town\": null,\n" +
            "    \"receiver_address\": \"成都市成华区春熙苑-一期2栋1单元5楼17号\",\n" +
            "    \"receiver_zip\": null,\n" +
            "    \"receiver_mobile\": \"13438339116\",\n" +
            "    \"receiver_phone\": \"13438339116\",\n" +
            "    \"buyer_message\": \"\\n缺货时电话与我沟通\",\n" +
            "    \"post_fee\": \"0\",\n" +
            "    \"buyer_email\": null,\n" +
            "    \"trade_from\": \"0\",\n" +
            "    \"expcmpname\": null,\n" +
            "    \"modified\": \"2020-09-01 13:39:23\",\n" +
            "    \"confirm_time\": \"2020-09-01 13:03:30\",\n" +
            "    \"created\": \"2020-09-01 13:03:30\",\n" +
            "    \"end_time\": null,\n" +
            "    \"buyer_memo\": null,\n" +
            "    \"seller_memo\": null,\n" +
            "    \"o2o_shop_id\": \"11947278\",\n" +
            "    \"buyer_flag\": 0,\n" +
            "    \"seller_flag\": 0,\n" +
            "    \"delivery_type\": \"9966\",\n" +
            "    \"daynum\": \"2\",\n" +
            "    \"applyCancel\": null,\n" +
            "    \"o2o_auto_confirm\": null,\n" +
            "    \"vatTaxpayerNumber\": null,\n" +
            "    \"invoice_contect\": null,\n" +
            "    \"invoice_name\": null,\n" +
            "    \"invoice_type\": null,\n" +
            "    \"rx_audit_status\": null,\n" +
            "    \"havecfy\": null,\n" +
            "    \"cfycheck\": null,\n" +
            "    \"type\": \"11_1\",\n" +
            "    \"paytype\": \"1\",\n" +
            "    \"seller_cod_fee\": \"0\",\n" +
            "    \"buyer_cod_fee\": \"0\",\n" +
            "    \"payment\": \"161\",\n" +
            "    \"discount_fee_dtl\": \"0\",\n" +
            "    \"yfx_fee\": \"0\",\n" +
            "    \"discount_fee_eccode\": 0,\n" +
            "    \"discount_fee_sum\": \"43\",\n" +
            "    \"cod_payment\": 0,\n" +
            "    \"settlement_amount\": \"0\",\n" +
            "    \"commission_fee\": \"11.62\",\n" +
            "    \"package_fee\": \"1\",\n" +
            "    \"sumdiscount\": \"38\",\n" +
            "    \"postfee_dis\": \"5\",\n" +
            "    \"postfee_nodis\": \"5\",\n" +
            "    \"customerpayment\": \"167\",\n" +
            "    \"pay_time\": \"2020-09-01 13:03:44\",\n" +
            "    \"omsstatus\": \"3\",\n" +
            "    \"omsdeliverytype\": \"1\",\n" +
            "    \"pushtime\": \"2020-09-01 13:09:42\",\n" +
            "    \"receiver_lat\": \"30.68211\",\n" +
            "    \"receiver_lng\": \"104.12271\",\n" +
            "    \"delivery_time\": \"请于2020-09-01 17:00:00~2020-09-01 17:40:00 送达\",\n" +
            "    \"delivery_time_type\": 1,\n" +
            "    \"riderverifycode\": null,\n" +
            "    \"selfverifycode\": null,\n" +
            "    \"stamp\": \"2020-09-01T13:09:39.429+08:00\",\n" +
            "    \"mark_desc\": null,\n" +
            "    \"membercard\": null,\n" +
            "    \"orderdetaillist\": [\n" +
            "        {\n" +
            "            \"olorderno\": \"2021128611000161\",\n" +
            "            \"clientid\": \"933e06f5e05c46e9854875a66d4be965\",\n" +
            "            \"ectype\": \"11\",\n" +
            "            \"oid\": \"1002250_1\",\n" +
            "            \"price\": \"68\",\n" +
            "            \"num_iid\": \"2021142668\",\n" +
            "            \"item_meal_id\": \"0\",\n" +
            "            \"num\": \"1\",\n" +
            "            \"total_fee\": \"204\",\n" +
            "            \"payment\": \"204\",\n" +
            "            \"discount_fee\": \"0\",\n" +
            "            \"adjust_fee\": \"0\",\n" +
            "            \"modified\": \"2020-09-01 13:09:41\",\n" +
            "            \"title\": \"金丐 醋酸钙胶囊 0.6g*15粒/盒\",\n" +
            "            \"sku_properties_name\": null,\n" +
            "            \"sku_id\": \"6925958520605\",\n" +
            "            \"outer_iid\": \"1002250\",\n" +
            "            \"isdelete\": 0,\n" +
            "            \"upc\": \"6925958520605\",\n" +
            "            \"orderdetailid\": \"1002250_1\",\n" +
            "            \"isgift\": \"false\"\n" +
            "        },\n" +
            "        {\n" +
            "            \"olorderno\": \"2021128611000161\",\n" +
            "            \"clientid\": \"933e06f5e05c46e9854875a66d4be965\",\n" +
            "            \"ectype\": \"11\",\n" +
            "            \"oid\": \"1002250_6\",\n" +
            "            \"price\": \"0\",\n" +
            "            \"num_iid\": \"2021142668\",\n" +
            "            \"item_meal_id\": \"0\",\n" +
            "            \"num\": \"1\",\n" +
            "            \"total_fee\": \"0\",\n" +
            "            \"payment\": \"0\",\n" +
            "            \"discount_fee\": \"0\",\n" +
            "            \"adjust_fee\": \"0\",\n" +
            "            \"modified\": \"2020-09-01 13:09:41\",\n" +
            "            \"title\": \"金丐 醋酸钙胶囊 0.6g*15粒/盒\",\n" +
            "            \"sku_properties_name\": null,\n" +
            "            \"sku_id\": \"6925958520605\",\n" +
            "            \"outer_iid\": \"1002250\",\n" +
            "            \"isdelete\": 0,\n" +
            "            \"upc\": \"6925958520605\",\n" +
            "            \"orderdetailid\": \"1002250_6\",\n" +
            "            \"isgift\": \"true\"\n" +
            "        }\n" +
            "    ],\n" +
            "    \"orderprescriptionlist\": [],\n" +
            "    \"ordergiftlist\": []\n" +
            "}";
}
