package cn.hydee.middle.business.order.nethttp;

import cn.hydee.middle.business.order.Enums.DsConstants;
import cn.hydee.middle.business.order.dto.rsp.baseinfo.LogisticsCompanyResDTO;
import cn.hydee.middle.business.order.entity.DeliveryLogisticsCompany;
import cn.hydee.middle.business.order.entity.OrderInfo;
import cn.hydee.middle.business.order.http.NetHttpAdapter;
import cn.hydee.middle.business.order.mapper.DeliveryLogisticsCompanyMapper;
import cn.hydee.middle.business.order.mapper.OrderInfoMapper;
import cn.hydee.middle.business.order.service.image.FileUpLoadService;
import cn.hydee.middle.business.order.v2.manager.base.OrderBasicManager;
import cn.hydee.unified.model.order.OrderInformation;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import lombok.extern.slf4j.Slf4j;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.junit4.SpringRunner;
import org.springframework.util.CollectionUtils;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.List;

@SpringBootTest(webEnvironment = SpringBootTest.WebEnvironment.RANDOM_PORT)
@RunWith(SpringRunner.class)
@Slf4j
public class NetHttpAdapterTest {

    @Autowired
    private NetHttpAdapter netHttpAdapter;

    @Resource
    private DeliveryLogisticsCompanyMapper deliveryLogisticsCompanyMapper;

    @Autowired
    private FileUpLoadService fileUpLoadService;

    @Autowired
    private OrderBasicManager orderBasicManager;

    @Autowired
    private OrderInfoMapper orderInfoMapper;

    @Test
    public void getorderInfomation() {
        OrderInfo orderInfo = orderInfoMapper.selectOne(new LambdaQueryWrapper<OrderInfo>().eq(OrderInfo::getThirdOrderNo, "2800902381065453691"));
        OrderInformation orderInformation = orderBasicManager.getOrderInformation(orderInfo);
        System.out.println(orderInformation);
    }

//    @Test
//    public void getOrderDetail(){
//        NetOrderGetSingleReqDto netOrderGetSingleReqDto = new NetOrderGetSingleReqDto();
//        netOrderGetSingleReqDto.setOlorderno("1686031038094395136");
//        netOrderGetSingleReqDto.setEctype("43");
//        ResponseNet<List<AddOrderInfoReqDto>> addOrderInfoReqDto = netHttpAdapter.getOrderDetailSingle("888888","43","41c9e98ce4884f4880c5e2925864bb50","457ca2d53a5144b08e5f5a1eab553ac1",netOrderGetSingleReqDto);
//        System.out.println("获取订单详情记录："+JSON.toJSON(addOrderInfoReqDto));
//    }

    /**
     * 获取物流公司
     */
    @Test
    public void getLogisticsCompany(){
        //根据商户编码查询sessionKey
        /*String sessionKey = "457ca2d53a5144b08e5f5a1eab553ac1";
        String clientCode = "3f3908e79c2843748437ef2f5bf60f91";
        ResponseNet<List<LogisticsCompanyResDTO>> logisticsCompany = netHttpAdapter.getLogistics("888888",
                "9998",clientCode,sessionKey);
        List<LogisticsCompanyResDTO> logisticsCompanyList = logisticsCompany.getData();*/

        //京东健康快递公司
        List<LogisticsCompanyResDTO> logisticsCompanyList = new ArrayList<>();
        LogisticsCompanyResDTO logisticsCompanyResDTO1 = new LogisticsCompanyResDTO();
        logisticsCompanyResDTO1.setLogisticsCode("2087");
        logisticsCompanyResDTO1.setLogisticsName("京东快递");
        logisticsCompanyList.add(logisticsCompanyResDTO1);

        LogisticsCompanyResDTO logisticsCompanyResDTO2 = new LogisticsCompanyResDTO();
        logisticsCompanyResDTO2.setLogisticsCode("467");
        logisticsCompanyResDTO2.setLogisticsName("顺丰快递");
        logisticsCompanyList.add(logisticsCompanyResDTO2);

        LogisticsCompanyResDTO logisticsCompanyResDTO3 = new LogisticsCompanyResDTO();
        logisticsCompanyResDTO3.setLogisticsCode("470");
        logisticsCompanyResDTO3.setLogisticsName("申通快递");
        logisticsCompanyList.add(logisticsCompanyResDTO3);

        LogisticsCompanyResDTO logisticsCompanyResDTO4 = new LogisticsCompanyResDTO();
        logisticsCompanyResDTO4.setLogisticsCode("463");
        logisticsCompanyResDTO4.setLogisticsName("圆通快递");
        logisticsCompanyList.add(logisticsCompanyResDTO4);

        LogisticsCompanyResDTO logisticsCompanyResDTO5 = new LogisticsCompanyResDTO();
        logisticsCompanyResDTO5.setLogisticsCode("1499");
        logisticsCompanyResDTO5.setLogisticsName("中通速递");
        logisticsCompanyList.add(logisticsCompanyResDTO5);

        LogisticsCompanyResDTO logisticsCompanyResDTO6 = new LogisticsCompanyResDTO();
        logisticsCompanyResDTO6.setLogisticsCode("1327");
        logisticsCompanyResDTO6.setLogisticsName("韵达快递");
        logisticsCompanyList.add(logisticsCompanyResDTO6);

        LogisticsCompanyResDTO logisticsCompanyResDTO7 = new LogisticsCompanyResDTO();
        logisticsCompanyResDTO7.setLogisticsCode("1748");
        logisticsCompanyResDTO7.setLogisticsName("百世快递");
        logisticsCompanyList.add(logisticsCompanyResDTO7);

        if(!CollectionUtils.isEmpty(logisticsCompanyList)){
            for (LogisticsCompanyResDTO dto : logisticsCompanyList){
                DeliveryLogisticsCompany deliveryLogisticsCompany = new DeliveryLogisticsCompany();
                deliveryLogisticsCompany.setLogisticsCode(dto.getLogisticsCode()).setLogisticsName(dto.getLogisticsName()).setPlatformCode("1001").setState(DsConstants.INTEGER_ONE);
                deliveryLogisticsCompanyMapper.insert(deliveryLogisticsCompany);
            }
        }
    }
}
