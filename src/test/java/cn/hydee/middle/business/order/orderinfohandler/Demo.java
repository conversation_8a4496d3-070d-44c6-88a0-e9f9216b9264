package cn.hydee.middle.business.order.orderinfohandler;

import cn.hydee.middle.business.order.dto.req.AddOrderDetailReqDto;
import org.apache.commons.lang3.StringUtils;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.List;

public class Demo {
    public static void main(String[] args){
        List<AddOrderDetailReqDto> detailListDto = new ArrayList<>();
        AddOrderDetailReqDto addOrderDetailReqDto = new AddOrderDetailReqDto();
        addOrderDetailReqDto.setTotal_fee("");
        detailListDto.add(addOrderDetailReqDto);

        AddOrderDetailReqDto addOrderDetailReqDto2 = new AddOrderDetailReqDto();
        addOrderDetailReqDto2.setTotal_fee("");
        detailListDto.add(addOrderDetailReqDto2);

        AddOrderDetailReqDto addOrderDetailReqDto3 = new AddOrderDetailReqDto();
        addOrderDetailReqDto3.setTotal_fee("");
        detailListDto.add(addOrderDetailReqDto3);

        //sum(total_Fee)
        BigDecimal totalFeeSum = detailListDto.stream()
                .filter(v-> StringUtils.isNotEmpty(v.getTotal_fee()))
                .map(AddOrderDetailReqDto::getTotal_fee)
                .map(total_fee->new BigDecimal(total_fee))
                .reduce(BigDecimal.ZERO,BigDecimal::add);
        System.out.println("总金额："+totalFeeSum);


        BigDecimal discountShareSum = BigDecimal.ZERO;
        BigDecimal discountShare = new BigDecimal("4.685");
        discountShareSum = discountShareSum.add(discountShare).setScale(2, BigDecimal.ROUND_HALF_UP);
        System.out.println("已分摊金额："+discountShareSum);
    }
}
