package cn.hydee.middle.business.order.refund.handle.impl;

import cn.hydee.middle.business.order.BaseTest;
import cn.hydee.middle.business.order.Enums.ErpStateEnum;
import cn.hydee.middle.business.order.Enums.OrderStateEnum;
import cn.hydee.middle.business.order.entity.OrderInfo;
import cn.hydee.middle.business.order.mapper.OrderInfoMapper;
import com.baomidou.mybatisplus.core.conditions.update.UpdateWrapper;
import org.junit.Test;
import org.springframework.beans.factory.annotation.Value;

import javax.annotation.Resource;

/**
 * <AUTHOR> (moatkon)
 * @date 2024年01月23日 10:21
 * @email: <EMAIL>
 */
public class RefundOrderProcessServiceImplTest extends BaseTest {

    @Resource
    private OrderInfoMapper orderInfoMapper;

    @Value("${business.order.updateErpStateOnOff:true}")
    private Boolean updateErpStateOnOff;

    @Test
    public void updateTest() {
        System.out.println(updateErpStateOnOff);
        for (; ; ) {
            OrderInfo orderInfo = new OrderInfo();
            orderInfo.setOrderNo(1785219391724155648L);

            OrderInfo curOrderInfo = orderInfoMapper.selectBaseInfoByOrderNo(orderInfo.getOrderNo());
            if (curOrderInfo.getOrderState().equals(OrderStateEnum.CLOSED.getCode())) { // 如果订单是已关闭
                OrderInfo update = new OrderInfo();
                update.setErpState(ErpStateEnum.CANCELED.getCode());

                UpdateWrapper<OrderInfo> updateWrapper = new UpdateWrapper<>();
                updateWrapper.lambda().eq(OrderInfo::getOrderNo, orderInfo.getOrderNo())
                        .eq(OrderInfo::getOrderState, OrderStateEnum.CLOSED.getCode())
                        .eq(OrderInfo::getErpState, orderInfo.getErpState());
                int update1 = orderInfoMapper.update(update, updateWrapper);
                System.out.println(update1);
            }
        }
    }
}
