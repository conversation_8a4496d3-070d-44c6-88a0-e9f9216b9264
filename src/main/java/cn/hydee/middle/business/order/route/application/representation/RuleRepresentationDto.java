package cn.hydee.middle.business.order.route.application.representation;

import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.fasterxml.jackson.databind.ser.std.ToStringSerializer;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

@Data
@ApiModel(description = "规则信息")
public class RuleRepresentationDto {

  @JsonSerialize(using = ToStringSerializer.class)
  @ApiModelProperty(value = "规则Id")
  private Long id;

  @ApiModelProperty(value = "规则名称")
  private String ruleName;

  @ApiModelProperty(value = "规则类型 分单-HAND_OUT 接单-RECEIVE")
  private String ruleType;

  @ApiModelProperty(value = "是否所有场景必选 0-否 1-是")
  private Integer required;

  @ApiModelProperty(value = "处理类型")
  private String handleType;

  @ApiModelProperty(value = "意义")
  private String meaning;
}
