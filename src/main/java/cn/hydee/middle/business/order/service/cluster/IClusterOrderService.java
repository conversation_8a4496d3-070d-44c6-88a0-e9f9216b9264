
  
package cn.hydee.middle.business.order.service.cluster;

import java.util.List;

import com.baomidou.mybatisplus.core.metadata.IPage;

import cn.hydee.middle.business.order.dto.rsp.baseinfo.QueryStoreByCityRspDto;
import cn.hydee.middle.business.order.point.dto.req.AreaReqDto;
import cn.hydee.middle.business.order.point.dto.req.OnlineStoreOrderReqDto;
import cn.hydee.middle.business.order.point.dto.req.PointClusterBaseReqDto;
import cn.hydee.middle.business.order.point.dto.req.PointClusterReqDto;
import cn.hydee.middle.business.order.point.dto.req.StoreReqDto;
import cn.hydee.middle.business.order.point.dto.rsp.AreaRspDto;
import cn.hydee.middle.business.order.point.dto.rsp.MerchantCityRspDto;
import cn.hydee.middle.business.order.point.dto.rsp.OnlineStoreOrderCountDto;
import cn.hydee.middle.business.order.point.dto.rsp.PointClusterRspDto;
import cn.hydee.middle.business.order.point.dto.rsp.QueryOnlineStoreAndOrderRspDto;

/**  
 * ClassName:ClusterOrderService <br/>  
 * Date:     2020年12月8日 下午5:00:51 <br/>  
 * <AUTHOR>  
 */
public interface IClusterOrderService {

	/**  
	 * queryMerchantCityList:通过商户编码获取旗下门店分布城市数据. <br/>  
	 *  
	 * @param merCode 商户编码
	 * @return  List
	 * <AUTHOR>  
	 * @date 2020年12月8日 下午5:05:26
	 */
	List<MerchantCityRspDto> queryMerchantCityList(String merCode);

	/**  
	 * queryStoreByCity:通过城市编码分页查询门店. <br/>  
	 *  
	 * @param req {@link StoreReqDto}
	 * @return  IPage
	 * <AUTHOR>  
	 * @date 2020年12月8日 下午5:05:33
	 */
	IPage<QueryStoreByCityRspDto> queryStoreByCity(StoreReqDto req);

	/**  
	 * queryOrderByCluster:按地图层级获取聚合订单数据. <br/>  
	 *  
	 * @param req {@link PointClusterReqDto}
	 * @return  List
	 * <AUTHOR>  
	 * @date 2020年12月8日 下午5:05:37
	 */
	List<PointClusterRspDto> queryOrderByCluster(PointClusterReqDto req);
	
	/**
	 * 根据线下门店编码检索门店数量、订单数量、平台订单分布
	 *  
	 * @param req {@link PointClusterBaseReqDto}
	 * @return  QueryOnlineStoreAndOrderRspDto
	 * <AUTHOR>  
	 * @date 2020年12月21日 下午11:48:06
	 */
	QueryOnlineStoreAndOrderRspDto queryOnlineStoreAndOrderCount(PointClusterBaseReqDto req);
	
	/**
	 * 
	 * 查省市区数据
	 *  
	 * @param merCode
	 * @param req
	 * @return  
	 * <AUTHOR>  
	 * @date 2020年12月22日 下午12:56:28
	 */
	List<AreaRspDto> queryAreaData(String merCode,AreaReqDto req);

	/**  
	 * 获取线上门店总量、订单总量
	 *  
	 * @param req
	 * @return  
	 * <AUTHOR>  
	 * @date 2020年12月22日 下午8:35:27
	 */
	OnlineStoreOrderCountDto queryAllOnlineStoreAndOrderCount(OnlineStoreOrderReqDto req);

}
  
