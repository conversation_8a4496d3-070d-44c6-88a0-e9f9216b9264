package cn.hydee.middle.business.order.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

import java.io.Serializable;
import java.util.Date;

/**
 * <p>
 * 切换新服务商户门店
 * </p>
 *
 * <AUTHOR>
 * @since 2021-04-20
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
public class MerchantWsInfo implements Serializable {

    private static final long serialVersionUID=1L;

    @TableId(value = "id", type = IdType.AUTO)
    private Long id;

    /**
     * 商户编码
     */
    private String merCode;

    /**
     * 线下门店编码
     */
    private String organizationCode;

    /**
     * 0-非全部切换 1-全部切换
     */
    private Integer allFlag;

    /**
     * 创建时间
     */
    private Date createTime;

    /**
     * 末次修改时间
     */
    private Date modifyTime;

    /**
     * 老版本写redis标识，0-开启 1-关闭 默认开启
     */
    private Integer oldPrintFlag;

    /**
     * 接收商户所有门店声音，0-不接收 1-接收 默认0
     */
    private Integer allStoreSound;

}
