package cn.hydee.middle.business.order.route.domain.external;

import com.alibaba.fastjson.annotation.JSONField;
import java.util.List;
import lombok.Data;

/**
 * 请求.net 调用三方比价接口
 *
 * <AUTHOR>
 * @date 2024/03/29 14:49
 **/
@Data
public class ReqCreateRiderOrderDto {

    /// <summary>
    /// 订单信息
    /// </summary>
    public RiderOrderInfoDto order;

    /// <summary>
    /// 发货人信息
    /// </summary>
    public RiderTransportInfoDto transport ;

    /// <summary>
    /// 收货人信息
    /// </summary>
    public RiderReceiverInfoDto receiver;

    /// <summary>
    /// 商品信息
    /// </summary>
    public List<RiderItemInfoDto> items;
    @Data
    public static class RiderOrderInfoDto {
        /// <summary>
        /// 平台类型
        /// </summary>
        public String ectype ;
        /// <summary>
        /// 企业编码
        /// </summary>
        public String groupid ;
        /// <summary>
        /// 网店编码
        /// </summary>
        public String clientid ;
        /// <summary>
        /// 配送ID  由外部系统生成
        /// </summary>
        public Long deliveryid;
        /// <summary>
        /// 订单ID 
        /// </summary>
        public String orderid ;
        /// <summary>
        /// 门店ID
        /// </summary>
        public String shopid ;

        /// <summary>
        /// 订单所在城市的code
        /// </summary>
        public String citycode ;

        /// <summary>
        /// 255	是 回调地址, 订单状态变更时会调用此接口传递状态信息
        /// </summary>
        @JSONField(name="callback_url")
        public String callbackUrl ;
        /// <summary>
        /// 是 订单类型（1:即时单，3:预约单）
        /// </summary>
        @JSONField(name="order_type")
        public Integer orderType ;

        /// <summary>
        /// 否 下单时间
        /// </summary>
        @JSONField(name="order_add_time")
        public String orderAddTime ;
        /// <summary>
        /// 10	是 订单总金额（不包含商家的任何活动以及折扣的金额）
        /// </summary>
        @JSONField(name="order_total_amount")
        public String orderTotalAmount;

        /// <summary>
        /// 10	是 客户需要支付的金额
        /// </summary>
        @JSONField(name="order_actual_amount")
        public String orderActualAmount ;
        /// <summary>
        /// 11	否 订单总重量（kg），营业类型选定为果蔬生鲜、商店超市、其他三类时必填，大于0kg并且小于等于15kg
        /// </summary>
        @JSONField(name="order_weight")
        public String orderWeight ;

        /// <summary>
        /// <summary>
        /// 255	否 商户备注信息
        /// </summary>
        @JSONField(name="partner_remark")
        public String partnerRemark ;
        /// <summary>
        /// 255	否 用户备注
        /// </summary>
        @JSONField(name="order_remark")
        public String orderRemark ;
        /// <summary>
        /// 是 是否需要发票, 0:不需要, 1:需要
        /// </summary>
        @JSONField(name="is_invoiced")
        public Integer isInvoiced ;
        /// <summary>
        /// 128	否 发票抬头, 如果需要发票, 此项必填
        /// </summary>
        public String invoice ;
        /// <summary>
        /// 是 订单支付状态 0:未支付 1:已支付
        /// </summary>
        @JSONField(name="order_payment_status")
        public Integer orderPaymentStatus ;
        /// <summary>
        /// 是否需要垫付 1:是 0:否 (垫付订单金额，非运费)
        /// </summary>
        @JSONField(name="is_prepay")
        public Integer isPrepay ;
        /// <summary>
        /// 是 订单支付方式 1:在线支付
        /// </summary>
        @JSONField(name="order_payment_method")
        public Integer orderPaymentMethod ;
        /// <summary>
        /// 是 是否需要ele代收 0:否
        /// </summary>
        @JSONField(name="is_agent_payment")
        public Integer isAgentPayment ;
        /// <summary>
        /// 否 需要代收时客户应付金额, 如果需要ele代收 此项必填
        /// </summary>
        @JSONField(name="require_payment_pay")
        public String requirePaymentPay;
        /// <summary>
        /// 6	否 商家订单流水号, 方便配送骑手到店取货, 支持数字, 字母及#等常见字符. 如不填写对骑手到店取货的效率产生影响，建议填写
        /// </summary>
        @JSONField(name="serial_number")
        public String serialNumber ;

        /// <summary>
        /// 配送服务代码，详情见合同 飞速达:4002 快速达:4011 及时达:4012 集中送:4013
        /// </summary>
        @JSONField(name="delivery_service_code")
        public String deliveryServiceCode ;
        /// <summary>
        /// 货物价格
        /// </summary>
        @JSONField(name="goods_value")
        public String goodsValue ;

        /// <summary>
        /// 预约单配送时间
        /// </summary>
        @JSONField(name="expect_time")
        public String expectTime ;


        /// <summary>
        /// 预约配送时间
        /// </summary>
        @JSONField(name="expected_delivery_time")
        public String expectedDeliveryTime ;

        /// <summary>
        /// 是否保价  0：非保价；1：保价  顺丰同城必填
        /// </summary>
        @JSONField(name="is_insured")
        public Integer isInsured;

        /// <summary>
        /// 是否是专人直送订单 0：否；1：是   顺丰同城必填
        /// </summary>
        @JSONField(name="is_person_direct")
        public Integer isPersonDirect ;

        /// <summary>
        /// 保价金额
        /// </summary>
        @JSONField(name="declared_value")
        public Integer declaredValue ;
        /// <summary>
        /// 支付方式，0、账期支付，1、余额支付；
        /// </summary>
        @JSONField(name="pay_type_code")
        public Integer payTypeCode ;


        /// <summary>
        /// 预询标识 入单时需传入，标识本次预询
        /// </summary>
        @JSONField(name="t_index_id")
        public String tIndexId ;

        /// <summary>
        /// 优惠后配送费总价格(含入参小费金额) 入单实际价格 取自预下单接口出
        /// </summary>
        @JSONField(name="actual_delivery_amount_cent")
        public String actualDeliveryAmountCent ;

        /// <summary>
        /// 服务商品id
        /// 校验预询配送费价格时 必传
        /// </summary>
        @JSONField(name="service_goods_id")
        public String serviceGoodsId ;

        /// <summary>
        /// 基础商品id
        /// 校验预询配送费价格时 必传
        /// </summary>
        @JSONField(name="base_goods_id")
        public String baseGoodsId ;

        /// <summary>
        /// 订单来源
        /// </summary>
        @JSONField(name="order_source")
        public String orderSource ;

        /// <summary>
        /// 订单来源订单
        /// </summary>
        @JSONField(name="order_source_order_id")
        public String orderSourceOrderId ;

        /// <summary>
        /// 0:不使用， 1:使用 默认使用
        /// </summary>
        @JSONField(name="use_coupon")
        public String useCoupon ;
    }

    @Data
    public static class RiderTransportInfoDto {
        /// <summary>
        /// 是 取货点经纬度来源（1:腾讯地图, 2:百度地图, 3:高德地图），蜂鸟建议使用高德地图
        /// </summary>
        @JSONField(name="position_source")
        public String positionSource ;
        /// <summary>
        /// 32	是 门店名称（支持汉字、符号、字母的组合），后期此参数将预留另用
        /// </summary>
        public String name ;
        /// <summary>
        /// 255	是 取货点地址，后期此参数将预留另用
        /// </summary>
        public String address ;
        /// <summary>
        /// 是 取货点经度，取值范围0～180，后期此参数将预留另用
        /// </summary>
        public String longitude ;
        /// <summary>
        /// 是 取货点纬度，取值范围0～90，后期此参数将预留另用
        /// </summary>
        public String latitude ;

        /// <summary>
        /// 16	是 取货点联系方式, 只支持手机号,400开头电话以及座机号码
        /// </summary>
        public String phone ;

        /// <summary>
        ///  取货点备注
        /// </summary>
        public String remark ;
    }
    @Data
    public static class RiderReceiverInfoDto {
        /// <summary>
        /// 是 收货经纬度来源（1:腾讯地图, 2:百度地图, 3:高德地图），蜂鸟建议使用高德地图
        /// </summary>
        @JSONField(name="position_source")
        public String positionSource ;
        /// <summary>
        /// 30	是 收货人姓名
        /// </summary>
        public String name ;
        /// <summary>
        /// 64	是 收货人联系方式，只支持手机号，400开头电话，座机号码以及95013开头、长度13位的虚拟电话
        /// </summary>
        public String phone ;
        /// <summary>
        /// 64	否 收货人备用联系方式，只支持手机号，400开头电话，座机号码以及95013开头、长度13位的虚拟电话
        /// </summary>
        public String second_phone ;
        /// <summary>
        /// 255	是 收货人地址
        /// </summary>
        public String address ;
        /// <summary>
        /// 是 收货人经度，取值范围0～180
        /// </summary>
        public String longitude ;
        /// <summary>
        /// 是 收货人纬度，取值范围0～90
        /// </summary>
        public String latitude ;
    }

    @Data
    public static class RiderItemInfoDto {
        /// <summary>
        /// 否 商品编号
        /// </summary>
        @JSONField(name="item_id")
        public String itemId ;
        /// <summary>
        /// 是 商品名称(不超过128个字符)
        /// </summary>
        @JSONField(name="item_name")
        public String itemName ;
        /// <summary>
        /// 是 商品数量
        /// </summary>
        @JSONField(name="item_quantity")
        public Integer itemQuantity ;
        /// <summary>
        /// 是 商品原价
        /// </summary>
        @JSONField(name="item_price")
        public String itemPrice ;
        /// <summary>
        /// 是 商品实际支付金额，必须是乘以数量后的金额，否则影响售后环节的赔付标准
        /// </summary>
        @JSONField(name="item_actual_price")
        public String itemActualPrice ;
        /// <summary>
        /// 否 商品尺寸
        /// </summary>
        @JSONField(name="item_size")
        public Integer itemSize ;
        /// <summary>
        /// 否 商品备注(不超过255个字符)
        /// </summary>
        @JSONField(name="item_remark")
        public String itemRemark ;

        /// <summary>
        /// 是 是否代购 0:否
        /// </summary>
        @JSONField(name="is_agent_purchase")
        public int isAgentPurchase ;

    }

}

