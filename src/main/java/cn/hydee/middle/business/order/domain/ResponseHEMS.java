package cn.hydee.middle.business.order.domain;

import cn.hydee.middle.business.order.Enums.DsConstants;
import cn.hydee.starter.dto.ErrorType;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

@Data
public class ResponseHEMS<T>  {
    @ApiModelProperty("请求成功标识")
    private Boolean success;
    @ApiModelProperty("0 成功 1 失败")
    private Integer code;
    @ApiModelProperty("请求返回信息")
    private String msg;
    @ApiModelProperty("请求返回实体对象")
    private T data;
    private Long timestamp = System.currentTimeMillis();

    public ResponseHEMS() {
    }

    // 成功
    public boolean checkSuccess() {
        return Boolean.TRUE.equals(success) || DsConstants.INTEGER_ZERO.equals(this.code);
    }

    public static <T> ResponseHEMS<T> success() {
        ResponseHEMS<T> base = new ResponseHEMS();
        base.setSuccess(Boolean.TRUE);
        base.setCode(0);
        base.setMsg("操作成功");
        return base;
    }

    public static <T> ResponseHEMS<T> invalidData(String errMsg) {
        ResponseHEMS<T> base = new ResponseHEMS();
        base.setSuccess(Boolean.FALSE);
        base.setCode(1);
        base.setMsg(errMsg);
        return base;
    }

    public static <T> ResponseHEMS<T> failed(String errMsg) {
        ResponseHEMS<T> base = new ResponseHEMS();
        base.setSuccess(Boolean.FALSE);
        base.setCode(1);
        base.setMsg(errMsg);
        return base;
    }
}
