package cn.hydee.middle.business.order.mapper;

import cn.hydee.middle.business.order.entity.DeliveryLogisticsCompany;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import org.apache.ibatis.annotations.Param;
import org.springframework.stereotype.Repository;

import java.util.List;

/**
 * <p>
 * 发货物流公司表 Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2020-12-12
 */
@Repository
public interface DeliveryLogisticsCompanyMapper extends BaseMapper<DeliveryLogisticsCompany> {

    /**
     * 查询O2O第三方配送列表
     * @param dto
     * @return
     */
    //List<DeliveryLogisticsCompany> queryDeliveryCompany(DeliveryLogisticsCompany dto);

    /**
     * 批量新增
     * @param insertLogisticsList
     * @return
     */
    int insertBatch(List<DeliveryLogisticsCompany> insertLogisticsList);

    /**
     * 批量修改
     * @param updateLogisticsList
     * @return
     */
    int updateByBatch(List<DeliveryLogisticsCompany> updateLogisticsList);

    /**
     * 关联物流编码查询hems物流
     * @param platformCode
     * @param logisticsCode
     * @param relatePlatformCode
     * @return
     */
    DeliveryLogisticsCompany queryOneByRelatePlatformCode(@Param("platformCode") String platformCode,
                                                          @Param("logisticsCode") String logisticsCode,
                                                          @Param("relatePlatformCode") String relatePlatformCode);
}
