
package cn.hydee.middle.business.order.batch.imports.dto;

import cn.hydee.batch.annotation.EnableBatchImportTask;
import cn.hydee.batch.dto.BatchImportBaseDTO;
import cn.hydee.middle.business.order.batch.annotation.ValidateValue;
import cn.hydee.middle.business.order.batch.imports.constant.ImportConstant;
import cn.hydee.middle.business.order.batch.imports.process.BatchSetStoreDeliveryProcessor;
import com.alibaba.excel.annotation.ExcelIgnore;
import com.alibaba.excel.annotation.ExcelProperty;
import com.alibaba.excel.annotation.write.style.ColumnWidth;
import com.alibaba.excel.annotation.write.style.ContentRowHeight;
import com.alibaba.excel.annotation.write.style.HeadRowHeight;
import lombok.Data;
import lombok.EqualsAndHashCode;

@Data
@EqualsAndHashCode(callSuper = true)
@EnableBatchImportTask(businessType = ImportConstant.SET_STOREDELIVERY, taskName = "批量设置配送门店",
        processor = BatchSetStoreDeliveryProcessor.class, batchSize = 5, checkHead = false)
@HeadRowHeight(value = 50)
@ColumnWidth(30)
@ContentRowHeight(30)
public class BatchSetStoreDeliveryDto extends BatchImportBaseDTO {

	@ExcelProperty(value = "*平台编码（必填）")
	@ValidateValue(message="平台编码值为空或不合法")
	private String platformCode;

	@ExcelProperty(value = "平台名称（非必填）")
	private String platformName;

	@ExcelProperty(value = "*平台门店编码（必填）")
	@ValidateValue(message="平台门店编码值为空或不合法")
	private String onlineStoreCode;

	@ExcelProperty(value = "线上门店名称（非必填）")
	private String onlineStoreName;

	@ExcelProperty(value = "*配送平台编码（必填）")
	@ValidateValue(message="配送平台编码值为空或不合法")
	private String deliveryPlatCode;

	@ExcelProperty(value = "配送平台名称（非必填）")
	private String deliveryPlatName;

	@ExcelProperty(value = "*配送门店ID（必填）")
	@ValidateValue(message="配送门店ID值为空或不合法")
	private String deliveryStoreCode;

	@ExcelProperty(value = "配送门店名称（非必填）")
	private String deliveryStoreName;

	@ExcelProperty(value = "处理结果")
	private String result = ImportConstant.FAIL;

	@ExcelIgnore
	private String onlineStoreId;
}
