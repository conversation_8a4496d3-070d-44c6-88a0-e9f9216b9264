package cn.hydee.middle.business.order.dto.req;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.Max;
import javax.validation.constraints.Min;
import javax.validation.constraints.NotNull;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2020/1/2 19:51
 */
@Data
public class RefundAuditHandleReq extends RefundHandleBaseReq {
    @ApiModelProperty(value = "备注", required = true)
    @NotNull(message = "备注不能为空")
    private String remark;

    @ApiModelProperty(value = "审核类型，1同意仅退款，2同意退货退款，3拒绝", required = true)
    @NotNull(message = "审核类型不能为空")
    @Min(value = 1, message = "审核类型最小值为1")
    @Max(value = 3, message = "审核类型最大值为3")
    private Integer type;

    @ApiModelProperty(value = "是否为二次弹框，确认骑手取消失败也退款；0 第一次弹框 1 第二次弹框", required = true)
    @NotNull(message = "是否为二次弹框的确认不能为空")
    @Min(value = 0, message = "只能为0或1")
    @Max(value = 1, message = "只能为0或1")
    private Integer doubleCheck;

    @ApiModelProperty(value = "拒绝原因编码")
    private String reasonCode;

    @ApiModelProperty(value = "拒绝原因描述")
    private String reasonMsg;

}
