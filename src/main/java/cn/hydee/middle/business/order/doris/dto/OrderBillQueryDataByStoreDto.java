package cn.hydee.middle.business.order.doris.dto;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * <AUTHOR>
 * @date 2021/06/24
 */
@Data
public class OrderBillQueryDataByStoreDto extends OrderBillPercentDto {

    private static final long serialVersionUID = -667446040160943498L;

    @ApiModelProperty(value = "组织机构编码")
    private String organizationCode;
    @ApiModelProperty(value = "组织机构名称")
    private String organizationName;
    @ApiModelProperty(value = "平台")
    private String platformCode;
    @ApiModelProperty(value = "线上店编码")
    private String onlineStoreCode;
    @ApiModelProperty(value = "线上店名称")
    private String onlineStoreName;

}
