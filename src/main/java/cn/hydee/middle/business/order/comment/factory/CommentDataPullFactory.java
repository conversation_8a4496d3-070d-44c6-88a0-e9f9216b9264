
package cn.hydee.middle.business.order.comment.factory;

import java.util.List;
import java.util.Map;
import java.util.concurrent.ConcurrentHashMap;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import cn.hydee.middle.business.order.comment.base.dto.CommentAllDto;
import cn.hydee.middle.business.order.comment.base.dto.req.PullCommentDataReqDto;
import cn.hydee.middle.business.order.comment.base.enums.PullCommentDataPlatformTypeEnum;
import cn.hydee.middle.business.order.comment.strategy.AbstractCommentDataPullStrategy;

@Service
public class CommentDataPullFactory {
	
	private final Map<String, AbstractCommentDataPullStrategy> strategies = new ConcurrentHashMap<>();
	
	private AbstractCommentDataPullStrategy getStrategy(String key) {
		return strategies.get(key);
	}
	
	@Autowired
    public CommentDataPullFactory(Map<String, AbstractCommentDataPullStrategy> strategyMap) {
        this.strategies.clear();
        strategyMap.forEach((k, v)-> this.strategies.put(k, v));
    }
	
	public List<CommentAllDto> pullData(PullCommentDataPlatformTypeEnum typeEnum,PullCommentDataReqDto reqDto,String contextId){
		AbstractCommentDataPullStrategy strategy = this.getStrategy(typeEnum.getCode());
		return strategy.pullData(reqDto,contextId);
	}
	
	public void saveData(List<CommentAllDto> dataList) {
		AbstractCommentDataPullStrategy strategy = this.getStrategy(PullCommentDataPlatformTypeEnum.MT.getCode());
		strategy.saveData(dataList);
	}
}
  
