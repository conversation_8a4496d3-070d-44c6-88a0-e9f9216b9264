package cn.hydee.middle.business.order.route.application.representation;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * <AUTHOR>
 * @date 2024/4/2
 * @since 1.0
 */
@Data
public class RouteAllotRepresentationExportDto {

    @ApiModelProperty(value = "id")
    private String id;

    @ApiModelProperty(value = "调出门店编码")
    private String outStoreCode;

    @ApiModelProperty(value = "调出门店名称")
    private String outStoreName;

    @ApiModelProperty(value = "调入门店编码")
    private String inStoreCode;

    @ApiModelProperty(value = "调入门店名称")
    private String inStoreName;

    @ApiModelProperty(value = "平台订单号")
    private String thirdNo;

    @ApiModelProperty(value = "系统订单号")
    private String omsNo;

    @ApiModelProperty(value = "订单号类型 ORDER-销售单 REFUND-退款单")
    private String orderType;

    @ApiModelProperty(value = "心云调拨单号")
    private String omsAllotNo;

    @ApiModelProperty(value = "pos调拨单号")
    private String posAllotNo;

    @ApiModelProperty(value = "调拨单状态 WAIT-待发送到POS  PROCEED-处理中 FAIL-失败  SUCCESS-成功")
    private String allotStatus;

    @ApiModelProperty(value = "调拨成功时间")
    private String allotTime;

    @ApiModelProperty(value = "失败原因")
    private String failMessage;

    @ApiModelProperty(value = "version")
    private String version;
}
