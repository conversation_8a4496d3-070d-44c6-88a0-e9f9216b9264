package cn.hydee.middle.business.order.dto.req.prescription;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.NotNull;
import java.util.Date;
import java.util.List;

/**
 * 处方审核状态变更通知对象
 * http://************/hcloud-open-api/#/merchant/%E8%8D%AF%E4%BA%8B%E4%BA%91/%E5%A4%84%E6%96%B9%E7%94%B3%E8%AF%B7%E5%8D%95%E7%8A%B6%E6%80%81%E5%8F%98%E6%9B%B4%E9%80%9A%E7%9F%A5
 */
@Data
public class PrescriptionCallbackReqDto {

    @ApiModelProperty(value = "回调事件类型：100、开方回调 | 200、审方回调,理论上固定200")
    private Integer eventType;
    @ApiModelProperty(value = "处方申请单状态，当eventType = 200 时，status为 10、待审核|20、审核通过|21、审核不通过")
    @NotNull
    private Integer status;
    @ApiModelProperty(value = "开方渠道：0、自主开方|1、莲藕|10、自有处方")
    private Integer presChannel;
    @ApiModelProperty(value = "药事云处方申请单号")
    private String presNo;
    @ApiModelProperty(value = "外部订单号")
    @NotEmpty
    private String orderNo;
    @ApiModelProperty(value = "外部处方单号")
    private String outPresNo;
    @ApiModelProperty(value = "开方医师，当eventType = 100 时，并且回调为完成开方，则有返回")
    private String prescribeDoctorName;
    @ApiModelProperty(value = "开方时间，当eventType = 100 时，并且回调为完成开方，则有返回")
    private Date prescribeTime;
    @ApiModelProperty(value = "医师拒方原因，当eventType = 100 时，并且回调为医师驳回，则有返回")
    private String rejectedDesc;
    @ApiModelProperty(value = "审方药师，当eventType = 200 时，并且回调为审核通过/审核不通过，则有返回")
    private String auditorName;
    @ApiModelProperty(value = "审核时间，当eventType = 200 时，并且回调为审核通过/审核不通过，则有返回")
    private Date auditTime;
    @ApiModelProperty(value = "药师审方意见，当eventType = 200 时，并且回调为审核通过/审核不通过，则有返回")
    private String auditorSuggestion;
    @ApiModelProperty(value = "处方单图片（关注URL即可），图片带有效期的（10分钟），对接方从药事云拿到图片地址之后，需要下载图片自己存储")
    private List<PrescriptionImgBaseDto> presPicList;
    @ApiModelProperty(value = "审核后合成的图片（关注URL即可），图片带有效期的（10分钟），对接方从药事云拿到图片地址之后，需要下载图片自己存储")
    private List<PrescriptionImgBaseDto> auditedPicList;
    @ApiModelProperty(value = "扩展信息，对接方在提交处方申请信息时传入，药事云在回调时原样返回")
    private String extend;

}
