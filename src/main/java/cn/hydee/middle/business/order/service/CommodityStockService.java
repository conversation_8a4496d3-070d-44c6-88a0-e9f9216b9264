package cn.hydee.middle.business.order.service;

import cn.hydee.middle.business.order.Enums.UnLockStockTypeEnum;
import cn.hydee.middle.business.order.dto.req.CommodityStockReqDto;
import cn.hydee.middle.business.order.dto.rsp.StockDeductRspDto;
import cn.hydee.middle.business.order.entity.CommodityStock;
import cn.hydee.middle.business.order.entity.OrderDetail;
import cn.hydee.middle.business.order.entity.OrderInfo;
import cn.hydee.middle.business.order.entity.b2c.OmsOrderInfo;
import cn.hydee.starter.dto.ResponseBase;
import com.baomidou.mybatisplus.extension.service.IService;

import java.util.List;

/**
 * @author: xin.tu
 * @date: 2023/1/6 10:10
 * @menu:
 */
public interface CommodityStockService extends IService<CommodityStock> {

    void lockStock(OrderInfo orderInfo, List<OrderDetail> orderDetailList, ResponseBase<StockDeductRspDto> resp);


    void unlockStock(OrderInfo orderInfo, List<OrderDetail> orderDetailList);

    /** erp下账完成通知商品中台释放库存
     * <AUTHOR>
     * @Description
     * @date 2023/12/15 14:13
     */
    void erpSucceedUnlockStock(OrderInfo orderInfo);


    /** 异常订单换货锁定库存
     * <AUTHOR>
     * @Description
     * @date 2023/12/15 14:13
     */
    void abnormalExchange(OrderInfo orderInfo, List<OrderDetail> orderDetailList,String note);



    /** 锁定库存
     * <AUTHOR>
     * @Description
     * @date 2023/12/15 14:13
     */
    void lockStockNew(OrderInfo orderInfo, List<OrderDetail> orderDetail,String note);

    /** 释放锁定库存
     * <AUTHOR>
     * @Description
     * @date 2023/12/15 14:13
     */
    void unLockStockNew(OrderInfo orderInfo, List<OrderDetail> orderDetail, String note, UnLockStockTypeEnum unLockStockTypeEnum);



    /** 异常订单强审锁定库存
     * <AUTHOR>
     * @Description
     * @date 2023/12/15 14:13
     */
    void abnormalExamination(OrderInfo orderInfo,String note);


    /** erp下账完成通知商品中台释放库存
     * <AUTHOR>
     * @Description
     * @date 2023/12/15 14:13
     */
    void toolUnlockStock(OrderInfo orderInfo);


    /**
     *  B2C释放锁定库存
     * @param omsOrderInfo
     */
    void unLockStockB2C (OmsOrderInfo omsOrderInfo);


    void unLockStockJob(CommodityStockReqDto dto);

}
