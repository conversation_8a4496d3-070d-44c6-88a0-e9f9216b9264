package cn.hydee.middle.business.order.mapper;

import cn.hydee.middle.business.order.entity.OrderCommodityCostPrice;
import cn.hydee.middle.business.order.entity.OrderCommodityCostPriceHistory;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import org.springframework.stereotype.Repository;

/**
 * <p>
 * 商品明细成本价表 Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2021-01-12
 */
@Repository
public interface OrderCommodityCostPriceHistoryMapper extends BaseMapper<OrderCommodityCostPriceHistory> {

}
