package cn.hydee.middle.business.order.dto.req.baseinfo;

import cn.hydee.starter.dto.PageBase;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * <AUTHOR>
 */
@Data
@EqualsAndHashCode(callSuper = true)
public class DeliveryClientQueryDTO extends PageBase {

    @ApiModelProperty(value = "线上门店ID")
    private String onlineStoreId;

    @ApiModelProperty(value = "配送中心",notes = "配送中心")
    private String platformCode;

    @ApiModelProperty(value = "配送门店ID",notes = "配送门店ID")
    private String deliveryStoreCode;

    @ApiModelProperty(value = "配送门店",notes = "配送门店")
    private String deliveryStoreName;

    @ApiModelProperty(value = "商户编码")
    private String merCode;
}
