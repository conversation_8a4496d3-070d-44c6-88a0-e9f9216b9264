package cn.hydee.middle.business.order.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;

/**
 * <p>
 * ERP会员订单信息
 * </p>
 *
 * <AUTHOR>
 * @since 2020-05-09
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
public class ErpOrderDetail implements Serializable {

    private static final long serialVersionUID=1L;

    @TableId(value = "id", type = IdType.AUTO)
    private Long id;

    /**
     * 订单主键ID
     */
    private Long orderId;

    /**
     * 商户编码
     */
    private String merCode;

    /**
     * 商品erp编码
     */
    private String erpCode;

    /**
     * 商品名称
     */
    private String commodityName;

    /**
     * 商品数量
     */
    private Double commodityCount;

    /**
     * 单价
     */
    private BigDecimal price;

    /**
     * 商品总额（商品数量 * 单价）
     */
    private BigDecimal totalAmount;

    /**
     * 创建时间
     */
    private Date createTime;

    /**
     * 修改时间
     */
    private Date modifyTime;

    /**
     * 商品参与活动类型(0：正常销售，1:特价商品，2：促销商品，3：会员活动商品)
     */
    private Integer gainType;

    /** 商品通用名 */
    private String commonName;

    /** 商品条形码 */
    private String barCode;

    /** 营业员员工编码 */
    private String saleClerk;

    /** 成本价 */
    private BigDecimal costPrice;

    /** 中药标志（0-非中药，1-中药） */
    private Integer chinaMedicineFlag;
    /**
     * 商品批准文号
     */
    private String approvalNumber;
    /**
     * 商品规格信息
     */
    private String specDesc;

    /** 商品分类 */
    private String classType;
}
