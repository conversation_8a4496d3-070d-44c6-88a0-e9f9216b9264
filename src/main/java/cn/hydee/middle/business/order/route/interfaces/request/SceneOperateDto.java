package cn.hydee.middle.business.order.route.interfaces.request;

import io.swagger.annotations.ApiModelProperty;
import java.util.List;
import lombok.Data;

/**
 * 场景新增、编辑参数
 */
@Data
public class SceneOperateDto {

  @ApiModelProperty(value = "场景Id")
  private Long id;

  @ApiModelProperty(value = "场景名称")
  private String sceneName;

  @ApiModelProperty(value = "场景类型")
  private String sceneType;

  @ApiModelProperty(value = "场景说明")
  private String mark;

  @ApiModelProperty(value = "场景规则id集合")
  private List<Long> ruleIds;

  @ApiModelProperty(value = "创建人")
  private String createdName;

  @ApiModelProperty(value = "版本号")
  private Long version;

}
