package cn.hydee.middle.business.order.thirdbill.service;

import cn.hydee.middle.business.order.Enums.ErpStateEnum;
import cn.hydee.middle.business.order.Enums.OrderLockFlagEnum;
import cn.hydee.middle.business.order.Enums.OrderStateEnum;
import cn.hydee.middle.business.order.domain.OrderInfoAllDomain;
import cn.hydee.middle.business.order.dto.req.AddOrderInfoReqDto;
import cn.hydee.middle.business.order.dto.rsp.CommodityRspDto;
import cn.hydee.middle.business.order.dto.rsp.OnlineStoreInfoRspDto;
import cn.hydee.middle.business.order.entity.OrderInfo;
import cn.hydee.middle.business.order.entity.StoreBillConfig;

import java.util.List;

/**
 * 京东到家调整单服务
 * <AUTHOR>
 * @version 3.1.9
 * @date 2020/08/31
 */
public interface IJdDaojiaChangBillService {


    /**
     * 重建订单信息
     * @param oldOrderInfo 原订单信息
     * @param addOrderInfoReqDto 调整单信息
     * @param onlineStoreInfoRspDto 门店信息
     * @param storeBillConfig 下账配置
     * @return 订单信息
     */
    OrderInfoAllDomain reCreateOrder(OrderInfo oldOrderInfo, AddOrderInfoReqDto addOrderInfoReqDto,
                                     OnlineStoreInfoRspDto onlineStoreInfoRspDto, StoreBillConfig storeBillConfig,
                                     List<CommodityRspDto> commodityRspDtoList, ErpStateEnum erpStateEnum,
                                     OrderStateEnum orderStateEnum, OrderLockFlagEnum orderLockFlagEnum);
}
