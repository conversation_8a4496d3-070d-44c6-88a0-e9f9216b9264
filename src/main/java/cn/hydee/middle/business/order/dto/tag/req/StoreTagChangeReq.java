package cn.hydee.middle.business.order.dto.tag.req;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.NotNull;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2024/8/8
 * @since 1.0
 */
@Data
public class StoreTagChangeReq {

    @ApiModelProperty("标签id列表")
    @NotEmpty(message = "标签Id列表不能为空")
    private List<Long> tagIds;

    @ApiModelProperty("标签组id")
    @NotNull(message = "标签组id不能为空")
    private Long groupId;

    @ApiModelProperty("操作人Id")
    private String userId;
}
