package cn.hydee.middle.business.order.controller;

import cn.hydee.middle.business.order.dto.req.OrderChangeSelfDeliveryUpdateDelayMinutesReqDto;
import cn.hydee.middle.business.order.dto.req.OrderChangeSelfDeliveryUpdateDeliveryReqDto;
import cn.hydee.middle.business.order.dto.req.OrderChangeSelfDeliveryUpdateStateReqDto;
import cn.hydee.middle.business.order.entity.OrderChangeSelfDeliveryConfig;
import cn.hydee.middle.business.order.service.OrderChangeSelfDeliveryConfigService;
import cn.hydee.middle.business.order.service.OrderChangeSelfDeliveryService;
import cn.hydee.starter.controller.AbstractController;
import cn.hydee.starter.dto.ResponseBase;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.web.bind.annotation.*;

import java.util.List;
import java.util.concurrent.Executor;

import static cn.hydee.middle.business.order.configuration.ThreadPoolConfig.ORDER_BUSINESS_ASYNC_THREAD_POOL;

/**
 * <AUTHOR>
 * @date 2020/12/27 下午3:38
 */
@RestController
@RequestMapping("/${api.version}/ds/order/changeSelfDelivery")
@Api(tags = "订单转自配送控制器")
public class OrderChangeSelfDeliveryController extends AbstractController {

    @Autowired
    private OrderChangeSelfDeliveryConfigService orderChangeSelfDeliveryConfigService;
    @Autowired
    private OrderChangeSelfDeliveryService orderChangeSelfDeliveryService;

    @Qualifier(ORDER_BUSINESS_ASYNC_THREAD_POOL)
    @Autowired
    private Executor asyncThreadPool;

    @ApiOperation(value = "获取订单转自配送配置")
    @GetMapping("/getConfigListByStoreId")
    public ResponseBase<List<OrderChangeSelfDeliveryConfig>> getConfigListByStoreId(@RequestParam("merCode") String merCode, @RequestParam("onlineStoreId") Long onlineStoreId) {
        List<OrderChangeSelfDeliveryConfig> configList = orderChangeSelfDeliveryConfigService.getConfigListByStoreId(merCode, onlineStoreId);
        return generateSuccess(configList);
    }

    @ApiOperation(value = "修改配置状态")
    @PostMapping("/updateConfigState")
    public ResponseBase<Boolean> updateConfigState(@RequestBody OrderChangeSelfDeliveryUpdateStateReqDto reqDto,
                                                   @RequestHeader("userId") String userId,
                                                   @RequestHeader("userName") String userName) {
        return generateSuccess(orderChangeSelfDeliveryConfigService.updateConfig(reqDto, userId, userName));
    }

    @ApiOperation(value = "修改配置延迟时间")
    @PostMapping("/updateConfigDelayMinutes")
    public ResponseBase<Boolean> updateConfigDelayMinutes(@RequestBody OrderChangeSelfDeliveryUpdateDelayMinutesReqDto reqDto,
                                                          @RequestHeader("userId") String userId,
                                                          @RequestHeader("userName") String userName) {
        return generateSuccess(orderChangeSelfDeliveryConfigService.updateConfig(reqDto, userId, userName));
    }

    @ApiOperation(value = "选择配送方式")
    @PostMapping("/updateConfigDelivery")
    public ResponseBase<Boolean> updateConfigDelivery(@RequestBody OrderChangeSelfDeliveryUpdateDeliveryReqDto reqDto,
                                                      @RequestHeader("userId") String userId,
                                                      @RequestHeader("userName") String userName) {
        return generateSuccess(orderChangeSelfDeliveryConfigService.updateConfig(reqDto, userId, userName));
    }

    @ApiOperation(value = "处理待转自配送订单")
    @PostMapping("/autoChangeSelfDelivery")
    public ResponseBase<Boolean> autoChangeSelfDelivery() {
        asyncThreadPool.execute(() -> orderChangeSelfDeliveryService.autoChangeSelfDelivery());// 每条数据依次处理,这里做异步慢慢执行
        return generateSuccess(true);
    }
}
