package cn.hydee.middle.business.order.dto.req;

import cn.hydee.starter.dto.PageBase;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * <AUTHOR>
 */
@Data
public class EmployeeReqDto extends PageBase {

    @ApiModelProperty(value = "员工姓名,模糊检索")
    private String empName;

    @ApiModelProperty(value = "商家编码")
    private String merCode;

    @ApiModelProperty(value = "门店编码")
    private String stCode;
}
