package cn.hydee.middle.business.order.v2.manager.base;

import cn.hydee.middle.business.order.Enums.DsConstants;
import cn.hydee.middle.business.order.dto.req.BatchCommodityInfoReqDto;
import cn.hydee.middle.business.order.dto.req.BatchThirdStoreCommodityReqDto;
import cn.hydee.middle.business.order.dto.req.StockDeductReqDto;
import cn.hydee.middle.business.order.dto.req.asyn.CommodityCreateCallbackDto;
import cn.hydee.middle.business.order.dto.req.asyn.CommodityCreateReqDto;
import cn.hydee.middle.business.order.dto.rsp.BatchThirdStoreCommodityRspDto;
import cn.hydee.middle.business.order.dto.rsp.CommodityCreateRspDto;
import cn.hydee.middle.business.order.dto.rsp.CommodityRspDto;
import cn.hydee.middle.business.order.dto.rsp.StockDeductRspDto;
import cn.hydee.middle.business.order.feign.MiddleMerchandiseClient;
import cn.hydee.middle.business.order.service.rocket.MessageProducerService;
import cn.hydee.starter.dto.ResponseBase;
import com.alibaba.fastjson.JSON;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Component;

import java.util.List;
import java.util.Set;

/**
 * MerchandiseBaseService
 * <AUTHOR>
 * @since 2020/8/21 15:04
 */
@Slf4j
@Component
public class MerchandiseBaseManager {
    
    @Autowired
    private MiddleMerchandiseClient middleMerchandiseClient;
    @Autowired
    private MessageProducerService messageProducerService;

    public ResponseBase<StockDeductRspDto> stockDeductionWrapper(StockDeductReqDto stockDeductReqDto){
        try {
            ResponseBase<StockDeductRspDto> base = middleMerchandiseClient.stockOccupyStockV2(stockDeductReqDto);
            log.info("MiddleMerchandise:stockDeduct, requestParam:{}, result:{}", JSON.toJSONString(stockDeductReqDto), JSON.toJSONString(base));
            return base;
        } catch (Exception e){
            log.error("MiddleMerchandise:stockDeduction error, orderNo:{}", stockDeductReqDto.getOrderNo(), e);
        }
        return null;
    }
    
    
    public ResponseBase<List<CommodityRspDto>> batchGetCommodityInfoInner(String merCode, Set<String> erpCodeSet){
        BatchCommodityInfoReqDto batchCommodityInfoReqDto = new BatchCommodityInfoReqDto();
        batchCommodityInfoReqDto.setMerCode(merCode);
        batchCommodityInfoReqDto.setErpCodes(erpCodeSet);
        batchCommodityInfoReqDto.setHasCatecory(true);
        ResponseBase<List<CommodityRspDto>> baseCommodity = middleMerchandiseClient.batchGetCommodityByErpCode(batchCommodityInfoReqDto);
        log.info("middleMerchandise:batchGetCommodityInfo, requestParam:{}, result:{}",JSON.toJSONString(batchCommodityInfoReqDto),JSON.toJSON(baseCommodity));
        return baseCommodity;
    }

	public ResponseBase<List<CommodityRspDto>> batchGetCommodityInfoNoException(String thirdOrderNo,String merCode,
			Set<String> erpCodeSet) {
		try {
            ResponseBase<List<CommodityRspDto>> baseCommodity = this.batchGetCommodityInfoInner(merCode, erpCodeSet);
            return baseCommodity;
        } catch (Exception e){
            log.error("MerchandiseBaseService: batchGetCommodityInfoNoException error", e);
        }
        return null;
	}


    public ResponseBase<List<CommodityRspDto>> batchGetCommodityInfoNoExceptionV2(String merCode,
                                                                                Set<String> erpCodeSet) {
        try {
            ResponseBase<List<CommodityRspDto>> baseCommodity = this.batchGetCommodityInfoInner(merCode, erpCodeSet);
            return baseCommodity;
        } catch (Exception e){
            log.error("新增订单查询商品信息失败：", e);
        }
        return null;
    }


    /**
     * 查询三方门店商品
     * @param merCode
     * @param platformCode
     * @param onlineStoreCode
     * @param orgCode
     * @param erpCodes
     * @return
     */
    public ResponseBase<BatchThirdStoreCommodityRspDto> batchGetThirdStoreCommodity(String merCode, String platformCode,
                                                                                          String onlineStoreCode, String orgCode, List<String> erpCodes){
        BatchThirdStoreCommodityReqDto reqDto = new BatchThirdStoreCommodityReqDto();
        reqDto.setMerCode(merCode);
        reqDto.setPlatformCode(platformCode);
        reqDto.setOnlineStoreCode(onlineStoreCode);
        reqDto.setOrgCode(orgCode);
        reqDto.setMatchStatu(DsConstants.INTEGER_ONE);
        reqDto.setErpCodeList(erpCodes);
        try {
            ResponseBase<BatchThirdStoreCommodityRspDto> responseBase = middleMerchandiseClient.batchGetThirdStoreCommodity(reqDto.getMerCode(), reqDto);
            log.info("MerchandiseBaseService batchGetThirdStoreCommodity,request: {}, resp: {}", JSON.toJSONString(reqDto),JSON.toJSONString(responseBase));
            return responseBase;
        }catch (Exception e){
            log.error("MerchandiseBaseService batchGetThirdStoreCommodity error: ",e);
        }
        return null;
    }

    /**
     * 异步调用商品服务创建商品，成功返回后清除异常标记
     * @param reqDto
     */
    @Async("asyncCallExecutor")
    public void commodityCreateAsync(CommodityCreateReqDto reqDto){
        ResponseBase<List<CommodityCreateRspDto>> responseBase = null;
        try{
            responseBase = middleMerchandiseClient.commodityAutoCreate(reqDto);
        }catch (Exception e){
            log.info("三方商品不存在时异步创建商品失败, req:{}, resp:{} ",JSON.toJSONString(reqDto),JSON.toJSONString(responseBase),e);
            return;
        }
        log.info("三方商品不存在时异步创建商品结束, req:{}, resp:{} ",JSON.toJSONString(reqDto),JSON.toJSONString(responseBase));
        if(responseBase != null && responseBase.checkSuccess()){
            //因需要重新查商品信息确认是否存在赠品，发送延迟消息，避免订单创建流程还未处理完成
            CommodityCreateCallbackDto callbackDto = new CommodityCreateCallbackDto();
            callbackDto.setMerCode(reqDto.getMerCode());
            callbackDto.setPlatformCode(reqDto.getPlatformCode());
            callbackDto.setThirdOrderNo(reqDto.getThirdOrderNo());
            messageProducerService.commodityCreateCallbackMessage(callbackDto);
        }
    }

}
