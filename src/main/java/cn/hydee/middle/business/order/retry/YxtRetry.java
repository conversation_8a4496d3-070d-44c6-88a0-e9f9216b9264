package cn.hydee.middle.business.order.retry;

import lombok.extern.slf4j.Slf4j;

/**
 * 封装一个简单的重试
 *
 * <AUTHOR> (moatkon)
 * @date 2024年01月17日 14:19
 * @email: <EMAIL>
 */
@Slf4j
public class YxtRetry {
    /**
     * 重试方法
     *
     * @param retryTimes 重试次数
     * @param retry      重试函数
     */
    public static void retry(int retryTimes, Retry retry) {
        int count = 0;
        for (int times = 1; times <= retryTimes; times++) {
            try {
                retry.r();
                break;
            } catch (Exception e) {
                count = count + 1;
                log.info("RetryInfo 异常次数{},异常信息: {}", 1, e.getMessage());

                sleep(1);

                if (count == retryTimes) {
                    log.error("重试{}次后依然失败,抛出异常", count, e);
                    throw e;
                }
            }
        }
    }

    private static void sleep(Integer seconds) {
        try {
            Thread.sleep(seconds * 1000);
        } catch (Exception ignore) {
        }
    }

    public static void main(String[] args) {
        try {
            YxtRetry.retry(6, () -> {
                System.out.println(1);
//                int o = 23 / 0; //模拟异常
            });
        } catch (Exception e) {
            throw new RuntimeException(e);
        }
    }
}
