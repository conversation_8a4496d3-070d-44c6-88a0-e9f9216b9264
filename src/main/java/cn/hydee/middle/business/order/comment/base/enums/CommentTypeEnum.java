
package cn.hydee.middle.business.order.comment.base.enums;  

public enum CommentTypeEnum {
	
	NEGATIVE_COMMENT(0, "差评"),
	MIDDLE_COMMENT(1, "中评"),
	FAVOURABLE_COMMENT(2, "好评"),
	;

    private Integer code;
    private String msg;
    
    public static Integer convertEnumCode(int totalScore) {
    	
    	if(1<=totalScore && 2 >= totalScore) {
    		return NEGATIVE_COMMENT.getCode();
    	}
    	if(3==totalScore) {
    		return MIDDLE_COMMENT.getCode();
    	}
    	if(4<=totalScore && 5 >= totalScore) {
    		return FAVOURABLE_COMMENT.getCode();
    	}
    	return NEGATIVE_COMMENT.getCode();
    }
    
    CommentTypeEnum(Integer code, String msg) {
        this.code = code;
        this.msg = msg;
    }

    public Integer getCode() {
        return code;
    }

    public String getMsg() {
        return msg;
    }
}
  
