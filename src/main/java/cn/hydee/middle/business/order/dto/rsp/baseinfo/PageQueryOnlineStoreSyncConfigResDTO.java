package cn.hydee.middle.business.order.dto.rsp.baseinfo;

import cn.hydee.middle.business.order.dto.tag.resp.StoreTagListResp;
import cn.hydee.middle.business.order.entity.tag.OnlineStoreTag;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.List;


/**
 * <AUTHOR>
 */
@Data
public class PageQueryOnlineStoreSyncConfigResDTO {


  @ApiModelProperty(value = "商户编码")
  private String merCode;

  @ApiModelProperty(value = "平台编码")
  private String platformCode;

  @ApiModelProperty(value = "平台名称")
  private String platformName;

  /**
   * 第三方网店编码
   */
  @ApiModelProperty(value = "第三方网店编码 B2C有值")
  private String onlineClientOutCode;

  @ApiModelProperty(value = "网店编码")
  private String onlineClientCode;

  @ApiModelProperty(value = "网店名称")
  private String onlineClientName;


  /**
   * 外部门店ID
   */
  @ApiModelProperty(value = "外部门店ID")
  private String outShopId;

  @ApiModelProperty(value = "线上平台店铺编码")
  private String platformShopId;


  @ApiModelProperty(value = "线上门店编码")
  private String onlineStoreCode;

  @ApiModelProperty(value = "线上门店名称")
  private String onlineStoreName;


  @ApiModelProperty(value = "线下门店编码")
  private String organizationCode;

  @ApiModelProperty(value = "线下门店名称")
  private String organizationName;

  @ApiModelProperty(value = "门店状态 （1：上线 0：下线 2 已删除）")
  private Integer status;

  @ApiModelProperty(value = "营业状态（0：歇业 1：营业中 2：休息）")
  private Integer openStatus;



  @ApiModelProperty(value = "服务模式")
  private String serviceMode;

  @ApiModelProperty(value = "B2C平台的O2O业务的线上门店（0：不是 1：是）  1:天猫同城购; 当前0")
  private Integer inheritB2C;

  @ApiModelProperty(value = "授权类型 1-商家应用授权，2-服务商应用授权;当前 1")
  private Integer accessType;



  /**
   * 同步价格标志 0不同步，1同步
   */
  @ApiModelProperty(value = "同步价格标志 0不同步，1同步")
  private Integer syncPrice;

  /**
   * 同步库存标志 0不同步，1同步
   */
  @ApiModelProperty(value = "同步库存标志 0不同步，1同步")
  private Integer syncStock;

  /**
   * 库存同步比例
   */
  @ApiModelProperty(value = "库存同步比例")
  private Integer syncStockRatio;

  /**
   * 是否同步价格,0不同步，1同步(平台使用) 2023/2/6
   */
  private Integer platformSyncPrice;
  /**
   * 同步价格门店编码
   */
  private String syncPriceStoreCode;

  @ApiModelProperty(value = "店铺标签")
  private List<StoreTagListResp> onlineStoreTagList;

}
