package cn.hydee.middle.business.order.util;

import cn.hydee.middle.business.order.annotation.SensitiveField;
import org.springframework.stereotype.Component;

import java.lang.reflect.Field;
import java.util.Objects;

/***
 * 解密实现
 */
@Component
public class AESDecrypt implements DecryptUtil {

    @Override
    public <T> T decrypt(T result) throws IllegalAccessException {
        Class<?> resultClass = result.getClass();
        Field[] declaredFields = resultClass.getDeclaredFields();
        for (Field field : declaredFields) {
            SensitiveField sensitiveField = field.getAnnotation(SensitiveField.class);
            if (!Objects.isNull(sensitiveField)) {
                field.setAccessible(true);
                Object object = field.get(result);
                if (object instanceof String) {
                    String value = (String) object;
                    field.set(result, AESUtils.decrypt(value));
                }
            }
        }
        return result;
    }
}
