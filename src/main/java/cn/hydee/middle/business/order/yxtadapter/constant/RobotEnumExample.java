package cn.hydee.middle.business.order.yxtadapter.constant;

import com.yxt.common.wechatrobot.enums.IRobotConfig;

/**
 * Created by Intellij IDEA.
 *
 * <AUTHOR>
 * Date:  2023/10/9
 */
public enum RobotEnumExample implements IRobotConfig {


    ORDER_PUSH_FAIL1("dev|https://qyapi.weixin.qq.com/cgi-bin/webhook/send?key=8e54dc74-d229-42c4-a68f-60d089a61ffb;" +
            "pro|https://qyapi.weixin.qq.com/cgi-bin/webhook/send?key=c55155e1-f9bf-4b40-af91-3caea1e32b73", "订单 {%d} 推动 B2C 失败，原因 {%s}"),

    REFUND_ORDER_PUSH_FAIL1("dev|https://qyapi.weixin.qq.com/cgi-bin/webhook/send?key=8e54dc74-d229-42c4-a68f-60d089a61ffb;" +
            "pro|https://qyapi.weixin.qq.com/cgi-bin/webhook/send?key=c55155e1-f9bf-4b40-af91-3caea1e32b73", "退款单 {%s} 推送B2C 失败，原因 {%s}", true),

    ASSIGNMENT_FAIL("dev|https://qyapi.weixin.qq.com/cgi-bin/webhook/send?key=8e54dc74-d229-42c4-a68f-60d089a61ffb;" +
            "pro|https://qyapi.weixin.qq.com/cgi-bin/webhook/send?key=c55155e1-f9bf-4b40-af91-3caea1e32b73", "任务{%d}执行失败，任务ID{%d},{%s}"),

    OMS_ORDER_CREATE_FAIL("dev|https://qyapi.weixin.qq.com/cgi-bin/webhook/send?key=8e54dc74-d229-42c4-a68f-60d089a61ffb;" +
            "pro|https://qyapi.weixin.qq.com/cgi-bin/webhook/send?key=c55155e1-f9bf-4b40-af91-3caea1e32b73", "订单{%d} 创建OMS订单失败，原因{%s}"),

    OMS_ORDER_PUSH_FAIL("dev|https://qyapi.weixin.qq.com/cgi-bin/webhook/send?key=8e54dc74-d229-42c4-a68f-60d089a61ffb;" +
            "pro|https://qyapi.weixin.qq.com/cgi-bin/webhook/send?key=c55155e1-f9bf-4b40-af91-3caea1e32b73", "订单{%d} 创建OMS订单失败，原因{%s}"),

    OMS_CANCEL_ORDER_PUSH_FAIL("dev|https://qyapi.weixin.qq.com/cgi-bin/webhook/send?key=8e54dc74-d229-42c4-a68f-60d089a61ffb;" +
            "pro|https://qyapi.weixin.qq.com/cgi-bin/webhook/send?key=c55155e1-f9bf-4b40-af91-3caea1e32b73", "订单{%d} 取消同步OMS失败，原因{%s}"),

    OMS_REFUND_ORDER_CREATE_FAIL("dev|https://qyapi.weixin.qq.com/cgi-bin/webhook/send?key=8e54dc74-d229-42c4-a68f-60d089a61ffb;" +
            "pro|https://qyapi.weixin.qq.com/cgi-bin/webhook/send?key=c55155e1-f9bf-4b40-af91-3caea1e32b73", "退款单{%d} 创建OMS失败，原因{%s}"),


    ORDER_OVER_TIME_NO_BILL("dev|https://qyapi.weixin.qq.com/cgi-bin/webhook/send?key=8e54dc74-d229-42c4-a68f-60d089a61ffb;" +
            "pro|https://qyapi.weixin.qq.com/cgi-bin/webhook/send?key=c55155e1-f9bf-4b40-af91-3caea1e32b73", "订单 {\n%s\n} 超过 %d 分钟未下账， 请处理"),

    OMS_ORDER_CALLBACK_FAIL("dev|https://qyapi.weixin.qq.com/cgi-bin/webhook/send?key=8e54dc74-d229-42c4-a68f-60d089a61ffb;" +
            "pro|https://qyapi.weixin.qq.com/cgi-bin/webhook/send?key=c55155e1-f9bf-4b40-af91-3caea1e32b73", "oms订单{%s}同步心云失败，原因{%s}"),

    OMS_REFUND_ORDER_CALLBACK_FAIL("dev|https://qyapi.weixin.qq.com/cgi-bin/webhook/send?key=8e54dc74-d229-42c4-a68f-60d089a61ffb;" +
            "pro|https://qyapi.weixin.qq.com/cgi-bin/webhook/send?key=c55155e1-f9bf-4b40-af91-3caea1e32b73", "oms回退单{%s}同步心云失败，原因{%s}"),


    OMS_DELIVERY_ORDER_CALLBACK_FAIL("dev|https://qyapi.weixin.qq.com/cgi-bin/webhook/send?key=8e54dc74-d229-42c4-a68f-60d089a61ffb;" +
            "pro|https://qyapi.weixin.qq.com/cgi-bin/webhook/send?key=c55155e1-f9bf-4b40-af91-3caea1e32b73", "oms订单{%s}配送信息同步心云失败，原因{%s}"),

    O2O_ORDER_STATUS_TRANSFORM_NULL("dev|https://qyapi.weixin.qq.com/cgi-bin/webhook/send?key=8e54dc74-d229-42c4-a68f-60d089a61ffb;" +
            "pro|https://qyapi.weixin.qq.com/cgi-bin/webhook/send?key=c55155e1-f9bf-4b40-af91-3caea1e32b73", "订单{%s}转换状态失败，订单状态{%s}"),

    GET_DELIVERY_TYPE_FAIL("dev|https://qyapi.weixin.qq.com/cgi-bin/webhook/send?key=8e54dc74-d229-42c4-a68f-60d089a61ffb;" +
            "pro|https://qyapi.weixin.qq.com/cgi-bin/webhook/send?key=c55155e1-f9bf-4b40-af91-3caea1e32b73", "获取{%s}平台{%s}门店配送方式失败"),

    ORDER_PROFIT_FOREWARN("dev|https://qyapi.weixin.qq.com/cgi-bin/webhook/send?key=6e579440-a685-4771-ada4-7aae2a95a2db;" +
        "pro|https://qyapi.weixin.qq.com/cgi-bin/webhook/send?key=3745865e-7fb1-49ab-91db-ab832086bc13", "%s的%s订单号：%s中的商品%s[%s]，%s，请检查相应的配置。");

    /**
     * webhook
     * <p>
     * <a href="https://developer.work.weixin.qq.com/document/path/91770#%E5%A6%82%E4%BD%95%E4%BD%BF%E7%94%A8%E7%BE%A4%E6%9C%BA%E5%99%A8%E4%BA%BA">...</a>
     * </p>
     */
    private final String webhook;

    /**
     * 告警内容
     */
    private final String content;

    /**
     * 是否@值班人
     */
    private Boolean needNotifyOncall;

    RobotEnumExample(String webhook, String content) {
        this.webhook = webhook;
        this.content = content;
    }

    RobotEnumExample(String webhook, String content, boolean needNotifyOncall) {
        this.webhook = webhook;
        this.content = content;
        this.needNotifyOncall = needNotifyOncall;
    }

    @Override
    public String getWebhook() {
        return webhook;
    }

    @Override
    public String getContent() {
        return content;
    }

    @Override
    public Boolean needNotifyOncall() {
        return needNotifyOncall;
    }
}