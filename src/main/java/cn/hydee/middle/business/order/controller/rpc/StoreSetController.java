package cn.hydee.middle.business.order.controller.rpc;

import cn.hydee.middle.business.order.dto.req.StoreConfigQuery;
import cn.hydee.middle.business.order.dto.req.baseinfo.StoreSetReq;
import cn.hydee.middle.business.order.entity.DsOnlineStore;
import cn.hydee.middle.business.order.service.baseinfo.DsOnlineStoreConfigService;
import cn.hydee.starter.controller.AbstractController;
import cn.hydee.starter.dto.ResponseBase;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.validation.BindingResult;
import org.springframework.web.bind.annotation.*;

import javax.validation.Valid;
import java.util.List;

/**
 * <AUTHOR>
 */
@RestController
@RequestMapping(value = "/${api.version}/store-set")
@Api(tags = "门店配置控制器")
@Slf4j
public class StoreSetController extends AbstractController {

    private final DsOnlineStoreConfigService dsOnlineStoreConfigService;

    public StoreSetController(DsOnlineStoreConfigService dsOnlineStoreConfigService) {
        this.dsOnlineStoreConfigService = dsOnlineStoreConfigService;
    }

    @ApiOperation(value = "批量更新门店配置信息", notes = "批量更新门店配置信息")
    @PutMapping("/_batch")
    public ResponseBase<Void> batchSetStoreConfig(@Valid @RequestBody StoreSetReq setReq,
                                              BindingResult result){
        checkValid(result);
        dsOnlineStoreConfigService.batchSetStoreConfig(setReq);
        return ResponseBase.success();
    }

    @ApiOperation(value = "单个查询平台，网店，门店配置信息", notes = "单个查询平台，网店，门店配置信息")
    @PostMapping("/_query")
    public ResponseBase<List<DsOnlineStore>> queryStoreConfig(@Valid @RequestBody StoreConfigQuery queryBase,
                                                              BindingResult result){
        checkValid(result);
        List<DsOnlineStore> configList = dsOnlineStoreConfigService.queryStoreConfig(queryBase);
        return generateObjectSuccess(configList);
    }
}
