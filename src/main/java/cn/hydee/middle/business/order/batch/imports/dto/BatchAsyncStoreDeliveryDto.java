
package cn.hydee.middle.business.order.batch.imports.dto;

import cn.hydee.batch.annotation.EnableBatchImportTask;
import cn.hydee.batch.dto.BatchImportBaseDTO;
import cn.hydee.middle.business.order.batch.annotation.ValidateValue;
import cn.hydee.middle.business.order.batch.imports.constant.ImportConstant;
import cn.hydee.middle.business.order.batch.imports.process.BatchAsyncStoreDeliveryProcessor;
import com.alibaba.excel.annotation.ExcelProperty;
import com.alibaba.excel.annotation.write.style.ColumnWidth;
import com.alibaba.excel.annotation.write.style.ContentRowHeight;
import com.alibaba.excel.annotation.write.style.HeadRowHeight;
import lombok.Data;
import lombok.EqualsAndHashCode;

@Data
@EqualsAndHashCode(callSuper = true)
@EnableBatchImportTask(businessType = ImportConstant.ASYNC_STOREDELIVERY, taskName = "批量上传配送门店",
        processor = BatchAsyncStoreDeliveryProcessor.class, batchSize = 5, checkHead = false)
@HeadRowHeight(value = 50)
@ColumnWidth(30)
@ContentRowHeight(30)
public class BatchAsyncStoreDeliveryDto extends BatchImportBaseDTO {

	@ExcelProperty(value = "*配送门店id（必填）")
	@ValidateValue(message="配送门店ID值为空或不合法")
	private String deliveryStoreCode;

	@ExcelProperty(value = "配送门店名称（美团配送必填）")
	private String deliveryStoreName;

	@ExcelProperty(value = "处理结果")
	private String result = ImportConstant.FAIL;
}
