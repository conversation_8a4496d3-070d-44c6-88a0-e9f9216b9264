package cn.hydee.middle.business.order.dto.message;

import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.Accessors;

import java.io.Serializable;

@Data
@NoArgsConstructor
@Accessors(chain = true)
public class NotifyMessage implements Serializable {

	private static final long serialVersionUID = -2139972109917843659L;

	private String handleType;

	/** 订单号 */
	private String orderNo;

	/** 配置的拣货超时时长：分钟 */
	private Integer pickNotifyMinsConf;

	/** 配置的发货超时时长：分钟 */
	private Integer deliveryNotifyMinsConf;

	/** 配置的拣货超时提醒次数 */
	private Integer pickNotify;

	/** 配置的发货超时提醒次数 */
	private Integer deliveryNotify;

	public NotifyMessage(String handleType, String orderNo, Integer pickNotifyMinsConf, Integer deliveryNotifyMinsConf, Integer pickNotify, Integer deliveryNotify) {
		this.handleType = handleType;
		this.orderNo = orderNo;
		this.pickNotifyMinsConf = pickNotifyMinsConf;
		this.deliveryNotifyMinsConf = deliveryNotifyMinsConf;
		this.pickNotify = pickNotify;
		this.deliveryNotify = deliveryNotify;
	}

}
