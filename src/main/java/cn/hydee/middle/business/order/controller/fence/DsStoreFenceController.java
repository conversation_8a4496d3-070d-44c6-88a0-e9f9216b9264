package cn.hydee.middle.business.order.controller.fence;

import cn.hydee.middle.business.order.dto.fence.BatchSaveStoreFenceSyncConfigReq;
import cn.hydee.middle.business.order.dto.fence.PageStoreFenceSyncConfigReq;
import cn.hydee.middle.business.order.dto.fence.PageStoreFenceSyncConfigRes;
import cn.hydee.middle.business.order.dto.fence.StoreFenceSyncConfigRes;
import cn.hydee.middle.business.order.service.fence.DsStoreFenceService;
import cn.hydee.starter.controller.AbstractController;
import cn.hydee.starter.dto.PageDTO;
import cn.hydee.starter.dto.ResponseBase;
import com.baomidou.mybatisplus.core.metadata.IPage;
import io.swagger.annotations.ApiOperation;
import lombok.AllArgsConstructor;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestHeader;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

/**
 * <AUTHOR>
 * @date 2024/06/24 15:52
 **/
@RestController
@RequestMapping("/${api.version}/ds/storeFence/")
@Validated
@AllArgsConstructor
public class DsStoreFenceController extends AbstractController {

    private final DsStoreFenceService dsStoreFenceService;

    /**
     * 批量保存或者更新门店电子围栏配置
     * */

    @ApiOperation(value = "批量保存或者更新门店电子围栏配置", notes = "批量保存或者更新门店电子围栏配置")
    @PostMapping("/saveOrUpdateConfig")
    public ResponseBase<Void> batchSaveOrUpdateStoreFenceSyncConfig( @RequestHeader("userId") String userId,@RequestBody BatchSaveStoreFenceSyncConfigReq req){
        dsStoreFenceService.batchSaveOrUpdateStoreFenceSyncConfig(req,userId);
        return generateSuccess(null);
    }


    /***
     * 查询门店配置信息
     * */
    @ApiOperation(value = "查询门店配置信息", notes = "查询门店配置信息")
    @GetMapping("/getConfig/{storeCode}")
    public ResponseBase<StoreFenceSyncConfigRes> getStoreFenceSyncConfigByStoreCode(@PathVariable("storeCode") String storeCode) {
        return generateSuccess(dsStoreFenceService.getStoreFenceSyncConfigByStoreCode(storeCode));
    }
    @ApiOperation(value = "查询同步日志", notes = "查询同步日志")
    @PostMapping("/getSyncLog")
    public ResponseBase<PageDTO<PageStoreFenceSyncConfigRes>> pageStoreFenceSyncConfig(@RequestBody PageStoreFenceSyncConfigReq req) {
        return generateSuccess(dsStoreFenceService.pageStoreFenceSyncConfig(req));
    }

}
