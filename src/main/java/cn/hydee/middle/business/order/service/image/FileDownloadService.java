package cn.hydee.middle.business.order.service.image;

import cn.hutool.core.collection.CollUtil;
import cn.hydee.middle.business.order.util.ExceptionUtil;
import cn.hydee.middle.business.order.util.HttpManager;
import lombok.extern.slf4j.Slf4j;
import net.lingala.zip4j.core.ZipFile;
import net.lingala.zip4j.exception.ZipException;
import net.lingala.zip4j.model.ZipParameters;
import net.lingala.zip4j.util.Zip4jConstants;
import okhttp3.Call;
import okhttp3.Request;
import okhttp3.Response;
import okhttp3.ResponseBody;
import org.springframework.stereotype.Service;

import java.io.IOException;
import java.io.InputStream;
import java.net.URLDecoder;
import java.util.List;

/**
 * 文件下载类
 *
 * <AUTHOR>
 */
@Service
@Slf4j
public class FileDownloadService {

    public void downloadPicture(ZipFile zipFile, List<String> urls) {
        if (CollUtil.isEmpty(urls)) {
            return;
        }
        try {
            ZipParameters para = new ZipParameters();
            para.setCompressionMethod(Zip4jConstants.COMP_DEFLATE);

            para.setSourceExternalStream(true);
            para.setEncryptFiles(false);
            for (String url : urls) {
                Request request = new Request.Builder()
                        .get()
                        .url(url)
                        .build();
                Call call = HttpManager.client.newCall(request);
                try (Response response = call.execute()) {
                    ResponseBody body = response.body();
                    if (!response.isSuccessful() || body == null) {
                        continue;
                    }
                    InputStream inputStream = body.byteStream();
                    para.setFileNameInZip(URLDecoder.decode(url.substring(url.lastIndexOf("/") + 1).split("\\?")[0],"UTF-8"));
                    zipFile.addStream(inputStream, para);
                    inputStream.close();
                }
            }
        } catch (IOException | ZipException e) {
            throw ExceptionUtil.getWarnException("1003","下载压缩图片失败");
        }
    }
}
