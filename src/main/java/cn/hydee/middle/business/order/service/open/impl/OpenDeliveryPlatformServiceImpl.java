package cn.hydee.middle.business.order.service.open.impl;

import cn.hydee.middle.business.order.entity.OpenDeliveryPlatform;
import cn.hydee.middle.business.order.mapper.OpenDeliveryPlatformMapper;
import cn.hydee.middle.business.order.service.open.OpenDeliveryPlatformService;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;


/**
 * <AUTHOR>
 * @date 2021/6/8 下午4:59
 */
@Service
public class OpenDeliveryPlatformServiceImpl implements OpenDeliveryPlatformService {

    @Autowired
    private OpenDeliveryPlatformMapper openDeliveryPlatformMapper;

    @Override
    public OpenDeliveryPlatform get(String merCode, String platformName) {
        QueryWrapper<OpenDeliveryPlatform> queryWrapper = new QueryWrapper();
        queryWrapper.lambda()
                .eq(OpenDeliveryPlatform::getMerCode, merCode)
                .eq(OpenDeliveryPlatform::getPlatformName, platformName);
        return openDeliveryPlatformMapper.selectOne(queryWrapper);
    }
}
