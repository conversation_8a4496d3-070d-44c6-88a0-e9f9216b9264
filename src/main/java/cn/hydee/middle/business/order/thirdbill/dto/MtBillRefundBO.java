package cn.hydee.middle.business.order.thirdbill.dto;

import com.fasterxml.jackson.annotation.JsonIgnore;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotNull;
import java.io.Serializable;
import java.math.BigDecimal;

/**
 * 录入美团退款金额响应消息对象
 * <AUTHOR>
 * @version 3.7.5
 * @date 2020/07/23
 */
@Data
public class MtBillRefundBO implements Serializable {

    private static final long serialVersionUID = -1325568114494538210L;

    @NotNull
    @ApiModelProperty(value = "订单号")
    private String orderNo;

    @ApiModelProperty(value = "原订单编号")
    private String dayNum;

    @NotNull
    @ApiModelProperty(value = "退款单号")
    private String refundNo;

    @NotNull
    @ApiModelProperty(value = "返还佣金金额")
    private BigDecimal feeRefund;

    @NotNull
    @ApiModelProperty(value = "商户返还平台优惠")
    private BigDecimal platformDiscount;

    @NotNull
    @ApiModelProperty(value = "平台返还商家优惠")
    private BigDecimal shopDiscount;

    @JsonIgnore
    private BigDecimal shopRefund;

    @ApiModelProperty(value = "原订单下单时间")
    private String createTime;
}
