package cn.hydee.middle.business.order.util;

import cn.hydee.middle.business.order.doris.dto.req.OrderReportStatisticsQueryReqDto;

import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @since 2020/12/22 20:50
 */
public class RedisKeyUtil {

    public static final String o2oPrefix = "o2o-";

    public static final String erpSynsLastIdKey = "o2o-erp-order-lastId";

    public static final String erpSynsUniqueKey = "o2o-erp-order-unique";

    public static final String orderSyncLastIdKey = "o2o-order-count-lastId";

    public static final String orderSyncUniqueKey = "o2o-order-count-unique";

    public static final String orderInfoSyncLastIdKey = "o2o-order-sync-lastId";

    public static final String orderInfoSyncUniqueKey = "o2o-order-sync-unique";

    public static final String refundOrderSyncLastIdKey = "o2o-refund-order-count-lastId";

    public static final String refundOrderSyncUniqueKey = "o2o-refund-order-count-unique";

    public static final String DSCLOUD_PRINT_KEY = "oms:o2o:dscloud:print:%s:%s";

    public static final String ORDERCOUNT_KEY = "oms:o2o:dscloud:ordercount:%s:%s";

    public static final String ORDER_QUERY_2 = "oms:o2o:dscloud:orderquery2:%s";

    public static final String WSCOPENB2C = "oms:o2o:dscloud:openb2c";

    public static final String PICKCONFIRM_KEY = "oms:o2o:cache:pickconfirm:%s";

    public static final String DSCLOUD_SOUND_KEY = "oms:o2o:dscloud:sound:%s:%s";

    public static final String orderChangeSelfDeliveryKey = "o2o-order-change-self-delivery";

    public static final String SERVICE_REPORT_GENEARATE = "o2o-service-report-generate";

    // 批量发货订单key
    public final static String REDIS_KEY_ORDERNO = "oms:o2o:batchimport:express:alldata:orderNo:";
    // 批量发货快递单key
    public final static String REDIS_KEY_EXPRESSNO = "oms:o2o:batchimport:express:alldata:expressNo:";
    // 微商城快递数据key
    public final static String LOGISTICS_COMPANY_KEY = "oms:o2o:cache:logistics:company:ydjia:43";
    // 商户旗下城市数据key
    public final static String OMS_CACHE_MERCHANT_CITY_KEY = "oms:o2o:cache:merchant:city:%s";
    // 城市线下门店数据key
    public final static String OMS_CACHE_ALL_STORE_BY_CITY_KEY = "oms:o2o:cache:all:store:by:city:%s";
    // 所有省市区
    public final static String OMS_CACHE_ALL_PRINCE_CITY_AREA = "oms:o2o:cache:all:prince:city:area";
    
    // 强审锁key
    public final static String OMS_LOCK_LOCK_STOCK_RETRY_KEY = "oms:o2o:order:lock:lockStockRetry:%s";

    // 仓库数据缓存key
    public final static String REDIS_KEY_STORE_HOUSE_KEY = "oms:o2o:cache:storehouse:%s";

    // 经营报表分析-销售-历史时间
    public final static String REDIS_KEY_ORDER_STATISTICS_PAY_TIME_KEY = "oms:o2o:cache:statistics:%s:pay:time:%s";
    // 经营报表分析-下账-历史时间
    public final static String REDIS_KEY_ORDER_STATISTICS_BILL_TIME_KEY = "oms:o2o:cache:statistics:%s:bill:time:%s";

    // 延迟任务key
    public final static String REDIS_KEY_DELAY_TASK_KEY = "oms:o2o:task:delay:%s";

    // 延迟任务监听key
    public final static String REDIS_KEY_DELAY_TASK_LISTEN_KEY = "oms:o2o:task:delay:listen";
    /** 呼叫骑手并发key */
    public final static String REDIS_KEY_CALL_RIDER_KEY = "oms:o2o:call:rider:%s:%s";
    /** 呼叫骑手参数key */
    public final static String REDIS_KEY_CALL_RIDER_PARAM_KEY = "oms:o2o:param:call:rider:%s:%s";
    /** 延迟呼叫骑手开关 */
    public final static String REDIS_KEY_DELAY_CALL_RIDER_OPEN_KEY = "oms:o2o:constant:delaycallrider:open";
    /** 比对开关 */
    public final static String REDIS_KEY_ORDER_DIFF_OPEN_KEY = "oms:o2o:constant:orderdiff:open";
    /** 内存排序开关 */
    public final static String REDIS_KEY_DORIS_SORT_KEY = "oms:o2o:constant:memory:sort:open";
    //用户平台权限key
    public final static String REDIS_KEY_MER_USER_PLAT_KEY = "oms:o2o:user:plat:%s:%s:%s";
    //服务商门店消息处理任务key
    public final static String REDIS_KEY_STORE_MESSAGE_KEY = "oms:o2o:store:message";
    //同步门店key
    public final static String REDIS_KEY_SYNC_STORE_KEY = "oms:o2o:store:sync:%s:%s:%s";

    public final static String REDIS_KEY_RECEIVE_TIMEOUT = "oms:receive-timeout:%s";

    public final static String REDIS_KEY_LOCAL_STOCK = "oms:o2o:local_stock:%s:%s";
    public final static String REDIS_KEY_COMMODITY_STOCK = "oms:o2o:commodity-stock-lock:%s";
    public final static String SEND_COMMODITY_NO_VALID_ORDER_CURRENT_LIMITING = "oms:o2o:commodity:no_valid_order_limit_%s_%s_%s";

    public final static String REDIS_KEY_PLAT_AUTHORITY = "oms:o2o:plat:auth:%s";

    public final static String REDIS_KEY_ORDER_SYNC = "oms:o2o:order:sync:%s:%s:%s";

    public final static String REDIS_KEY_ORDER_RECALCULATE = "oms:o2o:order:recalculate:%s";

    //同步门店key
    public final static String REDIS_KEY_SYNC_STORE_BY_EXCEL_KEY = "oms:o2o:store:sync:%s";

    //门店地址
    public final static String REDIS_GEO_KEY_STORE_LOCATION = "oms:o2o:geo:store:location";

    //查看收货信息次数
    public final static String REDIS_KEY_VIEW_RECEIVER_NUM = "oms:order:view:receiver:num:%s";

    //处理因库存不足下账失败订单，缓存有库存的商品key
    public static final String FAIL_ACCOUNT_ORDER_COMMODITY_STOCK_KEY = "oms:b2c:cache:fail:account:order:commodity:stock:%s";

    public final static String REDIS_KEY_ACCOUNT_ORDER_CACHE_KEY = "oms:account:order:cache:%s";
    public final static String REDIS_KEY_ACCOUNT_REFUND_CACHE_KEY = "oms:account:refund:cache:%s";

    public static final String BATCH_PICK_FAIL_KEY = "oms:o2o:cache:batch:pick:fail:%s";

    public static final String STORE_ACCESS_KEY = "oms:store:cache:%s";

    //子公司信息缓存
    public static final String REDIS_KEY_SUB_COMPANY_CACHE_KEY = "oms:o2o:sub:company:cache:%s";

    public static String getRedisKeyAccountOrderCache(String orderNo){
        return String.format(REDIS_KEY_ACCOUNT_ORDER_CACHE_KEY, orderNo);
    }

    public static String getRedisKeyAccountRefundCache(String orderNo){
        return String.format(REDIS_KEY_ACCOUNT_REFUND_CACHE_KEY, orderNo);
    }

    public static String getRedisKeyViewReceiverNum(String userId){
        return String.format(REDIS_KEY_VIEW_RECEIVER_NUM, userId);
    }

    public static String getCommodityNoValidOrderLimitKey(String merCode, String orgCode, String erpCode) {
        return String.format(SEND_COMMODITY_NO_VALID_ORDER_CURRENT_LIMITING, merCode, orgCode, erpCode);
    }

    public static String getLockStockRetryKey(Long orderNo){
        return String.format(OMS_LOCK_LOCK_STOCK_RETRY_KEY, orderNo.toString());
    }

    public static String getLocalStock(String merCode, String onlineStoreCode) {
        return String.format(REDIS_KEY_LOCAL_STOCK, merCode, onlineStoreCode);
    }

    public static String getCommodityStockLock(String orderNo) {
        return String.format(REDIS_KEY_COMMODITY_STOCK, orderNo);
    }

    public static String getReceiveTimeoutKey(String merCode) {
        return String.format(REDIS_KEY_RECEIVE_TIMEOUT, merCode);
    }
    
    public static String getErpSyncLastIdKey(){
        return erpSynsLastIdKey;
    }

    public static String getErpSyncUniqueKey(){
        return erpSynsUniqueKey;
    }

    public static String getOrderSyncLastIdKey(){
        return orderInfoSyncLastIdKey;
    }

    public static String getOrderSyncUniqueKey(){
        return orderInfoSyncUniqueKey;
    }

    public static String getOrderChangeSelfDeliveryKey(){
        return orderChangeSelfDeliveryKey;
    }

    /**
     * 商户门店订单统计key
     * @param merCode 商户编码
     * @param storeCode 门店编码
     */
    public static String getOrderCountKey(String merCode,String storeCode){
        return String.format(ORDERCOUNT_KEY, merCode,storeCode);
    }

    /**
     * 获取微商城开通b2c的商户
     */
    public static String getWscopenb2c(){
        return WSCOPENB2C;
    }

    /**
     * 是否开启2.0订单查询
     * @param merCode 商户编码
     */
    public static String getQueryConfig(String merCode){
        return String.format(ORDER_QUERY_2, merCode);
    }


    /**
     * 获取拣货复核缓存数据
     * @param orderNo
     * @return
     */
    public static String getPickconfirmCacheKey(Long orderNo){
        return String.format(PICKCONFIRM_KEY,orderNo);
    }

    /**
     *
     * getDscloudPrintKey:商户门店打印key
     *
     * @param merCode 商户编码
     * @param storeCode 门店编码
     * @return  redis key
     * <AUTHOR>
     * @date 2021年1月4日 下午5:04:47
     */
    public static String getDscloudPrintKey(String merCode,String storeCode){
        return String.format(DSCLOUD_PRINT_KEY, merCode,storeCode);
    }

    /**
     *
     * getDscloudPrintKey:商户门店声音key
     *
     * @param merCode 商户编码
     * @param storeCode 门店编码
     * @return  redis key
     * <AUTHOR>
     * @date 2021年1月4日 下午5:04:47
     */
    public static String getDscloudSoundKey(String merCode,String storeCode){
        return String.format(DSCLOUD_SOUND_KEY, merCode,storeCode);
    }

    /**
    * @Description: 仓库数据缓存key
    * @Param: [extend]
    * @return: java.lang.String
    * @Author: syuson
    * @Date: 2021-5-13
    */
    public static String getStoreHouseKey(String extend){
        return String.format(REDIS_KEY_STORE_HOUSE_KEY, extend);
    }


    public static String getOrderStatisticsPayTimeKey(OrderReportStatisticsQueryReqDto reqDto){
        String md5Str = getOrderReportStatisticsStr(reqDto);
        return String.format(REDIS_KEY_ORDER_STATISTICS_PAY_TIME_KEY, reqDto.getMerCode(),md5Str);
    }

    public static String getOrderStatisticsBillTimeKey(OrderReportStatisticsQueryReqDto reqDto){
        String md5Str = getOrderReportStatisticsStr(reqDto);
        return String.format(REDIS_KEY_ORDER_STATISTICS_BILL_TIME_KEY, reqDto.getMerCode(),md5Str);
    }

    private static String getOrderReportStatisticsStr(OrderReportStatisticsQueryReqDto reqDto) {
        String str = "%s_%s_%s_%s_%s_%s";
        String organizationCodeStr = reqDto.getOrganizationCodeList().stream().collect(Collectors.joining("|"));
        String platformCodeStr = reqDto.getPlatformCodeList().stream().collect(Collectors.joining("|"));
        String key = String.format(str,reqDto.getMerCode(),reqDto.getGroupByType(),platformCodeStr,organizationCodeStr
                ,reqDto.getTimeStart().getTime(),reqDto.getTimeEnd().getTime());
        return MD5Utils.MD5Encode(key,"UTF-8");
    }

    /**
    * @Description: 延迟任务key
    * @Param: [extend]
    * @return: java.lang.String
    * @Author: syuson
    * @Date: 2021-7-14
    */
    public static String getDelayTaskKey(String extend){
        return String.format(REDIS_KEY_DELAY_TASK_KEY,extend);
    }

    public static String getCallRiderKey(String merCode,Long orderNo){
        return String.format(REDIS_KEY_CALL_RIDER_KEY,merCode, orderNo);
    }

    public static String getCallRiderParamKey(String merCode,Long orderNo){
        return String.format(REDIS_KEY_CALL_RIDER_PARAM_KEY,merCode, orderNo);
    }

    public static String getUserPlatKey(String merCode,String userId,String serviceModel){
        return String.format(REDIS_KEY_MER_USER_PLAT_KEY,merCode,userId,serviceModel);
    }

    public static String getStoreMessageKey(){
        return REDIS_KEY_STORE_MESSAGE_KEY;
    }

    public static String getSyncStoreKey(String merCode,String platformCode,String clientCode){
        return String.format(REDIS_KEY_SYNC_STORE_KEY,merCode,platformCode,clientCode);
    }

    public static String getAuthorityPlatKey(String merCode){
        return String.format(REDIS_KEY_PLAT_AUTHORITY,merCode);
    }

    public static String getAuthorityPlatHashKey(String merCode,String userId,String serviceMode){
        return String.format("%s_%s_%s",merCode,userId,serviceMode);
    }

    public static String getOrderSyncKey(String merCode,String orgCode, Integer index){
        return String.format(REDIS_KEY_ORDER_SYNC,merCode,orgCode,index);
    }

    public static String getRecalculateKey(Long orderNo){
        return String.format(REDIS_KEY_ORDER_RECALCULATE,orderNo);
    }

    public static String getSyncStoreByExcelKey(String clientCode){
        return String.format(REDIS_KEY_SYNC_STORE_BY_EXCEL_KEY,clientCode);
    }

    public static String getRedisKeyFailAccountOrderCommodityStock(String organizationCode,String erpCode){
        return String.format(FAIL_ACCOUNT_ORDER_COMMODITY_STOCK_KEY+organizationCode, erpCode);
    }

    public static String getRedisKeyBatchPickFailKey(String json){
        return String.format(BATCH_PICK_FAIL_KEY, json);
    }

    public static String getStoreAccessKey(String merCode, String platformCode, String onlineStoreCode, String onlineClientCode) {
        return String.format(STORE_ACCESS_KEY, merCode + "-" + platformCode + "-" + onlineStoreCode + "-" + onlineClientCode);
    }

    public static String getRedisKeySubCompanyKey(String organizationCode){
        return String.format(REDIS_KEY_SUB_COMPANY_CACHE_KEY, organizationCode);
    }

}
