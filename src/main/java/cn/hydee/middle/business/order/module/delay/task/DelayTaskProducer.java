package cn.hydee.middle.business.order.module.delay.task;

import cn.hydee.middle.business.order.module.delay.model.DelayTask;
import cn.hydee.middle.business.order.util.JsonUtil;
import cn.hydee.middle.business.order.util.RedisKeyUtil;
import cn.hydee.middle.business.order.util.redis.RedisZSetUtil;
import org.springframework.stereotype.Service;

import java.util.Date;

/**
 * <AUTHOR>
 * @date 2021/07/14
 */
@Service
public class DelayTaskProducer {

    private boolean validateScore(long score){
        Date now = new Date();
        if(now.getTime() < score){
            return Boolean.TRUE;
        }
        return Boolean.FALSE;
    }

    /**
    * @Description: 下发延迟任务
    * @Param: [delayTask]
    * @return: boolean
    * @Author: syuson
    * @Date: 2021-7-14
    */
    public boolean pushDelayTask(DelayTask delayTask){
        String key = RedisKeyUtil.getDelayTaskKey("one");
        long score = delayTask.convertScore();
        if(!validateScore(score)){
            return Boolean.FALSE;
        }
        // 任务是否存在
        String value = JsonUtil.object2Json(delayTask.getTaskInfo());
        Long rankIndex = RedisZSetUtil.rank(key,value);
        if( null != rankIndex && rankIndex >= 0){
            // 更新score
            RedisZSetUtil.remove(key,value);
        }
        Boolean pushFlag = RedisZSetUtil.addByScoreNoException(key,value,score);
        return pushFlag.booleanValue();
    }

    public boolean pushRiderPollingTask(DelayTask delayTask,String oldValue){
        String key = RedisKeyUtil.getDelayTaskKey("one");
        long score = delayTask.convertScore();
        if(!validateScore(score)){
            return Boolean.FALSE;
        }
        //任务存在先删除，重新添加
        if(oldValue != null){
            Long rankIndex = RedisZSetUtil.rank(key,oldValue);
            if(rankIndex != null && rankIndex >= 0){
                RedisZSetUtil.remove(key,oldValue);
            }
        }
        String value = JsonUtil.object2Json(delayTask.getTaskInfo());
        Boolean pushFlag = RedisZSetUtil.addByScoreNoException(key,value,score);
        return pushFlag.booleanValue();
    }

}
