package cn.hydee.middle.business.order.storeautosync.domain.storeconfig.handler;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hydee.middle.business.order.Enums.DeliveryPlatformEnum;
import cn.hydee.middle.business.order.Enums.PlatformCodeEnum;
import cn.hydee.middle.business.order.entity.*;
import cn.hydee.middle.business.order.mapper.DsDeliveryStoreRepo;
import cn.hydee.middle.business.order.mapper.DsOnlineStoreDeliveryRepo;
import cn.hydee.middle.business.order.mapper.OrderChangeSelfDeliveryConfigMapper;
import cn.hydee.middle.business.order.service.OrderChangeSelfDeliveryConfigService;
import cn.hydee.middle.business.order.service.baseinfo.DsDeliveryStoreService;
import cn.hydee.middle.business.order.service.baseinfo.DsOnlineStoreConfigService;
import cn.hydee.middle.business.order.service.baseinfo.DsOnlineStoreDeliveryService;
import cn.hydee.middle.business.order.storeautosync.constants.StoreSyncStatus;
import cn.hydee.middle.business.order.storeautosync.domain.storeconfig.entity.StoreAutoConfigDTO;
import cn.hydee.middle.business.order.storeautosync.infrastructure.mysql.entity.BaseStepEntity;
import cn.hydee.middle.business.order.storeautosync.infrastructure.mysql.entity.DsOnlineStoreAutoConfigStep;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import java.util.function.Function;
import javafx.util.Pair;
import org.jetbrains.annotations.Nullable;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.time.LocalDateTime;
import java.util.Collections;
import java.util.List;
import java.util.function.Consumer;

import static cn.hydee.middle.business.order.storeautosync.domain.adapter.StoreSyncAdapter.onlineStore2ConfigEntityConvert;

@Component
public class SelfDeliveryStepHandler extends StoreConfigAbstractHandler {

    @Autowired
    private OrderChangeSelfDeliveryConfigService orderChangeSelfDeliveryConfigService;

    @Autowired
    private OrderChangeSelfDeliveryConfigMapper orderChangeSelfDeliveryConfigMapper;

    @Autowired
    private DsDeliveryStoreService dsDeliveryStoreService;

    @Autowired
    private DsOnlineStoreDeliveryRepo dsOnlineStoreDeliveryRepo;

    @Autowired
    private DsOnlineStoreConfigService dsOnlineStoreConfigService;

    @Autowired
    private DsOnlineStoreDeliveryService dsOnlineStoreDeliveryService;

    @Override
    public Consumer<DsOnlineStoreAutoConfigStep> handle(StoreAutoConfigDTO onlineStore) {

        String remark = "";
        //先添加默认的配送方式
        dsOnlineStoreDeliveryService.createDefaultDeliverySingle(onlineStore.getMerCode(), onlineStore.getId());
        LocalDateTime creatTime = LocalDateTime.now();
        BaseStepEntity baseStepEntity = new BaseStepEntity();
        baseStepEntity.setCreateTime(creatTime);
        //获取配送方式
        Pair<List<DsDeliveryStore>, String> deliveryStorePair = dsDeliveryStoreService.getDeliveryStore(onlineStore.getMerCode(), onlineStore.getOnlineStoreCode(), onlineStore.getOnlineStoreName());
        List<DsDeliveryStore> dsDeliveryStores = deliveryStorePair.getKey();
        remark = deliveryStorePair.getValue();
        baseStepEntity.setRemark(remark);
        if (CollUtil.isEmpty(dsDeliveryStores)) {
            baseStepEntity.setStatus(StoreSyncStatus.FAIL.getCode());
            baseStepEntity.setFinishTime(LocalDateTime.now());
            baseStepEntity.setRemark("未找到对应的配送门店," + remark);
            return step -> step.setSelfDeliveryStep(baseStepEntity);
        }
        //店铺-配送方式绑定
        dsOnlineStoreDeliveryService.createOnlineStoreDelivery(dsDeliveryStores, onlineStore.getId());

        //获取店铺可以转自配送的配送方式,按照 美团 > 达达 > 顺丰 > 员工配送 的优先级获取
        DsOnlineStoreDelivery dsOnlineStoreDelivery = getStoreSelfDelivery(onlineStore.getId());
        if(dsOnlineStoreDelivery == null){
            baseStepEntity.setStatus(StoreSyncStatus.FAIL.getCode());
            baseStepEntity.setFinishTime(LocalDateTime.now());
            baseStepEntity.setRemark("未找到可用的自配送方式," + remark);
            return step -> step.setSelfDeliveryStep(baseStepEntity);
        }
        dsOnlineStoreConfigService.update(Wrappers.<DsOnlineStoreConfig>lambdaUpdate()
            .set(DsOnlineStoreConfig::getSelfDeliveryType, dsOnlineStoreDelivery.getDeliveryType())
            .eq(DsOnlineStoreConfig::getOnlineStoreId, onlineStore.getId())
        );

        // 订单转自配送设置
        orderChangeSelfDeliveryConfigService.orderChangeSelfDeliveryProcess(onlineStore, dsOnlineStoreDelivery);

        baseStepEntity.setStatus(StoreSyncStatus.SUCCESS.getCode());
        baseStepEntity.setCreateTime(creatTime);
        baseStepEntity.setFinishTime(LocalDateTime.now());
        return step -> step.setSelfDeliveryStep(baseStepEntity);
    }

    @Nullable
    private DsOnlineStoreDelivery getStoreSelfDelivery(Long onlineStoreId) {
        List<DsOnlineStoreDelivery> dsOnlineStoreDeliveries = dsOnlineStoreDeliveryRepo.selectList(
            Wrappers.<DsOnlineStoreDelivery>lambdaQuery()
                .eq(DsOnlineStoreDelivery::getOnlineStoreId, onlineStoreId)
        );
        DsOnlineStoreDelivery dsOnlineStoreDelivery = dsOnlineStoreDeliveries.stream()
            .filter(o -> DeliveryPlatformEnum.MEITUAN_RIDER.getName().equals(o.getDeliveryType()))
            .findFirst().orElse(null);
        if (dsOnlineStoreDelivery == null) {
            dsOnlineStoreDelivery = dsOnlineStoreDeliveries.stream()
                .filter(o -> DeliveryPlatformEnum.DADA.getName().equals(o.getDeliveryType()))
                .findFirst().orElse(null);
        }
        if (dsOnlineStoreDelivery == null) {
            dsOnlineStoreDelivery = dsOnlineStoreDeliveries.stream()
                .filter(o -> DeliveryPlatformEnum.SFTC.getName().equals(o.getDeliveryType()))
                .findFirst().orElse(null);
        }
        if (dsOnlineStoreDelivery == null) {
            dsOnlineStoreDelivery = dsOnlineStoreDeliveries.stream()
                .filter(o -> DeliveryPlatformEnum.STAFF.getName().equals(o.getDeliveryType()))
                .findFirst().orElse(null);
        }
        return dsOnlineStoreDelivery;
    }

    @Override
    public Integer getOrder() {
        return 3;
    }

    @Override
    public Function<DsOnlineStoreAutoConfigStep, Boolean> dependencyHandlerResult() {
        return step -> step.getOrganizationStep().getStatus().equals(StoreSyncStatus.SUCCESS.getCode());
    }

    @Override
    public Consumer<DsOnlineStoreAutoConfigStep> callback(String errorMessage) {
        BaseStepEntity baseStepEntity = new BaseStepEntity();
        baseStepEntity.setStatus(StoreSyncStatus.FAIL.getCode());
        baseStepEntity.setFinishTime(LocalDateTime.now());
        baseStepEntity.setRemark(errorMessage);
        return step -> step.setSelfDeliveryStep(baseStepEntity);
    }

    @Override
    public Consumer<DsOnlineStoreAutoConfigStep> repair(DsOnlineStore onlineStore, DsOnlineStoreAutoConfigStep step) {
        List<StoreAutoConfigDTO> configStoreList = onlineStore2ConfigEntityConvert(Collections.singletonList(onlineStore), null);
        if (CollUtil.isEmpty(configStoreList)) {
            return null;
        }
        return handle(configStoreList.get(0));
    }
}
