package cn.hydee.middle.business.order.util.sqlbuild.base;

import java.util.Collection;

/**
 * 查询相关接口
 * @param <Children>  返回对象
 * @param <R> String对象
 * @param <T> 实体类对象
 * @param <K> SFuction</T> 函数式接口
 *
 * <AUTHOR>
 */
public interface ICustomQuerySqlBase <Children,R,T,K> {

    /**
     * 设置查询字段
     *
     * @param columns 字段数组
     * @return children
     */
    Children select(K... columns);

    /**
     * java对象构建select 后面的列表名称
     * @param flag
     * @param columns
     * @return
     */
    Children select(boolean flag, R... columns);

    /**
     * java对象构建select 后面的列表名称
     * @param entity
     * @return
     */
    Children select(T entity);



    /**
     * 查询条件 SQL 片段
     * @return
     */
    String getSqlSelect();

    /**
     * from操作
     * @param tableName
     * @return
     */
    Children from(R tableName);



    /**
     * 获取where字段
     * @return
     */
    Children where();

    /**
     * 左连接
     * @param tableName
     * @return
     */
    Children leftJoin(R tableName);

    /**
     * 右连接
     * @param tableName
     * @return
     */
    Children rightJoin(R tableName);

    /**
     * 连接
     * @param tableName
     * @return
     */
    Children join(R tableName);

    /**
     * 别名
     * @param name
     * @return
     */
    Children as(R name);
    /**
     * 表连接条件
     * @return
     */
    Children on();

    /**
     * in查询
     * @param column
     * @param coll
     * @return
     */
    Children in(K column, Collection<?> coll);

    /**
     * 等于
     * @param column
     * @param val
     * @return
     */
    Children eq(K column, Object val);

    /**
     * 小于
     * @param column
     * @param val
     * @return
     */
    Children lt(K column, Object val);
    /**
     * 小于等于
     * @param column
     * @param val
     * @return
     */
    Children le(K column, Object val);

    /**
     * 大于
     * @param column
     * @param val
     * @return
     */
    Children gt(K column, Object val);

    /**
     * 大于等于
     * @param column
     * @param val
     * @return
     */
    Children ge(K column, Object val);

    /**
     * 不等于
     * @param column
     * @param val
     * @return
     */
    Children ne(K column, Object val);

    /**
     * 模糊查询
     * @param column
     * @param val
     * @return
     */
    Children like(K column, Object val);

    /**
     * 模糊查询非
     * @param column
     * @param val
     * @return
     */
    Children noLike(K column, Object val);

    /**
     * and
     * @return
     */
    Children and();

    /**
     * or
     * @return
     */
    Children or();

    /**
     * 排序
     * @param column
     * @return
     */
    Children orderBy(K... column);

    /**
     * 分组状态
     * @param column
     * @return
     */
    Children groupBy(K... column);

    /**
     * 分组后查询条件
     * @return
     */
    Children having();

}
