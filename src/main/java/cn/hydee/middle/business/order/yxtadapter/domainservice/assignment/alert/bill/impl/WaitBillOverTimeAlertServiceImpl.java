package cn.hydee.middle.business.order.yxtadapter.domainservice.assignment.alert.bill.impl;

import cn.hydee.middle.business.order.Enums.ErpStateEnum;
import cn.hydee.middle.business.order.Enums.OrderStateEnum;
import cn.hydee.middle.business.order.entity.OrderInfo;
import cn.hydee.middle.business.order.mapper.OrderInfoMapper;
import cn.hydee.middle.business.order.yxtadapter.constant.RobotEnumExample;
import cn.hydee.middle.business.order.yxtadapter.domainservice.assignment.alert.bill.WaitBillOverTimeAlertService;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.yxt.common.wechatrobot.util.WxRobotOkHttpUtils;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;
import java.util.stream.Stream;

@Slf4j
@Service
@RequiredArgsConstructor
public class WaitBillOverTimeAlertServiceImpl implements WaitBillOverTimeAlertService {

    private final OrderInfoMapper orderInfoMapper;

    private static final Integer MAX_NUMBER = 5;

    @Override
    public void pullOverTimeWaitBillRecord(Long overTime) {
        LocalDateTime handleTime = LocalDateTime.now().minusMinutes(overTime);

        List<OrderInfo> orderInfos = orderInfoMapper.selectList(new LambdaQueryWrapper<OrderInfo>()
                .eq(OrderInfo::getErpState, ErpStateEnum.WAIT_SALE.getCode())
                .le(OrderInfo::getCreated,handleTime)
                .in(OrderInfo::getOrderState, Arrays.asList(OrderStateEnum.UN_DELIVERY.getCode(),OrderStateEnum.POSTING.getCode(),OrderStateEnum.COMPLETED.getCode()))
                .ge(OrderInfo::getCreated,LocalDateTime.now().withHour(0).withMinute(0).withSecond(0).withNano(0))
        );

        sendWaitBillAlert(orderInfos,overTime);
    }


    @Override
    public void sendWaitBillAlert(List<OrderInfo> orderInfo, Long overTime) {
        Integer limit = countStep(orderInfo.size());
        List<String> list = orderInfo.stream().map(x-> {
            return x.getOrganizationCode() + " : " + x.getOrderNo();
        }).collect(Collectors.toList());
        List<List<String>> groupList = Stream.iterate(0, n -> n + 1).limit(limit).parallel().map(a -> list.stream().skip(a * MAX_NUMBER).limit(MAX_NUMBER).collect(Collectors.toList())).collect(Collectors.toList());

        groupList.forEach(x-> {
            String orderNoStr = x.stream().map(Object::toString).collect(Collectors.joining(",\n"));
            String content = RobotEnumExample.ORDER_OVER_TIME_NO_BILL.getContent();
            WxRobotOkHttpUtils.post(RobotEnumExample.ORDER_OVER_TIME_NO_BILL,String.format(content,orderNoStr,overTime));
        });

    }
    private Integer countStep(Integer size){
        return (size + MAX_NUMBER -1) / MAX_NUMBER;
    }
}
