package cn.hydee.middle.business.order.util.sqlbuild;

import cn.hydee.middle.business.order.util.sqlbuild.CustomDeleteWrapper;
import cn.hydee.middle.business.order.util.sqlbuild.CustomQueryWrapper;
import cn.hydee.middle.business.order.util.sqlbuild.CustomUpdateWrapper;
import cn.hydee.middle.business.order.util.sqlbuild.sqlcheck.CustomSqlCheck;
import cn.hydee.middle.business.order.util.sqlbuild.sqlcheck.ICustomSqlCheck;
import cn.hydee.middle.business.order.util.sqlbuild.sqlformat.CustomSqlFormat;
import cn.hydee.middle.business.order.util.sqlbuild.sqlformat.ICustomSqlFormat;

/**
 * cn.hydee.middle.business.order.util.sqlbuild.builder
 *
 * <AUTHOR> @version 1.0
 * @date 2020/8/18 15:44
 **/
public class CustomSqlFactory {

    private CustomSqlFactory(){

    }
    /**
     * 创建sql格式化实例
     * @return
     */
    public static ICustomSqlFormat createCustomSqlFormat(){
        return new CustomSqlFormat();
    }

    /**
     * 创建sql合法性验证实例
     * @return
     */
    public static ICustomSqlCheck createCustomSqlCheck(){
        return new CustomSqlCheck();
    }

}
