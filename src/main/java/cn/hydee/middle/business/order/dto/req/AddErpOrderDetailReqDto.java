package cn.hydee.middle.business.order.dto.req;

import lombok.Data;

import java.math.BigDecimal;

@Data
public class AddErpOrderDetailReqDto {

    /** 商品erp编码 */
    private String erpCode;

    /** 商品名称 */
    private String commodityName;

    /** 商品数量 */
    private Double commodityCount;

    /** 单价 */
    private BigDecimal price;

    /** 商品总额（商品数量 * 单价） */
    private BigDecimal totalAmount;

    /** 商品参与活动类型(0：正常销售，1:特价商品，2：促销商品，3：会员活动商品) */
    private Integer gainType;

    /** 商品通用名 */
    private String commonName;

    /** 商品条形码 */
    private String barCode;

    /** 营业员员工编码 */
    private String saleClerk;

    /** 成本价 */
    private BigDecimal costPrice;

    /** 中药标志（0-非中药，1-中药） */
    private Integer isChinaMedicine;

    /** 中药标志（0-非中药，1-中药），自建字段，非接口返回的 */
    private Integer chinaMedicineFlag;
    /**
     * 商品批准文号
     */
    private String approvalNumber;
    /**
     * 商品规格信息
     */
    private String specDesc;

    /** 商品分类 */
    private String classType;
}
