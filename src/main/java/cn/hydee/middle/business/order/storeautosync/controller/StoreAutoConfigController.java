package cn.hydee.middle.business.order.storeautosync.controller;

import cn.hutool.core.collection.ListUtil;
import cn.hydee.middle.business.order.storeautosync.domain.storeconfig.StoreAutoConfigManager;
import cn.hydee.middle.business.order.storeautosync.facade.storeconfig.entity.StoreAutoConfigSyncMessage;
import cn.hydee.middle.business.order.storeautosync.facade.storeconfig.entity.StoreConfigExecuteRequest;
import cn.hydee.middle.business.order.storeautosync.facade.storeconfig.entity.StoreConfigRepairRequest;
import cn.hydee.middle.business.order.storeautosync.facade.storeconfig.service.StoreAutoConfigService;
import cn.hydee.starter.controller.AbstractController;
import cn.hydee.starter.dto.ResponseBase;
import com.alibaba.fastjson.JSON;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

@RestController
@RequestMapping("/${api.version}/ds/store/config")
@Api(tags = "网店自动初始化配置")
public class StoreAutoConfigController extends AbstractController {

    @Autowired
    private StoreAutoConfigService storeAutoConfigService;

    @Autowired
    private StoreAutoConfigManager storeAutoConfigManager;

    @PostMapping("/repair")
    @ApiOperation("重新执行某一项配置")
    public ResponseBase<Void> storeConfigRepair(
            @RequestBody StoreConfigRepairRequest request
    ) {
        storeAutoConfigService.storeConfigRepair(request);
        return ResponseBase.success();
    }

    @PostMapping("/execute")
    @ApiOperation("job执行")
    public ResponseBase<Void> storeConfigExecute(
            @RequestBody String requestJson
    ) {
        storeAutoConfigService.storeConfigExecute(JSON.parseObject(requestJson, StoreConfigExecuteRequest.class));
        return ResponseBase.success();
    }

    @PostMapping("/mq/process")
    @ApiOperation("重新执行mq")
    public ResponseBase<Void> storeConfigRepair(
            @RequestBody StoreAutoConfigSyncMessage request
    ) {
        storeAutoConfigService.handleStoreAutoConfigSyncMessage(ListUtil.of(request));
        return ResponseBase.success();
    }
}
