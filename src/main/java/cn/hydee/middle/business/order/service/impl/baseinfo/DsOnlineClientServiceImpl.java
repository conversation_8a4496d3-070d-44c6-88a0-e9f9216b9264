package cn.hydee.middle.business.order.service.impl.baseinfo;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import cn.hydee.middle.business.order.Enums.DsConstants;
import cn.hydee.middle.business.order.Enums.DsErrorType;
import cn.hydee.middle.business.order.Enums.PlatformCodeEnum;
import cn.hydee.middle.business.order.domain.ResponseNet;
import cn.hydee.middle.business.order.dto.req.BaseInfoOrByOrIdDto;
import cn.hydee.middle.business.order.dto.req.DsOnlineClientReqDTO;
import cn.hydee.middle.business.order.dto.req.OnlineStoreByOrDto;
import cn.hydee.middle.business.order.dto.req.baseinfo.*;
import cn.hydee.middle.business.order.dto.rsp.baseinfo.*;
import cn.hydee.middle.business.order.entity.*;
import cn.hydee.middle.business.order.feign.MiddleBaseInfoClient;
import cn.hydee.middle.business.order.http.DsInterfaceHttpAdapter;
import cn.hydee.middle.business.order.mapper.*;
import cn.hydee.middle.business.order.service.VerifyService;
import cn.hydee.middle.business.order.service.baseinfo.DsMerchantGroupInfoService;
import cn.hydee.middle.business.order.service.baseinfo.DsOnlineClientService;
import cn.hydee.middle.business.order.service.baseinfo.DsOnlineStoreDeliveryService;
import cn.hydee.middle.business.order.service.baseinfo.DsOnlineStoreService;
import cn.hydee.middle.business.order.service.cache.BaseInfoCache;
import cn.hydee.middle.business.order.util.ExceptionUtil;
import cn.hydee.middle.business.order.util.RedisKeyUtil;
import cn.hydee.middle.business.order.util.redis.RedisHashUtil;
import cn.hydee.starter.dto.PageDTO;
import cn.hydee.starter.dto.ResponseBase;
import cn.hydee.starter.exception.WarnException;
import cn.hydee.starter.util.BeanUtil;
import cn.hydee.starter.util.ExLogger;
import cn.hydee.starter.util.UUIDUtil;
import com.alibaba.fastjson.JSON;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Lazy;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.data.redis.core.StringRedisTemplate;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;
import org.springframework.util.StringUtils;

import java.util.*;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 */
@Service
@Slf4j
public class DsOnlineClientServiceImpl extends ServiceImpl<DsOnlineClientRepo, DsOnlineClient> implements DsOnlineClientService {

    @Autowired
    private DsInterfaceHttpAdapter thirdPartyHttpAdapter;
    @Autowired
    private MiddleBaseInfoClient middleBaseInfoClient;
    @Autowired
    private BaseInfoCache baseInfoCache;
    @Autowired
    private DsMerchantGroupInfoService merchantGroupInfoService;
    @Lazy
    @Autowired
    private DsOnlineStoreService dsOnlineStoreService;
    @Autowired
    private DsOnlineStoreDeliveryService onlineStoreDeliveryService;
    @Autowired
    private DsMerchantGroupInfoRepo merchantGroupInfoRepo;
    @Autowired
    private DsOnlineClientRepo dsOnlineClientRepo;
    @Autowired
    private DsOnlineStoreRepo dsOnlineStoreRepo;
    @Autowired
    private DsDeliveryClientRepo deliveryClientRepo;
    @Autowired
    private DsDeliveryStoreRepo deliveryStoreRepo;
    @Autowired
    private DsOnlineStoreDeliveryRepo onlineStoreDeliveryRepo;
    @Autowired
    private DsOnlineStoreConfigRepo onlineStoreConfigRepo;
    @Autowired
    private DsStoreOrderConfigRepo storeOrderConfigRepo;
    @Autowired
    private DsStoreSoundConfigRepo storeSoundConfigRepo;
    @Autowired
    private VerifyService verifyService;
    @Autowired
    private StringRedisTemplate stringRedisTemplate;

    @Override
    public PageDTO<DsOnlineClientResDTO> queryOnlineShop(String merCode, DsOnlineClientQueryDTO dto) throws IllegalAccessException, InstantiationException {
        QueryWrapper<DsOnlineClient> queryWrapper = new QueryWrapper<>();
        LambdaQueryWrapper<DsOnlineClient> lambda = queryWrapper.lambda();
        lambda.eq(DsOnlineClient::getMerCode, merCode);
        lambda.ne(DsOnlineClient::getSyncCreate, DsConstants.INTEGER_ONE);
        if (!StringUtils.isEmpty(dto.getPlatformCode())) {
            lambda.eq(DsOnlineClient::getPlatformCode, dto.getPlatformCode());
        }
        if (!StringUtils.isEmpty(dto.getServiceMode())) {
            lambda.eq(DsOnlineClient::getServiceMode, dto.getServiceMode());
        }
        if (!StringUtils.isEmpty(dto.getOnlineClientName())) {
            lambda.like(DsOnlineClient::getOnlineClientName, dto.getOnlineClientName());
        }
        PageDTO<DsOnlineClientResDTO> pageDTO = new PageDTO<>();
        int count = dsOnlineClientRepo.selectCount(queryWrapper);
        List<DsOnlineClient> list = new ArrayList<>();
        if (count != 0) {
            IPage<DsOnlineClient> page = new Page<>(dto.getCurrentPage(), dto.getPageSize(), count);
            lambda.orderByDesc(DsOnlineClient::getModifyTime);
            list = dsOnlineClientRepo.selectPage(page, queryWrapper).getRecords();
        }
        pageDTO.setData(BeanUtil.copyList(list, DsOnlineClientResDTO.class));
        pageDTO.setTotalCount(count);
        pageDTO.setTotalPage((count + dto.getPageSize() - 1) / dto.getPageSize());
        return pageDTO;
    }

    @Override
    public void createOnlineShop(String merCode, DsOnlineClientCreateUpdateDTO dto) {
        wscClientExist(merCode, dto);
        // 验证【商户 + 平台 + AppID + SecretKey】是否已经授过权，限制同一账号重复创建（适用美团、饿百、京东到家）
        isOnlineClientRepeat(merCode, dto);
        // 验证网店名称是否存在
        isOnlineClientExist(merCode, dto);
        // 查询商户名称
        ResponseBase<MerchantResDTO> merchantRes = middleBaseInfoClient.getMerchantByCode(merCode);
        MerchantResDTO merchant = merchantRes.getData();
        // 调用—创建授权接口
        CreateAuthReqDto createAuthReqDto = new CreateAuthReqDto(merchant, dto);
        ResponseNet<String> base = thirdPartyHttpAdapter.createAuthO2O(createAuthReqDto);

        if (!base.checkSuccess() || base.getData() == null) {
            ExLogger.logger()
                    .field("clientCreateAuthError")
                    .field(createAuthReqDto.getClientid())
                    .error("clientCreateAuthError：{}-{}", base.getCode(), base.getMsg());
            throw ExceptionUtil.getWarnException(String.valueOf(base.getCode()), base.getMsg());
        }
        // 获取授权sessionKey
        String sessionKey = base.getData();
        // 保存商户分组信息
        merchantGroupInfoService.saveMerchantGroupInfo(merCode, createAuthReqDto.getGroupname(), sessionKey);
        // 保存线上网店信息
        DsOnlineClient dsOnlineClient = new DsOnlineClient();
        BeanUtils.copyProperties(dto, dsOnlineClient);
        dsOnlineClient.setMerCode(merCode);
        dsOnlineClient.setOnlineClientCode(createAuthReqDto.getClientid());
        dsOnlineClient.setAuthTime(new Date());
        dsOnlineClient.setSecretKey(sessionKey);

        int result = dsOnlineClientRepo.insert(dsOnlineClient);
        if (result <= 0) {
            log.error("新增网店失败：{}", dsOnlineClient);
            return;
        }
        if (needSyncStore(dto.getPlatformCode())) {
            // 同步门店信息
            dto.setOnlineClientCode(dsOnlineClient.getOnlineClientCode());
            dto.setMerCode(merCode);
            dsOnlineStoreService.synStore(dto, createAuthReqDto.getClientid(), sessionKey);
            // 每个商户对应的每个线上门店都有对应两种配送方式（员工配送,到店自提，快递配送）
            onlineStoreDeliveryService.createDefaultDelivery(merCode, dto.getPlatformCode());
        }
        //新增网店时删除平台权限的缓存
        String authorityPlatKey = RedisKeyUtil.getAuthorityPlatKey(merCode);
        RedisHashUtil.deleteHash(authorityPlatKey);
    }

    /**
     * 服务商模式创建网店
     *
     * @param merCode
     * @param dto
     */
    @Override
    public void createOnlineShopWithSupplier(String merCode, DsOnlineClientCreateUpdateDTO dto) {
        //重复性校验 商户编码 + 平台 + 商家账号
        isOnlineClientRepeatWithSupplier(merCode, dto);
        //网店名称重复性校验
        isOnlineClientExist(merCode, dto);
        // 查询商户名称
        ResponseBase<MerchantResDTO> merchantRes = middleBaseInfoClient.getMerchantByCode(merCode);
        MerchantResDTO merchant = merchantRes.getData();
        // 保存商户分组信息
        merchantGroupInfoService.saveMerchantGroupInfo(merCode, merchant.getMerName(), DsConstants.STRING_INIT_MERCHANT_SEC);

        String clientCode = UUIDUtil.generateUuid();
        // 保存线上网店信息
        DsOnlineClient dsOnlineClient = new DsOnlineClient();
        BeanUtils.copyProperties(dto, dsOnlineClient);
        dsOnlineClient.setMerCode(merCode);
        dsOnlineClient.setOnlineClientCode(clientCode);
        dsOnlineClient.setAuthTime(new Date());

        int result = dsOnlineClientRepo.insert(dsOnlineClient);
        if (result <= 0) {
            log.error("新增网店失败：{}", dsOnlineClient);
            return;
        }
        //新增网店时删除平台权限的缓存
        String authorityPlatKey = RedisKeyUtil.getAuthorityPlatKey(merCode);
        RedisHashUtil.deleteHash(authorityPlatKey);
        //网店保存成功恢复可能存在的供应商门店
//        supplierStoreDealService.updateSupplierStoreMessage(DsConstants.INTEGER_ONE,dsOnlineClient);
    }

    private void wscClientExist(String merCode, DsOnlineClientCreateUpdateDTO dto) {
        //【微商城只能授权一个网店校验】https://www.tapd.cn/61969829/prong/stories/view/1161969829001044738
        if (!DsConstants.O2O.equalsIgnoreCase(dto.getServiceMode()) || !PlatformCodeEnum.YD_JIA.getCode().equals(dto.getPlatformCode())) {
            return;
        }
        QueryWrapper<DsOnlineClient> clientWrapper = new QueryWrapper<>();
        clientWrapper.lambda().eq(DsOnlineClient::getMerCode, merCode)
                .eq(DsOnlineClient::getPlatformCode, PlatformCodeEnum.YD_JIA.getCode())
                .eq(DsOnlineClient::getServiceMode, DsConstants.O2O);
        List<DsOnlineClient> clientList = dsOnlineClientRepo.selectList(clientWrapper);
        if (!CollectionUtils.isEmpty(clientList)) {
            String clientName = clientList.get(0).getOnlineClientName();
            throw ExceptionUtil.getWarnException(DsErrorType.YDJ_ONLINE_CLIENT_EXIST.getCode(), String.format(DsErrorType.YDJ_ONLINE_CLIENT_EXIST.getMsg(), clientName));
        }
    }

    private void isOnlineClientExist(String merCode, DsOnlineClientCreateUpdateDTO dto) {
        QueryWrapper<DsOnlineClient> clientWrapper = new QueryWrapper<>();
        clientWrapper.lambda().eq(DsOnlineClient::getMerCode, merCode)
                .eq(DsOnlineClient::getPlatformCode, dto.getPlatformCode())
                .eq(DsOnlineClient::getServiceMode, dto.getServiceMode())
                .eq(DsOnlineClient::getOnlineClientName, dto.getOnlineClientName());
        List<DsOnlineClient> clientList = dsOnlineClientRepo.selectList(clientWrapper);
        if (clientList != null && !clientList.isEmpty()) {
            throw ExceptionUtil.getWarnException(DsErrorType.CLIENT_NAME_IS_EXIST);
        }
    }

    /**
     * 新增网店授权时按照【商户 + 平台 + AppID + SecretKey】做唯一性校验；避免重复授权(适用范围：美团、饿百、京东到家)
     */
    private void isOnlineClientRepeat(String merCode, DsOnlineClientCreateUpdateDTO dto) {
        if (PlatformCodeEnum.MEITUAN.getCode().equals(dto.getPlatformCode())
                || PlatformCodeEnum.E_BAI.getCode().equals(dto.getPlatformCode())
                || PlatformCodeEnum.JD_DAOJIA.getCode().equals(dto.getPlatformCode())) {
            QueryWrapper<DsOnlineClient> clientWrapper = new QueryWrapper<>();
            clientWrapper.lambda()
                    .eq(DsOnlineClient::getMerCode, merCode)
                    .eq(DsOnlineClient::getPlatformCode, dto.getPlatformCode())
                    .eq(DsOnlineClient::getAppid, dto.getAppid())
                    .eq(DsOnlineClient::getAppSecret, dto.getAppSecret());
            List<DsOnlineClient> clientList = dsOnlineClientRepo.selectList(clientWrapper);
            if (!CollectionUtils.isEmpty(clientList)) {
                String msg = "此商户 %s平台的【AppID + SecretKey】已授权，请勿重复授权";
                String message = String.format(msg,
                        dto.getPlatformName()
                );
                throw ExceptionUtil.getWarnException(DsErrorType.ONLINE_CLIENT_IS_REPEAT.getCode(), message);
            }
        }
    }

    private void isOnlineClientRepeatWithSupplier(String merCode, DsOnlineClientCreateUpdateDTO dto) {
        QueryWrapper<DsOnlineClient> queryWrapper = new QueryWrapper<>();
        queryWrapper.lambda().eq(DsOnlineClient::getPlatformCode, dto.getPlatformCode())
                .eq(DsOnlineClient::getAppid, dto.getAppid())
                .eq(DsOnlineClient::getAccessType, dto.getAccessType());
        List<DsOnlineClient> clientList = dsOnlineClientRepo.selectList(queryWrapper);
        if (!CollectionUtils.isEmpty(clientList)) {
            DsMerchantGroupInfo dsMerchantGroupInfo = merchantGroupInfoService.querySessionkeyByMerCode(clientList.get(0).getMerCode());
            String msg = "该美团商家账号已授权至OMS（%s + %s），不允许再次授权，请确认美团商家账号后重新授权！";
            String message = String.format(msg, dsMerchantGroupInfo.getMerName(), clientList.get(0).getOnlineClientName());
            throw ExceptionUtil.getWarnException(DsErrorType.ONLINE_CLIENT_IS_REPEAT.getCode(), message);
        }
    }

    @Override
    public void updateOnlineShop(String merCode, DsOnlineClientCreateUpdateDTO dto) {
        QueryWrapper<DsOnlineClient> queryWrapper = new QueryWrapper<>();
        queryWrapper.lambda().eq(DsOnlineClient::getId, dto.getId());
        DsOnlineClient dsOnlineClient = dsOnlineClientRepo.selectOne(queryWrapper);
        if (dsOnlineClient == null) {
            throw WarnException.builder().code(DsErrorType.ONLINE_CLIENT_IS_NOT_EXIST.getCode())
                    .message(DsErrorType.ONLINE_CLIENT_IS_NOT_EXIST.getMsg()).build();
        }
        if (!dsOnlineClient.getAccessType().equals(dto.getAccessType())) {
            throw WarnException.builder().code(DsErrorType.INCORRECT_CLIENT_ACCESSTYPE.getCode())
                    .message(DsErrorType.INCORRECT_CLIENT_ACCESSTYPE.getMsg()).build();
        }
        if (!dto.getOnlineClientName().equals(dsOnlineClient.getOnlineClientName())) {
            // 验证网店名称是否存在
            isOnlineClientExist(merCode, dto);
        }
        // 查询商户名称

        ResponseBase<MerchantResDTO> merchantRes = middleBaseInfoClient.getMerchantByCode(merCode);
        MerchantResDTO merchant = merchantRes.getData();
        String merName = null;
        if (merchant != null) {
            merName = merchant.getMerName();
        }
        // 只有微商城的商家id是填的merCode值
        if (!StringUtils.isEmpty(dto.getSellerId()) && (PlatformCodeEnum.YD_JIA.getCode()).equals(dto.getPlatformCode())) {
            ResponseBase<MerchantResDTO> merchantInfo = middleBaseInfoClient.getMerchantByCode(dto.getSellerId());
            MerchantResDTO merchantDto = merchantInfo.getData();
            if (merchantDto == null) {
                throw ExceptionUtil.getWarnException(DsErrorType.MER_NOT_EXISTS.getCode(),
                        "在平台上未找到网店ID‘" + dto.getSellerId() + "’,请核对后重新输入！");
            }
        }
        ResponseNet<String> base=null;
        // 调用接口修改授权信息
        UpdateAuthReqDto updateAuthReqDto = new UpdateAuthReqDto(merchant, dto);
        base = thirdPartyHttpAdapter.updateAuthO2O(updateAuthReqDto);
        if (!base.checkSuccess() || base.getData() == null) {
            ExLogger.logger()
                    .field("clientUpdateAuthError")
                    .field(dto.getOnlineClientCode())
                    .error("clientUpdateAuthError：{}-{}", base.getCode(), base.getMsg());
            throw ExceptionUtil.getWarnException(String.valueOf(base.getCode()), base.getMsg());
        }
        String sessionKey = base.getData();
        DsMerchantGroupInfo dsMerchantGroupInfo = new DsMerchantGroupInfo();
        dsMerchantGroupInfo.setMerCode(merCode);
        dsMerchantGroupInfo.setMerName(merName);
        dsMerchantGroupInfo.setSessionKey(sessionKey);
        // 保存商户分组信息
        merchantGroupInfoService.saveMerchantGroupInfo(merCode, merName, sessionKey);
        // 修改线上网店信息
        this.lambdaUpdate().eq(DsOnlineClient::getId, dto.getId())
                .set(DsOnlineClient::getOnlineClientName, dto.getOnlineClientName())
                .set(DsOnlineClient::getAppid, dto.getAppid())
                .set(DsOnlineClient::getAppSecret, dto.getAppSecret())
                .set(DsOnlineClient::getSellerId, dto.getSellerId())
                .set(DsOnlineClient::getAccessToken, dto.getAccessToken())
                .set(DsOnlineClient::getRefreshToken, dto.getRefreshToken())
                .set(DsOnlineClient::getServerUrl, dto.getServerUrl())
                .update();

        // 同步门店信息
        if (needSyncStore(dto.getPlatformCode())) {
            dto.setMerCode(merCode);
            dsOnlineStoreService.synStore(dto, dsOnlineClient.getOnlineClientCode(), sessionKey);
            // 每个商户对应的每个线上门店都有对应几种配送方式（员工配送,到店自提，快递配送）
            onlineStoreDeliveryService.createDefaultDelivery(merCode, dto.getPlatformCode());
        }
    }

    @Override
    public void updateOnlineShopWithSupplier(String merCode, DsOnlineClientCreateUpdateDTO dto) {
        QueryWrapper<DsOnlineClient> queryWrapper = new QueryWrapper<>();
        queryWrapper.lambda().eq(DsOnlineClient::getId, dto.getId());
        DsOnlineClient dsOnlineClient = dsOnlineClientRepo.selectOne(queryWrapper);
        if (dsOnlineClient == null) {
            throw WarnException.builder().code(DsErrorType.ONLINE_CLIENT_IS_NOT_EXIST.getCode())
                    .message(DsErrorType.ONLINE_CLIENT_IS_NOT_EXIST.getMsg()).build();
        }
        if (!dsOnlineClient.getAccessType().equals(dto.getAccessType())) {
            throw WarnException.builder().code(DsErrorType.INCORRECT_CLIENT_ACCESSTYPE.getCode())
                    .message(DsErrorType.INCORRECT_CLIENT_ACCESSTYPE.getMsg()).build();
        }
        if (!dto.getOnlineClientName().equals(dsOnlineClient.getOnlineClientName())) {
            // 验证网店名称是否存在
            isOnlineClientExist(merCode, dto);
        }
        if (!dto.getAppid().equals(dsOnlineClient.getAppid())) {
            //校验商家账号
            isOnlineClientRepeatWithSupplier(merCode, dto);
        }
        // 查询商户名称
        ResponseBase<MerchantResDTO> merchantRes = middleBaseInfoClient.getMerchantByCode(merCode);
        MerchantResDTO merchant = merchantRes.getData();
        String merName = null;
        if (merchant != null) {
            merName = merchant.getMerName();
        }
        // 保存商户分组信息
        merchantGroupInfoService.saveMerchantGroupInfo(merCode, merName, null);

        boolean result = this.lambdaUpdate().eq(DsOnlineClient::getId, dsOnlineClient.getId())
                .set(DsOnlineClient::getOnlineClientName, dto.getOnlineClientName())
                .set(DsOnlineClient::getAppid, dto.getAppid())
                .update();
        //同步门店
        dsOnlineStoreService.synStore(dto, dto.getOnlineClientCode(), null);
//        if(!result){
//            return;
//        }
//        dsOnlineClient.setOnlineClientName(dto.getOnlineClientName());
//        dsOnlineClient.setAppid(dto.getAppid());
        //网店更新成功恢复可能存在的供应商门店
//        supplierStoreDealService.updateSupplierStoreMessage(DsConstants.INTEGER_TWO,dsOnlineClient);
    }

    private boolean needSyncStore(String platformCode) {
        return !PlatformCodeEnum.JD_DAOJIA.getCode().equals(platformCode)
                && !PlatformCodeEnum.PING_AN_CENTRAL.getCode().equals(platformCode)
                && !PlatformCodeEnum.PA_CITY.getCode().equals(platformCode)
                && !PlatformCodeEnum.PA_COMMON_O2O.getCode().equals(platformCode)
                && !PlatformCodeEnum.TY_O2O.getCode().equals(platformCode)
                && !PlatformCodeEnum.JD_HEALTH.getCode().equals(platformCode);
    }

    @Override
    public List<DsGetPlatformClientStoreResDTO> getPlatformAndClientAndStore(String merCode, String platformCode) {
        List<DsGetPlatformClientStoreResDTO> platformClientStoreList = new ArrayList<>();
        QueryWrapper<DsOnlineClient> queryWrapper = new QueryWrapper<>();
        queryWrapper.lambda().eq(DsOnlineClient::getMerCode, merCode)
                .eq(DsOnlineClient::getPlatformCode, platformCode)
                .orderByDesc(DsOnlineClient::getCreateTime);
        List<DsOnlineClient> onlineClientList = dsOnlineClientRepo.selectList(queryWrapper);
        if (onlineClientList == null || onlineClientList.isEmpty()) {
            throw ExceptionUtil.getWarnException(DsErrorType.ONLINE_CLIENT_IS_NOT_EXIST);
        }

        QueryWrapper<DsOnlineStoreConfig> storeConfigWrapper = new QueryWrapper<>();
        storeConfigWrapper.lambda().eq(DsOnlineStoreConfig::getMerCode, merCode);
        List<DsOnlineStoreConfig> storeConfigs = onlineStoreConfigRepo.selectList(storeConfigWrapper);
        Map<Long, DsOnlineStoreConfig> storeConfigMap = storeConfigs.stream().
                collect(Collectors.toMap(DsOnlineStoreConfig::getOnlineStoreId, item -> item, (v1, v2) -> v1));

        /** 查询该网店对应的线上门店 **/
        for (DsOnlineClient onlineClient : onlineClientList) {
            DsGetPlatformClientStoreResDTO platformClientStore = new DsGetPlatformClientStoreResDTO();
            QueryWrapper<DsOnlineStore> storeWrapper = new QueryWrapper<>();
            storeWrapper.lambda().eq(DsOnlineStore::getMerCode, merCode)
                    .eq(DsOnlineStore::getPlatformCode, platformCode)
                    .eq(DsOnlineStore::getOnlineClientCode, onlineClient.getOnlineClientCode())
                    .nested(ew -> ew.notIn(DsOnlineStore::getStatus, DsConstants.INTEGER_TWO)
                            .or(wrapper -> wrapper.isNull(DsOnlineStore::getStatus)))
                    .orderByDesc(DsOnlineStore::getCreateTime);
            List<DsOnlineStore> storeList = dsOnlineStoreRepo.selectList(storeWrapper);
            if (storeList != null && !storeList.isEmpty()) {
                for (DsOnlineStore config : storeList) {
                    DsOnlineStoreConfig storeConfig = storeConfigMap.get(config.getId());
                    if (null != storeConfig) {
                        config.setSyncStock(storeConfig.getSyncStock());
                        config.setSyncStockRatio(storeConfig.getSyncStockRatio());
                        config.setSyncPrice(storeConfig.getSyncPrice());
                    }
                }
                platformClientStore.setOnlineStoreList(storeList);
            } else {
                platformClientStore.setOnlineStoreList(new ArrayList<>());
            }
            platformClientStore.setPlatformName(onlineClient.getPlatformName());
            platformClientStore.setMerCode(onlineClient.getMerCode());
            platformClientStore.setPlatformCode(onlineClient.getPlatformCode());
            platformClientStore.setPlatformName(onlineClient.getPlatformName());
            platformClientStore.setOnlineClientCode(onlineClient.getOnlineClientCode());
            platformClientStore.setOnlineClientName(onlineClient.getOnlineClientName());
            platformClientStore.setAppid(onlineClient.getAppid());
            platformClientStore.setAppSecret(onlineClient.getAppSecret());
            platformClientStore.setAuthDeadline(onlineClient.getAuthDeadline());
            platformClientStore.setAuthTime(onlineClient.getAuthTime());
            platformClientStore.setSellerId(onlineClient.getSellerId());
            platformClientStore.setAccessToken(onlineClient.getAccessToken());
            platformClientStore.setRefreshToken(onlineClient.getRefreshToken());
            platformClientStore.setAccessType(onlineClient.getAccessType());
            platformClientStoreList.add(platformClientStore);
        }
        return platformClientStoreList;
    }

    @Override
    public List<DsGetPlatformClientStoreResDTO> getPlatformAndClientAndAuthorityStore(String userId, String merCode, String platformCode, boolean hasConfig, String serviceMode) {
        return getPlatformClientStoreCommon(userId, merCode, platformCode, hasConfig, serviceMode, null);
    }

    @Override
    public List<DsGetPlatformClientStoreResDTO> getPlatformClientAuthorityStoreByOrg(String userId, OnlineStoreByOrDto reqDto) {
        List<String> orCodeList = new ArrayList<>();
        if (!CollectionUtils.isEmpty(reqDto.getOrgIdList())) {
            //商户平台获取集合信息
            BaseInfoOrByOrIdDto dto = new BaseInfoOrByOrIdDto();
            dto.setMerCode(reqDto.getMerCode());
            dto.setStatus(1);
            dto.setOnlineStoreStatus(1);
//        dto.setOnlineStatus(1);
            dto.setOnlyStore(0);
            dto.setCurrentPage(1);
            dto.setPageSize(20000);
            dto.setOrgIdList(reqDto.getOrgIdList());
            ResponseBase<PageDTO<StoreResDTO>> base = middleBaseInfoClient.queryOrgSubStoreList(dto);
            if (base.getData().getTotalCount() == 0) {
                return new ArrayList<>();
            }
            List<StoreResDTO> storeResDTOList = base.getData().getData();
            storeResDTOList.forEach(item -> orCodeList.add(item.getStCode()));
        }
        return getPlatformClientStoreCommon(userId, reqDto.getMerCode(), reqDto.getPlatformCode(), reqDto.getConfig() == null ? false : reqDto.getConfig(), null, orCodeList);
    }

    public List<DsGetPlatformClientStoreResDTO> getPlatformClientStoreCommon(String userId, String merCode, String platformCode, boolean hasConfig, String serviceMode, List<String> orCodeList) {
        /** 1.根据平台查询网店信息 */
        List<DsGetPlatformClientStoreResDTO> platformClientStoreList = new ArrayList<>();
        QueryWrapper<DsOnlineClient> queryWrapper = new QueryWrapper<>();
        LambdaQueryWrapper<DsOnlineClient> lambda = queryWrapper.lambda();
        lambda.eq(DsOnlineClient::getMerCode, merCode)
                .eq(DsOnlineClient::getPlatformCode, platformCode);
        if (!StringUtils.isEmpty(serviceMode)) {
            lambda.eq(DsOnlineClient::getServiceMode, serviceMode);
        }
        List<DsOnlineClient> onlineClientList = dsOnlineClientRepo.selectList(queryWrapper);
        if (onlineClientList == null || onlineClientList.isEmpty()) {
            ExceptionUtil.getWarnException(DsErrorType.ONLINE_CLIENT_IS_NOT_EXIST);
        }

        /** 2.根据用户查询有权限的线下门店 */
        MyStoreDTO dto = new MyStoreDTO();
        dto.setMerCode(merCode);
        dto.setStatus(1);
        dto.setOnlineStoreStatus(1);
        dto.setUserId(userId);
        dto.setCurrentPage(1);
        dto.setPageSize(20000);
        ResponseBase<PageDTO<StoreResDTO>> base = baseInfoCache.queryStoreByUser(dto);

        //【ID1022984】【优化】OMS机构取值范围优化
        List<StoreResDTO> storeResDTOList = baseInfoCache.storeDataAppendStoreHouseData(dto, base);

        if (CollectionUtils.isEmpty(storeResDTOList)) {
            return null;
        }
        Map<String, DsOnlineStore> authorityStoreMap = new HashMap<>();
        if (!CollectionUtils.isEmpty(storeResDTOList)) {
            // 线下门店编码集合
            Set<String> stCodes = null;
            if (!CollectionUtils.isEmpty(orCodeList)) {
                //过滤筛选的门店
                stCodes = storeResDTOList.stream().map(StoreResDTO::getStCode).filter(item -> orCodeList.contains(item)).collect(Collectors.toSet());
            } else {
                stCodes = storeResDTOList.stream().map(StoreResDTO::getStCode).collect(Collectors.toSet());
            }

            /** 3.根据有权限的线下门店查询线上门店 */
            QueryWrapper<DsOnlineStore> storeQueryWrapper = new QueryWrapper<>();
            storeQueryWrapper.lambda()
                    .eq(DsOnlineStore::getMerCode, merCode)
                    .eq(DsOnlineStore::getPlatformCode, platformCode)
                    .in(DsOnlineStore::getOrganizationCode, stCodes)
                    .nested(ew -> ew.notIn(DsOnlineStore::getStatus, DsConstants.INTEGER_TWO)
                            .or(wrapper -> wrapper.isNull(DsOnlineStore::getStatus)));
            List<DsOnlineStore> authorityOnlineStoreList = dsOnlineStoreRepo.selectList(storeQueryWrapper);
            // 4. 将该用户有权限的线上门店封装成Map
            authorityStoreMap = authorityOnlineStoreList.stream().collect(Collectors.toMap(DsOnlineStore::getOnlineStoreCode, store -> store, (a, b) -> a));
        }

        /** 5.查询该网店对应的所有线上门店 **/
        Map<Long, DsOnlineStore> map = new HashMap<>();
        for (DsOnlineClient onlineClient : onlineClientList) {
            List<DsOnlineStore> lastOnlineStoreList = new ArrayList<>();
            DsGetPlatformClientStoreResDTO platformClientStore = new DsGetPlatformClientStoreResDTO();
            QueryWrapper<DsOnlineStore> storeWrapper = new QueryWrapper<>();
            storeWrapper.lambda().eq(DsOnlineStore::getMerCode, merCode)
                    .eq(DsOnlineStore::getPlatformCode, platformCode)
                    .eq(DsOnlineStore::getOnlineClientCode, onlineClient.getOnlineClientCode())
                    .nested(ew -> ew.notIn(DsOnlineStore::getStatus, DsConstants.INTEGER_TWO)
                            .or(wrapper -> wrapper.isNull(DsOnlineStore::getStatus)));
            List<DsOnlineStore> storeList = dsOnlineStoreRepo.selectList(storeWrapper);
            if (storeList != null && !storeList.isEmpty()) {
                for (DsOnlineStore dsOnlineStore : storeList) {
                    /** 6.如果有权限的线上门店包含查询出来的所有的线上门店，则添加到新的线上门店集合 */
                    if (!authorityStoreMap.isEmpty() && authorityStoreMap.containsKey(dsOnlineStore.getOnlineStoreCode())) {
                        lastOnlineStoreList.add(dsOnlineStore);
                        map.put(dsOnlineStore.getId(), dsOnlineStore);
                    }
                }
            }
            platformClientStore.setOnlineStoreList(lastOnlineStoreList);
            platformClientStore.setPlatformName(onlineClient.getPlatformName());
            platformClientStore.setMerCode(onlineClient.getMerCode());
            platformClientStore.setPlatformCode(onlineClient.getPlatformCode());
            platformClientStore.setPlatformName(onlineClient.getPlatformName());
            platformClientStore.setOnlineClientCode(onlineClient.getOnlineClientCode());
            platformClientStore.setOnlineClientName(onlineClient.getOnlineClientName());
            platformClientStore.setAppid(onlineClient.getAppid());
            platformClientStore.setAppSecret(onlineClient.getAppSecret());
            platformClientStore.setAuthDeadline(onlineClient.getAuthDeadline());
            platformClientStore.setAuthTime(onlineClient.getAuthTime());
            platformClientStore.setSellerId(onlineClient.getSellerId());
            platformClientStore.setAccessToken(onlineClient.getAccessToken());
            platformClientStore.setRefreshToken(onlineClient.getRefreshToken());
            platformClientStore.setAccessType(onlineClient.getAccessType());
            platformClientStoreList.add(platformClientStore);
        }
        if (hasConfig && !CollectionUtils.isEmpty(map)) {
            QueryWrapper<DsOnlineStoreConfig> wrapper = new QueryWrapper<>();
            wrapper.lambda().in(DsOnlineStoreConfig::getOnlineStoreId, map.keySet());
            List<DsOnlineStoreConfig> resList = onlineStoreConfigRepo.selectList(wrapper);
            if (!CollectionUtils.isEmpty(resList)) {
                resList.forEach(model -> {
                    DsOnlineStore dsOnlineStore = map.get(model.getOnlineStoreId());
                    if (dsOnlineStore != null) {
                        dsOnlineStore.setSyncPrice(model.getSyncPrice());
                        dsOnlineStore.setSyncStock(model.getSyncStock());
                    }
                });
            }
        }
        return platformClientStoreList;
    }

    public List<DsGetPlatformClientStoreResDTO> getPlatformClientStoreCommonFix(String userId, String merCode, String platformCode, boolean hasConfig, String serviceMode, List<String> orCodeList) {
        /** 1.根据平台查询网店信息 */
        List<DsGetPlatformClientStoreResDTO> platformClientStoreList = new ArrayList<>();
        QueryWrapper<DsOnlineClient> queryWrapper = new QueryWrapper<>();
        LambdaQueryWrapper<DsOnlineClient> lambda = queryWrapper.lambda();
        lambda.eq(DsOnlineClient::getMerCode, merCode)
                .eq(DsOnlineClient::getPlatformCode, platformCode);
        if (!StringUtils.isEmpty(serviceMode)) {
            lambda.eq(DsOnlineClient::getServiceMode, serviceMode);
        }
        List<DsOnlineClient> onlineClientList = dsOnlineClientRepo.selectList(queryWrapper);
        if (onlineClientList == null || onlineClientList.isEmpty()) {
            ExceptionUtil.getWarnException(DsErrorType.ONLINE_CLIENT_IS_NOT_EXIST);
        }

        /** 2.根据用户查询有权限的线下门店 */
        MyStoreDTO dto = new MyStoreDTO();
        dto.setMerCode(merCode);
        dto.setStatus(1);
        dto.setOnlineStoreStatus(1);
        dto.setUserId(userId);
        dto.setCurrentPage(1);
        dto.setPageSize(20000);
        ResponseBase<PageDTO<StoreResDTO>> base = baseInfoCache.queryStoreByUser(dto);

        //【ID1022984】【优化】OMS机构取值范围优化
        List<StoreResDTO> storeResDTOList = baseInfoCache.storeDataAppendStoreHouseData(dto, base);

        if (CollectionUtils.isEmpty(storeResDTOList)) {
            return null;
        }
        Map<String, DsOnlineStore> authorityStoreMap = new HashMap<>();
        if (!CollectionUtils.isEmpty(storeResDTOList)) {
            // 线下门店编码集合
            Set<String> stCodes = null;
            if (!CollectionUtils.isEmpty(orCodeList)) {
                //过滤筛选的门店
                stCodes = storeResDTOList.stream().map(StoreResDTO::getStCode).filter(item -> orCodeList.contains(item)).collect(Collectors.toSet());
            } else {
                stCodes = storeResDTOList.stream().map(StoreResDTO::getStCode).collect(Collectors.toSet());
            }

            /** 3.根据有权限的线下门店查询线上门店 */
            QueryWrapper<DsOnlineStore> storeQueryWrapper = new QueryWrapper<>();
            storeQueryWrapper.lambda()
                    .eq(DsOnlineStore::getMerCode, merCode)
                    .eq(DsOnlineStore::getPlatformCode, platformCode)
                    .in(DsOnlineStore::getOrganizationCode, stCodes)
                    .nested(ew -> ew.notIn(DsOnlineStore::getStatus, DsConstants.INTEGER_TWO)
                            .or(wrapper -> wrapper.isNull(DsOnlineStore::getStatus)));
            List<DsOnlineStore> authorityOnlineStoreList = dsOnlineStoreRepo.selectList(storeQueryWrapper);
            // 4. 将该用户有权限的线上门店封装成Map
            authorityStoreMap = authorityOnlineStoreList.stream().collect(Collectors.toMap(DsOnlineStore::getOnlineStoreCode, store -> store, (a, b) -> a));
        } else {
            authorityStoreMap = new HashMap<>();
        }

        /** 5.查询该网店对应的所有线上门店 **/
        Map<Long, DsOnlineStore> map = new HashMap<>();
        QueryWrapper<DsOnlineStore> storeWrapper = new QueryWrapper<>();
        storeWrapper.lambda().eq(DsOnlineStore::getMerCode, merCode)
                .eq(DsOnlineStore::getPlatformCode, platformCode)
                .in(DsOnlineStore::getOnlineClientCode, onlineClientList.stream().map(DsOnlineClient::getOnlineClientCode).collect(Collectors.toList()))
                .nested(ew -> ew.notIn(DsOnlineStore::getStatus, DsConstants.INTEGER_TWO)
                        .or(wrapper -> wrapper.isNull(DsOnlineStore::getStatus)));
        Map<String, List<DsOnlineStore>> storeListMap = dsOnlineStoreRepo.selectList(storeWrapper).stream().collect(Collectors.groupingBy(DsOnlineStore::getOnlineStoreCode));
        for (DsOnlineClient onlineClient : onlineClientList) {
            List<DsOnlineStore> lastOnlineStoreList = new ArrayList<>();
            DsGetPlatformClientStoreResDTO platformClientStore = new DsGetPlatformClientStoreResDTO();
            List<DsOnlineStore> dsOnlineStores = storeListMap.get(onlineClient.getOnlineClientCode());
            if (!CollectionUtils.isEmpty(dsOnlineStores)) {
                for (DsOnlineStore dsOnlineStore : dsOnlineStores) {
                    if (authorityStoreMap.containsKey(dsOnlineStore.getOnlineStoreCode())) {
                        lastOnlineStoreList.add(dsOnlineStore);
                        map.put(dsOnlineStore.getId(), dsOnlineStore);
                    }
                }
            }
            platformClientStore.setOnlineStoreList(lastOnlineStoreList);
            platformClientStore.setMerCode(onlineClient.getMerCode());
            platformClientStore.setPlatformCode(onlineClient.getPlatformCode());
            platformClientStore.setPlatformName(onlineClient.getPlatformName());
            platformClientStore.setOnlineClientCode(onlineClient.getOnlineClientCode());
            platformClientStore.setOnlineClientName(onlineClient.getOnlineClientName());
            platformClientStore.setAppid(onlineClient.getAppid());
            platformClientStore.setAppSecret(onlineClient.getAppSecret());
            platformClientStore.setAuthDeadline(onlineClient.getAuthDeadline());
            platformClientStore.setAuthTime(onlineClient.getAuthTime());
            platformClientStore.setSellerId(onlineClient.getSellerId());
            platformClientStore.setAccessToken(onlineClient.getAccessToken());
            platformClientStore.setRefreshToken(onlineClient.getRefreshToken());
            platformClientStore.setAccessType(onlineClient.getAccessType());
            platformClientStoreList.add(platformClientStore);
        }

        if (hasConfig && !CollectionUtils.isEmpty(map)) {
            QueryWrapper<DsOnlineStoreConfig> wrapper = new QueryWrapper<>();
            wrapper.lambda().in(DsOnlineStoreConfig::getOnlineStoreId, map.keySet());
            List<DsOnlineStoreConfig> resList = onlineStoreConfigRepo.selectList(wrapper);
            if (!CollectionUtils.isEmpty(resList)) {
                resList.forEach(model -> {
                    DsOnlineStore dsOnlineStore = map.get(model.getOnlineStoreId());
                    if (dsOnlineStore != null) {
                        dsOnlineStore.setSyncPrice(model.getSyncPrice());
                        dsOnlineStore.setSyncStock(model.getSyncStock());
                    }
                });
            }
        }

        return platformClientStoreList;
    }

    @Override
    public boolean deleteInfoByMerCode(String merCode) {
        // 1.删除商户分组信息
        QueryWrapper<DsMerchantGroupInfo> merchantGroupWrapper = new QueryWrapper<>();
        merchantGroupWrapper.lambda().eq(DsMerchantGroupInfo::getMerCode, merCode);
        merchantGroupInfoRepo.delete(merchantGroupWrapper);

        // 2.删除o2o平台网店信息
        QueryWrapper<DsOnlineClient> onlineClientWrapper = new QueryWrapper<>();
        onlineClientWrapper.lambda().eq(DsOnlineClient::getMerCode, merCode);
        dsOnlineClientRepo.delete(onlineClientWrapper);

        // 3.删除o2o平台门店信息
        QueryWrapper<DsOnlineStore> onlineStoreWrapper = new QueryWrapper<>();
        onlineStoreWrapper.lambda().eq(DsOnlineStore::getMerCode, merCode);
        dsOnlineStoreRepo.delete(onlineStoreWrapper);

        // 4.删除o2o平台配送网店信息
        QueryWrapper<DsDeliveryClient> deliveryClientWrapper = new QueryWrapper<>();
        deliveryClientWrapper.lambda().eq(DsDeliveryClient::getMerCode, merCode);
        deliveryClientRepo.delete(deliveryClientWrapper);

        // 5.删除o2o平台配送门店信息
        QueryWrapper<DsDeliveryStore> deliveryStoreWrapper = new QueryWrapper<>();
        deliveryStoreWrapper.lambda().eq(DsDeliveryStore::getMerCode, merCode);
        deliveryStoreRepo.delete(deliveryStoreWrapper);

        // 6.删除o2o线上门店与配送门店关联信息
        QueryWrapper<DsOnlineStoreDelivery> onlineStoreDeliveryWrapper = new QueryWrapper<>();
        onlineStoreDeliveryWrapper.lambda().eq(DsOnlineStoreDelivery::getMerCode, merCode);
        onlineStoreDeliveryRepo.delete(onlineStoreDeliveryWrapper);

        /** 7.删除线上门店配置 **/
        QueryWrapper<DsOnlineStoreConfig> onlineStoreConfigWrapper = new QueryWrapper<>();
        onlineStoreConfigWrapper.lambda().eq(DsOnlineStoreConfig::getMerCode, merCode);
        onlineStoreConfigRepo.delete(onlineStoreConfigWrapper);

        /** 8.删除线上门店订单配置 **/
        QueryWrapper<DsStoreOrderConfig> storeOrderConfigWrapper = new QueryWrapper<>();
        storeOrderConfigWrapper.lambda().eq(DsStoreOrderConfig::getMerCode, merCode);
        storeOrderConfigRepo.delete(storeOrderConfigWrapper);

        /** 9.删除声音设置配置 **/
        QueryWrapper<DsStoreSoundConfig> storeSoundConfigWrapper = new QueryWrapper<>();
        storeSoundConfigWrapper.lambda().eq(DsStoreSoundConfig::getMerCode, merCode);
        storeSoundConfigRepo.delete(storeSoundConfigWrapper);
        return true;
    }

    @Override
    public PlatformResDTO getPlatformByMerCode(String merCode, String userId, String serviceModel) {
        List<String> platformCodes = new ArrayList<>();
        List<MerCodeToPlatform> platformList = new ArrayList<>();
        PlatformResDTO platformResDTO = new PlatformResDTO();
        if (userId == null) {
//            QueryWrapper<DsOnlineClient> clientWrapper = new QueryWrapper<>();
//            clientWrapper.select("distinct platform_code");
//            clientWrapper.lambda().eq(DsOnlineClient::getMerCode,merCode);
//            if (!StringUtils.isEmpty(serviceModel)) {
//                clientWrapper.lambda().eq(DsOnlineClient::getServiceMode, serviceModel);
//            }
//            List<DsOnlineClient> platformCodeList = dsOnlineClientRepo.selectList(clientWrapper);
            List<DsOnlineClient> platformCodeList = dsOnlineClientRepo.getDsOnlineClientList(merCode, serviceModel);
            if (platformCodeList != null && !platformCodeList.isEmpty()) {
                for (DsOnlineClient onlineClient : platformCodeList) {
                    String platformCode = onlineClient.getPlatformCode();
                    platformCodes.add(platformCode);
                }
            }
            platformCodes = platformCodes.stream().distinct().collect(Collectors.toList());
            platformResDTO.setPlatformCodes(platformCodes);

            // 查询商户已开通的平台
            QueryWrapper<DsOnlineClient> platformWrapper = new QueryWrapper<>();
            platformWrapper.select("distinct mer_code,platform_code,platform_name,service_mode");
            platformWrapper.lambda().eq(DsOnlineClient::getMerCode, merCode);
            List<DsOnlineClient> platformsList = dsOnlineClientRepo.selectList(platformWrapper);
            if (platformsList != null && !platformsList.isEmpty()) {
                BeanUtils.copyProperties(platformsList, platformList);
                for (DsOnlineClient dsOnlineClient : platformsList) {
                    //如果serviceModel是O2O，记录是B2C并且serviceMask是2或者99，需要返回
                    if (DsConstants.O2O.equals(serviceModel)) {
                        if (DsConstants.B2C.equals(dsOnlineClient.getServiceMode()) &&
                                !(PlatformCodeEnum.INHERIT_TM_TCG.getCode().equals(dsOnlineClient.getPlatformCode()))) {
                            continue;
                        }
                    }
                    //如果serviceModel是B2C，记录不是B2C，不需要返回
                    if (DsConstants.B2C.equals(serviceModel) && !serviceModel.equals(dsOnlineClient.getServiceMode())) {
                        continue;
                    }
                    MerCodeToPlatform merCodeToPlatform = new MerCodeToPlatform();
                    BeanUtils.copyProperties(dsOnlineClient, merCodeToPlatform);
                    platformList.add(merCodeToPlatform);
                }
            }
            platformList = platformList.stream().distinct().collect(Collectors.toList());
            platformResDTO.setPlatformList(platformList);
        } else {
            List<String> platformCodeList = verifyService.verifyPlatformCodeAndGet(merCode, userId, Collections.singletonList(DsConstants.Global));
            if (!CollectionUtils.isEmpty(platformCodeList) && DsConstants.Global.equals(platformCodeList.get(0))) {
                platformResDTO.setPlatformCodes(Collections.emptyList());
                platformResDTO.setPlatformList(Collections.emptyList());
                return platformResDTO;
            }

            if (CollectionUtils.isEmpty(platformCodeList)) {
                return getPlatformByMerCode(merCode, null, serviceModel);
            }

            for (int i = 0; i < platformCodeList.size(); i++) {
                if (("O2O".equals(serviceModel) && PlatformCodeEnum.getByCode(platformCodeList.get(i)) == null) ||
                        ("B2C".equals(serviceModel) && PlatformCodeEnum.getByCode(platformCodeList.get(i)) != null)) {
                    continue;
                }
                platformCodes.add(platformCodeList.get(i));
                MerCodeToPlatform merCodeToPlatform = new MerCodeToPlatform();
                merCodeToPlatform.setMerCode(merCode);
                if (null != PlatformCodeEnum.getByCode(platformCodeList.get(i))) {
                    merCodeToPlatform.setServiceMode("O2O");
                } else {
                    merCodeToPlatform.setServiceMode("B2C");
                }
                merCodeToPlatform.setPlatformCode(platformCodeList.get(i));
                merCodeToPlatform.setPlatformName(PlatformCodeEnum.getByCode(platformCodeList.get(i)).getType());
                platformList.add(merCodeToPlatform);
            }
            platformResDTO.setPlatformCodes(platformCodes);
            platformResDTO.setPlatformList(platformList);
        }
        return platformResDTO;
    }

    private String getB2CPlatformName(String merCode, String platformCode) {
        return baseMapper.getPlatFormCodeName(merCode, platformCode);
    }

    @Override
    public DsOnlineClient queryOnlineClientById(Long onlineClientId) {
        QueryWrapper<DsOnlineClient> clientWrapper = new QueryWrapper<>();
        clientWrapper.lambda().eq(DsOnlineClient::getId, onlineClientId);
        return dsOnlineClientRepo.selectOne(clientWrapper);
    }

    /**
     * 查询服务商模式授权网店信息
     *
     * @param merCode
     * @param platformCode
     * @param clientCode
     * @return
     */
    @Override
    public DsOnlineClient queryOnlienClientByUniq(String merCode, String platformCode, String clientCode) {
        QueryWrapper<DsOnlineClient> queryWrapper = new QueryWrapper<>();
        queryWrapper.lambda().eq(DsOnlineClient::getMerCode, merCode)
                .eq(DsOnlineClient::getPlatformCode, platformCode)
                .eq(DsOnlineClient::getOnlineClientCode, clientCode);
        return dsOnlineClientRepo.selectOne(queryWrapper);
    }

    @Override
    public DsOnlineClient querySupplierClient(String merCode, String platformCode, String clientCode) {
        QueryWrapper<DsOnlineClient> queryWrapper = new QueryWrapper<>();
        queryWrapper.lambda().eq(DsOnlineClient::getMerCode, merCode)
                .eq(DsOnlineClient::getPlatformCode, platformCode)
                .eq(!StringUtils.isEmpty(clientCode), DsOnlineClient::getOnlineClientCode, clientCode)
                .eq(DsOnlineClient::getAccessType, DsConstants.INTEGER_TWO);
        return dsOnlineClientRepo.selectOne(queryWrapper);
    }

    /**
     * 查询服务商门店信息-商品专用
     *
     * @param platformCode
     * @param storeCode
     * @return
     */
    @Override
    public DsOnlineStore querySupplierStore(String platformCode, String storeCode) {
        DsOnlineStore dsOnlineStore = dsOnlineClientRepo.selectSupplierStore(platformCode, storeCode);
        if (dsOnlineStore == null) {
            throw ExceptionUtil.getWarnException(DsErrorType.COMMENT_CLIENT_STORE_NOT_EXIST);
        }
        return dsOnlineStore;
    }

    @Override
    public List<String> getAuthorityPlatByMerCode(String merCode, String userId, String serviceMode) {
        String key = RedisKeyUtil.getAuthorityPlatKey(merCode);
        String hashKey = RedisKeyUtil.getAuthorityPlatHashKey(merCode, userId, serviceMode);
        Object value = RedisHashUtil.hashGet(key, hashKey);
        if (value != null && !StringUtils.isEmpty(value)) {
            return JSON.parseArray((String) value, String.class);
        }
        PlatformResDTO platformResDTO = getPlatformByMerCode(merCode, userId, serviceMode);
        if (platformResDTO == null) {
            return Collections.emptyList();
        }
        List<String> platformCodeList = platformResDTO.getPlatformCodes();
        if (CollectionUtils.isEmpty(platformCodeList)) {
            return platformCodeList;
        }

        RedisHashUtil.hashPut(key, hashKey, JSON.toJSONString(platformCodeList), DsConstants.INTEGER_FOUR, TimeUnit.HOURS);

        return platformCodeList;
    }

    @Override
    public DsOnlineClient getByOnlineClientOutCode(DsOnlineClientReqDTO dto) {
        QueryWrapper<DsOnlineClient> queryWrapper = new QueryWrapper<>();
        queryWrapper.lambda().eq(DsOnlineClient::getMerCode, dto.getMerCode())
                .eq(DsOnlineClient::getPlatformCode, dto.getPlatformCode())
                .eq(!StringUtils.isEmpty(dto.getOnlineClientOutCode()), DsOnlineClient::getOnlineClientOutCode, dto.getOnlineClientOutCode())
                .eq(!StringUtils.isEmpty(dto.getOnlineClientCode()), DsOnlineClient::getOnlineClientCode, dto.getOnlineClientCode())
                .eq(DsOnlineClient::getAccessType, DsConstants.INTEGER_ONE);
        DsOnlineClient dsOnlineClient = dsOnlineClientRepo.selectOne(queryWrapper);
        if (dsOnlineClient != null && StrUtil.isNotBlank(dto.getOnlineStoreCode())) {
            List<DsOnlineStore> dsOnlineStoreList = dsOnlineStoreRepo.selectList(Wrappers.<DsOnlineStore>lambdaQuery()
                .eq(DsOnlineStore::getMerCode, dsOnlineClient.getMerCode())
                .eq(DsOnlineStore::getPlatformCode, dsOnlineClient.getPlatformCode())
                .eq(DsOnlineStore::getOnlineStoreCode, dto.getOnlineStoreCode())
                .eq(DsOnlineStore::getOnlineClientCode, dsOnlineClient.getOnlineClientCode())
                .last(" limit 1 "));
            if(CollUtil.isNotEmpty(dsOnlineStoreList)){
                return dsOnlineClient.setPlatformShopId(dsOnlineStoreList.get(0).getPlatformShopId());
            }
        }
        return dsOnlineClient;
    }

    @Override
    public PageDTO<DsOnlineClientResDTO> queryOnlineShopByPlatformCode(String merCode, List<String> platformCodes, List<String> clientCodes) throws IllegalAccessException, InstantiationException {
        int currentPage = 1;
        int pageSize = 20;
        QueryWrapper<DsOnlineClient> queryWrapper = new QueryWrapper<>();
        LambdaQueryWrapper<DsOnlineClient> lambda = queryWrapper.lambda();
        lambda.eq(DsOnlineClient::getMerCode, merCode)
                .ne(DsOnlineClient::getSyncCreate, DsConstants.INTEGER_ONE)
                .in(DsOnlineClient::getPlatformCode, platformCodes)
                .in(CollUtil.isNotEmpty(clientCodes), DsOnlineClient::getOnlineClientCode, clientCodes);
        PageDTO<DsOnlineClientResDTO> pageDTO = new PageDTO<>();
        int count = dsOnlineClientRepo.selectCount(queryWrapper);
        List<DsOnlineClient> list = new ArrayList<>();
        if (count != 0) {
            IPage<DsOnlineClient> page = new Page<>(currentPage, pageSize, count);
            lambda.orderByDesc(DsOnlineClient::getModifyTime);
            list = dsOnlineClientRepo.selectPage(page, queryWrapper).getRecords();
        }
        pageDTO.setData(BeanUtil.copyList(list, DsOnlineClientResDTO.class));
        pageDTO.setTotalCount(count);
        pageDTO.setTotalPage((count + pageSize - 1) / pageSize);
        return pageDTO;
    }

    @Override
    public The3DsStoreResDTO getStoreAccessByStoreCode(String merCode, String platformCode, String onlineStoreCode, String onlineClientCode) {
        String storeAccessKey = RedisKeyUtil.getStoreAccessKey(merCode, platformCode, onlineStoreCode, onlineClientCode);
        String storeCacheData = stringRedisTemplate.opsForValue()
            .get(storeAccessKey);
        if(ObjectUtil.isNotNull(storeCacheData)){
            return JSON.parseObject(storeCacheData, The3DsStoreResDTO.class);
        }
        List<The3DsStoreResDTO> storeAccessList = dsOnlineClientRepo.getStoreAccessByStoreCode(merCode, platformCode, onlineStoreCode, onlineClientCode);
        storeAccessList.stream().findFirst().ifPresent(storeAccess -> stringRedisTemplate.opsForValue().set(storeAccessKey, JSON.toJSONString(storeAccess), 4, TimeUnit.HOURS));
        return storeAccessList.stream().findFirst().orElse(null);
    }

    @Override
    public The3DsOnlineClientResDTO getOnlineStoreByPlatformShopId(String merCode, String platformCode, String platformShopId, String onlineClientCode) {

        return dsOnlineClientRepo.getOnlineStoreByPlatformShopId(merCode, platformCode, platformShopId, onlineClientCode).stream().findFirst().orElse(null);
    }


}
