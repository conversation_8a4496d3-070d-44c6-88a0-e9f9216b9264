package cn.hydee.middle.business.order.storeautosync.infrastructure.mysql.service.impl;

import cn.hydee.middle.business.order.storeautosync.infrastructure.mysql.entity.DsOnlineStoreAuthRecord;
import cn.hydee.middle.business.order.storeautosync.infrastructure.mysql.mapper.DsOnlineStoreAuthRecordMapper;
import cn.hydee.middle.business.order.storeautosync.infrastructure.mysql.service.DsOnlineStoreAuthRecordService;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import org.springframework.stereotype.Service;

@Service
public class DsOnlineStoreAuthRecordServiceImpl extends ServiceImpl<DsOnlineStoreAuthRecordMapper, DsOnlineStoreAuthRecord> implements DsOnlineStoreAuthRecordService {

}
