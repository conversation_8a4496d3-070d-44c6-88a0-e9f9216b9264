package cn.hydee.middle.business.order.account.check.controller;

import cn.hydee.middle.business.order.Enums.DsConstants;
import cn.hydee.middle.business.order.Enums.DsErrorType;
import cn.hydee.middle.business.order.account.check.base.dto.req.*;
import cn.hydee.middle.business.order.account.check.base.dto.rsp.QueryErpRspDto;
import cn.hydee.middle.business.order.account.check.base.dto.rsp.QueryErpStoreRspDto;
import cn.hydee.middle.business.order.account.check.service.IErpAccountCheckService;
import cn.hydee.middle.business.order.dto.req.VerifyReqDto;
import cn.hydee.middle.business.order.dto.rsp.VerifyRspDto;
import cn.hydee.middle.business.order.service.ErpCheckResultServiceImpl;
import cn.hydee.middle.business.order.service.VerifyService;
import cn.hydee.middle.business.order.util.DateUtil;
import cn.hydee.middle.business.order.util.ExceptionUtil;
import cn.hydee.starter.controller.AbstractController;
import cn.hydee.starter.dto.ResponseBase;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.util.CollectionUtils;
import org.springframework.util.StringUtils;
import org.springframework.validation.BindingResult;
import org.springframework.web.bind.annotation.*;

import javax.validation.Valid;
import java.util.Collections;
import java.util.List;
import java.util.concurrent.atomic.AtomicInteger;

/**
 * <AUTHOR>
 * @date 2021年03月31日 18:49
 */
@RestController
@RequestMapping("/${api.version}/ds/erp/account/check")
@Api(tags = "erp对账处理控制器")
public class ErpAccountCheckController extends AbstractController {

    @Autowired
    private VerifyService verifyService;
    @Autowired
    private ErpCheckResultServiceImpl erpCheckResultService;
    @Autowired
    private IErpAccountCheckService erpAccountCheckService;

    private volatile AtomicInteger running = new AtomicInteger(0);

    @ApiOperation(value = "非修复订单，批量重对账", notes = "非修复订单，批量重对账")
    @PostMapping("/reErpAccountCheckByOrderNoList")
    public ResponseBase<String> reErpAccountCheckByOrderNoList(@Valid @RequestBody RepairErpCheckAccountReqDto reqDto){
        if (CollectionUtils.isEmpty(reqDto.getOrderNoList())) {
            return generateSuccess("reqDto.getOrderNoList() is empty");
        }
        if (running.compareAndSet(0, 1)) {
            try {
                for (Long orderNo : reqDto.getOrderNoList()) {
                    erpAccountCheckService.erpAccountCheckByOrderNo(orderNo,false);
                }
                return generateSuccess("reErpAccountCheckByOrderNoList finished");
            } finally {
                running.set(0);
            }
        }
        return generateSuccess("reErpAccountCheckByOrderNoList is still running");
    }

    @ApiOperation(value = "某个时间内erp下账对账", notes = "某个时间内erp下账对账")
    @PostMapping("/timRangeErpAccountCheck")
    public ResponseBase<String> timeRangeErpAccountCheck(@Valid @RequestBody ErpCheckTimeRangeReqDto reqDto){
        if (running.compareAndSet(0, 1)) {
            try {
                erpAccountCheckService.erpAccountTimeRangeCheck(reqDto);
                return generateSuccess("timeRangeErpAccountCheck finished");
            } finally {
                running.set(0);
            }
        }
        return generateSuccess("erpCheck is still running");
    }

    @ApiOperation(value = "昨日erp下账对账", notes = "昨日erp下账对账")
    @PostMapping("/yesterdayErpAccountCheck")
    public ResponseBase<String> yesterdayErpAccountCheck(){
        if (running.compareAndSet(0, 1)) {
            try {
                erpAccountCheckService.erpAccountYesterdayCheck();
                return generateSuccess("yesterdayErpAccountCheck finished");
            } finally {
                running.set(0);
            }
        }
        return generateSuccess("erpCheck is still running");
    }

    @ApiOperation(value = "分页获取erp对账汇总数据", notes = "分页获取erp对账汇总数据")
    @PostMapping("/page/querySummary")
    public ResponseBase<QueryErpRspDto> querySummary(
            @RequestHeader("userId") String userId,
            @RequestHeader("merCode") String merCode,
            @Valid @RequestBody QueryErpAccountCheckReqDto query,
            BindingResult result) {
        checkValid(result);
        query.setMerCode(merCode);
        List<String> platformCodeList = verifyService.verifyPlatformCodeAndGet(merCode, userId, Collections.singletonList(query.getPlatformCode()));
        if(StringUtils.isEmpty(query.getOrganizationCode())){
            throw ExceptionUtil.getWarnException(DsErrorType.ORGANIZATION_CODE_IS_NULL);
        }
        VerifyRspDto verifyDto = verifyService.verifyTimeAndOrganization(VerifyReqDto.builder()
                .merCode(query.getMerCode())
                .organizationCode(query.getOrganizationCode())
                .userId(userId)
                .storeFlag(0)
                .verifyFlag(1)
                .build());
        query.setOrganizationCodeList(verifyDto.getOrganizatioinList());
        if(StringUtils.isEmpty(query.getDataType())) {
            query.setDataType(DsConstants.INTEGER_ZERO);
        }
        if(null == query.getOrderTimeEnd()){
            query.setOrderTimeEnd(DateUtil.getBeforeDateEnd(1));
        }
        return generateSuccess(erpCheckResultService.querySummary(query,platformCodeList));
    }

    @ApiOperation(value = "erp对账-门店下的订单数据", notes = "erp对账-门店下的订单数据")
    @PostMapping("/page/order/search")
    public ResponseBase<QueryErpStoreRspDto> queryOrderErpCheckResult(
            @RequestHeader("userId") String userId,
            @RequestHeader("merCode") String merCode,
            @Valid @RequestBody QueryErpAccountCheckDetailSearchReqDto query,
            BindingResult result) {
        checkValid(result);
        query.setMerCode(merCode);
        if(StringUtils.isEmpty(query.getDataType())) {
            query.setDataType(DsConstants.INTEGER_ZERO);
        }
        if(null == query.getOrderTimeEnd()){
            query.setOrderTimeEnd(DateUtil.getBeforeDateEnd(1));
        }
        return generateSuccess(erpCheckResultService.queryDetail(query));
    }

    @ApiOperation(value = "erp对账-单条手工核账", notes = "erp对账-单条手工核账")
    @PostMapping("/handleErpCheckOrder")
    public ResponseBase<Void> handleErpCheckOrder(
            @RequestHeader("userId") String userId,
            @RequestHeader("merCode") String merCode,
            @Valid @RequestBody ErpCheckHandleReqDto params,
            BindingResult result) {
        checkValid(result);
        erpCheckResultService.handleErpCheckOrder(userId,merCode,params);
        return generateSuccess(null);
    }

    @ApiOperation(value = "重算erp下账对账,不要在定时任务执行时执行", notes = "重算erp下账对账,不要在定时任务执行时执行")
    @PostMapping("/repairErpAccountCheck")
    public ResponseBase<String> repairErpAccountCheck(@Valid @RequestBody RepairErpCheckAccountReqDto reqDto){
        if (running.compareAndSet(0, 1)) {
            try {
                String result = erpAccountCheckService.repairErpAccountCheck(reqDto);
                return generateSuccess(StringUtils.isEmpty(result)? "repairErpAccountCheck finished":result);
            } finally {
                running.set(0);
            }
        }
        return generateSuccess("erpCheck is still running");
    }

    @ApiOperation(value = "erp对账状态同步",notes = "erp对账数据状态同步")
    @PostMapping("/syncStatus")
    public ResponseBase<Void> syncStatus(@RequestParam("orderNo") Long orderNo){
        erpAccountCheckService.syncStatus(orderNo);
        return generateSuccess(null);
    }

}
