
package cn.hydee.middle.business.order.client.ws.dto;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;

/**
 * 
 * <AUTHOR>
 * @date 2021/03/16
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class ReceiveFromWsMessageDTO implements Serializable {

    private static final long serialVersionUID = 9004292516973051065L;

    /**
     * 与oms交互唯一消息id
     */
    private String omsId;

    /**
     * sessionID
     */
    private String sessionId;

    /**
     * 商户编码
     */
    private String merCode;

    /**
     * 门店编码
     */
    private String storeCode;

    /**
     * 设备唯一标识信息
     */
    private String deviceId;

    /**
     * 消息请求ID，用于识别响应
     */
    private String msgId;

    /**
     * 消息类型，deviceInfo，reply，setting，voice，print
     */
    private String msgType;

    /**
     * 数据
     */
    private Object data;

}
