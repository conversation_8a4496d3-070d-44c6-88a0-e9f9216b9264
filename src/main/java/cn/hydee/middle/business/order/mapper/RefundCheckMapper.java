package cn.hydee.middle.business.order.mapper;

import cn.hydee.middle.business.order.entity.RefundCheck;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import org.apache.ibatis.annotations.Param;
import org.springframework.stereotype.Repository;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;

@Repository
@Transactional(rollbackFor = Exception.class)
public interface RefundCheckMapper extends BaseMapper<RefundCheck> {
	
	public List<RefundCheck> selectListByRefundNo(Long refundNo);

	List<RefundCheck> selectListByRefundNoList(@Param("refundNoList") List<Long> refundNoList);

	int insertBatch(List<RefundCheck> list);

}
