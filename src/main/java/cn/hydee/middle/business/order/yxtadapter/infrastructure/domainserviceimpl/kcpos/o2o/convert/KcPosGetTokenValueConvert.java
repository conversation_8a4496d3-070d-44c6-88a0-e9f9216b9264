package cn.hydee.middle.business.order.yxtadapter.infrastructure.domainserviceimpl.kcpos.o2o.convert;

import cn.hydee.middle.business.order.dto.rsp.baseinfo.StoreResDTO;
import cn.hydee.middle.business.order.yxtadapter.domain.dictionary.InnerStoreDictionary;
import lombok.extern.slf4j.Slf4j;

@Slf4j
public class KcPosGetTokenValueConvert {

    public static InnerStoreDictionary innerStoreDictionaryConvert(StoreResDTO storeResDTO) {

        InnerStoreDictionary innerStoreDictionary = new InnerStoreDictionary();

        innerStoreDictionary.setOrganizationCode(storeResDTO.getStCode());
        innerStoreDictionary.setOrganizationName(storeResDTO.getStName());
        innerStoreDictionary.setInnerAppId(storeResDTO.getRomensAppId());
        innerStoreDictionary.setInnerAppSecret(storeResDTO.getRomensAppSecret());
        innerStoreDictionary.setPosMode(3);
        innerStoreDictionary.setIsOpenNew("1");

        return innerStoreDictionary;
    }
}
