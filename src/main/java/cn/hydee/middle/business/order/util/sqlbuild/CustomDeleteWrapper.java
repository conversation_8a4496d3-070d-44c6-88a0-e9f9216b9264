package cn.hydee.middle.business.order.util.sqlbuild;


import cn.hydee.middle.business.order.entity.OrderInfo;
import cn.hydee.middle.business.order.util.sqlbuild.base.AbstractCustomSqlBase;
import cn.hydee.middle.business.order.util.sqlbuild.base.BuildValueUtil;
import cn.hydee.middle.business.order.util.sqlbuild.base.CustomSqlConstant;
import cn.hydee.middle.business.order.util.sqlbuild.base.ICustomDeleteSqlBase;
import cn.hydee.middle.business.order.util.sqlbuild.sqlcheck.CustomSqlCheck;
import cn.hydee.middle.business.order.util.sqlbuild.sqlformat.CustomSqlFormat;

/**
 * com.stylefeng.guns.modular.code.base.QuerySql
 *
 * <AUTHOR> Li
 * @version 1.0
 * @date 2020/8/14 19:47
 **/
public class CustomDeleteWrapper<T> extends AbstractCustomSqlBase<CustomDeleteWrapper<T>,T> implements ICustomDeleteSqlBase<CustomDeleteWrapper<T>> {

    private StringBuilder  querySql = new StringBuilder("");

    @Override
    public CustomDeleteWrapper<T> Builder() {
        return this;
    }

    @Override
    public CustomDeleteWrapper<T> clearCondition() {
        querySql = new StringBuilder("");
        return this;
    }

    @Override
    protected CustomDeleteWrapper<T> addCondition(String column, String type, Object val) {
        BuildValueUtil buildValueUtil = new BuildValueUtil();
        try{
            buildValueUtil.buildValue(column,type,val);
        }catch(Exception e){
            e.printStackTrace();
            this.querySql = null;
            this.querySql = new StringBuilder();
            this.querySql.append("yyyy");
        }
        this.querySql.append(buildValueUtil.getValue().toString());
        return this;
    }


    @Override
    public String getDeleteSql() {
        return querySql.toString();
    }

    @Override
    public CustomDeleteWrapper<T> delete() {
        return addCondition("", CustomSqlConstant.DELETE,"");
    }

}
