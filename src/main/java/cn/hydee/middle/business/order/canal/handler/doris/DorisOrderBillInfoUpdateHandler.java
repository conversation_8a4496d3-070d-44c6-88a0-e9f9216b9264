package cn.hydee.middle.business.order.canal.handler.doris;

import cn.hydee.middle.business.order.Enums.DsConstants;
import cn.hydee.middle.business.order.canal.handler.DorisLog;
import cn.hydee.middle.business.order.canal.handler.IEventHandler;
import cn.hydee.middle.business.order.doris.dto.Order2DorisBillDataDto;
import cn.hydee.middle.business.order.kafka.OrderKafkaProducer;
import cn.hydee.middle.business.order.kafka.canal.CanalDorisBillData;
import cn.hydee.middle.business.order.util.JsonUtil;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;
import org.springframework.util.StringUtils;

import javax.annotation.Resource;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2021/06/30
 */
@Service("dorisOrderBillInfoHandlerUpdate")
public class DorisOrderBillInfoUpdateHandler implements IEventHandler {

    private final OrderKafkaProducer orderKafkaProducer;

    @Resource
    private DorisLog dorisLog;

    public DorisOrderBillInfoUpdateHandler(OrderKafkaProducer orderKafkaProducer) {
        this.orderKafkaProducer = orderKafkaProducer;
    }

    @Override
    public void handle(String rowData) {
        CanalDorisBillData canalData = JsonUtil.json2Object(rowData,CanalDorisBillData.class);
        if(null == canalData){
            return;
        }
        List<Order2DorisBillDataDto> updateList = canalData.getData();
        List<Order2DorisBillDataDto> oldList = canalData.getOld();
        if(CollectionUtils.isEmpty(updateList) || CollectionUtils.isEmpty(oldList)){
            return;
        }
        for (int i = 0; i < updateList.size(); i++) {
            // 修改后
            Order2DorisBillDataDto updateAfterDto = updateList.get(i);
            Order2DorisBillDataDto oldDto = oldList.get(i);
            // 修改前
            Order2DorisBillDataDto updateBeforeDto = Order2DorisBillDataDto.updateBeforeData(updateAfterDto,oldDto);
            if(null == updateBeforeDto){
                continue;
            }
            if(!organizationOrStateUnChangeFlag(updateBeforeDto,updateAfterDto)){
                updateBeforeDto.setOrderNum(DsConstants.INTEGER_ZERO);
            }
            // 构建一条金额相反的修改前数据
            updateBeforeDto.reverseAmount();
            if(null == updateBeforeDto || StringUtils.isEmpty(updateBeforeDto.getOrganizationCode()) || StringUtils.isEmpty(updateBeforeDto.getOrganizationName())
                ||  null == updateAfterDto || StringUtils.isEmpty(updateAfterDto.getOrganizationCode()) || StringUtils.isEmpty(updateAfterDto.getOrganizationName()) ){
                continue;
            }
            String messageBefore = updateBeforeDto.sortString();
            dorisLog.printLog("DorisOrderBillInfoUpdateHandler before", messageBefore);
            orderKafkaProducer.sendOrderBillMessage(messageBefore);
            // 构建一条修改后的数据
            String messageAfter = updateAfterDto.sortString();
            dorisLog.printLog("DorisOrderBillInfoUpdateHandler after", messageAfter);
            orderKafkaProducer.sendOrderBillMessage(messageAfter);
        }
    }

    /**
    * @Description: 组织机构、订单状态是否变化
    * @Param: [updateBeforeDto, updateAfterDto]
    * @return: boolean
    * @Author: syuson
    * @Date: 2021-6-30
    */
    private boolean organizationOrStateUnChangeFlag(Order2DorisBillDataDto updateBeforeDto,Order2DorisBillDataDto updateAfterDto){
        boolean organizationOrStateUnChangeFlag = updateBeforeDto.getOrganizationCode().equals(updateAfterDto.getOrganizationCode())
                && updateBeforeDto.getOrganizationName().equals(updateAfterDto.getOrganizationName())
                && updateBeforeDto.getOrderState().equals(updateAfterDto.getOrderState())
                && updateBeforeDto.getTagAppoint().equals(updateAfterDto.getTagAppoint())
                && updateBeforeDto.getTagChangeStore().equals(updateAfterDto.getTagChangeStore())
                && updateBeforeDto.getTagDeliveryType().equals(updateAfterDto.getTagDeliveryType())
                && updateBeforeDto.getTagIntegral().equals(updateAfterDto.getTagIntegral())
                && updateBeforeDto.getTagMedicalInsurance().equals(updateAfterDto.getTagMedicalInsurance())
                && updateBeforeDto.getTagNewCustomer().equals(updateAfterDto.getTagNewCustomer())
                && updateBeforeDto.getTagPayType().equals(updateAfterDto.getTagPayType())
                && updateBeforeDto.getTagPrescription().equals(updateAfterDto.getTagPrescription())
                && updateBeforeDto.getTagTransferDelivery().equals(updateAfterDto.getTagTransferDelivery())
                && updateBeforeDto.getTagPostType().equals(updateAfterDto.getTagPostType());
        return organizationOrStateUnChangeFlag;
    }
}
