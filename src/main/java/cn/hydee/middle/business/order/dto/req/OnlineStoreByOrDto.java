package cn.hydee.middle.business.order.dto.req;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.List;

/**
 * <AUTHOR>
 * @since 2021/5/19 15:49
 */
@Data
public class OnlineStoreByOrDto {


    @ApiModelProperty(value = "商户号")
    private String merCode;


    @ApiModelProperty(value = "渠道")
    private String platformCode;

    @ApiModelProperty(value = "是否需要查同步配置信息")
    private Boolean config;

    @ApiModelProperty(value = "商户机构id的集合")
    private List<String> orgIdList;
}
