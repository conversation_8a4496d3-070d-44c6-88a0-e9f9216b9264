package cn.hydee.middle.business.order.yxtadapter.domain.correctiontool.qry;

import io.swagger.annotations.ApiModelProperty;
import java.io.Serializable;
import lombok.Data;
import org.hibernate.validator.constraints.Length;

/**
 * 修数工具返回类 Created by Intellij IDEA.
 *
 * <AUTHOR> Date:  2024/1/3
 */
@Data
public class DevOpsOrderInfoReq implements Serializable {

  /**
   * 系统订单号
   */
  @ApiModelProperty(value = "系统订单号")
  @Length(min=1,max=19,message = "非法的系统订单号")
  private String orderNo;
}
