package cn.hydee.middle.business.order.route.db.mysql.mapper;

import cn.hydee.middle.business.order.route.db.mysql.model.RouteTimePeriodSet;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import java.util.List;

/**
* <AUTHOR>
* @description 针对表【route_time_period_set(订单路由规则时间段配置表)】的数据库操作Mapper
* @createDate 2024-03-20 11:22:23
* @Entity cn.hydee.middle.business.order.yxtadapter.domainservice.route.db.mysql.model.RouteTimePeriodSet
*/
public interface RouteTimePeriodSetMapper extends BaseMapper<RouteTimePeriodSet> {

  void batchInsert(List<RouteTimePeriodSet> RouteTimePeriodSet);
}




