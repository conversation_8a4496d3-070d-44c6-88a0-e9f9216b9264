package cn.hydee.middle.business.order.controller;

import java.io.IOException;
import java.util.Date;
import java.util.List;

import javax.servlet.http.HttpServletResponse;

import org.springframework.http.MediaType;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import com.google.common.collect.Lists;

import cn.hydee.middle.business.order.eo.BatchCreateReqEO;
import cn.hydee.middle.business.order.eo.BatchExpressDeliveryEO;
import cn.hydee.middle.business.order.eo.DemoData;
import cn.hydee.middle.business.order.util.ExcelUtil;
import cn.hydee.starter.controller.AbstractController;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;

/**
 *  EO 代表 Excel Object， Excel对象
 *
 *  1. 定义需要展示列的EO。 规范: 放入eo包中
 * @see DemoData
 *  2.调用ExcelUtil.write方法. 规范: 放在ExcelTemplateController中
 * @see ExcelTemplateController#batchCreateTemplate(HttpServletResponse)
 *
 *  ps: 暂时只写了简单的往外写，如有其他需求，比如写多个sheet，合并单元格，多表头等需求
 *  请@panda, 来在ExcelUtil中写你需求的功能
 */
@RestController
@RequestMapping(value = "/${api.version}/template")
@Api(value = "WEB - 模板下载", produces = MediaType.APPLICATION_JSON_UTF8_VALUE, tags = {"模板下载接口" })
@Slf4j
public class ExcelTemplateController extends AbstractController {

    @GetMapping("test")
    @ApiOperation(httpMethod = "GET", value = "测试模板下载")
    public void testDownload(HttpServletResponse resp) throws IOException {
        List<DemoData> datas = Lists.newArrayList();
        for (int i = 0; i < 10; i++) {
            DemoData demoData = new DemoData();
            demoData.setTitle("Title" + i).setDate(new Date());
            datas.add(demoData);
        }
        ExcelUtil.write(resp, DemoData.class, "测试excel", datas);
    }

    @GetMapping("/Batch_Create")
    @ApiOperation("批量上传商户模板excel导出接口")
    public void batchCreateTemplate(HttpServletResponse resp) throws IOException {
        ExcelUtil.write(resp, BatchCreateReqEO.class, "批量上传模板", new BatchCreateReqEO());
    }

    /**
     *  todo-liuxin
     */
    @GetMapping("/liu-xin")
    @ApiOperation("刘心模板接口")
    public void xinTemplate(HttpServletResponse resp) throws IOException {
        ExcelUtil.write(resp, BatchCreateReqEO.class, "刘心模板", new BatchCreateReqEO());
    }
    
    @GetMapping("/batch/express/delivery")
    @ApiOperation("批量发货模板")
    public void batchExpressDeliveryTemplate(HttpServletResponse resp) throws IOException {
        ExcelUtil.writeXls(resp, BatchExpressDeliveryEO.class, "批量发货模板", new BatchExpressDeliveryEO());
    }

}
