package cn.hydee.middle.business.order.elasticsearch.requestbuild;

import cn.hydee.middle.business.order.Enums.DeliveryTypeEnum;
import cn.hydee.middle.business.order.Enums.DsConstants;
import cn.hydee.middle.business.order.Enums.OrderStateEnum;
import cn.hydee.middle.business.order.dto.req.OrderPageReqDto;
import cn.hydee.middle.business.order.elasticsearch.config.EsUtils;
import cn.hydee.middle.business.order.util.DateUtil;
import java.util.ArrayList;
import java.util.List;
import org.elasticsearch.action.search.SearchRequest;
import org.elasticsearch.index.query.BoolQueryBuilder;
import org.elasticsearch.index.query.QueryBuilder;
import org.elasticsearch.index.query.QueryBuilders;
import org.elasticsearch.search.builder.SearchSourceBuilder;
import org.elasticsearch.search.sort.SortOrder;
import org.springframework.util.CollectionUtils;
import org.springframework.util.StringUtils;

/**
 * orderMapper.selectOrderPage  查询条件构建-订单查询
 * <AUTHOR>
 * @since 2021/11/3 10:16
 */
public class OrderNormalQueryBuild {

    public static SearchRequest assemblyRequest(String erpOrderInfoIndex, OrderPageReqDto pageBase, List<String> organizationList, List<String> platformCodeList) {
//        SearchRequest request = new SearchRequest(erpOrderInfoIndex);
        SearchRequest request = EsUtils.searchIndex(erpOrderInfoIndex);
        request.source(buildSearchSource(pageBase,organizationList,platformCodeList));
        return request;
    }

    public static SearchRequest assemblyRequestForSourceOrgCode(String erpOrderInfoIndex, OrderPageReqDto pageBase, List<String> sourceOrganizationList, List<String> platformCodeList) {
//        SearchRequest request = new SearchRequest(erpOrderInfoIndex);
        SearchRequest request = EsUtils.searchIndex(erpOrderInfoIndex);
        request.source(buildSearchSourceForSourceOrgCode(pageBase,sourceOrganizationList,platformCodeList));
        return request;
    }

    // orderMapper.searchOrderAllPage  的查询信息
    private static SearchSourceBuilder buildSearchSource(OrderPageReqDto pageBase, List<String> organizationList, List<String> platformCodeList) {
        SearchSourceBuilder searchSourceBuilder = new SearchSourceBuilder();
        // 设置分页查询
        searchSourceBuilder.size(pageBase.getPageSize());
        searchSourceBuilder.from((pageBase.getCurrentPage()-1)*pageBase.getPageSize());
        //es 限制1w条  设置参数取消限制
        searchSourceBuilder.trackTotalHits(true);
        // 设置排序
        if(null == pageBase.getOrderBy()){
            searchSourceBuilder.sort("createTime", SortOrder.DESC);
        }else if(DsConstants.INTEGER_ONE.equals(pageBase.getOrderBy())){
            searchSourceBuilder.sort("created", SortOrder.ASC);
        }else if(DsConstants.INTEGER_TWO.equals(pageBase.getOrderBy())){
            searchSourceBuilder.sort("created", SortOrder.DESC);
        }else{
            searchSourceBuilder.sort("createTime", SortOrder.DESC);
        }
        // 搜索过滤条件
        searchSourceBuilder.query(buildQuery(pageBase,organizationList,platformCodeList));
        return searchSourceBuilder;
    }

    private static SearchSourceBuilder buildSearchSourceForSourceOrgCode(OrderPageReqDto pageBase, List<String> sourceOrganizationList, List<String> platformCodeList) {
        SearchSourceBuilder searchSourceBuilder = new SearchSourceBuilder();
        // 设置分页查询
        searchSourceBuilder.size(pageBase.getPageSize());
        searchSourceBuilder.from((pageBase.getCurrentPage()-1)*pageBase.getPageSize());
        //es 限制1w条  设置参数取消限制
        searchSourceBuilder.trackTotalHits(true);
        // 设置排序
        if(null == pageBase.getOrderBy()){
            searchSourceBuilder.sort("createTime", SortOrder.DESC);
        }else if(DsConstants.INTEGER_ONE.equals(pageBase.getOrderBy())){
            searchSourceBuilder.sort("created", SortOrder.ASC);
        }else if(DsConstants.INTEGER_TWO.equals(pageBase.getOrderBy())){
            searchSourceBuilder.sort("created", SortOrder.DESC);
        }else{
            searchSourceBuilder.sort("createTime", SortOrder.DESC);
        }
        // 搜索过滤条件
        searchSourceBuilder.query(buildQueryForSourceOrgCode(pageBase,sourceOrganizationList,platformCodeList));
        return searchSourceBuilder;
    }
    // orderMapper.searchOrderAllPage  的查询条件
    private static QueryBuilder buildQuery(OrderPageReqDto pageBase, List<String> organizationList, List<String> platformCodeList) {
        BoolQueryBuilder boolBuilder = QueryBuilders.boolQuery();
        // 商户号
        if(!StringUtils.isEmpty(pageBase.getMerCode())) {
            boolBuilder.filter(QueryBuilders.termQuery("merCode", pageBase.getMerCode()));
        }
        boolBuilder.filter(QueryBuilders.termQuery("serviceMode", DsConstants.O2O));
        if(!StringUtils.isEmpty(pageBase.getThirdOrderNo())) {
            boolBuilder.filter(QueryBuilders.termQuery("thirdOrderNo", pageBase.getThirdOrderNo()));
        }
        if(null != pageBase.getOrderNo()) {
            boolBuilder.filter(QueryBuilders.termQuery("orderNo", pageBase.getOrderNo()));
        }
        {
            boolBuilder.filter(QueryBuilders.termQuery("lockFlag",DsConstants.INTEGER_ZERO));
        }
        if((!StringUtils.isEmpty(pageBase.getDeliveryPlatName())) && (!DeliveryTypeEnum.PLAT.getMsg().equals(pageBase.getDeliveryPlatName()))){
            boolBuilder.filter(QueryBuilders.termQuery("deliveryPlatName",pageBase.getDeliveryPlatName()));
        }
        if((!StringUtils.isEmpty(pageBase.getDeliveryPlatName())) && DeliveryTypeEnum.PLAT.getMsg().equals(pageBase.getDeliveryPlatName())){
            String[] arrays = new String[DsConstants.INTEGER_TWO];
            List<String> list = new ArrayList<>();
            list.add(DeliveryTypeEnum.PLAT.getCode());
            list.add(DeliveryTypeEnum.PLAT_THIRD.getCode());
            list.toArray(arrays);
            boolBuilder.filter(QueryBuilders.termsQuery("deliveryType", arrays));
        }
        if(null != pageBase.getOrderState() && (!DsConstants.INTEGER_ZERO.equals(pageBase.getOrderState()))){
            boolBuilder.filter(QueryBuilders.termQuery("orderState", pageBase.getOrderState()));
        }
        if(null != pageBase.getBeginTime()) {
            boolBuilder.filter(QueryBuilders.rangeQuery("created").gte(DateUtil.getLongDate(pageBase.getBeginTime())));
        }
        if(null != pageBase.getEndTime()) {
            boolBuilder.filter(QueryBuilders.rangeQuery("created").lte(DateUtil.getLongDate(pageBase.getEndTime())));
        }
        if(!StringUtils.isEmpty(pageBase.getDayNum())){
            boolBuilder.filter(QueryBuilders.termQuery("dayNum", pageBase.getDayNum()));
        }
        if(!StringUtils.isEmpty(pageBase.getIsPrescription())){
            boolBuilder.filter(QueryBuilders.termQuery("isPrescription", pageBase.getIsPrescription()));
        }
        if(!StringUtils.isEmpty(pageBase.getPrescriptionStatus())){
            boolBuilder.filter(QueryBuilders.termQuery("prescriptionStatus", pageBase.getPrescriptionStatus()));
        }
        if(OrderStateEnum.UN_PICK.getCode().equals(pageBase.getOrderState())){
            /**
             * 解析这个难搞的sql
             * (oi.appointment = 0) or
             *            	( oi.appointment = 1 and oi.appointment_business_flag = 1 and oi.appointment_business_type <> 2 ) )
             **/
            BoolQueryBuilder boolBuilder2 = QueryBuilders.boolQuery();
            BoolQueryBuilder boolBuilder4 = QueryBuilders.boolQuery();
            boolBuilder4.filter(QueryBuilders.termQuery("appointment",DsConstants.INTEGER_ZERO));
            BoolQueryBuilder boolBuilder3 = QueryBuilders.boolQuery();
            boolBuilder3.filter(QueryBuilders.termQuery("appointment", DsConstants.INTEGER_ONE));
            boolBuilder3.filter(QueryBuilders.termQuery("appointmentBusinessFlag",DsConstants.INTEGER_ONE));
            boolBuilder3.filter(QueryBuilders.boolQuery().mustNot(QueryBuilders.termQuery("appointmentBusinessType",DsConstants.INTEGER_TWO)));
            boolBuilder2.should(boolBuilder3);
            boolBuilder2.should(boolBuilder4);
            boolBuilder.filter(boolBuilder2);
        }
        if(!CollectionUtils.isEmpty(platformCodeList)) {
            String[] arrays = new String[platformCodeList.size()];
            platformCodeList.toArray(arrays);
            boolBuilder.filter(QueryBuilders.termsQuery("thirdPlatformCode", arrays));
        }
        if(!CollectionUtils.isEmpty(organizationList)) {
            String[] arrays = new String[organizationList.size()];
            organizationList.toArray(arrays);
            boolBuilder.filter(QueryBuilders.termsQuery("organizationCode", arrays));
        }
        if(!StringUtils.isEmpty(pageBase.getReceiverName())) {
            boolBuilder.filter(QueryBuilders.termQuery("receiverName", pageBase.getReceiverName()));
        }
        if(!StringUtils.isEmpty(pageBase.getBuyerName())) {
            boolBuilder.filter(QueryBuilders.termQuery("buyerName", pageBase.getBuyerName()));
        }
        if(!StringUtils.isEmpty(pageBase.getReceiverTelephone())) {
            String[] arrays = new String[DsConstants.INTEGER_TWO];
            List<String> list = new ArrayList<String>();
            list.add(pageBase.getReceiverTelephone());
            list.add(pageBase.getReceiverTelephoneEncrypted());
            list.toArray(arrays);
            boolBuilder.filter(QueryBuilders.termsQuery("receiverMobile", arrays));
        }
        if(null != pageBase.getDeliveryTimeType()) {
            boolBuilder.filter(QueryBuilders.termQuery("deliveryTimeType", pageBase.getDeliveryTimeType()));
        }
        if(null != pageBase.getDeliveryType()) {
            boolBuilder.filter(QueryBuilders.termQuery("deliveryType", pageBase.getDeliveryType()));
        }
        if (!StringUtils.isEmpty(pageBase.getSelfVerifyCode())) {
            boolBuilder.filter(QueryBuilders.termQuery("selfVerifyCode", pageBase.getSelfVerifyCode()));
        }
        if(!StringUtils.isEmpty(pageBase.getOnlineStoreCode())) {
            boolBuilder.filter(QueryBuilders.termQuery("onlineStoreCode", pageBase.getOnlineStoreCode()));
        }
        return boolBuilder;
    }

    private static QueryBuilder buildQueryForSourceOrgCode(OrderPageReqDto pageBase, List<String> sourceOrganizationList, List<String> platformCodeList) {
        BoolQueryBuilder boolBuilder = QueryBuilders.boolQuery();
        // 商户号
        if(!StringUtils.isEmpty(pageBase.getMerCode())) {
            boolBuilder.filter(QueryBuilders.termQuery("merCode", pageBase.getMerCode()));
        }
        boolBuilder.filter(QueryBuilders.termQuery("serviceMode", DsConstants.O2O));
        if(!StringUtils.isEmpty(pageBase.getThirdOrderNo())) {
            boolBuilder.filter(QueryBuilders.termQuery("thirdOrderNo", pageBase.getThirdOrderNo()));
        }
        if(null != pageBase.getOrderNo()) {
            boolBuilder.filter(QueryBuilders.termQuery("orderNo", pageBase.getOrderNo()));
        }
        {
            boolBuilder.filter(QueryBuilders.termQuery("lockFlag",DsConstants.INTEGER_ZERO));
        }
        if((!StringUtils.isEmpty(pageBase.getDeliveryPlatName())) && (!DeliveryTypeEnum.PLAT.getMsg().equals(pageBase.getDeliveryPlatName()))){
            boolBuilder.filter(QueryBuilders.termQuery("deliveryPlatName",pageBase.getDeliveryPlatName()));
        }
        if((!StringUtils.isEmpty(pageBase.getDeliveryPlatName())) && DeliveryTypeEnum.PLAT.getMsg().equals(pageBase.getDeliveryPlatName())){
            String[] arrays = new String[DsConstants.INTEGER_TWO];
            List<String> list = new ArrayList<>();
            list.add(DeliveryTypeEnum.PLAT.getCode());
            list.add(DeliveryTypeEnum.PLAT_THIRD.getCode());
            list.toArray(arrays);
            boolBuilder.filter(QueryBuilders.termsQuery("deliveryType", arrays));
        }
        if(null != pageBase.getOrderState() && (!DsConstants.INTEGER_ZERO.equals(pageBase.getOrderState()))){
            boolBuilder.filter(QueryBuilders.termQuery("orderState", pageBase.getOrderState()));
        }
        if(null != pageBase.getBeginTime()) {
            boolBuilder.filter(QueryBuilders.rangeQuery("created").gte(DateUtil.getLongDate(pageBase.getBeginTime())));
        }
        if(null != pageBase.getEndTime()) {
            boolBuilder.filter(QueryBuilders.rangeQuery("created").lte(DateUtil.getLongDate(pageBase.getEndTime())));
        }
        if(!StringUtils.isEmpty(pageBase.getDayNum())){
            boolBuilder.filter(QueryBuilders.termQuery("dayNum", pageBase.getDayNum()));
        }
        if(!StringUtils.isEmpty(pageBase.getIsPrescription())){
            boolBuilder.filter(QueryBuilders.termQuery("isPrescription", pageBase.getIsPrescription()));
        }
        if(!StringUtils.isEmpty(pageBase.getPrescriptionStatus())){
            boolBuilder.filter(QueryBuilders.termQuery("prescriptionStatus", pageBase.getPrescriptionStatus()));
        }
        if(OrderStateEnum.UN_PICK.getCode().equals(pageBase.getOrderState())){
            /**
             * 解析这个难搞的sql
             * (oi.appointment = 0) or
             *            	( oi.appointment = 1 and oi.appointment_business_flag = 1 and oi.appointment_business_type <> 2 ) )
             **/
            BoolQueryBuilder boolBuilder2 = QueryBuilders.boolQuery();
            BoolQueryBuilder boolBuilder4 = QueryBuilders.boolQuery();
            boolBuilder4.filter(QueryBuilders.termQuery("appointment",DsConstants.INTEGER_ZERO));
            BoolQueryBuilder boolBuilder3 = QueryBuilders.boolQuery();
            boolBuilder3.filter(QueryBuilders.termQuery("appointment", DsConstants.INTEGER_ONE));
            boolBuilder3.filter(QueryBuilders.termQuery("appointmentBusinessFlag",DsConstants.INTEGER_ONE));
            boolBuilder3.filter(QueryBuilders.boolQuery().mustNot(QueryBuilders.termQuery("appointmentBusinessType",DsConstants.INTEGER_TWO)));
            boolBuilder2.should(boolBuilder3);
            boolBuilder2.should(boolBuilder4);
            boolBuilder.filter(boolBuilder2);
        }
        if(!CollectionUtils.isEmpty(platformCodeList)) {
            String[] arrays = new String[platformCodeList.size()];
            platformCodeList.toArray(arrays);
            boolBuilder.filter(QueryBuilders.termsQuery("thirdPlatformCode", arrays));
        }
        if(!CollectionUtils.isEmpty(sourceOrganizationList)) {
            String[] arrays = new String[sourceOrganizationList.size()];
            sourceOrganizationList.toArray(arrays);
            boolBuilder.filter(QueryBuilders.termsQuery("sourceOrganizationCode", arrays));
        }
        if(!StringUtils.isEmpty(pageBase.getReceiverName())) {
            boolBuilder.filter(QueryBuilders.termQuery("receiverName", pageBase.getReceiverName()));
        }
        if(!StringUtils.isEmpty(pageBase.getBuyerName())) {
            boolBuilder.filter(QueryBuilders.termQuery("buyerName", pageBase.getBuyerName()));
        }
        if(!StringUtils.isEmpty(pageBase.getReceiverTelephone())) {
            String[] arrays = new String[DsConstants.INTEGER_TWO];
            List<String> list = new ArrayList<String>();
            list.add(pageBase.getReceiverTelephone());
            list.add(pageBase.getReceiverTelephoneEncrypted());
            list.toArray(arrays);
            boolBuilder.filter(QueryBuilders.termsQuery("receiverMobile", arrays));
        }
        if(null != pageBase.getDeliveryTimeType()) {
            boolBuilder.filter(QueryBuilders.termQuery("deliveryTimeType", pageBase.getDeliveryTimeType()));
        }
        if(null != pageBase.getDeliveryType()) {
            boolBuilder.filter(QueryBuilders.termQuery("deliveryType", pageBase.getDeliveryType()));
        }
        if (!StringUtils.isEmpty(pageBase.getSelfVerifyCode())) {
            boolBuilder.filter(QueryBuilders.termQuery("selfVerifyCode", pageBase.getSelfVerifyCode()));
        }
        if(!StringUtils.isEmpty(pageBase.getOnlineStoreCode())) {
            boolBuilder.filter(QueryBuilders.termQuery("onlineStoreCode", pageBase.getOnlineStoreCode()));
        }
        return boolBuilder;
    }

}
