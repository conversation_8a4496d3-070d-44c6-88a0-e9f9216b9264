package cn.hydee.middle.business.order.dto.req;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotEmpty;
import java.io.Serializable;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2024/2/26
 * @since 1.0
 */
@Data
public class StoreStockCheckReqDTO implements Serializable {

    @ApiModelProperty("门店")
    @NotEmpty(message = "门店 不能为空")
    private String storeCode;

    @ApiModelProperty("商品批次列表")
    @NotEmpty(message = "商品批次列表不能为空")
    private List<LotExistBO> lotExistBOList;

    @Data
    public static class LotExistBO {

        @ApiModelProperty("商品编码")
        @NotEmpty(message = "商品编码 不能为空")
        private String erpCode;

        @ApiModelProperty("批号")
        @NotEmpty(message = "批号 不能为空")
        private String lotCode;

    }
}
