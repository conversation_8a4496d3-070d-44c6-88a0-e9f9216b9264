package cn.hydee.middle.business.order.module.orderdiff.handler.constant;

import cn.hydee.middle.business.order.Enums.PlatformCodeEnum;

/**
 * <AUTHOR>
 * @date 2021/12/09
 */
public class OrderDiffConstant {

    public enum OrderDiffType {

        /** 美团 */
        MT_DIFF(PlatformCodeEnum.MEITUAN.getCode(), "meiTuanOrderDiffHandler"),
        /** 饿百 */
        EB_DIFF(PlatformCodeEnum.E_BAI.getCode(), "ebaiOrderDiffHandler"),
        /** 京东健康 */
        JD_HEALTH(PlatformCodeEnum.JD_HEALTH.getCode(), "jdHealthOrderDiffHandler"),
        /** 京东到家 */
        JD_DJ(PlatformCodeEnum.JD_DAOJIA.getCode(), "jdDaoJiaOrderDiffHandler")
        ;

        private String type;
        private String serviceName;

        OrderDiffType(String type, String serviceName) {
            this.type = type;
            this.serviceName = serviceName;
        }

        public String getType() {

            return this.type;
        }

        public String getServiceName() {

            return this.serviceName;
        }
    }
}
