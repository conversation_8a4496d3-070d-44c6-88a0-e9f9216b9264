package cn.hydee.middle.business.order.route.application.command;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2024/4/2
 * @since 1.0
 */
@Data
public class RouteAllotUpdateCommand {

    @ApiModelProperty(value = "调拨单Id")
    private String allotId;

    @ApiModelProperty(value = "系统订单号")
    private String omsNo;

    @ApiModelProperty(value = "商品明细列表")
    private List<AllotDetail> allotDetails;

    @Data
    public static class AllotDetail {

        @ApiModelProperty(value = "商品明细Id")
        private String orderDetailId;

        @ApiModelProperty(value = "商品erp编码")
        private String erpCode;

        @ApiModelProperty(value = "商品名称")
        private String goodName;

        @ApiModelProperty(value = "商品批号")
        private String commodityBatchNo;

        @ApiModelProperty(value = "数量")
        private String count;

        @ApiModelProperty(value = "状态")
        private String allotStatus;

        @ApiModelProperty(value = "新批号")
        private String newCommodityBatchNo;

    }
}

