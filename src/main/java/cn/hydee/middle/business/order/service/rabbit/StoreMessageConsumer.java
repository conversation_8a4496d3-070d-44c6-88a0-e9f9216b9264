package cn.hydee.middle.business.order.service.rabbit;

import cn.hydee.middle.business.order.Enums.DsConstants;
import cn.hydee.middle.business.order.configuration.RabbitConfig;
import cn.hydee.middle.business.order.dto.message.NetStoreNotifyMessage;
import cn.hydee.middle.business.order.util.EnterpriseWeChatUtil;
import cn.hydee.starter.exception.WarnException;
import cn.hydee.starter.util.UUIDUtil;
import com.alibaba.fastjson.JSON;
import com.rabbitmq.client.Channel;
import lombok.extern.slf4j.Slf4j;
import org.springframework.amqp.core.Message;
import org.springframework.amqp.rabbit.annotation.Queue;
import org.springframework.amqp.rabbit.annotation.RabbitHandler;
import org.springframework.amqp.rabbit.annotation.RabbitListener;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.io.IOException;

@Component
@Slf4j
public class StoreMessageConsumer {

    @Autowired
    private Processor processor;
    @Autowired
    private EnterpriseWeChatUtil enterpriseWeChatUtil;

    @RabbitListener(queuesToDeclare = @Queue(RabbitConfig.STORE_QUEUE))
    @RabbitHandler
    public void process(Message message, Channel channel) throws IOException {
        String queue = message.getMessageProperties().getConsumerQueue();
        String messageId = UUIDUtil.generateUuid();
        try {
            log.info("rabbit supplier store message[{}],messageId:{}", new String(message.getBody()), messageId);
            processor.storeNotifyProcess(message);
            //ACK,确认一条消息已经被消费
            channel.basicAck(message.getMessageProperties().getDeliveryTag(), false);
        } catch (Exception e) {
            NetStoreNotifyMessage body = JSON.parseObject(message.getBody(), NetStoreNotifyMessage.class);
            log.error(String.format("StoreMessageConsumer process queue[%s], messageId:%s", queue, messageId), e);
            //NACK basicNack(deliveryTag, multiple, requeue)
            channel.basicNack(message.getMessageProperties().getDeliveryTag(), false, false);
            if (e instanceof WarnException) {
                enterpriseWeChatUtil.sendMsg(DsConstants.STORE_MESSAGE_ERROR, messageId, body.getOlshopid(), ((WarnException) e).getTipMessage());
                return;
            }
            // 向企业微信发送messageId
            enterpriseWeChatUtil.sendMsg(DsConstants.STORE_MESSAGE_ERROR, messageId, body.getOlshopid(), e.getMessage());
        }

    }

}
