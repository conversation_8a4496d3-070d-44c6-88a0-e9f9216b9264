package cn.hydee.middle.business.order.dto.fence;

import com.baomidou.mybatisplus.annotation.TableId;
import java.util.Date;
import lombok.Data;

/**
 * <AUTHOR>
 * @date 2024/06/25 20:29
 **/
@Data
public class PageStoreFenceSyncConfigRes {
    /**
     *
     */
    @TableId
    private Long id;

    /**
     * 门店编码
     */
    private String storeCode;

    private String storeName;

    /**
     * 三方平台编码
     */
    private String platformCode;

    private String platformName;

    /**
     * 操作
     */
    private String action;

    /**
     * 状态 SUCCESS-成功   FAIL-失败
     */
    private String status;

    /**
     * 操作结果
     */
    private String msg;

    /**
     * 操作人ID
     */
    private String operatorId;

    /**
     * 操作人姓名
     * */
    private String operatorName;

    /**
     * 创建时间
     */
    private Date createTime;

    /**
     * 修改时间
     */
    private Date updateTime;
}
