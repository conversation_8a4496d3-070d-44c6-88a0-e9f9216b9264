package cn.hydee.middle.business.order.mapper;

import cn.hydee.middle.business.order.dto.req.UpdateLogQueryReqDTO;
import cn.hydee.middle.business.order.entity.ErpRefundInfo;
import cn.hydee.middle.business.order.entity.UpdateLog;
import cn.hydee.starter.dto.PageDTO;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import org.apache.ibatis.annotations.Param;
import org.springframework.stereotype.Repository;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2021/4/6 上午11:03
 */
@Repository
public interface UpdateLogMapper extends BaseMapper<UpdateLog> {

    int insert(UpdateLog updateLog);

    void batchInsert(List<UpdateLog> updateLogList);

    IPage<UpdateLog> page(Page<UpdateLog> page, @Param("param") UpdateLogQueryReqDTO reqDTO);
}
