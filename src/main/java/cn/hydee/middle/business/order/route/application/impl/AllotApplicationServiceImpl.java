package cn.hydee.middle.business.order.route.application.impl;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.extra.spring.SpringUtil;
import cn.hydee.middle.business.order.Enums.DsConstants;
import cn.hydee.middle.business.order.Enums.DsErrorType;
import cn.hydee.middle.business.order.dto.rsp.baseinfo.EmployeeResDTO;
import cn.hydee.middle.business.order.entity.OrderInfo;
import cn.hydee.middle.business.order.entity.OrderPickInfo;
import cn.hydee.middle.business.order.entity.RefundOrder;
import cn.hydee.middle.business.order.feign.MiddleIdClient;
import cn.hydee.middle.business.order.mapper.OrderInfoMapper;
import cn.hydee.middle.business.order.mapper.OrderPickInfoMapper;
import cn.hydee.middle.business.order.mapper.RefundOrderMapper;
import cn.hydee.middle.business.order.route.application.AllotApplicationService;
import cn.hydee.middle.business.order.route.application.command.AllotOrderCreateCommand;
import cn.hydee.middle.business.order.route.application.command.RouteAllotQueryCommand;
import cn.hydee.middle.business.order.route.application.command.RouteAllotUpdateCommand;
import cn.hydee.middle.business.order.route.application.representation.RouteAllotDetailRepresentationDto;
import cn.hydee.middle.business.order.route.application.representation.RouteAllotRepresentationDto;
import cn.hydee.middle.business.order.route.db.mysql.model.RouteAllot;
import cn.hydee.middle.business.order.route.db.mysql.model.RouteAllotDetail;
import cn.hydee.middle.business.order.route.domain.AllotAggregate;
import cn.hydee.middle.business.order.route.domain.AllotDomainService;
import cn.hydee.middle.business.order.route.domain.dto.RouteAllotDomainQueryDto;
import cn.hydee.middle.business.order.route.domain.dto.RouteAllotDomainUpdateDto;
import cn.hydee.middle.business.order.route.domain.enums.AllotLogEnum;
import cn.hydee.middle.business.order.route.domain.enums.AllotStatusEnum;
import cn.hydee.middle.business.order.route.domain.enums.AllotTypeEnum;
import cn.hydee.middle.business.order.route.domain.external.RouteClientService;
import cn.hydee.middle.business.order.route.domain.repository.AllotRepository;
import cn.hydee.middle.business.order.service.async.HydeeEsSyncClientAsync;
import cn.hydee.middle.business.order.util.ExceptionUtil;
import cn.hydee.middle.business.order.yxtadapter.domain.baseinfo.SysOrganizationDto;
import cn.hydee.middle.business.order.yxtadapter.domainservice.hdpos.HdPosGateway;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.UpdateWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.transaction.support.TransactionTemplate;
import org.springframework.util.Assert;
import org.springframework.util.CollectionUtils;
import org.springframework.util.StringUtils;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;
import java.util.stream.Stream;

/**
 * <AUTHOR>
 * @date 2024/04/01 14:45
 **/
@Slf4j
@Service
@RequiredArgsConstructor
public class AllotApplicationServiceImpl implements AllotApplicationService {

    private final AllotRepository allotRepository;
    private final OrderPickInfoMapper orderPickInfoMapper;
    private final RouteClientService routeClientService;
    private final MiddleIdClient middleIdClient;
    private final HdPosGateway hdPosGateway;
    private final OrderInfoMapper orderInfoMapper;
    private final RefundOrderMapper refundOrderMapper;
    private final TransactionTemplate transactionTemplate;

    @Override
    @Transactional
    public void createOrderAllotOrder(AllotOrderCreateCommand command) {
        log.info("创建调拨单 orderNo: {}",command.getOrderInfo().getOrderNo());
        AllotAggregate aggregate = new AllotAggregate();
        List<String> codes = Stream.of(command.getOrderInfo().getOrganizationCode(), command.getOrderInfo().getSourceOrganizationCode()).distinct().collect(Collectors.toList());
        List<SysOrganizationDto> sysOrganizationDtos = routeClientService.batchGetOrgByCodes(codes);
        Map<String, String> pathMap = sysOrganizationDtos.stream().collect(Collectors.toMap(SysOrganizationDto::getOrCode, SysOrganizationDto::getParentPath));
        EmployeeResDTO employeeResDTO = routeClientService.queryEmpByCondition(command.getOrderInfo().getMerCode(), command.getOrderInfo().getSourceOrganizationCode());
        Long omsAllotNo = middleIdClient.getId(1).get(0);
        aggregate.createOrderAggregate(command.getOrderInfo(), command.getOrderDetail(),command.getOrderPickInfo(), employeeResDTO.getEmpCode(),pathMap,omsAllotNo);
        if (!aggregate.isRouteTransferOrder()) {
            return;
        }
        allotRepository.save(aggregate);
        RouteAllot allot = allotRepository.getByOmsNoAndType(aggregate.getOmsNo(), AllotTypeEnum.ORDER.name());
        List<RouteAllotDetail> allotDetails = allotRepository.getAllotDetailByAllotId(allot.getId());
        //新增ES日志
        StringBuilder sb =new StringBuilder();
        allotDetails.stream().forEach(detail -> sb.append("商品编码：").append(detail.getErpCode()).append(",")
                .append("商品批号：").append(detail.getCommodityBatchNo()).append(",")
                .append("数量：").append(detail.getCount()).append(";"));
        HydeeEsSyncClientAsync.allotLogToES(DsConstants.INTEGER_ONE,"500001",allot.getId().toString(), AllotLogEnum.CREATE_TRANSFER_ALLOT.getAction(),sb.toString(),null);
    }

    @Override
    public IPage<RouteAllotRepresentationDto> selectRouteAllotPage(RouteAllotQueryCommand command) {
        if(StringUtils.isEmpty(command.getOrgIdPath())){
            return new Page<>(command.getCurrentPage(), command.getPageSize());
        }
        routeClientService.validAuthorityByUserId(command.getUserId(), command.getOrgIdPath());
        RouteAllotDomainQueryDto dto = new RouteAllotDomainQueryDto();
        BeanUtil.copyProperties(command,dto);
        return allotRepository.selectRouteAllotPage(dto);
    }

    @Override
    public RouteAllotDetailRepresentationDto selectRouteAllotInfo(String allotId) {
        return allotRepository.selectRouteAllotInfo(allotId);
    }

    @Override
    @Transactional
    public void updateOrderAllotDetail(RouteAllotUpdateCommand command) {
        RouteAllotDomainUpdateDto updateDto = new RouteAllotDomainUpdateDto();
        BeanUtil.copyProperties(command,updateDto);
        RouteAllot allot = allotRepository.getAllotById(Long.parseLong(command.getAllotId()));
        List<RouteAllotDetail> allotDetail = allotRepository.getAllotDetailByAllotId(Long.parseLong(command.getAllotId()));
        //更新调拨单信息
        allotRepository.updateOrderAllotDetail(updateDto);
        if(AllotTypeEnum.ORDER.name().equals(allot.getOrderType())){
            //更新拣货信息(逻辑删除旧的，新增新的)
            List<RouteAllotDomainUpdateDto.AllotDetail> allotDetails = updateDto.getAllotDetails();
            List<RouteAllotDomainUpdateDto.AllotDetail> details = allotDetails.stream().filter(i -> AllotStatusEnum.FAIL.name().equals(i.getAllotStatus())).collect(Collectors.toList());
            details.forEach(i -> {
                if (StringUtils.isEmpty(i.getNewCommodityBatchNo())){
                    throw ExceptionUtil.getWarnException(DsErrorType.PARAM_VALID_ERROR.getCode(),"新批号不能为空");
                }
            });
            List<String> orderDetailIds = details.stream().map(RouteAllotDomainUpdateDto.AllotDetail::getOrderDetailId).distinct().collect(Collectors.toList());
            List<OrderPickInfo> insertPickInfos = new ArrayList<>();
            details.forEach(detail -> {
                OrderPickInfo insertPickInfo = new OrderPickInfo();
                insertPickInfo.setOrderDetailId(Long.parseLong(detail.getOrderDetailId()));
                insertPickInfo.setErpCode(detail.getErpCode());
                insertPickInfo.setCommodityBatchNo(detail.getNewCommodityBatchNo());
                insertPickInfo.setCount(detail.getCount());
                insertPickInfos.add(insertPickInfo);
            });
            if(!CollectionUtils.isEmpty(insertPickInfos)){
                OrderPickInfo updatePickInfo = new OrderPickInfo();
                updatePickInfo.setIsValid(DsConstants.INTEGER_ZERO);
                orderPickInfoMapper.update(updatePickInfo,new UpdateWrapper<OrderPickInfo>().lambda().in(OrderPickInfo::getOrderDetailId, orderDetailIds));
                insertPickInfos.forEach(orderPickInfoMapper::insert);
            }
        }

        StringBuilder sb = new StringBuilder();
        sb.append("修改前：");
        allotDetail.forEach(detail -> {
            sb.append("商品编码:").append(detail.getErpCode()).append(",")
                .append("批号:").append(detail.getCommodityBatchNo()).append(",")
                .append("数量:").append(detail.getCount()).append(",");
        });
        sb.append("修改后：");
        command.getAllotDetails().forEach(detail -> sb
                .append("商品编码:").append(detail.getErpCode()).append(",")
                /* 未修改批次号的商品明细新批次号为空，取原批次号 */
                .append("批号:").append(detail.getNewCommodityBatchNo() == null ? detail.getCommodityBatchNo() : detail.getNewCommodityBatchNo()).append(",")
                .append("数量:").append(detail.getCount()).append(","));
        //添加ES转单成功记录
        HydeeEsSyncClientAsync.allotLogToES(DsConstants.INTEGER_ONE,"500001",command.getAllotId(), AllotLogEnum.TRANSFER_ALLOT_UPDATE.getAction(),sb.toString(),null);
        //推送调拨单
        transactionTemplate.execute(transactionStatus -> {
            if (AllotTypeEnum.ORDER.name().equals(allot.getOrderType())) {
                hdPosGateway.orderAccounting(allot.getOmsNo().toString());
            }
            if (AllotTypeEnum.REFUND.name().equals(allot.getOrderType())) {
                OrderInfo orderInfo = orderInfoMapper.selectOne(new QueryWrapper<OrderInfo>().lambda().eq(OrderInfo::getThirdOrderNo, allot.getThirdOrderNo()));
                RefundOrder refundOrder = refundOrderMapper.selectByRefundNo(allot.getOmsNo());
                SpringUtil.getBean(AllotDomainService.class).sendHdCreateRefundAllot(orderInfo,refundOrder);
            }
            return null;
        });
    }

    @Override
    public void updateAllotState(Long allotId, AllotStatusEnum allotStatus) {

        RouteAllot allot = allotRepository.getAllotById(allotId);
        Assert.notNull(allot,"调拨单不存在");
        //修改状态
        allotRepository.updateAllotById(allotId,allotStatus.name(), null);

        if(!AllotStatusEnum.SUCCESS.name().equals(allotStatus.name())){
            return;
        }
        //状态修改为成功 推送海典下账
        if (AllotTypeEnum.ORDER.name().equals(allot.getOrderType())) {
            hdPosGateway.orderAccounting(allot.getOmsNo().toString());
            return;
        }
        if (AllotTypeEnum.REFUND.name().equals(allot.getOrderType())) {
            hdPosGateway.refundOrderAccounting(allot.getOmsNo().toString(), DsConstants.INTEGER_ONE);
            return;
        }
    }
}
