package cn.hydee.middle.business.order.configuration;



import com.github.benmanes.caffeine.cache.CacheLoader;
import com.github.benmanes.caffeine.cache.Caffeine;
import org.springframework.cache.CacheManager;
import org.springframework.cache.caffeine.CaffeineCacheManager;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.context.annotation.Primary;

import java.util.concurrent.TimeUnit;

/**
 * <AUTHOR>
 * @Description Caffeine缓存配置类
 * @Date 14:03 2025/2/11
 **/
@Configuration
public class CaffeineCacheConfig {

    @Bean
    @Primary
    public CacheManager cacheManager(CacheLoader<Object, Object> cacheLoader) {
        Caffeine<Object, Object> caffeine = Caffeine.newBuilder()
                //cache初始容量
                .initialCapacity(500)
                //缓存最大条数
                .maximumSize(2000)
                // 写入后多久过期
                .expireAfterWrite(10, TimeUnit.SECONDS)
                // 创建或更新之后多久刷新,需要设置cacheLoader
                .refreshAfterWrite(10, TimeUnit.SECONDS);
        CaffeineCacheManager cacheManager = new CaffeineCacheManager();
        cacheManager.setCaffeine(caffeine);
        cacheManager.setCacheLoader(cacheLoader);
        return cacheManager;
    }

    @Bean
    public CacheLoader<Object, Object> cacheLoader() {
        return new CacheLoader<Object, Object>() {
            @Override
            public Object load(Object key) {
                return null;
            }

            // 重写这个方法将oldValue值返回回去，进而刷新缓存
            @Override
            public Object reload(Object key, Object oldValue) {
                return oldValue;
            }
        };
    }

}
