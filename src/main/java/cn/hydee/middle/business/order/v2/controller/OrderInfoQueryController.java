package cn.hydee.middle.business.order.v2.controller;

import cn.hydee.middle.business.order.Enums.DsConstants;
import cn.hydee.middle.business.order.Enums.DsErrorType;
import cn.hydee.middle.business.order.Enums.OrderOverSoldEnum;
import cn.hydee.middle.business.order.dto.common.OrderCommonDTO;
import cn.hydee.middle.business.order.dto.common.OrderCommonQueryDTO;
import cn.hydee.middle.business.order.dto.order.forced_refresh.req.ForcedRefreshDto;
import cn.hydee.middle.business.order.dto.order.over.sold.OrderOverSoldFeedbackReqDto;
import cn.hydee.middle.business.order.dto.order.over.sold.OrderOverSoldReasonInfo;
import cn.hydee.middle.business.order.dto.order.over.sold.OrderOverSoldReqDto;
import cn.hydee.middle.business.order.dto.order.over.sold.OrderOverSoldRespDto;
import cn.hydee.middle.business.order.dto.req.VerifyReqDto;
import cn.hydee.middle.business.order.dto.rsp.OrderStateCountRspDto;
import cn.hydee.middle.business.order.dto.rsp.VerifyRspDto;
import cn.hydee.middle.business.order.module.biz.wait.handle.event.constant.WaitHandleEventConstants;
import cn.hydee.middle.business.order.module.biz.wait.handle.event.context.WaitHandleEventContext;
import cn.hydee.middle.business.order.module.biz.wait.handle.event.dto.WaitHandleBaseDto;
import cn.hydee.middle.business.order.module.biz.wait.handle.event.dto.WaitHandleReqDto;
import cn.hydee.middle.business.order.module.biz.wait.handle.event.event.EventHandlerFactory;
import cn.hydee.middle.business.order.module.biz.wait.handle.event.handler.WaitHandleEventHandler;
import cn.hydee.middle.business.order.service.CommodityExceptionFeedbackService;
import cn.hydee.middle.business.order.service.CommodityExceptionOrderService;
import cn.hydee.middle.business.order.service.EsEnhanceService;
import cn.hydee.middle.business.order.service.OrderInfoService;
import cn.hydee.middle.business.order.service.VerifyService;
import cn.hydee.middle.business.order.util.ExceptionUtil;
import cn.hydee.starter.controller.AbstractController;
import cn.hydee.starter.dto.ErrorType;
import cn.hydee.starter.dto.ResponseBase;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.google.common.collect.Lists;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import java.util.Collections;
import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;
import javax.validation.Valid;
import lombok.extern.slf4j.Slf4j;
import org.jetbrains.annotations.NotNull;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.util.Assert;
import org.springframework.util.CollectionUtils;
import org.springframework.util.StringUtils;
import org.springframework.validation.BindingResult;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestHeader;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

/**
 * 查询接口迁移
 */
@RestController
@RequestMapping("/${api.version2}/ds/order")
@Validated
@Api(tags = "订单查询接口")
@Slf4j
public class OrderInfoQueryController extends AbstractController {

    @Autowired
    private OrderInfoService orderInfoService;

    @Autowired
    private CommodityExceptionOrderService commodityExceptionOrderService;
    @Autowired
    private VerifyService verifyService;
    @Autowired
    private CommodityExceptionFeedbackService commodityExceptionFeedbackService;
    @Autowired
    private EsEnhanceService esEnhanceService;
    @Autowired
    private EventHandlerFactory eventHandlerFactory;


  @Value("${order.switch.waitHandleCount:true}")
  private Boolean orderWaitHandleCountSwitch;


  @ApiOperation(value = "获取线下门店订单各状态数量", notes = "获取线下门店订单各状态数量")
    @GetMapping("/state/count/{merCode}/{organCode}")
    public ResponseBase<OrderStateCountRspDto> getOrderCountByOrganCode2(
            @RequestHeader("userId") String userId,
            @PathVariable String merCode,
            @PathVariable String organCode){
        OrderStateCountRspDto orderStateCountRspDto = orderInfoService.getOrderCountByOrganCodeFromRedis(userId,merCode, organCode);
        return generateSuccess(orderStateCountRspDto);
    }
  @ApiOperation(value = "强制刷新最近24小时内的订单", notes = "强制刷新该状态订单到ES")
  @PostMapping("/state/forcedRefresh")
    public ResponseBase<String>   forcedRefresh(@RequestHeader("userId") String userId,
        @RequestHeader("merCode") String merCode,
        @RequestBody ForcedRefreshDto reqDto){

    String s = orderInfoService.forcedRefresh(reqDto);

    return generateSuccess(s);
  }



    @ApiOperation(value = "获取超卖原因枚举", notes = "获取超卖原因枚举")
    @GetMapping("/orderOverSold/reasonInfo")
    public ResponseBase<List<OrderOverSoldReasonInfo>> orderOverSoldReasonInfo(
            @RequestHeader("userId") String userId,
            @RequestHeader("merCode") String merCode){
        return generateSuccess(OrderOverSoldEnum.allReasonInfoList());
    }

    @ApiOperation(value = "超卖归因列表", notes = "超卖归因列表")
    @PostMapping("/orderOverSold/query")
    public ResponseBase<IPage<OrderOverSoldRespDto>> orderOverSoldQuery(
            @RequestHeader("userId") String userId,
            @RequestHeader("merCode") String merCode,
            @Valid @RequestBody OrderOverSoldReqDto reqDto){
        reqDto.setMerCode(merCode);

        // 平台编码鉴权
        List<String> platformCodeList = verifyService.verifyPlatformCodeAndGet(merCode, userId, reqDto.getThirdPlatformCodeList());
        reqDto.setThirdPlatformCodeList(platformCodeList);

        // 门店鉴权
        // 为空查有权限的全部数据
        if(StringUtils.isEmpty(reqDto.getOrganizationCode())){
            reqDto.setOrganizationCode(DsConstants.ORGANIZATION_CODE_ALL);
        }
        VerifyRspDto verifyDto = verifyService.verifyOrganizationAndGet(VerifyReqDto.builder()
                .merCode(reqDto.getMerCode())
                .organizationCode(reqDto.getOrganizationCode())
                .userId(userId)
                .build());
        Assert.notEmpty(verifyDto.getOrganizatioinList(), DsErrorType.PARAM_VALID_NO_AUTH_ORGANIZATION.getMsg());
        reqDto.setOrganizationCodeList(verifyDto.getOrganizatioinList());

        // 超卖原因类型集合
        if(!CollectionUtils.isEmpty(reqDto.getReasonTypeList())){
            reqDto.setReasonTypeList(reqDto.getReasonTypeList().stream().filter(reasonType -> OrderOverSoldEnum.allReasonCodeList().contains(reasonType))
                    .collect(Collectors.toList()));
        }
        if(CollectionUtils.isEmpty(reqDto.getReasonTypeList())){
            reqDto.setReasonTypeList(OrderOverSoldEnum.allReasonCodeList());
        }

        return generateSuccess(commodityExceptionOrderService.orderOverSoldQuery(reqDto));
    }

    @ApiOperation(value = "反馈超卖真实原因", notes = "反馈超卖真实原因")
    @PostMapping("/orderOverSold/feedback")
    public ResponseBase<String> orderOverSoldFeedback(
            @RequestHeader("userId") String userId,
            @RequestHeader("userName") String userName,
            @RequestHeader("merCode") String merCode,
            @Valid @RequestBody OrderOverSoldFeedbackReqDto reqDto) {
        reqDto.setUserId(userId);
        reqDto.setUserName(userName);
        reqDto.setMerCode(merCode);
        commodityExceptionFeedbackService.feedback(reqDto);
        return generateSuccess("感谢您的反馈，我们会继续努力做得更智能。");
    }

    @ApiOperation(value = "订单信息检索接口", notes = "商户后台首页专用")
    @PostMapping("/common/queryByOrderNoOrThirdNo")
    public ResponseBase<OrderCommonDTO> queryByOrderNoOrThirdNo(
            @RequestHeader("userId") String userId,
            @RequestHeader("merCode") String merCode,
            @Valid @RequestBody OrderCommonQueryDTO reqDto, BindingResult result) {
        checkValid(result);
        // 平台鉴权
        List<String> platformCodeList = platformAuthCheck(userId, merCode);
        // 门店鉴权
        VerifyRspDto verifyDto = organizationAuthCheck(userId, merCode);
        OrderCommonDTO orderCommonDTO = esEnhanceService.queryByOrderNoOrThirdNo(merCode,reqDto.getSearchKey(),platformCodeList,verifyDto.getOrganizatioinList());
        return generateSuccess(orderCommonDTO);
    }

    @ApiOperation(value = "待办提醒数据接口", notes = "商户后台首页专用")
    @PostMapping("/common/queryWaitHandleEvent")
    public ResponseBase<List<WaitHandleBaseDto>> queryWaitHandleEvent(
            @RequestHeader("userId") String userId,
            @RequestHeader("merCode") String merCode,
            @Valid @RequestBody WaitHandleReqDto reqDto, BindingResult result) {
//        checkValid(result);
//        if(CollectionUtils.isEmpty(reqDto.getWaitItem())){
//            return generateSuccess(Collections.emptyList());
//        }
//        List<WaitHandleEventConstants.CommonWaitHandleEventEnum> eventEnumList = reqDto.getWaitItem().stream().map(businessType -> {
//            WaitHandleEventConstants.CommonWaitHandleEventEnum enumInfo = WaitHandleEventConstants.CommonWaitHandleEventEnum.convertByBusinessType(businessType);
//            if(Objects.isNull(enumInfo)){
//                throw ExceptionUtil.getWarnException(ErrorType.PARA_ERROR.getCode(),"待办事项暂不支持："+businessType);
//            }
//            return enumInfo;
//        }).collect(Collectors.toList());
//        // 平台鉴权
//        List<String> platformCodeList = platformAuthCheck(userId, merCode);
//        // 门店鉴权
//        VerifyRspDto verifyDto = organizationAuthCheck(userId, merCode);
//        //
//        if (orderWaitHandleCountSwitch
//            && !CollectionUtils.isEmpty(verifyDto.getOrganizatioinList())
//            && verifyDto.getOrganizatioinList().size() > 1
//        ) {
//          return generateSuccess(Collections.emptyList());
//        }
//
//        List<WaitHandleBaseDto> waitHandleBaseDtoList = eventEnumList.stream().map(
//                eventInfo -> doHandle(merCode, platformCodeList, verifyDto, eventInfo)).collect(Collectors.toList());
        return generateSuccess(Lists.newArrayList());
//      return ResponseBase.success();
    }

    @NotNull
    private WaitHandleBaseDto doHandle(@RequestHeader("merCode") String merCode, List<String> platformCodeList, VerifyRspDto verifyDto, WaitHandleEventConstants.CommonWaitHandleEventEnum eventInfo) {
        WaitHandleEventHandler handler = eventHandlerFactory.getHandler(eventInfo.getCode());
        if(Objects.isNull(handler)){
            throw ExceptionUtil.getWarnException(ErrorType.PARA_ERROR.getCode(),"待办事项暂不支持："+eventInfo.getBusinessType());
        }
        return handler.handle(WaitHandleEventContext.buildBean(merCode,platformCodeList,verifyDto.getOrganizatioinList(),eventInfo));
    }

    @NotNull
    private VerifyRspDto organizationAuthCheck(@RequestHeader("userId") String userId, @RequestHeader("merCode") String merCode) {
        VerifyRspDto verifyDto = verifyService.verifyTimeAndOrganization(VerifyReqDto.builder()
                .merCode(merCode)
                .organizationCode(DsConstants.ORGANIZATION_CODE_ALL)
                .userId(userId)
                .storeFlag(0)
                .verifyFlag(1)
                .build());
        if (Objects.isNull(verifyDto) || CollectionUtils.isEmpty(verifyDto.getOrganizatioinList())) {
            throw ExceptionUtil.getWarnException(ErrorType.PARA_ERROR.getCode(), "该用户无门店权限");
        }
        return verifyDto;
    }

    @NotNull
    private List<String> platformAuthCheck(@RequestHeader("userId") String userId, @RequestHeader("merCode") String merCode) {
        List<String> platformCodeList = verifyService.verifyPlatformCodeAndGet(merCode, userId, Collections.emptyList());
        if (CollectionUtils.isEmpty(platformCodeList)) {
            // 表示超级管理员
            platformCodeList = Collections.emptyList();
        }
        return platformCodeList;
    }
}

