package cn.hydee.middle.business.order.yxtadapter.domain.oms.transform;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.collection.ListUtil;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.util.StrUtil;
import com.baomidou.mybatisplus.annotation.TableField;
import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.LinkedList;
import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;

/**
 * returnorder_std
 */
@Data
public class ReturnorderStd implements Serializable {
    private String guid;

    /**
     * 订单来源（basicinfo_soutce)
     */
    private String orderfrom;

    /**
     * 订单类型（basicinfo_type)
     */
    private String ordertype;

    /**
     * 手动插入退单时的当前时间
     */
    private String orderguid;

    /**
     * 原单code
     */
    private String ordercode;

    /**
     * 退单编号
     */
    private String returncode;

    /**
     * 门店编号
     */
    private String storecode;

    /**
     * 运费
     */
    private BigDecimal postfee;

    /**
     * 包装费
     */
    private BigDecimal packagefee;

    /**
     * 商品总金额
     */
    private BigDecimal productsamount;

    /**
     * 退单金额
     */
    private BigDecimal returnamount;

    /**
     * 申请人姓名
     */
    private String applyname;

    /**
     * 申请人电话
     */
    private String applyphone;

    /**
     * 退货原因
     */
    private String returnreason;

    /**
     * 退款状态
     */
    private String returnstatus;

    /**
     * 退货图片
     */
    private String returnpicture;

    /**
     * 退货地址
     */
    private String returnaddress;

    /**
     * 经度
     */
    private String lat;

    /**
     * 纬度
     */
    private String lng;

    /**
     * 申请时间
     */
    private String createtime;

    /**
     * 是否审核（0：否；1：是）
     */
    private Integer isauditing;

    /**
     * 审核人
     */
    private String auditingguid;

    /**
     * 审核时间
     */
    private String auditingtime;

    /**
     * 取货方式 -1：退回到门店 1：达达 2：蜂鸟 3：美团
     */
    private String transporttype;

    /**
     * 配送单号或内部人员工号
     */
    private String transnumber;

    /**
     * 配送编号
     */
    private String deliveryid;

    /**
     * 是否确认
     */
    private Integer isconfirm;

    /**
     * 确认人
     */
    private String confirmguid;

    /**
     * 确认时间
     */
    private String confirmdate;

    /**
     * 拒绝退款原因
     */
    private String rejectreason;

    /**
     * 内部处理批次号
     */
    private String batchnumber;

    /**
     * OMS内部状态 0：临时数据 1：正式数据
     */
    private Integer innerstatus;

    /**
     * 下账时间
     */
    private String accounttime;

    /**
     * 是否已下帐(1-下账完成，4-报损,5不可下账)
     */
    private Integer isaccounts;

    /**
     * 是否是自配送（空或0为自配送，否则平台配送）
     */
    private Integer deliverytype;

    /**
     * 退款单类型，空或0为退款退货，1为退款，2为直赔）
     */
    private Integer returntype;

    /**
     * 是否是整单退（0：部分退，1：整单退）
     */
    private Integer isfullrefund;

    /**
     * 是否是无效订单0：正常订单 1：无效订单 2：处理完的无效订单
     */
    private String isinvalid;


    /**
     * 原始订单信息-最终的快递公司（店员自送时为-1，可关联出最终配送方式）
     */
    @TableField(exist = false)
    private String transcompany;

    /**
     * 原始订单信息-一开始进来的订单是否是门店配送（0：否，1：是），此字段为不会修改
     */
    @TableField(exist = false)
    private Integer isselfdelivery;

    public YnOmsRefundOrderDeliveryDto transformToDeliveryDto(List<ReturnorderDetail> returnorderDetails) {
        //退单详情
        List<YnOmsRefundOrderDeliveryDto.YnOmsRefundDetailDto> refundDetailDtoList = new LinkedList<>();
        if (CollectionUtil.isNotEmpty(returnorderDetails)) {
            for (ReturnorderDetail orderDetail : returnorderDetails) {
                refundDetailDtoList.add(YnOmsRefundOrderDeliveryDto.YnOmsRefundDetailDto.builder()
                        .refundNo(orderDetail.getReturncode())
                        .erpCode(orderDetail.getGoodscode())
                        .commodityName(orderDetail.getGoodsname())
                        .refundCount(orderDetail.getReturncount())
                        .actualNetAmount(orderDetail.getGoodsamount())
                        .billPrice(orderDetail.getDiscountprice())
                        .buyerAmount(orderDetail.getGoodsamount())
                        .unitRefundPrice(orderDetail.getGoodsprice())
                        .originDetailPrice(orderDetail.getDiscountprice())
                        .createTime(DateUtil.parse(this.createtime))
                        .build());
            }
        }

        YnOmsRefundOrderDeliveryDto.YnOmsRefundCheckDto refundCheck = null;
        //退款检查
        if (this.isconfirm == 1 && StrUtil.isNotBlank(this.confirmguid)) {
            refundCheck = YnOmsRefundOrderDeliveryDto.YnOmsRefundCheckDto.builder()
                    .refundNo(this.returncode)
                    .checkerId(this.confirmguid)
                    .checkTime(this.confirmdate)
                    .checkMark(this.rejectreason)
                    .build();
        }

        //图片信息
        List<YnOmsRefundOrderDeliveryDto.YnOmsRefundPictureDto> refundPictureDtos = new LinkedList<>();
        List<String> pictureList = new ArrayList<>();
        if (StrUtil.isNotBlank(this.returnpicture)) {
            if (StrUtil.startWith(this.returnpicture, "[") && StrUtil.endWith(this.returnpicture, "]")) {
                pictureList = StrUtil.splitTrim(StrUtil.replaceChars(this.returnpicture, new char[]{'[', ']'}, ""), ",");
            } else {
                pictureList.add(this.returnpicture);
            }
        }
        if (CollUtil.isNotEmpty(pictureList)) {
            refundPictureDtos.addAll(pictureList.stream().map(picture -> YnOmsRefundOrderDeliveryDto.YnOmsRefundPictureDto.builder()
                    .refundNo(this.returncode)
                    .picture(picture)
                    .createTime(this.createtime)
                    .build()
            ).collect(Collectors.toList()));
        }

        //ERP退账明细
        YnOmsRefundOrderDeliveryDto.YnOmsErpRefundInfoDto erpRefundInfoDto = YnOmsRefundOrderDeliveryDto.YnOmsErpRefundInfoDto.builder()
                .refundGoodsTotal(this.productsamount)
                .refundMerchantTotal(this.returnamount)
                .refundPostFee(this.postfee)
                .packageFee(this.packagefee)
                .createTime(this.createtime)
                .orderNo(this.ordercode)
                .refundNo(this.returncode)
                .billTime(this.accounttime)
                .build();

        // 判断配送方式
        boolean isPlatformDelivery = true;
        //ISSELFDELIVERY     等于1就是商家配送
        //TRANSCOMPANY   不等于 1，2，4 就是商家配送
        if (this.isselfdelivery == 1) {
            isPlatformDelivery = false;
        }
        List<String> platformTransList = ListUtil.of("1", "2", "4");
        if (Objects.nonNull(this.transcompany) && !platformTransList.contains(this.transcompany)) {
            isPlatformDelivery = false;
        }

        return YnOmsRefundOrderDeliveryDto.builder()
                .onlineStoreCode(this.storecode)
                .refundType(this.returntype)
                .fullRefund(this.isfullrefund)
                .orderState(Integer.valueOf(this.returnstatus))
                .refundMerchantTotal(this.returnamount)
                .sourceOrganizationCode(this.storecode)
                .sourceChannelType(StrUtil.isNotEmpty(this.orderfrom) ? Integer.valueOf(this.orderfrom) : null)
                .transNumber(this.transnumber)
                .erpState(this.isaccounts)
                .refundReason(this.returnreason)
                .completeTime(DateUtil.parse(this.confirmdate))
                .erpRefundInfo(erpRefundInfoDto)
                .refundDetailList(refundDetailDtoList)
                .refundCheck(refundCheck)
                .refundPictureList(refundPictureDtos)
                .platformDelivery(isPlatformDelivery)
                .build();

    }
}