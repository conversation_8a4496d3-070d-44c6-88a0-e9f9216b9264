package cn.hydee.middle.business.order.route.domain.util;

import java.util.regex.Matcher;
import java.util.regex.Pattern;

public class ValidateUtil {


  public static boolean greaterThanZero(Long number){
    return number != null && number > 0 ;
  }

  /**
   * 比较是否大于0
   * @param number
   * @return
   */
  public static boolean greaterThanZero(Integer number){
    return number != null && number > 0 ;
  }

  /**
   * Json格式校验
   * @param jsonStr
   * @return
   */
  public static boolean jsonFormatCheck(String jsonStr){
    String jsonFormat = "\\{.*\\}|\\[.*\\]";
    Pattern pattern = Pattern.compile(jsonFormat);
    Matcher matcher = pattern.matcher(jsonStr);
    return matcher.matches();
  }

}
