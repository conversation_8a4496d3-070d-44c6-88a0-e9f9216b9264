package cn.hydee.middle.business.order.dto.req.baseinfo;

import com.baomidou.mybatisplus.annotation.TableField;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotNull;
import java.util.Date;


@Data
public class DsOnlineStoreSetDefaultResDto {

    @NotNull(message = "上一个默认门店ID")
    @ApiModelProperty(value = "线上门店id")
    private Long lastOnlineStoreId;

    @NotNull(message = "上一个默认门店的平台编码")
    @ApiModelProperty(value = "上一个默认门店的平台编码")
    private String platformCode;

    private String platformName;

    /**
     * 网店名称
     */
    private String onlineClientName;

    /**
     * 门店编码
     */
    private String onlineStoreCode;

    /**
     * 门店名称
     */
    private String onlineStoreName;

    /**
     * 线下门店编码
     */
    private String organizationCode;

    /**
     * 线下门店名称（冗余字段）
     */
    private String organizationName;

    /**
     * 营业状态（0：歇业 1：营业中 2：休息）
     */
    private Integer openStatus;

    /**
     * 外部门店ID
     */
    private String outShopId;

}
