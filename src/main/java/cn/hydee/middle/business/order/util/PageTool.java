package cn.hydee.middle.business.order.util;

import cn.hydee.starter.dto.PageDTO;
import com.baomidou.mybatisplus.core.metadata.IPage;

/**
 * 分页数据工具类
 *
 */
public class PageTool {
	
	/**
	 * 转换分页数据，除data之外的公共数据
	 * 
	 * @param <T> 目标实体类型（仅标识，不起实际作用）
	 * @param <E> 源实体类型（仅标识，不起实际作用）
	 * @param srcPage 源分页数据（MyBatis-PageHelper）
	 * @return
	 */
	public static <T, E> PageDTO<T> buildPageDTOwithoutData(IPage<E> srcPage) {
		PageDTO<T> destPage = new PageDTO<>();
		
		// 总记录数
		destPage.setTotalCount((int) srcPage.getTotal());
		
		// 总页数
		destPage.setTotalPage((int) srcPage.getPages());
		
		// 当前页码
		destPage.setCurrentPage((int) srcPage.getCurrent());
		
		// 每页记录数
		destPage.setPageSize((int) srcPage.getSize());
		return destPage;
	}

}