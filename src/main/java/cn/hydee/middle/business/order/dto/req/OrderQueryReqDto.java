package cn.hydee.middle.business.order.dto.req;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.List;

@Data
public class OrderQueryReqDto {

    @ApiModelProperty(value = "平台订单号集合")
    private List<String> thirdOrderNoList;

    @ApiModelProperty(value = "系统订单号集合")
    private List<Long> orderNoList;

    @ApiModelProperty(value = "商户号")
    private String merCode;

}
