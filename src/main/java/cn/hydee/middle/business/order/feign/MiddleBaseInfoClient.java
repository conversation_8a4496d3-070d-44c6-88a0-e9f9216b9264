package cn.hydee.middle.business.order.feign;

import cn.hydee.middle.business.order.dto.req.AccountLoginDTO;
import cn.hydee.middle.business.order.dto.req.BaseInfoOrByOrIdDto;
import cn.hydee.middle.business.order.dto.req.BaseInfoOrgTreeReqDto;
import cn.hydee.middle.business.order.dto.req.OrgInfoCodesReqDTO;
import cn.hydee.middle.business.order.dto.req.StoreListReqDTO;
import cn.hydee.middle.business.order.dto.req.baseinfo.*;
import cn.hydee.middle.business.order.dto.rsp.*;
import cn.hydee.middle.business.order.dto.rsp.baseinfo.*;
import cn.hydee.middle.business.order.dto.rsp.obOrder.MerchantRspDto;
import cn.hydee.middle.business.order.dto.rsp.obOrder.OrganizationRspDto;
import cn.hydee.middle.business.order.point.dto.req.AreaReqDto;
import cn.hydee.middle.business.order.point.dto.rsp.AreaRspDto;
import cn.hydee.middle.business.order.point.dto.rsp.QueryEmpInfoPlatformInfoResDTO;
import cn.hydee.middle.business.order.yxtadapter.domain.baseinfo.OrganizationCodeDTO;
import cn.hydee.middle.business.order.yxtadapter.domain.baseinfo.QueryEnableAndWxEmpByOrgCodeDTO;
import cn.hydee.middle.business.order.yxtadapter.domain.baseinfo.QueryStoreReq;
import cn.hydee.middle.business.order.yxtadapter.domain.baseinfo.SysOrganizationDto;
import cn.hydee.starter.dto.PageDTO;
import cn.hydee.starter.dto.ResponseBase;
import io.swagger.annotations.ApiModelProperty;
import io.swagger.annotations.ApiOperation;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import javax.validation.Valid;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2019/12/17
 */
@FeignClient(value = "hydee-middle-baseinfo")
public interface MiddleBaseInfoClient {

    /**
	 * 获取erp统一接口Url
	 */
    @GetMapping("/1.0/mer-conf/{merCode}/{type}")
    ResponseBase<HuditConfigRestDto> getHuditConfigByMerCode(@PathVariable(value="merCode") String merCode,
                                                             @PathVariable(value="type") String type);

    /**
	 * 根据用户ID查询员工数据
	 * 
	 * @param userId
	 *            用户ID
	 * @return 员工数据
	 */
    @GetMapping("/1.0/baseinfo/getEmployee/{userId}")
    ResponseBase<SysEmployeeResDTO> getEmployeeByUserId(@PathVariable(value="userId") String userId);

    /**
	 * 登录
	 * 
	 * @param dto
	 * @return
	 */
    @PostMapping("/1.0/acc/_login")
    ResponseBase<UserInfoDTO> login(@RequestBody AccountLoginDTO dto);

	@ApiOperation(value = "商户平台门店管理接口_门店多条件查询", notes = "商户平台门店管理接口_多条件查询")
    @PostMapping("/1.0/store/_search")
    ResponseBase<PageDTO<StoreResDTO>> queryStoreByCondition(@RequestBody QueryStoreDTO queryStoreDTO);

	@ApiOperation(value = "商户平台门店管理接口_门店多条件查询", notes = "商户平台门店管理接口_多条件查询")
	@PostMapping("/1.0/store/_search")
	ResponseBase<PageDTO<NewStoreResDTO>> newQueryStoreByCondition(@RequestBody QueryStoreDTO queryStoreDTO);

    @ApiOperation(
value = "商户平台门店管理接口_修改电商云是否有线上门店以及线上库存同步比例", notes = "商户平台门店管理接口_修改电商云是否有线上门店以及线上库存同步比例",
            response = String.class,
            responseContainer = "Object")
    @PutMapping(value = "/1.0/store/updateDsyOnlineInfo")
    ResponseBase<Boolean> updateDsyOnlineInfo(@RequestBody StoreCreateUpdateDTO updateDto);

	@ApiOperation(value = "分页查询机构", notes = "分页查询机构信息")
    @PostMapping("/1.0/baseinfo/queryOrganization")
    ResponseBase<PageDTO<StoreResDTO>> queryOrganization(@RequestBody DsOnlineStoreQueryDTO dto,
                                                   @RequestHeader("merCode") String merCode);


	@ApiOperation(value = "下载Excel", notes = "下载指定目录下的Excel文件")
    @PostMapping("/1.0/ds/shop/delivery/downloadExcel")
    void downloadExcel(@RequestParam("response") HttpServletResponse response,@RequestParam("request") HttpServletRequest request);


	@ApiModelProperty(value = "商户平台接口_根据商户编码查询商户名称", notes = "商户平台接口_根据商户编码查询商户名称")
    @GetMapping("/1.0/baseinfo/getMerchantByCode/{merCode}")
    ResponseBase<MerchantResDTO> getMerchantByCode(@PathVariable(value="merCode") String merCode);

	@ApiModelProperty(value = "商户平台接口_根据商户编码以及stCode查询线下门店数据", notes = "商户平台接口_根据商户编码以及stCode查询线下门店数据")
    @GetMapping("/1.0/baseinfo/getSysStoreByStCode/{busNo}/{merCode}")
    ResponseBase<SysStoreResDTO> getSysStoreByStCode(@PathVariable(value="busNo") String busNo,@PathVariable(value="merCode") String merCode);

	@ApiOperation(value = "商户平台接口_根据线下门店主键id查询线下门店数据", notes = "商户平台接口_根据线下门店主键id查询线下门店数据")
    @GetMapping("/1.0/baseinfo/getSysStoreInfoById/{orgId}")
    ResponseBase<SysStoreResDTO> getSysStoreInfoById(@PathVariable(value="orgId") String orgId);

	@ApiOperation(value = "多条件查询", notes = "多条件查询")
    @PostMapping("/1.0/employee/_search")
    ResponseBase<PageDTO<EmployeeResDTO>> queryEmpByCondition(@Valid  @RequestBody QueryEmpDTO queryEmpDTO);

	@ApiOperation(value = "根据用户id，查询用户可查看的门店", notes = "查询用户可查看的门店")
    @PostMapping("/1.0/store/my")
    ResponseBase<PageDTO<StoreResDTO>> queryStoreByUser(@Valid @RequestBody MyStorePCADTO dto);

	@ApiOperation(value = "根据用户id，查询用户可查看的门店", notes = "查询用户可查看的门店")
	@PostMapping("/1.0/store/myUsingCache")
	ResponseBase<PageDTO<StoreResDTO>> queryStoreByUserUsingCache(@Valid @RequestBody MyStoreDTO dto);

    /**
	 * 查询账户
	 * 
	 * @param id
	 *            ID
	 * @return
	 */
	@ApiOperation(value = "查询账户", notes = "根据ID查询单个账户")
    @GetMapping("/1.0/acc/{id}")
    public ResponseBase<AccountResDTO> getAccountById(@PathVariable("id") String id);

    /**
	 * 查询账号信息
	 * 
	 * @param merCode
	 *            商户编码
	 * @param account
	 *            账号
	 * @return
	 */
	@ApiOperation(value = "查询账号信息", notes = "查询账号信息")
    @PostMapping("/1.0/acc/_searchAccount")
    public  ResponseBase<AccountEmpResDTO> queryAccountByAccount(@RequestParam("merCode") String merCode,@RequestParam("account") String account);

    /**
	 * 员工ID信息
	 * 
	 * @param id
	 *            员工ID
	 * @return
	 */
	@ApiOperation(value = "查询账号信息", notes = "查询账号信息")
    @GetMapping("/1.0/employee/{id}")
    ResponseBase<AccountEmpResDTO> queryEmployeeById(@PathVariable("id") String id);

    /**
	 * 根据商户编号和员工编码查询信息
	 * 
	 * @param merCode
	 * @param empCode
	 * @return
	 */
    @ApiOperation(
value = "根据商户编号和员工编码查询信息", notes = "返回员工信息",
            response = String.class,
            responseContainer = "SysEmployee")
    @GetMapping("/1.0/employee/queryEmpInfoByCode/{merCode}/{empCode}")
    ResponseBase<EmployeeResDTO> queryEmpInfoByCode(@PathVariable("merCode") String merCode,@PathVariable("empCode") String empCode);

	@ApiOperation(value = "组织机构", notes = "dc仓组织机构查询")
	@GetMapping("1.0/organization/queryOrgByCode")
    ResponseBase<OrganizationRspDto> queryOrgByCode(@RequestParam("merCode") String merCode,@RequestParam("orCode") String orCode);
	
	@ApiOperation(value = "查询账户", notes = "云货架商户信息查询")
	@PostMapping("1.0/mer/_queryMerchantByCode")
    ResponseBase<MerchantRspDto> queryMerchantByCode(@RequestParam("merCode") String merCode);

	@ApiOperation(value = "根据商户编号查询商户门店所在的城市列表", notes = "根据商户编号查询商户门店所在的城市列表,第一个元素是总部所在城市")
	@GetMapping("/1.0/store/_queryMerchantCityList")
	ResponseBase<List<String>> queryMerchantCityList(@RequestParam("merCode") String merCode);
	
	@ApiOperation(value = "根据商户、城市查询商户该城市门店数据", notes = "根据商户、城市查询商户该城市门店数据")
	@PostMapping("/1.0/store/_queryStoreByCity")
	ResponseBase<PageDTO<QueryStoreByCityRspDto>> queryStoreByCity(@Valid @RequestBody QueryStoreByCityReqDto queryDto);
	
	@ApiOperation(value = "查区域信息", notes = "查区域信息")
	@PostMapping("/1.0/area/_search")
	ResponseBase<List<AreaRspDto>> queryArea(@Valid @RequestBody AreaReqDto queryDto);

	@ApiOperation(value = "根据商户编号和员工id查询平台权限编码列表", notes = "根据商户编号和员工id查询平台权限编码列表")
	@GetMapping("/1.0/employee/queryEmpInfoPlatformInfo/{merCode}/{userId}")
	ResponseBase<QueryEmpInfoPlatformInfoResDTO> queryEmpInfoPlatformInfo(@PathVariable("merCode") String merCode, @PathVariable("userId") String userId);


	@ApiOperation(value = "门店关键字精确查询", notes = "可以查询门店或仓库")
	@PostMapping("/1.0/store/_queryStoreByKey")
	ResponseBase<StoreSearchKeyResDTO> queryStoreByKey(@Valid @RequestBody StoreSearchKeyReqDTO reqDTO);


	@ApiOperation(value = "根据机构树的父节点获取叶子节点（机构信息）", notes = "根据机构树的父节点获取叶子节点（机构信息）")
		@PostMapping("/1.0/store/_queryOrgSubStoreList")
	ResponseBase<PageDTO<StoreResDTO>> queryOrgSubStoreList(@Valid @RequestBody BaseInfoOrByOrIdDto orIdDto);


	@ApiOperation(value = "查询门店上级机构", notes = "查询门店上级机构")
	@PostMapping("/1.0/organization/searchOrCodesParent")
	ResponseBase<List<ParentStoreRspDTO>> searchOrCodesParent(@Valid @RequestBody ParentStoreReqDTO reqDTO);

	@ApiOperation(value = "根据当前用户查询组织机构树",notes = "根据当前用户查询组织机构树")
	@PostMapping("/1.0/organization/searchOrgTreeByPid")
	ResponseBase<List<BaseInfoOrgTreeResDto>> searchOrgTreeByPid(@Valid @RequestBody BaseInfoOrgTreeReqDto reqDto);

	@GetMapping("/1.0/store/_queryStoreByMerCodeAndProvence")
	ResponseBase<List<StoreResDTO>> queryAllOrgByMerCode(@RequestParam("merCode") String merCode);

	@ApiOperation(value = "批量查询商户所属大区", notes = "批量查询商户所属大区")
	@PostMapping("/1.0/mer/queryMerchantRegionList")
	ResponseBase<List<MerchantRegionResp>> queryMerchantRegionList(@RequestBody List<String> merCodeList);

	@ApiOperation(value = "通过ID查询门店信息", notes = "通过ID查询门店信息")
	@GetMapping("/${api.version}/store/{id}")
	ResponseBase<StoreResDTO> query(@PathVariable String id);
	@ApiOperation(value = "通过ID查询门店信息", notes = "通过ID查询门店信息")
	@GetMapping("/${api.version}/store/romens/{id}")
	ResponseBase<StoreResDTO> queryromens(@PathVariable String id);

	@ApiOperation(value = "门店查询", notes = "门店查询")
	@PostMapping("/${api.version}/store/queryStoreByCond")
	ResponseBase<StoreResDTO> queryStoreByCond(@Valid @RequestBody QueryStoreReq req);

	@ApiOperation(value = "批量根据机构编码查询机构信息", notes = "批量根据机构编码查询机构信息")
	@PostMapping("/${api.version}/organization/batchGetOrgByCodes")
	ResponseBase<List<SysOrganizationDto>> batchGetOrgByCodes(@RequestBody List<String> codes);

	@ApiOperation(value = "根据指定经纬度获取附近门店 根据距离排序", notes = "根据指定经纬度获取附近门店 根据距离排序")
	@PostMapping("/${api.version}/store/queryStoreListV2")
	ResponseBase<PageDTO<StoreListResDTO>> queryStoreListV2(@RequestBody StoreListReqDTO storeListReqDTO);

	@ApiOperation(value = "根据组织查询门店带游标", notes = "根据组织查询门店带游标")
	@PostMapping("/${api.version}/organization/queryOrgByCodes")
	ResponseBase<OrgInfoCodesResDTO> queryOrgByCodes(@RequestBody OrgInfoCodesReqDTO orgInfoCodesReqDTO);

	@ApiOperation(
		value = "查询员工信息",
		notes = "查询员工信息",
		response = String.class,
		responseContainer = "Object")
	@PostMapping("/1.0/employee/getList")
	ResponseBase<List<EmployeeResDTO>> getList(@RequestBody List<String> ids);

	@PostMapping("/{version}/store/_batchSet")
	ResponseBase<Integer> batchSetForStore(@RequestBody StoreSetReq setReq, @PathVariable String version);

	@PostMapping("/${api.version}/api/store/superiors/info/list")
	ResponseBase<List<GetStoreInfoResDto>> getStoreSuperiorInfoList(@RequestBody GetStoreInfoReqDto req);


}
