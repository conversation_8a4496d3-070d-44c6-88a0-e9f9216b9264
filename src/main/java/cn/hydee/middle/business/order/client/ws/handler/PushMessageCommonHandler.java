package cn.hydee.middle.business.order.client.ws.handler;

import cn.hydee.loop.cure.common.utils.SpringBeanUtils;
import cn.hydee.middle.business.order.Enums.DeliveryPlatformEnum;
import cn.hydee.middle.business.order.Enums.DeliveryStateEnum;
import cn.hydee.middle.business.order.Enums.DsConstants;
import cn.hydee.middle.business.order.Enums.OrderStateEnum;
import cn.hydee.middle.business.order.Enums.oborder.ObOrderOriginTypeEnum;
import cn.hydee.middle.business.order.Enums.oborder.ObOrderTypeEnum;
import cn.hydee.middle.business.order.client.ws.constant.ClientWsConstants;
import cn.hydee.middle.business.order.client.ws.dto.*;
import cn.hydee.middle.business.order.client.ws.service.MerchantPrintVersionService;
import cn.hydee.middle.business.order.domain.OrderInfoAllDomain;
import cn.hydee.middle.business.order.dto.message.PrintMessage;
import cn.hydee.middle.business.order.dto.onlinestore.OnlineStorePrintTemplateDto;
import cn.hydee.middle.business.order.dto.req.AddOrderInfoReqDto;
import cn.hydee.middle.business.order.dto.req.PrintContentReqDto;
import cn.hydee.middle.business.order.dto.template.PrintPropertyDto;
import cn.hydee.middle.business.order.entity.*;
import cn.hydee.middle.business.order.mapper.CloudPrintContentMapper;
import cn.hydee.middle.business.order.mapper.DsOnlineStoreRepo;
import cn.hydee.middle.business.order.mapper.OrderInfoMapper;
import cn.hydee.middle.business.order.service.MerchantWsInfoService;
import cn.hydee.middle.business.order.service.OrderDeliveryRecordService;
import cn.hydee.middle.business.order.service.TemplateInfoService;
import cn.hydee.middle.business.order.util.JsonUtil;
import cn.hydee.starter.util.UUIDUtil;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.BeanUtils;
import org.springframework.util.CollectionUtils;
import org.springframework.util.StringUtils;

import java.math.BigDecimal;
import java.util.*;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @date 2021/03/24
 */
@Slf4j
public class PushMessageCommonHandler {

    private static final String STAR = "*";

    private static final String SERVICE_TYPE_TEMPLATE = "%s(%s)";

    private static MerchantWsInfoService merchantWsInfoService;
    private static PrintProductWsMessageHandler printProductWsMessageHandler;
    private static VoiceProductWsMessageHandler voiceProductWsMessageHandler;
    private static SettingProductWsMessageHandler settingProductWsMessageHandler;
    private static PrintTemplateProductWsMessageHandler printTemplateProductWsMessageHandler;
    private static VoiceSourceProductWsMessageHandler voiceSourceProductWsMessageHandler;
    private static DsOnlineStoreRepo dsOnlineStoreRepo;
    private static TemplateInfoService templateInfoService;
    private static CloudPrintContentMapper cloudPrintContentMapper;
    private static OrderInfoMapper orderInfoMapper;
    private static OrderDeliveryRecordService orderDeliveryRecordService;
    private static MerchantPrintVersionService merchantPrintVersionService;

    private static void getOrderDeliveryRecordService(){
        if(null == orderDeliveryRecordService) {
            orderDeliveryRecordService = SpringBeanUtils.getBean(OrderDeliveryRecordService.class);
        }
    }

    private static void getMerchantWsInfoService(){
        if(null == merchantWsInfoService) {
            merchantWsInfoService = SpringBeanUtils.getBean(MerchantWsInfoService.class);
        }
    }

    private static void getPrintHandler(){
        if(null == printProductWsMessageHandler) {
            printProductWsMessageHandler = SpringBeanUtils.getBean(PrintProductWsMessageHandler.class);
        }
        if(null == dsOnlineStoreRepo){
            dsOnlineStoreRepo = SpringBeanUtils.getBean(DsOnlineStoreRepo.class);
        }
        if(null == templateInfoService){
            templateInfoService = SpringBeanUtils.getBean(TemplateInfoService.class);
        }
        if(null == cloudPrintContentMapper){
            cloudPrintContentMapper = SpringBeanUtils.getBean(CloudPrintContentMapper.class);
        }
        if(null == orderInfoMapper){
            orderInfoMapper = SpringBeanUtils.getBean(OrderInfoMapper.class);
        }
        if(null == merchantPrintVersionService){
            merchantPrintVersionService = SpringBeanUtils.getBean(MerchantPrintVersionService.class);
        }
    }

    private static void getVoiceHandler(){
        if(null == voiceProductWsMessageHandler) {
            voiceProductWsMessageHandler = SpringBeanUtils.getBean(VoiceProductWsMessageHandler.class);
        }
    }

    private static void getSettingHandler(){
        if(null == settingProductWsMessageHandler) {
            settingProductWsMessageHandler = SpringBeanUtils.getBean(SettingProductWsMessageHandler.class);
        }
    }

    private static void getPrintTemplateHandler(){
        if(null == printTemplateProductWsMessageHandler) {
            printTemplateProductWsMessageHandler = SpringBeanUtils.getBean(PrintTemplateProductWsMessageHandler.class);
        }
    }

    private static void getVoiceSourceHandler(){
        if(null == voiceSourceProductWsMessageHandler) {
            voiceSourceProductWsMessageHandler = SpringBeanUtils.getBean(VoiceSourceProductWsMessageHandler.class);
        }
    }

    public static boolean needPrintSoundWebsocketFlag(String merCode,String organizationCode){
        // 与产品何一方确认，websocket全部商户放开 2021年7月8日14:04:36
        /*getMerchantWsInfoService();
        return merchantWsInfoService.needPrintSoundWebsocketFlag(merCode,organizationCode);*/
        return Boolean.TRUE;
    }

    //20210714 新增type(1-接单后打印，2-拣货后打印)
    public static boolean pushPrintData(OrderInfo orderInfo,Integer printType, CloudPrintContent cloudPrintContent,Integer templateType){
        getPrintHandler();
        // 获取打印模板
        DsStoreOrderConfig config = dsOnlineStoreRepo.getPrintTemplateId(orderInfo);
        Long printTemplateId = null;
        //未获取到信息则返回默认模板  如果获取到了则按传入的打印
        if(null != config){
            if(null == templateType){
                printTemplateId = (null == config.getPrintTemplateId())?config.getSecondPrintTemplateId(): config.getPrintTemplateId();
            }else if(DsConstants.INTEGER_ONE.equals(templateType)){
                printTemplateId = config.getPrintTemplateId();
            }else{
                printTemplateId = config.getSecondPrintTemplateId();
            }
            if(null == printTemplateId){
                printTemplateId = templateInfoService.getSystemPrintTemplateId();
            }
        } else {
            printTemplateId = templateInfoService.getSystemPrintTemplateId();
        }
        // 校验该消息以及模板是否存在允许下发的打印机
        List<String> deviceIdList = merchantPrintVersionService.needPrintDeviceIds(orderInfo.getMerCode(),orderInfo.getOrganizationCode(),printTemplateId);
        covertPrivacyProtection(cloudPrintContent,orderInfo.getMerCode(),printTemplateId);
        PrintDTO printDTO = new PrintDTO();
        BeanUtils.copyProperties(cloudPrintContent,printDTO);
        printDTO.setPrintTemplateId(printTemplateId);
        printDTO.setPrintType(printType);
        printDTO.setStatus(DsConstants.PRINT_STATUS_DOWNLOADED);
        Push2WsMessageDTO messageDTO = printProductWsMessageHandler.buildPrintData(orderInfo.getMerCode(),orderInfo.getOrganizationCode(), Arrays.asList(printDTO),deviceIdList);
        return printProductWsMessageHandler.producer(messageDTO,null);
    }

    public static boolean pushPrintDataForTest(OrderInfo orderInfo,Integer printType, CloudPrintContent cloudPrintContent,List<Long> printTemplateIdList,String deviceId){
        getPrintHandler();
        Long printTemplateId;
        if(CollectionUtils.isEmpty(printTemplateIdList)){
            printTemplateId = templateInfoService.getSystemPrintTemplateId();
        }else{
            printTemplateId = printTemplateIdList.get(0);
            if(DsConstants.LONG_ONE_NEGATE.equals(printTemplateId)){
                printTemplateId = templateInfoService.getSystemPrintTemplateId();
            }
        }
        List<String> deviceIdList = Arrays.asList(deviceId);
        covertPrivacyProtection(cloudPrintContent,orderInfo.getMerCode(),printTemplateId);
        PrintDTO printDTO = new PrintDTO();
        BeanUtils.copyProperties(cloudPrintContent,printDTO);
        printDTO.setPrintTemplateId(printTemplateId);
        printDTO.setPrintType(printType);
        printDTO.setStatus(DsConstants.PRINT_STATUS_DOWNLOADED);
        Push2WsMessageDTO messageDTO = printProductWsMessageHandler.buildPrintData(orderInfo.getMerCode(),orderInfo.getOrganizationCode(), Arrays.asList(printDTO),deviceIdList);
        return printProductWsMessageHandler.producer(messageDTO,null);
    }

    public static boolean pushPrintData(String merCode,String organizationCode,Integer printType){
        getPrintHandler();
        PrintContentReqDto printContentReqDto = new PrintContentReqDto();
        CloudPrintContent cloudPrintContentQuery = new CloudPrintContent();
        cloudPrintContentQuery.setMerCode(merCode);
        printContentReqDto.setMerCode(merCode);
        printContentReqDto.setStoreCode(organizationCode);
        printContentReqDto.setStatus(DsConstants.PRINT_STATUS_DOWNLOADED);
        printContentReqDto.setPageSize(DsConstants.FIFTY);
        //【*********】打印声音分表合并
        List<CloudPrintContent> cloudPrintContentList = cloudPrintContentMapper.selectPrintList(cloudPrintContentQuery.getTableNameNew(), printContentReqDto);
        // 没有积压
        if(CollectionUtils.isEmpty(cloudPrintContentList)){
            return Boolean.TRUE;
        }
        // 获取订单对应的线上门店信息
        List<Long> orderNoList = cloudPrintContentList.stream().map(CloudPrintContent::getOrderNo).collect(Collectors.toList());
        orderNoList = orderNoList.stream().distinct().collect(Collectors.toList());
        List<OrderInfo> partPropertyOrderInfoList = orderInfoMapper.selectOrderStoreInfoByOrderNo(merCode,orderNoList);
        if(CollectionUtils.isEmpty(partPropertyOrderInfoList)){
            return Boolean.FALSE;
        }
        Map<Long,OrderInfo> keyOrderInfoMap = partPropertyOrderInfoList.stream().collect(Collectors.toMap(OrderInfo::getOrderNo, a->a,(v1, v2)->v1));
        // 获取线上门店对应的打印模板
        // 线上门店去重
        List<OrderInfo> distinctOrderInfoList = partPropertyOrderInfoList.stream().collect(Collectors. collectingAndThen(
                Collectors.toCollection(() -> new TreeSet<>(Comparator.comparing(o -> getUniqueKey(o)))), ArrayList::new));
        List<OnlineStorePrintTemplateDto> onlineStorePrintTemplateDtoList = dsOnlineStoreRepo.getOnlineStorePrintTemplate(merCode,distinctOrderInfoList);
        Map<String,Long> keyPrintTemplateIdMap = new HashMap<>();
        if(!CollectionUtils.isEmpty(onlineStorePrintTemplateDtoList)){
            keyPrintTemplateIdMap.putAll(onlineStorePrintTemplateDtoList.stream().collect(Collectors.toMap(PushMessageCommonHandler::getUniqueKey,
                    OnlineStorePrintTemplateDto::getPrintTemplateId,(v1, v2)->v1)));
        }
        // 组装打印数据
        List<PrintDTO> printDTOList = new ArrayList<>();
        cloudPrintContentList.forEach(cloudPrintContent -> {
            // 获取打印模板
            Long printTemplateId = null;
            Long orderNo = cloudPrintContent.getOrderNo();
            OrderInfo orderInfo = keyOrderInfoMap.get(orderNo);
            if(null == orderInfo){
                printTemplateId = templateInfoService.getSystemPrintTemplateId();
            }else {
                printTemplateId = keyPrintTemplateIdMap.get(getUniqueKey(orderInfo));
                if(null == printTemplateId){
                    printTemplateId = templateInfoService.getSystemPrintTemplateId();
                }
            }
            covertPrivacyProtection(cloudPrintContent,merCode,printTemplateId);
            PrintDTO printDTO = new PrintDTO();
            BeanUtils.copyProperties(cloudPrintContent,printDTO);
            printDTO.setPrintTemplateId(printTemplateId);
            printDTO.setPrintType(printType);
            printDTO.setStatus(DsConstants.PRINT_STATUS_DOWNLOADED);
            printDTOList.add(printDTO);
        });
        Push2WsMessageDTO messageDTO = printProductWsMessageHandler.buildPrintData(merCode,organizationCode,printDTOList,Collections.emptyList());
        return printProductWsMessageHandler.producer(messageDTO,null);
    }


    private static String getUniqueKey(OnlineStorePrintTemplateDto onlineStorePrintTemplateDto){
        return onlineStorePrintTemplateDto.getPlatformCode()+onlineStorePrintTemplateDto.getOrganizationCode()
                +onlineStorePrintTemplateDto.getOnlineClientCode()+onlineStorePrintTemplateDto.getOnlineStoreCode();
    }

    private static String getUniqueKey(OrderInfo orderInfo){
        return orderInfo.getThirdPlatformCode()+orderInfo.getOrganizationCode()+orderInfo.getClientCode()+orderInfo.getOnlineStoreCode();
    }

    public static boolean pushVoiceData(String merCode, String organizationCode,Integer soundCount,CloudSoundContent soundContent,List<String> deviceIdList){
        getVoiceHandler();
        VoiceDTO voiceDTO = new VoiceDTO();
        BeanUtils.copyProperties(soundContent,voiceDTO);
        if(null == soundContent){
            soundCount = DsConstants.INTEGER_ONE;
        }
        voiceDTO.setSoundCount(soundCount);
        voiceDTO.setStatus(DsConstants.SOUNDSTATUS_DOWNLOADED);
        Push2WsMessageDTO messageDTO = voiceProductWsMessageHandler.buildVoiceData(merCode,organizationCode, Arrays.asList(voiceDTO),deviceIdList);
        // 【【语音播报】：总部能收到并播报所有门店的语音提醒】https://www.tapd.cn/61969829/prong/stories/view/1161969829001042637
        alsoNeedStoreHandle(messageDTO);
        return voiceProductWsMessageHandler.producer(messageDTO,null);
    }

    public static boolean pushPrintTemplateData(String merCode, String organizationCode, Integer dataType, List<PrintTemplateDTO.PrintTemplate> dataList){
        getPrintTemplateHandler();
        PrintTemplateDTO printTemplateDTO = new PrintTemplateDTO();
        printTemplateDTO.setDataType(dataType);
        printTemplateDTO.setDataList(dataList);
        Push2WsMessageDTO messageDTO = printTemplateProductWsMessageHandler.buildPrintTemplateData(merCode,organizationCode,printTemplateDTO);
        return printTemplateProductWsMessageHandler.producer(messageDTO,null);
    }

    public static boolean pushVoiceSourceData(String broadcastType,String merCode, String organizationCode, Integer dataType, List<SoundMessageInfo> soundSourceList){
        getVoiceSourceHandler();
        List<VoiceSourceDTO.VoiceSource> sourceData = soundSourceList.stream().map(data ->{{
            VoiceSourceDTO.VoiceSource source = new VoiceSourceDTO.VoiceSource();
            source.setType(data.getType());
            source.setTitle(data.getTitle());
            source.setContent(data.getContent());
            source.setBase64Code(data.getBase64code());
            return source;
        }}).collect(Collectors.toList());
        VoiceSourceDTO voiceSourceDTO = new VoiceSourceDTO();
        voiceSourceDTO.setDataType(dataType);
        voiceSourceDTO.setDataList(sourceData);
        Push2WsMessageDTO messageDTO = voiceSourceProductWsMessageHandler.buildVoiceSourceData(broadcastType,merCode,organizationCode,voiceSourceDTO);
        return voiceSourceProductWsMessageHandler.producer(messageDTO,null);
    }

    public static void buildPrintMessageForNewServiceWebsocket(OrderInfoAllDomain orderAll, PrintMessage printMessage, OrderPayInfo orderPayInfo) {
        getOrderDeliveryRecordService();
        // 平台优惠金额
        BigDecimal platformDiscount = orderPayInfo.getPlatformDiscount();
        printMessage.setPlatformDiscount(platformDiscount);
        // 商家优惠金额
        BigDecimal merchantDisCount = orderPayInfo.getMerchantDiscountSum();
        printMessage.setMerchantDisCount(merchantDisCount);
        // 商品明细优惠金额
        BigDecimal commodityDetailDiscount = orderPayInfo.getDiscountFeeDtl();
        printMessage.setCommodityDetailDiscount(commodityDetailDiscount);
        // 配送方式
        String deliveryType = getDeliveryType(orderAll);
        printMessage.setDeliveryType(deliveryType);
        // 送达方式
        String serviceType = getServiceType(orderAll);
        printMessage.setServiceType(serviceType);
        // 是否开发票,是否开票，1.开发票；2.不开发票
        String writeReceiptFlag;
        String needInvoice = orderAll.getNeedInvoice();
        if(DsConstants.STRING_ONE.equals(needInvoice)){
            writeReceiptFlag = "开发票";
        }else {
            writeReceiptFlag = "不开发票";
        }
        printMessage.setWriteReceiptFlag(writeReceiptFlag);
        // 天猫同城购
        AddOrderInfoReqDto.OmniPackage sameCityShopInfo = orderDeliveryRecordService.setSelfVerifyCode(orderAll.getOrderDeliveryRecord(), null);
        printMessage.setSameCityShopNo("");
        if(sameCityShopInfo != null){
            // 天猫同城购令牌
            String cityToken = sameCityShopInfo.getCity_token();
            printMessage.setSameCityShopNo(cityToken);
            // 取件码
            String pickUpCode = sameCityShopInfo.getPickup_code();
            printMessage.setSerial(pickUpCode);
        }
        // 【【小票打印】：备注信息打印】 https://www.tapd.cn/61969829/prong/stories/view/1161969829001034581
        printMessage.setOrderRemark(!StringUtils.isEmpty(orderAll.getBuyerRemark()) ? orderAll.getBuyerRemark() : orderAll.getBuyerMessage());
    }

    private static String getDeliveryType(OrderInfoAllDomain orderAll){
        String deliveryType = "";
        if(null == orderAll.getOrderDeliveryRecord() || StringUtils.isEmpty(orderAll.getOrderDeliveryRecord().getDeliveryType())){
            return deliveryType;
        }
        // copy from ： cn.hydee.middle.business.order.service.impl.OrderInfoServiceImpl.getAllOrderDetail
        OrderDeliveryRecord odr = orderAll.getOrderDeliveryRecord();
        switch (odr.getDeliveryType()) {
            case "1":
            case "2":
                StringBuilder platformDeliveryDesc = new StringBuilder("平台配送");
                if (OrderStateEnum.UN_TAKE.getCode().compareTo(orderAll.getOrderState()) < 0) {
                    platformDeliveryDesc.append("(")
                            .append(DeliveryStateEnum.getByCode(odr.getState()))
                            .append(")");
                }
                deliveryType = platformDeliveryDesc.toString();
                break;
            case "3":
                StringBuilder selfDeliveryDesc = new StringBuilder("商家自配送");
                if (!StringUtils.isEmpty(odr.getDeliveryPlatName())) {
                    selfDeliveryDesc.append("-")
                            .append(odr.getDeliveryPlatName());
                    if (OrderStateEnum.UN_TAKE.getCode().compareTo(orderAll.getOrderState()) < 0 && !DeliveryPlatformEnum.SJEXPRESS.getName().equals(odr.getDeliveryPlatName())) {
                        selfDeliveryDesc.append("(")
                                .append(DeliveryStateEnum.getByCode(odr.getState()))
                                .append(")");
                    }
                }
                deliveryType = selfDeliveryDesc.toString();
                break;
            case "4":
                deliveryType = "到店自提";
                break;
            case "5":
                deliveryType = "快递配送";
                break;
            default:
                break;
        }
        return deliveryType;
    }

    private static String getServiceType(OrderInfoAllDomain orderAll){
        String serviceType = "";
        if(ObOrderTypeEnum.APPOINTMENT_ORDER.getCode().equals(orderAll.getAppointment())){
            serviceType = ObOrderTypeEnum.APPOINTMENT_ORDER.getMsg();
            if(CollectionUtils.isEmpty(orderAll.getOrderDetailList())){
                return serviceType;
            }
            Integer originType = orderAll.getOrderDetailList().get(0).getOriginType();
            if(ObOrderOriginTypeEnum.CLOUD.getCode().equals(originType)){
                return String.format(SERVICE_TYPE_TEMPLATE,serviceType,ObOrderOriginTypeEnum.CLOUD.getMsg());
            }
            if(ObOrderOriginTypeEnum.DC.getCode().equals(originType)){
                return String.format(SERVICE_TYPE_TEMPLATE,serviceType,ObOrderOriginTypeEnum.DC.getMsg());
            }
            return serviceType;
        }
        if(DsConstants.INTEGER_ONE.equals(orderAll.getDeliveryTimeType())){
            serviceType = "预约送达";
        }else{
            serviceType = "立即送达";
        }
        if(!StringUtils.isEmpty(orderAll.getDeliveryTimeDesc())){
            serviceType = String.format(serviceType,orderAll.getDeliveryTimeDesc());
        }
        return serviceType;
    }

    private static void covertPrivacyProtection(CloudPrintContent cloudPrintContent,String merCode, Long printTemplateId){
        PrintPropertyDto printPropertyDto = templateInfoService.getPrintPropertyByTemplateId(merCode,printTemplateId);
        List<PrintPropertyDto.PrintProperty> printPropertyList = printPropertyDto.getPrivacyProtectionInfo();
        if(CollectionUtils.isEmpty(printPropertyList)){
            return;
        }
        boolean receiverNameCheckedFlag = Boolean.FALSE;
        boolean receiverPhoneCheckedFlag = Boolean.FALSE;
        boolean receiverBackupPhoneCheckedFlag = Boolean.FALSE;
        boolean receiverAddressCheckedFlag = Boolean.FALSE;
        for (PrintPropertyDto.PrintProperty printProperty : printPropertyList) {
            if(ClientWsConstants.PrintTemplatePropertyPrivacyProtectionInfo.RECEIVER_NAME.getProperty().equals(printProperty.getProperty())){
                receiverNameCheckedFlag = printProperty.isChecked();
            }
            if(ClientWsConstants.PrintTemplatePropertyPrivacyProtectionInfo.RECEIVER_PHONE.getProperty().equals(printProperty.getProperty())){
                receiverPhoneCheckedFlag = printProperty.isChecked();
            }
            if(ClientWsConstants.PrintTemplatePropertyPrivacyProtectionInfo.RECEIVER_BACKUP_PHONE.getProperty().equals(printProperty.getProperty())){
                receiverBackupPhoneCheckedFlag = printProperty.isChecked();
            }
            if(ClientWsConstants.PrintTemplatePropertyPrivacyProtectionInfo.RECEIVER_ADDRESS.getProperty().equals(printProperty.getProperty())){
                receiverAddressCheckedFlag = printProperty.isChecked();
            }
        }

        PrintMessage printMessage = JsonUtil.json2Object(cloudPrintContent.getBody(),PrintMessage.class);
        // 收货人 隐藏时只显示第一个字
        if(receiverNameCheckedFlag && !StringUtils.isEmpty(printMessage.getReceiverName())) {
            String receiverName = getStarString(printMessage.getReceiverName(), 1, printMessage.getReceiverName().length());
            printMessage.setReceiverName(receiverName);
        }
        // 收货人电话  11位电话号码隐藏中间四位，其他不隐藏
        if(receiverPhoneCheckedFlag && !StringUtils.isEmpty(printMessage.getReceiverPhone()) && DsConstants.INTEGER_ELEVEN == printMessage.getReceiverPhone().length()) {
            String receiverPhone = getStarString(printMessage.getReceiverPhone(), 3, 7);
            printMessage.setReceiverPhone(receiverPhone);
        }
        // 备用电话 11位电话号码隐藏中间四位，其他不隐藏
        if(receiverBackupPhoneCheckedFlag && !StringUtils.isEmpty(printMessage.getReceiverBackupPhone()) && DsConstants.INTEGER_ELEVEN == printMessage.getReceiverBackupPhone().length()) {
            String receiverBackupPhone = getStarString(printMessage.getReceiverBackupPhone(), 3, 7);
            printMessage.setReceiverBackupPhone(receiverBackupPhone);
        }
        // 收货人地址 隐藏后8个字，不够时全隐藏
        if(receiverAddressCheckedFlag && !StringUtils.isEmpty(printMessage.getReceiverAddress())) {
            int begin = printMessage.getReceiverAddress().length()-8;
            begin = begin > 0 ? begin : 0;
            String receiverAddress = getStarString(printMessage.getReceiverAddress(), begin, printMessage.getReceiverAddress().length());
            printMessage.setReceiverAddress(receiverAddress);
        }
        cloudPrintContent.setBody(JsonUtil.object2Json(printMessage));
    }

    private static void alsoNeedStoreHandle(Push2WsMessageDTO messageDTO){
        getMerchantWsInfoService();
        List<String> orCodeList = merchantWsInfoService.queryNeedAllStoreSound(messageDTO.getMerCode());
        if(CollectionUtils.isEmpty(orCodeList)){
            return;
        }
        List<String> alsoNeedPushOrCodeList = orCodeList.stream().filter(orCode->!messageDTO.getStoreCode().equalsIgnoreCase(orCode)).collect(Collectors.toList());
        if(CollectionUtils.isEmpty(alsoNeedPushOrCodeList)){
            return;
        }
        Push2WsMessageDTO message = Push2WsMessageDTO.builder().build();
        BeanUtils.copyProperties(messageDTO,message);
        message.setDeviceIdList(Collections.emptyList());
        alsoNeedPushOrCodeList.forEach(orCode ->{
            message.setOmsId(UUIDUtil.generateUuid());
            message.setStoreCode(orCode);
            voiceProductWsMessageHandler.producer(message,null);
        });
    }

    private static String getStarString(String content, int begin, int end) {
        if (begin >= content.length() || begin < 0) {
            return content;
        }
        if (end > content.length()){
            end = content.length();
        }
        if (end < 0) {
            return content;
        }
        if (begin >= end) {
            return content;
        }
        String starStr = "";
        for (int i = begin; i < end; i++) {
            starStr = starStr + STAR;
        }
        return content.substring(0, begin) + starStr + content.substring(end, content.length());
    }
}
