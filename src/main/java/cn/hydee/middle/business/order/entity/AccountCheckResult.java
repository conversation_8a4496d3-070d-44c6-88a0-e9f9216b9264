package cn.hydee.middle.business.order.entity;

import cn.hydee.middle.business.order.Enums.DsConstants;
import cn.hydee.middle.business.order.Enums.PlatformCodeEnum;
import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;
import org.springframework.beans.BeanUtils;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;
import java.util.Optional;

/**
 * <p>
 * 核对结果表
 * </p>
 *
 * <AUTHOR>
 * @since 2020-11-06
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
public class AccountCheckResult implements Serializable {

    private static final long serialVersionUID=1L;

    @TableId(value = "id", type = IdType.AUTO)
    private Long id;

    @ApiModelProperty(value = "商户编码")
    private String merCode;

    @ApiModelProperty(value = "平台编码")
    private String thirdPlatformCode;
    
    @ApiModelProperty(value = "平台名称")
    private String thirdPlatformName;

    @ApiModelProperty(value = "门店编码")
    private String onlineStoreCode;

    @ApiModelProperty(value = "门店名称")
    private String onlineStoreName;

    @ApiModelProperty(value = "外部门店ID")
    private String outShopId;

    @ApiModelProperty(value = "所属机构编码")
    private String organizationCode;

    @ApiModelProperty(value = "所属机构名称")
    private String organizationName;

    @ApiModelProperty(value = "系统订单号")
    private Long orderNo;

    @ApiModelProperty(value = "第三方平台订单号")
    private String thirdOrderNo;

    @ApiModelProperty(value = "对账结果,0：正常，1：金额差异，2：三方平台单边，3：oms单边 ")
    private Integer checkResult;

    @ApiModelProperty(value = "对账结果处置,0：未处置 1：已处置")
    private Integer resultHandelFlag;

    @ApiModelProperty(value = "0：平账  99：未知")
    private Integer resultHandelType;

    @ApiModelProperty(value = "商家实收(oms)")
    private BigDecimal merchantActualAmountOms;

    @ApiModelProperty(value = "商家实收(第三方)")
    private BigDecimal merchantActualAmountThird;

    @ApiModelProperty(value = "商家实收(差异)")
    private BigDecimal merchantActualAmountDiff;

    @ApiModelProperty(value = "商品总金额(oms)")
    private BigDecimal totalAmountOms;

    @ApiModelProperty(value = "商品总金额(第三方)")
    private BigDecimal totalAmountThird;

    @ApiModelProperty(value = "商品总金额(差异)")
    private BigDecimal totalAmountDiff;

    @ApiModelProperty(value = "平台优惠(oms)")
    private BigDecimal platformDiscountOms;

    @ApiModelProperty(value = "平台优惠(第三方)")
    private BigDecimal platformDiscountThird;

    @ApiModelProperty(value = "平台优惠(差异)")
    private BigDecimal platformDiscountDiff;

    @ApiModelProperty(value = "商家优惠(oms)")
    private BigDecimal merchantDiscountOms;

    @ApiModelProperty(value = "商家优惠(第三方)")
    private BigDecimal merchantDiscountThird;

    @ApiModelProperty(value = "商家优惠(差异)")
    private BigDecimal merchantDiscountDiff;

    @ApiModelProperty(value = "交易佣金(oms)")
    private BigDecimal brokerageAmountOms;

    @ApiModelProperty(value = "交易佣金(第三方)")
    private BigDecimal brokerageAmountThird;

    @ApiModelProperty(value = "交易佣金(差异)")
    private BigDecimal brokerageAmountDiff;

    @ApiModelProperty(value = "平台配送费(oms)")
    private BigDecimal platformDeliveryFeeOms;

    @ApiModelProperty(value = "平台配送费(第三方)")
    private BigDecimal platformDeliveryFeeThird;

    @ApiModelProperty(value = "平台配送费(差异)")
    private BigDecimal platformDeliveryFeeDiff;

    @ApiModelProperty(value = "商家配送费(oms)")
    private BigDecimal merchantDeliveryFeeOms;

    @ApiModelProperty(value = "商家配送费(第三方)")
    private BigDecimal merchantDeliveryFeeThird;

    @ApiModelProperty(value = "商家配送费(差异)")
    private BigDecimal merchantDeliveryFeeDiff;

    @ApiModelProperty(value = "平台打包费(oms)")
    private BigDecimal platformPackFeeOms;

    @ApiModelProperty(value = "平台打包费(第三方)")
    private BigDecimal platformPackFeeThird;

    @ApiModelProperty(value = "平台打包费(差异)")
    private BigDecimal platformPackFeeDiff;

    @ApiModelProperty(value = "商家打包费(oms)")
    private BigDecimal merchantPackFeeOms;

    @ApiModelProperty(value = "商家打包费(第三方)")
    private BigDecimal merchantPackFeeThird;

    @ApiModelProperty(value = "商家打包费(差异)")
    private BigDecimal merchantPackFeeDiff;

    @ApiModelProperty(value = "下单时间")
    private Date orderTime;

    @ApiModelProperty(value = "下账时间")
    private Date billTime;

    @ApiModelProperty(value = "账单日期")
    private Date accountTime;

    @ApiModelProperty(value = "第任务id")
    private String jobId;

    @ApiModelProperty(value = "创建时间")
    private Date createTime;

    @ApiModelProperty(value = "末次修改时间")
    private Date modifyTime;

	public AccountCheckResult convertResultData(AccountCheckResult result,AccountCheckOmsTemp omsData,AccountCheckChannelTemp channelData) {
	    // 配送费 1-平台收取 2-商家收取
	    Integer freightFeeFetch = null;
	    // 包装费 1-平台收取 2-商家收取
	    Integer packageFeeFetch = null;
		if(null != omsData) {
			freightFeeFetch = omsData.getFreightFeeFetch();
			packageFeeFetch = omsData.getPackageFeeFetch();
			BeanUtils.copyProperties(omsData, result,"accountTime");
			result.setMerchantActualAmountOms(omsData.getMerchantActualAmount());
			result.setTotalAmountOms(omsData.getTotalAmount());
			result.setPlatformDiscountOms(omsData.getPlatformDiscount());
			result.setMerchantDiscountOms(omsData.getMerchantDiscount());
			result.setBrokerageAmountOms(omsData.getBrokerageAmount());
			result.setPlatformDeliveryFeeOms(omsData.getPlatformDeliveryFee());
			result.setMerchantDeliveryFeeOms(omsData.getMerchantDeliveryFee());
			result.setPlatformPackFeeOms(omsData.getPlatformPackFee());
			result.setMerchantPackFeeOms(omsData.getMerchantPackFee());
		}
		if(null != channelData) {
			if(null == omsData) {
				BeanUtils.copyProperties(channelData, result);
			}
			result.setMerchantActualAmountThird(channelData.getMerchantActualAmount());
			result.setTotalAmountThird(channelData.getTotalAmount().abs());
			result.setPlatformDiscountThird(channelData.getPlatformDiscount().abs());
			result.setMerchantDiscountThird(channelData.getMerchantDiscount().abs());
			result.setBrokerageAmountThird(channelData.getBrokerageAmount().abs());
			result.setPlatformDeliveryFeeThird(channelData.getPlatformDeliveryFee().abs());
			result.setMerchantDeliveryFeeThird(channelData.getMerchantDeliveryFee().abs());
			if(null != omsData && PlatformCodeEnum.JD_DAOJIA.getCode().equals(channelData.getThirdPlatformCode())) {
				// 京东到家包装费暂时取oms包装费
				result.setPlatformPackFeeThird(omsData.getPlatformPackFee().abs());
				result.setMerchantPackFeeThird(omsData.getMerchantPackFee().abs());
			}else {
				result.setPlatformPackFeeThird(channelData.getPlatformPackFee().abs());
				result.setMerchantPackFeeThird(channelData.getMerchantPackFee().abs());
			}
			result.setAccountTime(channelData.getAccountTime());
			// 配送费 1-平台收取 2-商家收取
			if(DsConstants.INTEGER_ONE.equals(freightFeeFetch)) {
				result.setMerchantDeliveryFeeThird(BigDecimal.ZERO);
			}else if(DsConstants.INTEGER_TWO.equals(freightFeeFetch)) {
				result.setPlatformDeliveryFeeThird(BigDecimal.ZERO);
			}
			// 包装费 1-平台收取 2-商家收取
			if(DsConstants.INTEGER_ONE.equals(packageFeeFetch)) {
				result.setMerchantPackFeeThird(BigDecimal.ZERO);
			}else if(DsConstants.INTEGER_TWO.equals(packageFeeFetch)) {
				result.setPlatformPackFeeThird(BigDecimal.ZERO);
			}
		}
		result.calculateDiff();
		return result;
	}
	
    public void calculateDiff() {
    	this.merchantActualAmountOms = Optional.ofNullable(merchantActualAmountOms).orElse(BigDecimal.ZERO);
    	this.merchantActualAmountThird = Optional.ofNullable(merchantActualAmountThird).orElse(BigDecimal.ZERO);
		this.merchantActualAmountDiff = merchantActualAmountOms.subtract(merchantActualAmountThird);
		
		this.totalAmountOms = Optional.ofNullable(totalAmountOms).orElse(BigDecimal.ZERO);
		this.totalAmountThird = Optional.ofNullable(totalAmountThird).orElse(BigDecimal.ZERO);
		this.totalAmountDiff = totalAmountOms.subtract(totalAmountThird);
		
		this.platformDiscountOms = Optional.ofNullable(platformDiscountOms).orElse(BigDecimal.ZERO);
		this.platformDiscountThird = Optional.ofNullable(platformDiscountThird).orElse(BigDecimal.ZERO);
		this.platformDiscountDiff = platformDiscountOms.subtract(platformDiscountThird);
		
		this.merchantDiscountOms = Optional.ofNullable(merchantDiscountOms).orElse(BigDecimal.ZERO);
		this.merchantDiscountThird = Optional.ofNullable(merchantDiscountThird).orElse(BigDecimal.ZERO);
		this.merchantDiscountDiff = merchantDiscountOms.subtract(merchantDiscountThird);
		
		this.brokerageAmountOms = Optional.ofNullable(brokerageAmountOms).orElse(BigDecimal.ZERO);
		this.brokerageAmountThird = Optional.ofNullable(brokerageAmountThird).orElse(BigDecimal.ZERO);
		this.brokerageAmountDiff = brokerageAmountOms.subtract(brokerageAmountThird);
		
		this.platformDeliveryFeeOms = Optional.ofNullable(platformDeliveryFeeOms).orElse(BigDecimal.ZERO);
		this.platformDeliveryFeeThird  = Optional.ofNullable(platformDeliveryFeeThird).orElse(BigDecimal.ZERO);
		this.platformDeliveryFeeDiff = platformDeliveryFeeOms.subtract(platformDeliveryFeeThird);
		
		this.merchantDeliveryFeeOms = Optional.ofNullable(merchantDeliveryFeeOms).orElse(BigDecimal.ZERO);
		this.merchantDeliveryFeeThird = Optional.ofNullable(merchantDeliveryFeeThird).orElse(BigDecimal.ZERO);
		this.merchantDeliveryFeeDiff = merchantDeliveryFeeOms.subtract(merchantDeliveryFeeThird);
		
		this.platformPackFeeOms = Optional.ofNullable(platformPackFeeOms).orElse(BigDecimal.ZERO);
		this.platformPackFeeThird = Optional.ofNullable(platformPackFeeThird).orElse(BigDecimal.ZERO);
		this.platformPackFeeDiff = platformPackFeeOms.subtract(platformPackFeeThird);
		
		this.merchantPackFeeOms = Optional.ofNullable(merchantPackFeeOms).orElse(BigDecimal.ZERO);
		this.merchantPackFeeThird = Optional.ofNullable(merchantPackFeeThird).orElse(BigDecimal.ZERO);
		this.merchantPackFeeDiff = merchantPackFeeOms.subtract(merchantPackFeeThird);
    }
    
    public boolean hasDiff() {
    	if( this.merchantActualAmountDiff.abs().compareTo(BigDecimal.ZERO) > 0) {
    		return Boolean.TRUE;
    	};
    	// 【ID1015526】【优化】平台对账调优（二期），差异订单计算规则调整，商家实收金额差异为0即订单无差异
    	/*if( this.totalAmountDiff.abs().compareTo(BigDecimal.ZERO) > 0) {
    		return Boolean.TRUE;
    	};
    	if( this.platformDiscountDiff.abs().compareTo(BigDecimal.ZERO) > 0) {
    		return Boolean.TRUE;
    	};
    	if( this.merchantDiscountDiff.abs().compareTo(BigDecimal.ZERO) > 0) {
    		return Boolean.TRUE;
    	};
    	if( this.brokerageAmountDiff.abs().compareTo(BigDecimal.ZERO) > 0) {
    		return Boolean.TRUE;
    	};
    	if( this.platformDeliveryFeeDiff.abs().compareTo(BigDecimal.ZERO) > 0) {
    		return Boolean.TRUE;
    	};
    	if( this.merchantDeliveryFeeDiff.abs().compareTo(BigDecimal.ZERO) > 0) {
    		return Boolean.TRUE;
    	};
    	if( this.platformPackFeeDiff.abs().compareTo(BigDecimal.ZERO) > 0) {
    		return Boolean.TRUE;
    	};
    	if( this.merchantPackFeeDiff.abs().compareTo(BigDecimal.ZERO) > 0) {
    		return Boolean.TRUE;
    	};*/
    	return Boolean.FALSE;
    }

    public String uniqueKey(){
        return this.merCode + "_" + this.thirdPlatformCode + "_" + this.thirdOrderNo;
    }
}
