package cn.hydee.middle.business.order.repository;


import cn.hydee.middle.business.order.Enums.DsConstants;
import cn.hydee.middle.business.order.dto.account.PageAccountOrderReqDto;
import cn.hydee.middle.business.order.entity.account.AccountOrder;
import cn.hydee.middle.business.order.entity.account.AccountOrderDetail;
import cn.hydee.middle.business.order.mapper.account.AccountOrderDetailMapper;
import cn.hydee.middle.business.order.mapper.account.AccountOrderMapper;
import com.baomidou.dynamic.datasource.annotation.DS;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import java.util.Collections;
import java.util.List;
import lombok.RequiredArgsConstructor;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Repository;
import org.springframework.transaction.annotation.Transactional;

/**
 * 销售单下账数据库操作
 *
 * <AUTHOR>
 * @date 2024/05/29 10:59
 **/
@Repository
@RequiredArgsConstructor
public class AccountOrderRepository {

    private final AccountOrderMapper accountOrderMapper;
    private final AccountOrderDetailMapper accountDetailMapper;

    public AccountOrder getByOrderNo(Long omsOrderNo) {
        return accountOrderMapper.selectOne(new LambdaQueryWrapper<AccountOrder>().eq(AccountOrder::getOrderNo,omsOrderNo));
    }

    public List<AccountOrderDetail> listDetailByOrderNo(Long omsOrderNo) {
        return accountDetailMapper.selectList(new LambdaQueryWrapper<AccountOrderDetail>().eq(AccountOrderDetail::getOrderNo,omsOrderNo));
    }

    /**
     * 通过下账机构获取销售下账单
     * */
    public List<AccountOrder> listByAccOrgCode(String accOrgCode) {
        return accountOrderMapper.selectList(new LambdaQueryWrapper<AccountOrder>().eq(AccountOrder::getAccOrganizationCode,accOrgCode));
    }


    @DS(DsConstants.DB_ORDER_SLAVE)
    public IPage<AccountOrder> pageAccountOrder(PageAccountOrderReqDto reqDto) {
        LambdaQueryWrapper<AccountOrder> queryWrapper = new LambdaQueryWrapper<AccountOrder>()
            .in(CollectionUtils.isNotEmpty(reqDto.getAccOrganizationCode()), AccountOrder::getAccOrganizationCode,
                reqDto.getAccOrganizationCode())
            .eq(StringUtils.isNotBlank(reqDto.getState()), AccountOrder::getState, reqDto.getState())
            .eq(StringUtils.isNotBlank(reqDto.getOrderType()), AccountOrder::getOrderType, reqDto.getOrderType())
            .eq(StringUtils.isNotBlank(reqDto.getPosMode()), AccountOrder::getPosMode, reqDto.getPosMode())
            .eq(StringUtils.isNotBlank(reqDto.getPickType()),AccountOrder::getPickType,reqDto.getPickType())
            ;
        return accountOrderMapper.selectPage(new Page<>(reqDto.getCurrentPage(), reqDto.getPageSize()), queryWrapper);
    }

    @Transactional
    public void saveAccountOrderAndDetail(AccountOrder accountOrder, List<AccountOrderDetail> accountOrderDetails) {
        accountOrderMapper.insert(accountOrder);
        accountOrderDetails.forEach(accountDetailMapper::insert);
    }

    public void upAccountOrderById(AccountOrder accountOrder) {
        accountOrderMapper.updateById(accountOrder);
    }

    public List<AccountOrder> listByOrderNos(List<Long> orderNoList) {
        if (CollectionUtils.isEmpty(orderNoList)) {
            return Collections.emptyList();
        }
        return accountOrderMapper.selectList(new LambdaQueryWrapper<AccountOrder>().in(AccountOrder::getOrderNo, orderNoList));
    }
}
