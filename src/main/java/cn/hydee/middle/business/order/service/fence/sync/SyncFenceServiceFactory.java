package cn.hydee.middle.business.order.service.fence.sync;

import java.util.Collections;
import java.util.Map;

/**
 * <AUTHOR>
 * @date 2024/06/25 16:13
 **/
public class SyncFenceServiceFactory {

    public static Map<String,SyncFenceToPlatform> syncFenceToPlatformMap = Collections.synchronizedMap(new java.util.HashMap<String,SyncFenceToPlatform>());

    public static void register(String platform,SyncFenceToPlatform syncFenceToPlatform){
        syncFenceToPlatformMap.put(platform,syncFenceToPlatform);
    }

    public static SyncFenceToPlatform getSyncFenceToPlatform(String platform){
        return syncFenceToPlatformMap.get(platform);
    }

}
