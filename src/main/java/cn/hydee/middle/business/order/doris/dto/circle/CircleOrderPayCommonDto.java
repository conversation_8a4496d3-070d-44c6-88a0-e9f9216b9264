package cn.hydee.middle.business.order.doris.dto.circle;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;


/**
 * <AUTHOR>
 * @date 2021/06/21
 */
@Data
public class CircleOrderPayCommonDto implements Serializable {

    private static final long serialVersionUID = 9137097157659654960L;

    @ApiModelProperty(value = "有效单")
    private CircleDataDto<Integer> availableCount;
    @ApiModelProperty(value = "无效单")
    private CircleDataDto<Integer> unAvailableCount;
    @ApiModelProperty(value = "订单总金额")
    private CircleDataDto<BigDecimal> orderTotalAmount;
    @ApiModelProperty(value = "商品总金额")
    private CircleDataDto<BigDecimal> commodityTotalAmount;
    @ApiModelProperty(value = "买家实付金额")
    private CircleDataDto<BigDecimal> buyerActualAmount;
    @ApiModelProperty(value = "商家实收")
    private CircleDataDto<BigDecimal> merchantActualAmount;
    @ApiModelProperty(value = "商家配送费")
    private CircleDataDto<BigDecimal> merchantDeliveryFee;
    @ApiModelProperty(value = "平台配送费")
    private CircleDataDto<BigDecimal> platformDeliveryFee;
    @ApiModelProperty(value = "商家包装费")
    private CircleDataDto<BigDecimal> merchantPackFee;
    @ApiModelProperty(value = "平台包装费")
    private CircleDataDto<BigDecimal> platformPackFee;
    @ApiModelProperty(value = "平台佣金")
    private CircleDataDto<BigDecimal> brokerageAmount;
    @ApiModelProperty(value = "商家优惠金额")
    private CircleDataDto<BigDecimal> merchantDiscountSum;
    @ApiModelProperty(value = "平台优惠金额")
    private CircleDataDto<BigDecimal> platformDiscount;
    @ApiModelProperty(value = "商家明细优惠金额")
    private CircleDataDto<BigDecimal> discountFeeDtl;
    @ApiModelProperty(value = "商品成本价金额")
    private CircleDataDto<BigDecimal> commodityCostTotal;

}
