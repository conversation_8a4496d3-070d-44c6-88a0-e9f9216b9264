package cn.hydee.middle.business.order.yxtadapter.domainservice.hdpos;

import cn.hydee.middle.business.order.yxtadapter.infrastructure.domainserviceimpl.hdpos.context.HdPosBaseInfoContext;
import org.springframework.beans.factory.InitializingBean;

/**
 * <AUTHOR>
 * 退款单下账抽象处理类
 */
public interface RefundProcessor extends InitializingBean {

    /**
     * 退款单处理
     * @param context 订单信息上下文
     * @param businessFlag 退款类型 0-仅退款 1-退货退款
     * */
    boolean handleRefund(HdPosBaseInfoContext context, Integer businessFlag);

}
