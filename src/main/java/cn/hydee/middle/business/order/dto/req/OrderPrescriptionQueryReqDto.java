package cn.hydee.middle.business.order.dto.req;

import cn.hydee.starter.dto.PageBase;
import com.fasterxml.jackson.annotation.JsonIgnore;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

@Data
public class OrderPrescriptionQueryReqDto extends PageBase {
    @JsonIgnore
    private String merCode;

    @ApiModelProperty(value="订单号",notes="订单号")
    private Long orderNo;

    @ApiModelProperty(value = "第三方平台订单号")
    private String thirdOrderNo;

    @ApiModelProperty(value="平台code")
    private String thirdPlatFormCode;

    @ApiModelProperty(value ="线上门店编码")
    private String onlineCode;

}
