package cn.hydee.middle.business.order.doris.dto.req;

import io.swagger.annotations.ApiModelProperty;
import lombok.Builder;
import lombok.Data;

import java.io.Serializable;
import java.util.Date;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2021/06/25
 */
@Data
@Builder
public class PushMessage2KafkaReqDto implements Serializable {

    private static final long serialVersionUID = -1527788822141224703L;

    @ApiModelProperty(value = "商户编码（不需要入参，从header中获取）")
    private String merCode;

    @ApiModelProperty(value = "门店编码")
    private String storeCode;

    @ApiModelProperty(value = "订单修改开始时间（2021-06-01）")
    private Date timeStart;

    @ApiModelProperty(value = "订单修改结束时间（2021-06-20）")
    private Date timeEnd;

    @ApiModelProperty(value = "订单创单开始时间（2021-06-01）")
    private Date createTimeStart;

    @ApiModelProperty(value = "订单创单结束时间（2021-06-20）")
    private Date createTimeEnd;

    @ApiModelProperty(value = "系统订单集合",notes = "入参时，将不考虑时间")
    private List<Long> orderNoList;

    @ApiModelProperty(value = "开始index")
    private Integer startIndex;

    @ApiModelProperty(value = "查询长度")
    private Integer length;


    private Boolean notNullOnOff;

}
