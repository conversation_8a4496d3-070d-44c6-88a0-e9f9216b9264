
package cn.hydee.middle.business.order.point.dto.rsp;

import java.io.Serializable;
import java.util.List;

import io.swagger.annotations.ApiModelProperty;
import lombok.Builder;
import lombok.Data;

@Data
@Builder
public class OnlineStoreOrderCountDto implements Serializable{
	
	private static final long serialVersionUID = -435710937228570166L;

    @ApiModelProperty(value = "门店编码（线下）")
	private String stCode;
	
	@ApiModelProperty(value="线上门店数量（总和）")
	private int onlineStoreCount;
	
	@ApiModelProperty(value="订单数量（总和）")
	private int orderCount;
	
	@ApiModelProperty(value="平台订单分布")
	private List<OnlineStorePlatformOrderRspDto> platformOrderList;
}
  
