package cn.hydee.middle.business.order.util;

import com.fasterxml.jackson.core.JsonGenerator;
import com.fasterxml.jackson.databind.*;
import com.fasterxml.jackson.databind.type.CollectionType;
import lombok.extern.slf4j.Slf4j;
import org.springframework.util.StringUtils;

import java.io.IOException;
import java.util.ArrayList;
import java.util.Collections;
import java.util.List;
import java.util.regex.Matcher;
import java.util.regex.Pattern;

@Slf4j
public class JsonUtil {
	
	public static final String pattern_single = "\\{.*\\}";
	
	public static final String pattern_array = "\\[.*\\]";

	private static final ObjectMapper objectMapper = new ObjectMapper();
	private static final ObjectMapper objectMapperNull = new ObjectMapper();

	static {
		objectMapper.disable(SerializationFeature.FAIL_ON_EMPTY_BEANS);
		// 格式化json
		// objectMapper.enable(SerializationFeature.INDENT_OUTPUT);
		// 实体多字段不报错
		objectMapper.configure(DeserializationFeature.FAIL_ON_UNKNOWN_PROPERTIES, Boolean.FALSE);
		// null时转成空字符串
		objectMapper.getSerializerProvider().setNullValueSerializer(new JsonSerializer<Object>() {

			@Override
			public void serialize(Object value, JsonGenerator gen, SerializerProvider serializers) throws IOException {
				gen.writeString("");
			}
		});
		// 允许空转换
		objectMapper.configure(DeserializationFeature.ACCEPT_EMPTY_STRING_AS_NULL_OBJECT, true);


		objectMapperNull.disable(SerializationFeature.FAIL_ON_EMPTY_BEANS);
		// 实体多字段不报错
		objectMapperNull.configure(DeserializationFeature.FAIL_ON_UNKNOWN_PROPERTIES, Boolean.FALSE);
		// 允许空转换
		objectMapperNull.configure(DeserializationFeature.ACCEPT_EMPTY_STRING_AS_NULL_OBJECT, true);
	}

	/**
	 * 验证text是否为合法JSON字符串
	 * 
	 * @param text
	 * @return
	 */
	public static boolean isValidJson(String text) {
		boolean isValid = false;
		
		if (StringUtils.isEmpty(text)) {
			return isValid;
		}
		
		Matcher singleMatcher = Pattern.compile(pattern_single).matcher(text);
		
		Matcher arrayMatcher = Pattern.compile(pattern_array).matcher(text);
		
		if (singleMatcher.matches() || arrayMatcher.matches()) {
			isValid = true;
		}
		
		return isValid;
	}

	/**
	 *
	 * json2Object:JSON转换对象
	 * <p>
	 * 返回结果空处理判断
	 *
	 * @param content
	 * @param clazz
	 * @return
	 * <AUTHOR>
	 * @date 2021/03/15 下午2:23:13
	 */
	public static <T> T json2Object(String content, Class<T> clazz) {
		try {
			return objectMapper.readValue(content, clazz);
		} catch (Exception e) {
			log.error("JSON转换对象失败   ", e);
		}
		return null;
	}

	/**
	 *
	 * json2Object:JSON转换对象（空为null）
	 * <p>
	 * 返回结果空处理判断
	 *
	 * @param content
	 * @param clazz
	 * @return
	 * <AUTHOR>
	 * @date 2021/03/15 下午2:23:13
	 */
	public static <T> T json2ObjectNull(String content, Class<T> clazz) {
		try {
			return objectMapperNull.readValue(content, clazz);
		} catch (Exception e) {
			log.error("JSON转换对象失败   ", e);
		}
		return null;
	}

	/**
	 *
	 * json2Object:JSON转换对象
	 * <p>
	 * 返回结果空处理判断
	 *
	 * @param object
	 * @param clazz
	 * @return
	 * <AUTHOR>
	 * @date 2021/03/15 下午2:23:31
	 */
	public static <T> T json2Object(Object object, Class<T> clazz) {
		try {
			return objectMapper.readValue(object2Json(object), clazz);
		} catch (Exception e) {
			log.error("JSON转换对象失败   ", e);
		}
		return null;
	}

	/**
	 *
	 * json2Object:JSON转换对象（空为null）
	 * <p>
	 * 返回结果空处理判断
	 *
	 * @param object
	 * @param clazz
	 * @return
	 * <AUTHOR>
	 * @date 2021/03/15 下午2:23:31
	 */
	public static <T> T json2ObjectNull(Object object, Class<T> clazz) {
		try {
			return objectMapperNull.readValue(object2JsonNull(object), clazz);
		} catch (Exception e) {
			log.error("JSON转换对象失败   ", e);
		}
		return null;
	}

	/**
	 *
	 * json2ObjectList:JSON转换对象集合
	 * <p>
	 * 返回结果空集合处理
	 *
	 * @param content
	 * @param clazz
	 * @return
	 * <AUTHOR>
	 * @date 2021/03/15 下午2:23:45
	 */
	public static <T> List<T> json2ObjectList(String content, Class<T> clazz) {
		CollectionType valueType = objectMapper.getTypeFactory().constructCollectionType(ArrayList.class, clazz);
		try {
			return objectMapper.readValue(content, valueType);
		} catch (IOException e) {
			log.error("JSON格式化失败   ", e);
		}
		return Collections.emptyList();
	}

	/**
	 *
	 * json2ObjectList:JSON转换对象集合
	 * <p>
	 * 返回结果空集合处理
	 *
	 * @param obj
	 * @param clazz
	 * @return
	 * <AUTHOR>
	 * @date 2021/03/15 下午2:24:04
	 */
	public static <T> List<T> json2ObjectList(Object obj, Class<T> clazz) {
		CollectionType valueType = objectMapper.getTypeFactory().constructCollectionType(ArrayList.class, clazz);
		try {
			return objectMapper.readValue(object2Json(obj), valueType);
		} catch (IOException e) {
			log.error("JSON格式化失败   ", e);
		}
		return Collections.emptyList();
	}

	/**
	 *
	 * object2Json:JSON格式化
	 * <p>
	 * 返回结果空判断处理
	 *
	 * @param obj
	 * @return
	 * <AUTHOR>
	 * @date 2021/03/15 下午2:24:19
	 */
	public static String object2Json(Object obj) {
		try {
			return objectMapper.writeValueAsString(obj);
		} catch (Exception e) {
			log.error("JSON格式化失败   ", e);
		}
		return "";
	}

	/**
	 *
	 * object2Json:JSON格式化（空为null）
	 * <p>
	 * 返回结果空判断处理
	 *
	 * @param obj
	 * @return
	 * <AUTHOR>
	 * @date 2021/03/15 下午2:24:19
	 */
	public static String object2JsonNull(Object obj) {
		try {
			return objectMapperNull.writeValueAsString(obj);
		} catch (Exception e) {
			log.error("JSON格式化失败   ", e);
		}
		return "";
	}

}
