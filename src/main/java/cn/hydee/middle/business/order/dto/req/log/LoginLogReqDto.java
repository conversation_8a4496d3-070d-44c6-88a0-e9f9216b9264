package cn.hydee.middle.business.order.dto.req.log;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * cn.hydee.middle.business.order.dto.req.log
 *
 * <AUTHOR>
 * @version 1.0
 * @date 2020/7/8 11:10
 **/
@Data
public class LoginLogReqDto {
    @ApiModelProperty("平台编码 如美团京东等")
    private String  ectype;

    @ApiModelProperty("hems网店编码")
    private String  clientid;

    @ApiModelProperty("hems企业编码")
    private String groupid;

    @ApiModelProperty("用户编码")
    private String userId;

    @ApiModelProperty("客户端Ip")
    private String  userIP;

    @ApiModelProperty("孔明锁")
    private String  ati;

    @ApiModelProperty("app名称")
    private String  appName;

    @ApiModelProperty("平台关联账号（阿里健康门店名称）")
    private String  tId;

    @ApiModelProperty("登录成功或失败")
    private String  loginResult;

    @ApiModelProperty("登录信息")
    private String  loginMessage;
}
