package cn.hydee.middle.business.order.yxtadapter.domain.middle;

import com.baomidou.mybatisplus.annotation.TableField;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.math.BigDecimal;
import java.util.Date;

@Data
public class OrderDetailDTO {
    private Long id;
    @ApiModelProperty(value = "商家编码")
    private String merCode;
    @ApiModelProperty(value = "订单ID")
    private Long orderId;
    @ApiModelProperty(value = "商品ID")
    private Long commodityId;
    @ApiModelProperty(value = "商品编码")
    private String commodityCode;
    @ApiModelProperty(value = "商品名称")
    private String commodityName;
    @ApiModelProperty(value = "商品类型")
    private Integer commodityType;
    @ApiModelProperty(value = "商品来源，1-海典标准库，2-商家自定义，3-云货架")
    private Integer origin;
    @ApiModelProperty(value = "药品类型：0：甲类OTC，1：处方药，2：乙类OTC，3：非处方药")
    private Integer drugType;
    @ApiModelProperty(value = "商品个数")
    private Integer commodityNumber;
    @ApiModelProperty(value = "成本价")
    private BigDecimal costPrice;
    @ApiModelProperty(value = "商品原单价")
    private BigDecimal mprice;
    @ApiModelProperty(value = "商品售价")
    private BigDecimal commodityPrice;
    @ApiModelProperty(value = "商品健康贝价格")
    private Integer exchangeHb;
    @ApiModelProperty(value = "小计金额=商品售价*数量")
    private BigDecimal totalAmount;
    @ApiModelProperty(value = "健康贝金额健康贝数量")
    private Integer totalHb;
    @ApiModelProperty(value = "促销优惠金额")
    private BigDecimal couponAmount;
    @ApiModelProperty(value = "成交总额=小计金额-优惠")
    private BigDecimal totalActualAmount;
    @ApiModelProperty(value = "明细状态,2.待付款 4.待发货 6.待收货、待提货 8.待退货 10.待退款 12.已完成 20.已取消 30.退款完成")
    private Integer status;
    @ApiModelProperty(value = "促销活动ID,用于区分特惠和秒杀")
    private Long promotionId;

    @ApiModelProperty(value = "享受活动优惠信息，json保存")
    private String activityDiscountJson;

    @ApiModelProperty(value = "包裹ID")
    private Long packageId;
    @ApiModelProperty(value = "退款退货申请ID")
    private Long returnRequestId;
    @ApiModelProperty(value = "SKU主图")
    private  String  mPic;
    @ApiModelProperty(value = "是否组合商品0否1是")
    private Integer isCombinedCommodity;
    @ApiModelProperty(value = "是否主商品0否1是")
    private Integer isMainCommodity;
    @ApiModelProperty(value = "主商品商品id")
    private Long mainCommodityId;
    @ApiModelProperty(value = "是否促销活动0否1是")
    private Integer isPromotion;
    @ApiModelProperty(value = "商品规格属性描述")
    private String skuValue;
    private Long specId;
    @ApiModelProperty(value = "共享商品来源:1-云货架来源，2-DC仓来源")
    private Integer originType;
    @ApiModelProperty(value = "共享商品组织机构编码（云货架商家编码或DC仓编码)")
    private String originStCode;
    @ApiModelProperty(value = "是否供应商代发，0-否，1-是(云货架)")
    private Integer isDirectDelivery;
    @ApiModelProperty(value = "预计送达时间")
    private Date expectDeliveryTime;
    @ApiModelProperty(value = "退款退货申请对象")
    @TableField(exist =false)
    private Integer oldStatus;
    @ApiModelProperty(value = "活动优惠金额")
    private BigDecimal activityDiscountAmont;
    @ApiModelProperty(value = "促销商品类型N.正品G.赠品R.换购商品")
    private String pmtProductType;
    @ApiModelProperty(value = "是否中西药品 1-是 0-否")
    private Integer isMedical;
    @ApiModelProperty(value = "渠道类型(1.微信H5 2.微信小程序 3.微信小程序分享 4.直播)")
    private Integer sourceChannelType;
    @ApiModelProperty(value = "渠道ID")
    private String sourceChannelId;
    @ApiModelProperty(value = "参与活动规则快照，json保存")
    private String activityRuleJson;

    @TableField(exist =false)
    @ApiModelProperty(value = "供货商商品规格id(云货架)")
    private Long supplierSpecId;
    @TableField(exist =false)
    @ApiModelProperty(value = "供货商商品编码(云货架)")
    private String goodsCode;
    @TableField(exist =false)
    @ApiModelProperty(value = "结算方式，1-固定成本(云货架)")
    private Integer billType;
    @TableField(exist =false)
    @ApiModelProperty(value = "结算值(云货架)")
    private String billValue;

    @TableField(exist =false)
    @ApiModelProperty(value = "是否参与支付有礼 1.是 0.否")
    private Integer isPaidGift = 0;

    @ApiModelProperty(value = "商品医保")
    private String medicalInsuranceCode;
    @ApiModelProperty(value = "投放渠道Code")
    private String deliveryChannelCode;
    @TableField(exist =false)
    @ApiModelProperty(value = "供应商编码")
    private String spCode;

    @ApiModelProperty(value = "价格组id")
    private String priceId;

    @ApiModelProperty(value = "用户分销佣金")
    private BigDecimal cusDistributeAmount;

    @ApiModelProperty(value = "疫情管控商品，0：否，1：是")
    private Integer epidemicRegistration;

    @ApiModelProperty(value = "定金")
    private BigDecimal depositPayAmount;

    @ApiModelProperty(value = "尾款")
    private BigDecimal finalPaymentAmount;

}