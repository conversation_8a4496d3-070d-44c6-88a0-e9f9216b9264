package cn.hydee.middle.business.order.yxtadapter.domain.oms.transform;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;


import java.math.BigDecimal;

@Data
public class YnOmsRefundOrderDetail {

    @ApiModelProperty(value = "退单编号")
    private String returnCode;

    @ApiModelProperty(value = "商品编码")
    private String goodsCode;

    @ApiModelProperty(value = "商品名")
    private String goodsName;

    @ApiModelProperty(value = "退货数量")
    private Integer returnCount;

    @ApiModelProperty(value = "折后价（实际价格）")
    private BigDecimal discountPrice;

    @ApiModelProperty(value = "退款商品金额")
    private BigDecimal goodsAmount;

    @ApiModelProperty(value = "商品价格")
    private BigDecimal goodsPrice;


}
