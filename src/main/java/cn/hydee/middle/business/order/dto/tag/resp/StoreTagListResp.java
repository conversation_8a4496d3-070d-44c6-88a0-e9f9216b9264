package cn.hydee.middle.business.order.dto.tag.resp;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * <AUTHOR>
 * @date 2024/8/9
 * @since 1.0
 */
@Data
public class StoreTagListResp {

    @ApiModelProperty("店铺Id")
    private String onlineStoreId;

    @ApiModelProperty("标签Id")
    private String tagId;

    @ApiModelProperty("标签名称")
    private String tagName;

    @ApiModelProperty("标签类型（SYSTEM_TAG：系统标签，USER_DEFINE_TAG：自定义标签）")
    private String tagType;

    @ApiModelProperty("创建时间")
    private String createTime;
}
