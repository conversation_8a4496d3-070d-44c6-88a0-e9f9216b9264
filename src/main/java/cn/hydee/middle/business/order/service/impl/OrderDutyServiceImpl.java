package cn.hydee.middle.business.order.service.impl;

import cn.hydee.erp.model.base.BaseErpResp;
import cn.hydee.erp.model.order.OrderDutyReq;
import cn.hydee.erp.model.order.OrderDutyResp;
import cn.hydee.middle.business.order.dto.req.OrderDutySearchReq;
import cn.hydee.middle.business.order.entity.OrderDuty;
import cn.hydee.middle.business.order.feign.MiddleIdClient;
import cn.hydee.middle.business.order.http.ErpHttpAdapter;
import cn.hydee.middle.business.order.mapper.OrderDutyMapper;
import cn.hydee.middle.business.order.service.ErpNetfailStatisticsService;
import cn.hydee.middle.business.order.service.OrderDutyService;
import cn.hydee.middle.business.order.service.suport.HuditConfigService;
import cn.hydee.middle.business.order.util.Const;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;
import org.springframework.util.StringUtils;

import java.util.ArrayList;
import java.util.List;
import java.util.stream.Collectors;

/**
 * 班次服务
 * <AUTHOR>
 * @version 1.0
 * @date 2020/8/05 17:18
 **/
@Service
@Slf4j
public class OrderDutyServiceImpl extends ServiceImpl<OrderDutyMapper,OrderDuty> implements OrderDutyService {

    private final ErpHttpAdapter erpHttpAdapter;
    private final HuditConfigService huditConfigService;
    private final MiddleIdClient middleIdClient;

    @Autowired
    private ErpNetfailStatisticsService erpNetfailStatisticsService;

    public OrderDutyServiceImpl(ErpHttpAdapter erpHttpAdapter,
                                HuditConfigService huditConfigService,
                                MiddleIdClient middleIdClient) {
        this.erpHttpAdapter = erpHttpAdapter;
        this.huditConfigService = huditConfigService;
        this.middleIdClient = middleIdClient;
    }

    @Override
    public void syncOrderDuty(String merCode) {
        OrderDutyReq dutyReq = new OrderDutyReq();
        BaseErpResp<List<OrderDutyResp>> erpResp = erpHttpAdapter.orderDuty(merCode,dutyReq);

        erpNetfailStatisticsService.writeCallStatisticsToRedis(merCode,erpResp);

        if (erpResp == null || !erpResp.isSuccess()) {
            log.error("商家：{} 调用ERP班次接口失败：{}",merCode,erpResp == null ? "数据为空" :
                    erpResp.getMsg());
            return;
        }
        if (CollectionUtils.isEmpty(erpResp.getData())) {
            log.error("商家：{} 调用ERP班次返回为空",merCode);
            return;
        }
        //
        List<Long> ids = middleIdClient.getId(erpResp.getData().size());
        if (CollectionUtils.isEmpty(ids)) {
            log.error("商家：{} 调用ERP班次请求发号器失败",merCode);
            return;
        }
        // 新增数据到数据库
        List<OrderDuty> dutyList = new ArrayList<>();
        erpResp.getData().forEach(duty-> {
            OrderDuty orderDuty = new OrderDuty();
            BeanUtils.copyProperties(duty,orderDuty);
            orderDuty.setMerCode(merCode);
            if (StringUtils.isEmpty(orderDuty.getStoreCode())) {
                orderDuty.setStoreCode(Const.ALL);
            }
            orderDuty.setId(ids.remove(0));
            dutyList.add(orderDuty);
        });
        // 删除以前的数据
        baseMapper.deleteByMerCode(merCode);
        saveBatch(dutyList);
    }

    @Override
    public List<OrderDuty> getOrderDutyForStore(OrderDutySearchReq searchReq) {
        List<OrderDuty> res = baseMapper.getOrderDutyForStore(searchReq);
        if (CollectionUtils.isEmpty(res)) {
            syncOrderDuty(searchReq.getMerCode());
        }
        res = baseMapper.getOrderDutyForStore(searchReq);
        List<OrderDuty> last = res.stream().filter(model->searchReq.getStoreCode().equals(model.getStoreCode()))
                .collect(Collectors.toList());
        if (CollectionUtils.isEmpty(last)){
            return res;
        }
;        return last;
    }
}
