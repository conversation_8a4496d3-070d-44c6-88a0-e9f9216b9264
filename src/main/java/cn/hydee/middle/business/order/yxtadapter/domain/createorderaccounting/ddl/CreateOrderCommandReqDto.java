package cn.hydee.middle.business.order.yxtadapter.domain.createorderaccounting.ddl;

import cn.hydee.middle.business.order.Enums.PlatformCodeEnum;
import com.google.common.base.Preconditions;
import java.util.List;
import lombok.Getter;

/**
 * Created by Intellij IDEA.
 *
 * <AUTHOR> Date:  2024/3/14
 */
@Getter
public class CreateOrderCommandReqDto {


  /**
   * 平台code  必传
   */
  private String thirdPlatformCode;

  /**
   * 第三方平台订单号  可选
   */
  private String thirdOrderNo;


  /**
   * 下单门店编码 必传
   */
  private String sourceOrganizationCode;

  /**
   * 发货门店编码  必传
   */
  private String organizationCode;

  /**
   * 修补类型 必传 1. repairInventory 2. repairCompensate
   */
  private String repairType;

  /**
   * 修补文本记录  必传
   */
  private String repairText;

  /**
   * 修补明细 必传
   */
  private List<CreateOrderDetailCommandReqDto> orderDetailInfoList;


  public void setThirdPlatformCode(String thirdPlatformCode) {
    Preconditions.checkArgument(thirdPlatformCode != null && !thirdPlatformCode.isEmpty(),
        "thirdPlatformCode is required");
    Preconditions.checkState(PlatformCodeEnum.getByCode(thirdPlatformCode) != null,
        "thirdPlatformCode is invalid");
    this.thirdPlatformCode = thirdPlatformCode;

  }

  public void setThirdOrderNo(String thirdOrderNo) {
    if (null == thirdOrderNo || (!thirdOrderNo.isEmpty() && thirdOrderNo.matches("^(B\\d+)$"))) {
      this.thirdOrderNo = thirdOrderNo;
    } else {
      throw new IllegalArgumentException("thirdOrderNo is  invalid");
    }
  }

  public void setSourceOrganizationCode(String sourceOrganizationCode) {
    Preconditions.checkArgument(sourceOrganizationCode != null && !sourceOrganizationCode.isEmpty()
            && sourceOrganizationCode.matches("^[A-Z][a-zA-Z0-9]*[0-9]$"),
        "sourceOrganizationCode is required");
    this.sourceOrganizationCode = sourceOrganizationCode;
  }

  public void setOrganizationCode(String organizationCode) {
    Preconditions.checkArgument(
        organizationCode != null && !organizationCode.isEmpty() && organizationCode.matches(
            "^[A-Z][a-zA-Z0-9]*[0-9]$"), "organizationCode is required");
    this.organizationCode = organizationCode;
  }

  public void setRepairType(String repairType) {
    Preconditions.checkArgument(repairType != null && !repairType.isEmpty(),
        "repairType is required");
    this.repairType = repairType;
  }

  public void setRepairText(String repairText) {
    Preconditions.checkArgument(repairText != null && !repairText.isEmpty(),
        "repairText is required");
    this.repairText = repairText;
  }

  public void setOrderDetailInfoList(
      List<CreateOrderDetailCommandReqDto> orderDetailInfoList) {
    Preconditions.checkArgument(orderDetailInfoList != null && !orderDetailInfoList.isEmpty(),
        "orderDetailInfoList is required");
    this.orderDetailInfoList = orderDetailInfoList;
  }
}
