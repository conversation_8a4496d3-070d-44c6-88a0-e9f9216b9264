package cn.hydee.middle.business.order.mapper;

import cn.hydee.middle.business.order.eventtracking.dto.entity.EventReport;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import org.apache.ibatis.annotations.Param;
import org.springframework.stereotype.Repository;

import java.util.List;

@Repository
public interface EventReportMapper extends BaseMapper<EventReport> {

    int batchSaveEventReport(@Param("params")List<EventReport> reportList);

}
