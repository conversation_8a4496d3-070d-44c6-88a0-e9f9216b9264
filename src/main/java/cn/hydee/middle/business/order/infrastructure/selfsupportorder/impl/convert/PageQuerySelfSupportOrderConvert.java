package cn.hydee.middle.business.order.infrastructure.selfsupportorder.impl.convert;

import cn.hydee.middle.business.order.dto.rsp.baseinfo.PageQuerySelfSupportOrderResDTO;
import cn.hydee.middle.business.order.entity.OrderInfo;
import java.util.ArrayList;
import java.util.List;
import java.util.stream.Collectors;

/**
 * Created by Intellij IDEA.
 *
 * <AUTHOR> Date:  2024/6/19
 */
public class PageQuerySelfSupportOrderConvert {

  public static List<PageQuerySelfSupportOrderResDTO> convertToSelfSupportOrderList(List<OrderInfo> orderList){
    if(null==orderList){
      return null;
    }
    return orderList.stream()
        .map(PageQuerySelfSupportOrderConvert::convertToSelfSupportOrder).collect(
            Collectors.toList());
  }


  public static PageQuerySelfSupportOrderResDTO convertToSelfSupportOrder(OrderInfo order){
    if(null==order){
      return null;
    }
    PageQuerySelfSupportOrderResDTO selfSupportOrder = new PageQuerySelfSupportOrderResDTO();
    selfSupportOrder.setMerCode(order.getMerCode());
    selfSupportOrder.setPlatformCode(order.getThirdPlatformCode());
    selfSupportOrder.setServiceMode(order.getServiceMode());
    selfSupportOrder.setOrderNo(order.getOrderNo());
    selfSupportOrder.setThirdOrderNo(order.getThirdOrderNo());
    selfSupportOrder.setOrderState(order.getOrderState());
    selfSupportOrder.setMemberNo(order.getMemberNo());
   return selfSupportOrder;
  }
}
