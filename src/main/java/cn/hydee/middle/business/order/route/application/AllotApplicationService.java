package cn.hydee.middle.business.order.route.application;

import cn.hydee.middle.business.order.route.application.command.AllotOrderCreateCommand;
import cn.hydee.middle.business.order.route.application.command.RouteAllotQueryCommand;
import cn.hydee.middle.business.order.route.application.command.RouteAllotUpdateCommand;
import cn.hydee.middle.business.order.route.application.representation.RouteAllotDetailRepresentationDto;
import cn.hydee.middle.business.order.route.application.representation.RouteAllotRepresentationDto;
import cn.hydee.middle.business.order.route.domain.enums.AllotStatusEnum;
import com.baomidou.mybatisplus.core.metadata.IPage;

/**
 * <AUTHOR>
 * @date 2024/04/01 14:36
 **/
public interface AllotApplicationService {

    /**
     * 查询调拨单列表
     *
     * @param command
     * @return IPage<RouteAllotRepresentationDto>
     */
    IPage<RouteAllotRepresentationDto> selectRouteAllotPage(RouteAllotQueryCommand command);

    /**
     * 查询调拨单详情
     *
     * @param allotId
     * @return IPage<RouteAllotRepresentationDto>
     */
    RouteAllotDetailRepresentationDto selectRouteAllotInfo(String allotId);


    /**
     * 创建正向调拨单
     * */
    void createOrderAllotOrder(AllotOrderCreateCommand command);

    /**
     * 更新调拨单信息
     *
     * @param command
     * @return void
     */
    void updateOrderAllotDetail(RouteAllotUpdateCommand command);


    /**
     * 修改调拨单状态
     * */
    void updateAllotState(Long allotId, AllotStatusEnum allotStatus);

}
