package cn.hydee.middle.business.order.service.impl;

import cn.hydee.middle.business.order.Enums.DsConstants;
import cn.hydee.middle.business.order.Enums.OrderStateEnum;
import cn.hydee.middle.business.order.Enums.SoundTypeEnum;
import cn.hydee.middle.business.order.client.ws.handler.PushMessageCommonHandler;
import cn.hydee.middle.business.order.dto.message.SoundMessage;
import cn.hydee.middle.business.order.dto.redis.CloudSoundContentRedisDto;
import cn.hydee.middle.business.order.dto.req.SoundContentTestReqDto;
import cn.hydee.middle.business.order.entity.CloudSoundContent;
import cn.hydee.middle.business.order.feign.MiddleIdClient;
import cn.hydee.middle.business.order.mapper.CloudSoundContentMapper;
import cn.hydee.middle.business.order.mapper.DbTableMapper;
import cn.hydee.middle.business.order.service.SoundService;
import cn.hydee.middle.business.order.util.RedisKeyUtil;
import cn.hydee.middle.business.order.util.redis.RedisZSetUtil;
import com.alibaba.fastjson.JSON;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import javax.annotation.Resource;
import java.util.*;
import java.util.stream.Collectors;

@Service
public class SoundServiceImpl extends ServiceImpl<CloudSoundContentMapper, CloudSoundContent> implements SoundService {

    @Resource
    private DbTableMapper dbTableMapper;

    @Resource
    private CloudSoundContentMapper soundContentMapper;

    @Resource
    private MiddleIdClient middleIdClient;

    @Resource
    private SoundService soundService;

    @Value("${deleteMaxRow:50000}")
    private Integer deleteMaxRow;
    @Override
    public void initTable(String merCode) {
        //【*********】打印声音分表合并
        String tableName = DsConstants.DB_TABLE_SOUND_CONTENT;

        int count = dbTableMapper.countTable(tableName);
        if (count > 0) {
            return;
        }

        soundContentMapper.createTable(merCode);
    }

    @Override
    public int updateStatus(String merCode, Integer status, List<Long> ids) {
        return soundContentMapper.updateStatus(merCode, status, ids);
    }

    @Override
    public int removeByCustom(String merCode, String storeCode, Date endTime) {
        return soundContentMapper.deleteByCustom(merCode, storeCode, endTime);
    }

    @Override
    public int saveTestSoundContent(String merCode, SoundContentTestReqDto reqDto) {
        reqDto.setMerCode(merCode);
        //根据门店声音配置设置播放次数
        int count = Optional.ofNullable(setSoundCountByConfig(reqDto, reqDto.getSoundType())).orElse(DsConstants.INTEGER_ONE);
        // 保存到数据库
        List<CloudSoundContent> soundContents = new ArrayList<>();
        int result = 0;
        //查询改机构门店之前新增的测试数据并删除 重新插入
        //test_0018_1
        String testMark = "test_" + reqDto.getOrganizationCode() + "_" + reqDto.getSoundType();
        List<CloudSoundContent> soundContentList = soundContentMapper.selectTestSound(merCode, reqDto.getOrganizationCode(), testMark);
        if (!CollectionUtils.isEmpty(soundContentList)) {
            // 记录id集合
            List<Long> ids = soundContentList.stream().map(CloudSoundContent::getId).collect(Collectors.toList());
            // 1、将记录的状态改为：已下载
            soundContentMapper.updateStatus(merCode, DsConstants.SOUNDSTATUS_DOWNLOADED, ids);
        }
        SoundMessage soundMessage = new SoundMessage();
        soundMessage.setType(reqDto.getSoundType());
        soundMessage.setContent(SoundTypeEnum.getItem(reqDto.getSoundType()).getContent()); // 语音消息内容
        //默认给一个状态，不能为空
        soundMessage.setOrderState(OrderStateEnum.CLOSED.getCode());
        soundMessage.setMerCode(merCode);
        soundMessage.setOrderNo(middleIdClient.getId(1).get(0));
        soundMessage.setOnlineStoreCode(reqDto.getOnlineStoreCode());
        soundMessage.setOrganizationCode(reqDto.getOrganizationCode());
        soundMessage.setThirdPlatformCode(null);
        soundMessage.setThirdOrderNo(null);
        soundMessage.setMark("test_" + reqDto.getOrganizationCode() + "_" + reqDto.getSoundType());

        for (int i = 1; i <= count; i++) {
            // 订单语音播报消息
            CloudSoundContent soundContent = new CloudSoundContent();
            soundContent.setMerCode(reqDto.getMerCode())
                    .setOrganizationCode(reqDto.getOrganizationCode())
                    .setOrderNo(666666L)
                    .setOrderState(OrderStateEnum.CLOSED.getCode())
                    .setType(reqDto.getSoundType())
                    .setBody(JSON.toJSONString(soundMessage))
                    .setStatus(0);
            // 保存到数据库
            soundService.save(soundContent);
            soundContents.add(soundContent);
        }

        // 声音优化：切换 websocket
        // 老版新版流量不区分
        if (PushMessageCommonHandler.needPrintSoundWebsocketFlag(merCode, reqDto.getOrganizationCode())) {
            PushMessageCommonHandler.pushVoiceData(merCode, reqDto.getOrganizationCode(), count, soundContents.get(0), Collections.emptyList());
        }
        // 声音优化：push redis zet
        List<CloudSoundContent> redisSoundContentList = new ArrayList<>();
        for (CloudSoundContent cloudSoundContent : soundContents) {
            CloudSoundContentRedisDto redisDto = new CloudSoundContentRedisDto();
            BeanUtils.copyProperties(cloudSoundContent, redisDto);
            cloudSoundContent.setStatus(DsConstants.SOUNDSTATUS_DOWNLOADED);
            redisSoundContentList.add(cloudSoundContent);
        }
        if (!CollectionUtils.isEmpty(redisSoundContentList)) {
            soundService.updateBatchById(redisSoundContentList);
        }
        return result;
    }

    @Override
    public int dropDataByTime(Date endTime) {
        Long maxId = soundContentMapper.getMaxIdByEndTime(endTime);
        if (maxId == null) {
            return 0;
        }
        int count = 0;
        while (true) {
            int deleteRows = soundContentMapper.dropDataByTime(maxId, deleteMaxRow);
            if (deleteRows <= 0) {
                break;
            }
            count += deleteRows;
        }
        return count;
    }

    /**
     * @param reqDto
     * @param soundType
     * @return
     */
    public int setSoundCountByConfig(SoundContentTestReqDto reqDto, Integer soundType) {
        //判断声音类型
        Integer count = 1;
        switch (SoundTypeEnum.getItem(soundType)) {
            // 新订单
            case NEW_ORDER:
                count = reqDto.getNewOrder();
                break;
            // 预约单
            case APPOINTMENT_ORDER:
                count = reqDto.getBookingOrder();
                break;
            // 退款单
            case REFUND_ORDER:
                count = reqDto.getRefundOrder();
                break;
            // 取消单
            case CANCEL_ORDER:
                count = reqDto.getCancelOrder();
                break;
            // 催单
            case URGE_ORDER:
                count = reqDto.getUrgeOrder();
                break;
            // 配送异常
            case EXCEPTION_ORDER:
                count = reqDto.getDeliveryException();
                break;
            // 打印机断开
            case PRINTER_DISCONNECT:
                count = reqDto.getPrinterDisconnect();
                break;
            // 网络断开
            case NET_CLOSED:
                count = reqDto.getNetDisconnect();
                break;
            // 骑手取消订单
            case RIDER_CANCEL:
                count = reqDto.getRiderCancelOrder();
                break;
            // 骑手异常
            case RIDER_EXCEPTION:
                count = reqDto.getRiderAbnormal();
                break;
            // 待审方单
            case UN_CHECK_ORDER:
                count = reqDto.getWaitTrialParty();
                break;
            case ORDER_NEED_SALE_BILL:
                count = reqDto.getNeedBillOrder();
                break;
            case REFUND_ORDER_NEED_SALE_BILL:
                count = reqDto.getNeedBillRefund();
                break;
            case ORDER_NEED_SALE_BILL_FAIL:
                count = reqDto.getNeedBillOrderFail();
                break;

            default:
                count = 1;
                break;
        }
        return count;
    }

}
