package cn.hydee.middle.business.order.route.interfaces;

import cn.hutool.core.bean.BeanUtil;
import cn.hydee.middle.business.order.route.application.DeliveryFeeRuleApplicationService;
import cn.hydee.middle.business.order.route.application.command.deliveryfeerule.DeliveryFeeRuleDeleteCommand;
import cn.hydee.middle.business.order.route.application.command.deliveryfeerule.DeliveryFeeRuleOperateCommand;
import cn.hydee.middle.business.order.route.application.command.deliveryfeerule.DeliveryFeeRuleQueryCommand;
import cn.hydee.middle.business.order.route.application.representation.DeliveryFeeRuleRepresentationDto;
import cn.hydee.middle.business.order.route.interfaces.request.deliveryfeerule.DeliveryFeeRuleOperateDto;
import cn.hydee.middle.business.order.route.interfaces.request.deliveryfeerule.DeliveryFeeRuleQueryDto;
import cn.hydee.starter.controller.AbstractController;
import cn.hydee.starter.dto.ResponseBase;
import com.baomidou.mybatisplus.core.metadata.IPage;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import javax.validation.Valid;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2024/3/22
 * @since 1.0
 */
@RestController
@RequestMapping(value = "/${api.version}/ds/deliveryfeerule")
@Api(tags = "配送费计算规则controller")
public class DeliveryFeeRuleController extends AbstractController {

    @Resource
    private DeliveryFeeRuleApplicationService deliveryFeeRuleApplicationService;

    @ApiOperation(value = "获取配送费计算规则列表")
    @PostMapping(value = "/list")
    public ResponseBase<IPage<DeliveryFeeRuleRepresentationDto>> getDeliveryFeeRulePage(@RequestBody DeliveryFeeRuleQueryDto deliveryFeeRuleQueryDto) {
        DeliveryFeeRuleQueryCommand queryCommand = BeanUtil.toBean(deliveryFeeRuleQueryDto, DeliveryFeeRuleQueryCommand.class);
        return generateSuccess(deliveryFeeRuleApplicationService.selectDeliveryFeeRulePage(queryCommand));
    }

    @ApiOperation(value = "获取配送费计算规则详情")
    @PostMapping(value = "/info")
    public ResponseBase<DeliveryFeeRuleRepresentationDto> getDeliveryFeeRuleInfo(@RequestParam("deliveryRuleId") String deliveryRuleId) {
        return generateSuccess(deliveryFeeRuleApplicationService.selectDeliveryFeeRulePageInfo(deliveryRuleId));
    }

    @ApiOperation(value = "添加配送费计算规则信息")
    @PostMapping(value = "/add")
    public ResponseBase<Void> addDeliveryFeeRule(@RequestHeader("userId") String userId, @Valid @RequestBody DeliveryFeeRuleOperateDto deliveryFeeRuleOperateDto) {
        DeliveryFeeRuleOperateCommand operateCommand = BeanUtil.toBean(deliveryFeeRuleOperateDto, DeliveryFeeRuleOperateCommand.class);
        operateCommand.setCreatedBy(userId);
        operateCommand.setUpdatedBy(userId);
        deliveryFeeRuleApplicationService.saveDeliveryFeeRule(operateCommand);
        return generateSuccess(null);
    }

    @ApiOperation(value = "删除配送费计算规则信息")
    @PostMapping(value = "/delete")
    public ResponseBase<Void> deleteDeliveryFeeRule(@RequestParam("deliveryRuleId") String deliveryRuleId, @RequestHeader("userId") String userId) {
        DeliveryFeeRuleDeleteCommand deleteCommand = new DeliveryFeeRuleDeleteCommand();
        deleteCommand.setDeliveryRuleId(deliveryRuleId);
        deleteCommand.setUserId(userId);
        deliveryFeeRuleApplicationService.deleteDeliveryFeeRule(deleteCommand);
        return generateSuccess(null);
    }

    @ApiOperation(value = "更新配送费计算规则信息")
    @PostMapping(value = "/update")
    public ResponseBase<Void> updateDeliveryFeeRule(@RequestHeader("userId") String userId, @Valid @RequestBody DeliveryFeeRuleOperateDto deliveryFeeRuleOperateDto) {
        DeliveryFeeRuleOperateCommand operateCommand = BeanUtil.toBean(deliveryFeeRuleOperateDto, DeliveryFeeRuleOperateCommand.class);
        operateCommand.setUpdatedBy(userId);
        deliveryFeeRuleApplicationService.updateDeliveryFeeRule(operateCommand);
        return generateSuccess(null);
    }
}
