package cn.hydee.middle.business.order.dto.req.merch;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotBlank;

@Data
public class DsChangeStoreReq {
    @ApiModelProperty("商户编码")
    @NotBlank(message = "商户编码不能为空")
    private String merCode;
    @ApiModelProperty("平台编码")
    @NotBlank(message = "平台编码不能为空")
    private String platformCode;
    @ApiModelProperty("网店编码")
    @NotBlank(message = "网店编码不能为空")
    private String clientCode;
    @ApiModelProperty("线上门店编码")
    @NotBlank(message = "线上门店编码不能为空")
    private String onlineStoreCode;
    @ApiModelProperty("线下门店编码")
    @NotBlank(message = "线下门店编码不能为空")
    private String stCode;
    @ApiModelProperty("操作人")
    @NotBlank(message = "操作人不能为空")
    private String userName;
}
