package cn.hydee.middle.business.order.service.impl;

import cn.hutool.core.util.ObjectUtil;
import cn.hydee.middle.business.order.Enums.DsConstants;
import cn.hydee.middle.business.order.Enums.DsErrorType;
import cn.hydee.middle.business.order.Enums.PlatformCodeEnum;
import cn.hydee.middle.business.order.Enums.UpdateLogTypeEnum;
import cn.hydee.middle.business.order.dto.req.OrderChangeSelfDeliveryUpdateDelayMinutesReqDto;
import cn.hydee.middle.business.order.dto.req.OrderChangeSelfDeliveryUpdateDeliveryReqDto;
import cn.hydee.middle.business.order.dto.req.OrderChangeSelfDeliveryUpdateReqDto;
import cn.hydee.middle.business.order.dto.req.OrderChangeSelfDeliveryUpdateStateReqDto;
import cn.hydee.middle.business.order.entity.DsOnlineStore;
import cn.hydee.middle.business.order.entity.DsOnlineStoreDelivery;
import cn.hydee.middle.business.order.entity.OrderChangeSelfDeliveryConfig;
import cn.hydee.middle.business.order.entity.UpdateLog;
import cn.hydee.middle.business.order.mapper.DsOnlineStoreRepo;
import cn.hydee.middle.business.order.mapper.OrderChangeSelfDeliveryConfigMapper;
import cn.hydee.middle.business.order.service.OrderChangeSelfDeliveryConfigService;
import cn.hydee.middle.business.order.service.UpdateLogService;
import cn.hydee.middle.business.order.service.baseinfo.DsOnlineStoreDeliveryService;
import cn.hydee.middle.business.order.storeautosync.domain.storeconfig.entity.StoreAutoConfigDTO;
import cn.hydee.middle.business.order.util.ExceptionUtil;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.Objects;

/**
 * 订单转自配送配置service 实现类
 *
 * <AUTHOR>
 * @date 2020/12/25 下午3:10
 */
@Service
public class OrderChangeSelfDeliveryConfigServiceImpl implements OrderChangeSelfDeliveryConfigService {

    @Autowired
    private OrderChangeSelfDeliveryConfigMapper orderChangeSelfDeliveryConfigMapper;
    @Autowired
    private DsOnlineStoreDeliveryService dsOnlineStoreDeliveryService;
    @Autowired
    private DsOnlineStoreRepo dsOnlineStoreRepo;
    @Autowired
    private UpdateLogService updateLogService;

    @Override
    public OrderChangeSelfDeliveryConfig getConfig(String merCode, String onlineStoreCode, String thirdPlatformCode, String clientCode) {
        QueryWrapper<DsOnlineStore> queryWrapper = new QueryWrapper<>();
        queryWrapper.lambda()
                .eq(DsOnlineStore::getMerCode, merCode)
                .eq(DsOnlineStore::getPlatformCode, thirdPlatformCode)
                .eq(DsOnlineStore::getOnlineClientCode, clientCode)
                .eq(DsOnlineStore::getOnlineStoreCode, onlineStoreCode);

        DsOnlineStore dsOnlineStore = dsOnlineStoreRepo.selectOne(queryWrapper);
        if (dsOnlineStore == null) {
            return null;
        }
        return getConfigByStoreId(merCode, dsOnlineStore.getId(), thirdPlatformCode);
    }

    @Override
    public boolean updateConfig(OrderChangeSelfDeliveryUpdateReqDto reqDto, String userId, String userName) {
        OrderChangeSelfDeliveryConfig config = getConfigByStoreId(reqDto.getMerCode(), reqDto.getOnlineStoreId(), reqDto.getThirdPlatformCode());
        int row = 0;
        if (reqDto instanceof OrderChangeSelfDeliveryUpdateDelayMinutesReqDto) {
            Integer delayMinutes = ((OrderChangeSelfDeliveryUpdateDelayMinutesReqDto) reqDto).getDelayMinutes();
            if (PlatformCodeEnum.JD_DAOJIA.getCode().equals(reqDto.getThirdPlatformCode())) {
                if (delayMinutes < 10) {
                    throw ExceptionUtil.getWarnException(DsErrorType.PARAM_VALID_ERROR);
                }
            } else if (PlatformCodeEnum.MEITUAN.getCode().equals(reqDto.getThirdPlatformCode())) {
                if (delayMinutes < 15) {
                    throw ExceptionUtil.getWarnException(DsErrorType.PARAM_VALID_ERROR);
                }
            } else if (PlatformCodeEnum.E_BAI.getCode().equals(reqDto.getThirdPlatformCode())) {
                if (delayMinutes > 10) {
                    throw ExceptionUtil.getWarnException(DsErrorType.PARAM_VALID_ERROR);
                }
            }
            if (config == null) {
                row = insert(reqDto);
            } else {
                row = orderChangeSelfDeliveryConfigMapper.updateDelayMinutes(config.getId(), delayMinutes);
            }
            //操作日志
            UpdateLog updateLog = new UpdateLog();
            updateLog.setType(UpdateLogTypeEnum.SELF_DELIVERY_CONFIG.getType());
            updateLog.setMerCode(reqDto.getMerCode());
            updateLog.setTargetKey(String.valueOf(reqDto.getOnlineStoreId()));
            updateLog.setUserId(userId);
            updateLog.setUserName(userName);
            StringBuilder sb = new StringBuilder("修改平台配送转自配送配置时机为：");
            sb.append(delayMinutes).append("分钟").append(DsConstants.LOG_SPLITER);
            sb.append("平台：").append(PlatformCodeEnum.getByCode(reqDto.getThirdPlatformCode()) == null ? "未知" : PlatformCodeEnum.getByCode(reqDto.getThirdPlatformCode()).getType()).append(DsConstants.LOG_SPLITER);
            updateLog.setDesc(sb.toString());
            updateLogService.insert(updateLog);
        } else if (reqDto instanceof OrderChangeSelfDeliveryUpdateDeliveryReqDto) {
            Long onlineStoreDeliveryId = ((OrderChangeSelfDeliveryUpdateDeliveryReqDto) reqDto).getOnlineStoreDeliveryId();
            DsOnlineStoreDelivery dsOnlineStoreDelivery = dsOnlineStoreDeliveryService.get(onlineStoreDeliveryId);
            if (dsOnlineStoreDelivery == null || DsConstants.INTEGER_ZERO.equals(dsOnlineStoreDelivery.getStatus())) {
                throw ExceptionUtil.getWarnException(DsErrorType.DELIVERY_TYPE_NOT_EXIST);
            }
            if (config == null) {
                row = insert(reqDto);
            } else {
                row = orderChangeSelfDeliveryConfigMapper.updateDelivery(config.getId(), onlineStoreDeliveryId);
            }
            //操作日志
            UpdateLog updateLog = new UpdateLog();
            updateLog.setType(UpdateLogTypeEnum.SELF_DELIVERY_CONFIG.getType());
            updateLog.setMerCode(reqDto.getMerCode());
            updateLog.setTargetKey(String.valueOf(reqDto.getOnlineStoreId()));
            updateLog.setUserId(userId);
            updateLog.setUserName(userName);
            StringBuilder sb = new StringBuilder("修改平台配送转自配送配置方式为：");
            sb.append(dsOnlineStoreDelivery.getDeliveryType()).append(DsConstants.LOG_SPLITER);
            sb.append("平台：").append(PlatformCodeEnum.getByCode(reqDto.getThirdPlatformCode()) == null ? "未知" : PlatformCodeEnum.getByCode(reqDto.getThirdPlatformCode()).getType()).append(DsConstants.LOG_SPLITER);
            updateLog.setDesc(sb.toString());
            updateLogService.insert(updateLog);
        } else if (reqDto instanceof OrderChangeSelfDeliveryUpdateStateReqDto) {
            if (config == null) {
                throw ExceptionUtil.getWarnException(DsErrorType.PARAM_VALID_ERROR);
            } else {
                if (config.getDelayMinutes() == null || config.getOnlineStoreDeliveryId() == null) {
                    throw ExceptionUtil.getWarnException(DsErrorType.PARAM_VALID_ERROR);
                }
                row = orderChangeSelfDeliveryConfigMapper.updateState(config.getId(), ((OrderChangeSelfDeliveryUpdateStateReqDto) reqDto).getState());
            }
            //操作日志
            UpdateLog updateLog = new UpdateLog();
            updateLog.setType(UpdateLogTypeEnum.SELF_DELIVERY_CONFIG.getType());
            updateLog.setMerCode(reqDto.getMerCode());
            updateLog.setTargetKey(String.valueOf(reqDto.getOnlineStoreId()));
            updateLog.setUserId(userId);
            updateLog.setUserName(userName);
            StringBuilder sb = new StringBuilder("修改平台配送转自配送是否启用为：");
            sb.append(DsConstants.INTEGER_ONE.equals(((OrderChangeSelfDeliveryUpdateStateReqDto) reqDto).getState()) ? "开启" : "关闭").append(DsConstants.LOG_SPLITER);
            sb.append("平台：").append(PlatformCodeEnum.getByCode(reqDto.getThirdPlatformCode()) == null ? "未知" : PlatformCodeEnum.getByCode(reqDto.getThirdPlatformCode()).getType()).append(DsConstants.LOG_SPLITER);
            updateLog.setDesc(sb.toString());
            updateLogService.insert(updateLog);
        }
        return row > 0;
    }

    @Override
    public OrderChangeSelfDeliveryConfig getConfig(String merCode, Long onlineStoreId, Long onlineStoreDeliveryId, Integer state) {
        return orderChangeSelfDeliveryConfigMapper.getConfigByDeliveryId(merCode, onlineStoreId, onlineStoreDeliveryId, state);
    }

    @Override
    public List<OrderChangeSelfDeliveryConfig> getConfigListByStoreId(String merCode, Long onlineStoreId) {
        DsOnlineStore dsOnlineStore = dsOnlineStoreRepo.selectById(onlineStoreId);
        if(dsOnlineStore == null){
            return null;
        }

        List<OrderChangeSelfDeliveryConfig> configList = new ArrayList<>();

        String platformCode = dsOnlineStore.getPlatformCode();
        OrderChangeSelfDeliveryConfig config = getConfigByStoreId(merCode,onlineStoreId,platformCode);
        if(config == null && (PlatformCodeEnum.MEITUAN.getCode().equals(platformCode)
                ||PlatformCodeEnum.E_BAI.getCode().equals(platformCode) || PlatformCodeEnum.JD_DAOJIA.getCode().equals(platformCode))){
            config = new OrderChangeSelfDeliveryConfig();
            config.setThirdPlatformCode(platformCode);
            config.setMerCode(merCode);
            config.setOnlineStoreId(onlineStoreId);
            config.setState(0);
        }

        if(Objects.nonNull(config)){
            configList.add(config);
        }
        return configList;
    }

    @Override
    public OrderChangeSelfDeliveryConfig getConfigByStoreId(String merCode, Long onlineStoreId, String thirdPlatformCode) {
        return orderChangeSelfDeliveryConfigMapper.getConfigByStoreId(merCode, onlineStoreId, thirdPlatformCode);
    }

    private int insert(OrderChangeSelfDeliveryUpdateReqDto reqDto) {
        OrderChangeSelfDeliveryConfig config = new OrderChangeSelfDeliveryConfig();
        BeanUtils.copyProperties(reqDto, config);
        config.setState(DsConstants.INTEGER_ZERO);
        config.setCreateTime(new Date());
        config.setModifyTime(new Date());
        return orderChangeSelfDeliveryConfigMapper.insert(config);
    }

    @Override
    public void orderChangeSelfDeliveryProcess(StoreAutoConfigDTO onlineStore, DsOnlineStoreDelivery dsOnlineStoreDelivery) {
        OrderChangeSelfDeliveryConfig orderChangeSelfDeliveryConfig = orderChangeSelfDeliveryConfigMapper.getConfigByStoreId(dsOnlineStoreDelivery.getMerCode(), dsOnlineStoreDelivery.getOnlineStoreId(), onlineStore.getPlatformCode());
        OrderChangeSelfDeliveryConfig updateSelfDeliveryConfig = new OrderChangeSelfDeliveryConfig();
        if (ObjectUtil.isNotNull(orderChangeSelfDeliveryConfig)) {
            updateSelfDeliveryConfig.setId(orderChangeSelfDeliveryConfig.getId());
            updateSelfDeliveryConfig.setCreateTime(orderChangeSelfDeliveryConfig.getCreateTime());
        }
        updateSelfDeliveryConfig.setMerCode(dsOnlineStoreDelivery.getMerCode());
        updateSelfDeliveryConfig.setThirdPlatformCode(onlineStore.getPlatformCode());
        updateSelfDeliveryConfig.setOnlineStoreCode(onlineStore.getOnlineStoreCode());
        updateSelfDeliveryConfig.setOnlineStoreId(onlineStore.getId());
        updateSelfDeliveryConfig.setOnlineStoreDeliveryId(dsOnlineStoreDelivery.getId());

        updateSelfDeliveryConfig.setState(1);
        switch (Objects.requireNonNull(PlatformCodeEnum.getByCode(onlineStore.getPlatformCode()))) {
            case JD_DAOJIA:
            case E_BAI:
                updateSelfDeliveryConfig.setDelayMinutes(10);
                break;
            case MEITUAN:
            case DOUDIAN:
                updateSelfDeliveryConfig.setDelayMinutes(15);
                break;
            default:
                updateSelfDeliveryConfig.setState(0);
                break;
        }
        updateSelfDeliveryConfig.setModifyTime(new Date());
        if (ObjectUtil.isNull(orderChangeSelfDeliveryConfig)) {
            orderChangeSelfDeliveryConfigMapper.insert(updateSelfDeliveryConfig);
        } else {
            orderChangeSelfDeliveryConfigMapper.updateById(updateSelfDeliveryConfig);
        }
    }
}
