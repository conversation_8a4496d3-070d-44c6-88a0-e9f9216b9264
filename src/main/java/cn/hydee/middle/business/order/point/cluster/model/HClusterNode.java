package cn.hydee.middle.business.order.point.cluster.model;

import java.io.Serializable;

import cn.hydee.middle.business.order.point.cluster.model.base.Aggregation;
import lombok.Data;

/**
 * 聚合节点模型定义。
 */
@Data
public class HClusterNode <T extends Aggregation> implements Serializable {
    
	private static final long serialVersionUID = -1138283393291327111L;

	/**
     * 是否聚合对象
     */
    private boolean isCluster = false;

    /**
     * 聚合点的ID
     */
    private int clusterId = -1;

    /**
     * 聚合点数量
     */
    private int pointCount = 0;

    /**
     * 聚合点的X坐标
     */
    private double x = 0;

    /**
     * Y坐标
     */
    private double y = 0;

    /**
     * 聚合点为单点时存储应用层的对象模型。
     */
    private T data = null;
}
