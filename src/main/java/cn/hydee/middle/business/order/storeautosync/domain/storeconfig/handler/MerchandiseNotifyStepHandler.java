package cn.hydee.middle.business.order.storeautosync.domain.storeconfig.handler;

import cn.hutool.core.collection.CollUtil;
import cn.hydee.middle.business.order.entity.DsOnlineStore;
import cn.hutool.core.util.ObjectUtil;
import cn.hydee.middle.business.order.service.rocket.MessageProducerService;
import cn.hydee.middle.business.order.storeautosync.constants.NotifyMerchandiseType;
import cn.hydee.middle.business.order.storeautosync.constants.StoreSyncStatus;
import cn.hydee.middle.business.order.storeautosync.domain.storeconfig.entity.StoreAutoConfigDTO;
import cn.hydee.middle.business.order.storeautosync.facade.storeconfig.entity.NotifyMerchandiseSyncReq;
import cn.hydee.middle.business.order.storeautosync.infrastructure.mysql.entity.BaseStepEntity;
import cn.hydee.middle.business.order.storeautosync.infrastructure.mysql.entity.DsOnlineStoreAutoConfigStep;
import cn.hydee.middle.business.order.util.redis.RedisStringUtil;
import com.google.common.collect.Lists;
import org.apache.rocketmq.client.producer.SendResult;
import org.apache.rocketmq.client.producer.SendStatus;
import org.jetbrains.annotations.NotNull;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.time.LocalDateTime;
import java.util.Collections;
import java.util.List;
import java.util.concurrent.TimeUnit;
import java.util.function.Consumer;
import java.util.function.Function;

import static cn.hydee.middle.business.order.storeautosync.domain.adapter.StoreSyncAdapter.onlineStore2ConfigEntityConvert;

@Component
public class MerchandiseNotifyStepHandler extends StoreConfigAbstractHandler {

    @Autowired
    private MessageProducerService messageProducerService;

    @Override
    public Consumer<DsOnlineStoreAutoConfigStep> handle(StoreAutoConfigDTO onlineStore) {
        LocalDateTime creatTime = LocalDateTime.now();
//        String key = onlineStore.getPlatformCode() + "_" + onlineStore.getOnlineStoreCode();
//        String value = RedisStringUtil.getValue(key);
        BaseStepEntity baseStepEntity = new BaseStepEntity();
//        if (ObjectUtil.isNotEmpty(value)) {
//            baseStepEntity.setStatus(StoreSyncStatus.SUCCESS.getCode());
//            baseStepEntity.setRemark("已通知过商品中台,消息ID:"+value);
//            baseStepEntity.setCreateTime(creatTime);
//            baseStepEntity.setFinishTime(LocalDateTime.now());
//            return step -> step.setMerchandiseNotifyStep(baseStepEntity);
//        }
        //通知商品中台
        NotifyMerchandiseSyncReq req = new NotifyMerchandiseSyncReq();
        req.setType(NotifyMerchandiseType.STOCK.getCode());
        req.setMerCode(onlineStore.getMerCode());
        req.setPlatforms(getPlatforms(onlineStore));
        SendResult sendResult = messageProducerService.sendClientConfigSyncMessage(req);
        if (SendStatus.SEND_OK.equals(sendResult.getSendStatus())) {
//            RedisStringUtil.setValue(key,sendResult.getMsgId(),24L, TimeUnit.HOURS);
            baseStepEntity.setStatus(StoreSyncStatus.SUCCESS.getCode());
            baseStepEntity.setRemark(sendResult.getMsgId());
        } else {
            baseStepEntity.setStatus(StoreSyncStatus.FAIL.getCode());
        }
        baseStepEntity.setCreateTime(creatTime);
        baseStepEntity.setFinishTime(LocalDateTime.now());
        return step -> step.setMerchandiseNotifyStep(baseStepEntity);
    }

    @NotNull
    private static List<NotifyMerchandiseSyncReq.Platform> getPlatforms(StoreAutoConfigDTO onlineStore) {
        NotifyMerchandiseSyncReq.Platform.Store store = new NotifyMerchandiseSyncReq.Platform.Store();
        store.setClientCode(onlineStore.getOnlineClientCode());
        store.setOnlineStoreCode(onlineStore.getOnlineStoreCode());
        List<NotifyMerchandiseSyncReq.Platform.Store> stores = Lists.newArrayList();
        stores.add(store);

        NotifyMerchandiseSyncReq.Platform platform = new NotifyMerchandiseSyncReq.Platform();
        platform.setPlatformCode(onlineStore.getPlatformCode());
        platform.setOnlineStores(stores);
        List<NotifyMerchandiseSyncReq.Platform> platforms = Lists.newArrayList();
        platforms.add(platform);
        return platforms;
    }

    @Override
    public Integer getOrder() {
        return 2;
    }

    @Override
    public Function<DsOnlineStoreAutoConfigStep, Boolean> dependencyHandlerResult() {
        return step -> step.getOrganizationStep().getStatus().equals(StoreSyncStatus.SUCCESS.getCode());
    }

    @Override
    public Consumer<DsOnlineStoreAutoConfigStep> callback(String errorMessage) {
        BaseStepEntity baseStepEntity = new BaseStepEntity();
        baseStepEntity.setStatus(StoreSyncStatus.FAIL.getCode());
        baseStepEntity.setFinishTime(LocalDateTime.now());
        baseStepEntity.setRemark(errorMessage);
        return step -> step.setMerchandiseNotifyStep(baseStepEntity);
    }

    @Override
    public Consumer<DsOnlineStoreAutoConfigStep> repair(DsOnlineStore onlineStore, DsOnlineStoreAutoConfigStep step) {
        List<StoreAutoConfigDTO> configStoreList = onlineStore2ConfigEntityConvert(Collections.singletonList(onlineStore), null);
        if (CollUtil.isEmpty(configStoreList)) {
            return null;
        }
        return handle(configStoreList.get(0));
    }
}
