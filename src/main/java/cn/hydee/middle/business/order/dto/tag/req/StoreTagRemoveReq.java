package cn.hydee.middle.business.order.dto.tag.req;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.NotNull;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2024/8/8
 * @since 1.0
 */
@Data
public class StoreTagRemoveReq {


    @ApiModelProperty("标签id列表")
    @NotEmpty(message = "标签id不可为空")
    private List<Long> ids;

    @ApiModelProperty("操作人Id")
    private String userId;
}
