package cn.hydee.middle.business.order.service.rabbit;

import cn.hydee.middle.business.order.Enums.DsConstants;
import cn.hydee.middle.business.order.configuration.RabbitConfig;
import cn.hydee.middle.business.order.dto.message.NetOrderRemindNotifyMessage;
import cn.hydee.middle.business.order.util.EnterpriseWeChatUtil;
import cn.hydee.starter.exception.WarnException;
import cn.hydee.starter.util.UUIDUtil;
import com.alibaba.fastjson.JSON;
import com.rabbitmq.client.Channel;
import lombok.extern.slf4j.Slf4j;
import org.springframework.amqp.core.Message;
import org.springframework.amqp.rabbit.annotation.RabbitHandler;
import org.springframework.amqp.rabbit.annotation.RabbitListener;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.io.IOException;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2020/1/8 9:56
 */
@Component
@Slf4j
public class OrderRemindConsumer {

    @Autowired
    private Processor processor;
    @Autowired
    private EnterpriseWeChatUtil enterpriseWeChatUtil;

    @RabbitListener(queues = RabbitConfig.REMIND_QUEUE)
    @RabbitHandler
    public void process(Message message, Channel channel) throws IOException {
        String queue = message.getMessageProperties().getConsumerQueue();
        String messageId = UUIDUtil.generateUuid();
        try{
            log.info("rabbit order remind message[{}],messageId:{}", new String(message.getBody()),messageId);
            processor.orderRemindNotifyProcess(message);
            //ACK,确认一条消息已经被消费
            channel.basicAck(message.getMessageProperties().getDeliveryTag(), false);
        } catch (Exception e){
            NetOrderRemindNotifyMessage body = JSON.parseObject(message.getBody(), NetOrderRemindNotifyMessage.class);
            log.error(String.format("OrderRemindConsumer process queue[%s], messageId:%s, thirdOrderNo:%s", queue, messageId, body.getOlorderno()), e);
            //NACK basicNack(deliveryTag, multiple, requeue)
            channel.basicNack(message.getMessageProperties().getDeliveryTag(), false, false);
            if (e instanceof WarnException) {
                //内部提示不打印异常
//                enterpriseWeChatUtil.sendMsg(DsConstants.ORDER_REMIND_ERROR,messageId,body.getOlorderno(),((WarnException) e).getTipMessage());
                return;
            }
            // 向企业微信发送messageId
            enterpriseWeChatUtil.sendMsg(DsConstants.ORDER_REMIND_ERROR,messageId,body.getOlorderno(),e.getMessage());
        }
    }
}
