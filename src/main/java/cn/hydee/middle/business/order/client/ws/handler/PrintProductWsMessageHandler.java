package cn.hydee.middle.business.order.client.ws.handler;

import cn.hydee.middle.business.order.client.ws.dto.Push2WsMessageDTO;
import cn.hydee.middle.business.order.service.rocket.MessageProducerService;
import cn.hydee.middle.business.order.util.JsonUtil;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.util.StringUtils;

/**
 * <AUTHOR>
 * @date 2021/03/23
 */
@Service("printProductWsMessageHandler")
@Slf4j
public class PrintProductWsMessageHandler extends BaseProductWsMessageHandler{

    @Autowired
    private MessageProducerService producer;

    @Override
    public boolean producer(Push2WsMessageDTO message, String validMsg) {
        validMsg = checkParam(message,validMsg);
        if(!StringUtils.isEmpty(validMsg)){
            log.warn("[{}]====>printProductWsMessageHandler message valid,cause:{},data:{}",message.getOmsId(), validMsg,
                    JsonUtil.object2Json(message));
            return Boolean.FALSE;
        }

        // 推送消息信息给ws
        producer.push2WsMessage(message);
        return Boolean.TRUE;
    }
}
