package cn.hydee.middle.business.order.dto.rsp.baseinfo;

import cn.hydee.middle.business.order.entity.OpenDeliveryPlatform;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.Date;

@Data
public class DsDeliveryAccountResDTO {

    @ApiModelProperty(value = "配送网店主键ID")
    private Long id;

    @ApiModelProperty(value = "商户编码")
    private String merCode;

    @ApiModelProperty(value = "配送平台标记")
    private String platformCode;

    @ApiModelProperty(value = "o2o平台名")
    private String platformName;

    @ApiModelProperty(value = "appid")
    private String appid;

    @ApiModelProperty(value = "app_secret")
    private String appSecret;

    @ApiModelProperty(value = "网店编码")
    private String clientCode;

    @ApiModelProperty(value = "网店名称")
    private String clientName;

    @ApiModelProperty(value = "创建时间")
    private Date createTime;

    @ApiModelProperty(value = "末次修改时间")
    private Date modifyTime;

    @ApiModelProperty(value = "授权时间")
    private Date authTime;

    @ApiModelProperty(value = "商家ID")
    private String sellerId;

    /**
     * 开放配送平台id，只有开放配送平台才有值
     * {@link OpenDeliveryPlatform#id}
     **/
    private Integer openDeliveryPlatformId;
}
