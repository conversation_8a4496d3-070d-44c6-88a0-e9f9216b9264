package cn.hydee.middle.business.order.v2.manager.base;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.date.LocalDateTimeUtil;
import cn.hutool.core.util.NumberUtil;
import cn.hydee.middle.business.order.domain.ResponseNet;
import cn.hydee.middle.business.order.entity.DsDeliveryStore;
import cn.hydee.middle.business.order.entity.OrderDeliveryAddress;
import cn.hydee.middle.business.order.entity.OrderDetail;
import cn.hydee.middle.business.order.route.db.mysql.model.DeliveryFeeRule;
import cn.hydee.middle.business.order.route.db.mysql.model.DeliveryFeeRuleAddprice;
import cn.hydee.middle.business.order.route.domain.enums.AddPriceTypeEnum;
import cn.hydee.middle.business.order.route.domain.external.CreateRiderOrderResp;
import cn.hydee.middle.business.order.route.domain.external.ReqCreateRiderOrderDto;
import cn.hydee.middle.business.order.route.domain.model.OrderInfoAllDomainCheckValueObject;
import com.alibaba.fastjson.JSONObject;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import cn.hydee.middle.business.order.entity.OrderDeliveryRecord;
import cn.hydee.middle.business.order.mapper.OrderDeliveryRecordMapper;
import lombok.extern.slf4j.Slf4j;

import java.math.BigDecimal;
import java.sql.Time;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.util.*;
import java.util.concurrent.Future;
import java.util.stream.Collectors;

/**
 * 基础订单发货管理类
 * <AUTHOR>
 * @since 2020/8/24 9:51
 */
@Slf4j
@Component
public class OrderDeliveryBaseManager {

    @Autowired
    private OrderDeliveryRecordMapper orderDeliveryRecordMapper;

    public OrderDeliveryRecord selectByOrderNo(Long orderNo){
        return orderDeliveryRecordMapper.selectByOrderNo(orderNo);
    }



    private Future<BigDecimal> getDeliveryFee(OrderInfoAllDomainCheckValueObject orderInfoDomain, DsDeliveryStore dsDeliveryStore, String sessionKey) {
        return threadPoolExecutor.submit(() -> {
            try {
                //先调用美团、达达等配送平台
                ResponseNet<CreateRiderOrderResp> responseNet = hemsClientService.riderPreCompare(buildReverseOrderAddReq(orderInfoDomain, dsDeliveryStore), dsDeliveryStore.getMerCode(), sessionKey, dsDeliveryStore.getPlatformCode(), dsDeliveryStore.getDeliveryClientCode());
                log.info("调用三方平台获取配送费,三方平台订单号:{},返回结果：{}",orderInfoDomain.getThirdOrderNo(), JSONObject.toJSONString(responseNet));
                if (responseNet.getCode() == 0) {
                    return NumberUtil.toBigDecimal(responseNet.getData().getTotalPrice());
                }
                //请求失败 调用腾讯计算距离
                return getDeliveryFeeByRuleSet(orderInfoDomain,dsDeliveryStore);
            }catch (Exception e){
                log.warn("调用三方平台获取配送费异常,返回默认值,三方平台订单号:{}",
                        orderInfoDomain.getThirdOrderNo(), e);
                return getDeliveryFeeByRuleSet(orderInfoDomain,dsDeliveryStore);
            }});

    }


    /**
     * 通过配置运费规则计算配送费
     * @param orderInfoDomain
     * @param dsDeliveryStore
     * @return
     */
    private BigDecimal getDeliveryFeeByRuleSet(OrderInfoAllDomainCheckValueObject orderInfoDomain,DsDeliveryStore dsDeliveryStore){
        List<DeliveryFeeRuleAddprice> deliveryFeeRuleAddprices = deliveryFeeRuleRepository.listFeeRuleAddpriceByCityAndType(dsDeliveryStore.getCity(), AddPriceTypeEnum.DISTANCE_ADD_PRICE.name());
        if (CollUtil.isEmpty(deliveryFeeRuleAddprices)) {
            return null;
        }
        List<Long> ruleIds = deliveryFeeRuleRepository.listFeeRuleByIdsAndPlatFormCode(deliveryFeeRuleAddprices.stream().map(DeliveryFeeRuleAddprice::getDeliveryRuleId).collect(Collectors.toList()), dsDeliveryStore.getPlatformCode())
                .stream().map(DeliveryFeeRule::getId).collect(Collectors.toList());
        if (CollUtil.isEmpty(ruleIds)) {
            return null;
        }
        deliveryFeeRuleAddprices = deliveryFeeRuleAddprices.stream().filter(x-> ruleIds.contains(x.getDeliveryRuleId())).sorted(Comparator.comparingLong(DeliveryFeeRuleAddprice::getId).reversed()).collect(Collectors.toList());
        //调用腾讯地图获取距离
        Double distance = txMapClientService.getDistance(dsDeliveryStore.getLatitude(), dsDeliveryStore.getLongitude(), orderInfoDomain.getReceiverLat(), orderInfoDomain.getReceiverLng());
        if (Objects.isNull(distance)) {
            return null;
        }
        DeliveryFeeRule deliveryFeeRule = deliveryFeeRuleRepository.getDeliveryFeeRuleById(deliveryFeeRuleAddprices.get(0).getDeliveryRuleId());
        //配送费 = 起步价+距离加价+时间加价
        BigDecimal deliveryFee = deliveryFeeRule.getStartPrice();
        if(distance > Double.parseDouble(deliveryFeeRuleAddprices.get(0).getEndRange())){
            //如果配送距离大于最大区间 直接取最大区间对应的加价
            deliveryFee = deliveryFee.add(deliveryFeeRuleAddprices.get(0).getAddPrice());
        }else {
            //配送距离在区间内 取区间对应的加价
            Optional<DeliveryFeeRuleAddprice> fitlerRuleOptional = deliveryFeeRuleAddprices.stream().filter(rule -> Double.parseDouble(rule.getStartRange()) <= distance && distance < Double.parseDouble(rule.getEndRange())).findFirst();
            if (fitlerRuleOptional.isPresent()) {
                deliveryFee = deliveryFee.add(fitlerRuleOptional.get().getAddPrice());
            }
        }
        List<DeliveryFeeRuleAddprice> deliveryFeeRuleTimes = deliveryFeeRuleRepository.listFeeRuleAddpriceByCityAndType(dsDeliveryStore.getCity(), AddPriceTypeEnum.TIME_ADD_PRICE.name());
        deliveryFeeRuleTimes = deliveryFeeRuleTimes.stream().filter(x-> ruleIds.contains(x.getDeliveryRuleId())).collect(Collectors.toList());
        Optional<DeliveryFeeRuleAddprice> anyTime = deliveryFeeRuleTimes.stream()
                .filter(time -> judgeTime(orderInfoDomain.getCreated(), Time.valueOf(time.getStartRange()), Time.valueOf(time.getEndRange())))
                .findAny();
        if (anyTime.isPresent()) {
            deliveryFee = deliveryFee.add(anyTime.get().getAddPrice());
        }
        log.info("通过心云配送费规则获取配送费,三方平台订单号:{},返回结果：{}",orderInfoDomain.getThirdOrderNo(),deliveryFee);
        //匹配导返回起步价加阶梯价
        return NumberUtil.toBigDecimal(deliveryFee.doubleValue());
    }


    /**
     * 请求参数组装
     * @param orderInfoDomain
     * @param dsDeliveryStore
     * @return
     */
    private ReqCreateRiderOrderDto buildReverseOrderAddReq(OrderInfoAllDomainCheckValueObject orderInfoDomain, DsDeliveryStore dsDeliveryStore) {
        ReqCreateRiderOrderDto riderOrderAddReq = new ReqCreateRiderOrderDto();
        ReqCreateRiderOrderDto.RiderOrderInfoDto riderOrder = new ReqCreateRiderOrderDto.RiderOrderInfoDto();
        riderOrder.setOrderid(orderInfoDomain.getOrderNo().toString());
        riderOrder.setDeliveryid(orderInfoDomain.getOrderNo());
        riderOrder.setShopid(dsDeliveryStore.getDeliveryStoreCode());
        riderOrder.setDeliveryServiceCode(dsDeliveryStore.getDefaultServiceCode());
        riderOrder.setOrderSource("101");
        riderOrder.setOrderWeight("0.1");
        riderOrder.setIsInsured(0);
        riderOrder.setOrderType(1);
        riderOrder.setIsPersonDirect(0);
        riderOrder.setOrderTotalAmount(orderInfoDomain.getOrderPayInfo().getTotalAmount().toString());
        riderOrderAddReq.setOrder(riderOrder);

        ReqCreateRiderOrderDto.RiderReceiverInfoDto receiver = new ReqCreateRiderOrderDto.RiderReceiverInfoDto();
        OrderDeliveryAddress orderDeliveryAddress = orderInfoDomain.getOrderDeliveryAddress();
        receiver.setAddress(orderDeliveryAddress.getAddress());
        receiver.setName(orderDeliveryAddress.getReceiverName());
        receiver.setLatitude(orderInfoDomain.getReceiverLat());
        receiver.setLongitude(orderInfoDomain.getReceiverLng());
        receiver.setPhone(orderDeliveryAddress.getReceiverTelephone());
        receiver.setPositionSource("3");
        riderOrderAddReq.setReceiver(receiver);

        ReqCreateRiderOrderDto.RiderTransportInfoDto transport = new ReqCreateRiderOrderDto.RiderTransportInfoDto();
        transport.setAddress(dsDeliveryStore.getAddress());
        transport.setLatitude(dsDeliveryStore.getLatitude());
        transport.setLongitude(dsDeliveryStore.getLongitude());
        transport.setName(dsDeliveryStore.getDeliveryStoreName());
        transport.setPositionSource("3");
        transport.setPhone(dsDeliveryStore.getContactPhone());
        riderOrderAddReq.setTransport(transport);

        List<ReqCreateRiderOrderDto.RiderItemInfoDto> itemList = new ArrayList<>();
        for (OrderDetail orderDetail : orderInfoDomain.getOrderDetailList()) {
            ReqCreateRiderOrderDto.RiderItemInfoDto item = new ReqCreateRiderOrderDto.RiderItemInfoDto();
            item.setItemName(orderDetail.getCommodityName());
            item.setItemId(orderDetail.getErpCode());
            item.setItemPrice(orderDetail.getPrice().toString());
            item.setItemQuantity(orderDetail.getCommodityCount());
            item.setItemSize(1);
            item.setItemRemark("");
            item.setItemActualPrice(orderDetail.getPrice().toString());
            itemList.add(item);
        }
        riderOrderAddReq.setItems(itemList);
        return riderOrderAddReq;
    }

    private boolean judgeTime(Date nowDate, Time startTime, Time endTime) {
        LocalDateTime localDateTime = Objects.isNull(nowDate)?LocalDateTime.now(): LocalDateTimeUtil.of(nowDate);
        LocalDateTime startDate = startTime.toLocalTime().atDate(LocalDate.now());
        LocalDateTime endDate = endTime.toLocalTime().atDate(LocalDate.now());
        return localDateTime.isAfter(startDate) && localDateTime.isBefore(endDate);
    }
}
