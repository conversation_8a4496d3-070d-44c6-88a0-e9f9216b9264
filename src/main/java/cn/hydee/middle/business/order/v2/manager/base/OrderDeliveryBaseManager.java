package cn.hydee.middle.business.order.v2.manager.base;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.date.LocalDateTimeUtil;
import cn.hutool.core.util.NumberUtil;
import cn.hydee.middle.business.order.Enums.DsConstants;
import cn.hydee.middle.business.order.Enums.DsDeliveryType;
import cn.hydee.middle.business.order.domain.ResponseNet;
import cn.hydee.middle.business.order.dto.rsp.OnlineStoreInfoRspDto;
import cn.hydee.middle.business.order.entity.*;
import cn.hydee.middle.business.order.mapper.*;
import cn.hydee.middle.business.order.route.db.mysql.model.DeliveryFeeRule;
import cn.hydee.middle.business.order.route.db.mysql.model.DeliveryFeeRuleAddprice;
import cn.hydee.middle.business.order.route.domain.enums.AddPriceTypeEnum;
import cn.hydee.middle.business.order.route.domain.external.*;
import cn.hydee.middle.business.order.route.domain.repository.DeliveryFeeRuleRepository;
import cn.hydee.middle.business.order.v2.manager.DsOnlineStoreManager;
import cn.hydee.middle.business.order.v3.facade.NewOrderParamFacade;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.yxt.middle.baseinfo.res.store.StoreInfoDataResDTO;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.sql.Time;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.util.*;
import java.util.concurrent.Future;
import java.util.concurrent.ThreadPoolExecutor;
import java.util.stream.Collectors;

import static cn.hydee.middle.business.order.Enums.DsConstants.MER_CODE_YXT;

/**
 * 基础订单发货管理类
 *
 * <AUTHOR>
 * @since 2020/8/24 9:51
 */
@Slf4j
@Component
public class OrderDeliveryBaseManager {

    @Resource
    private OrderDeliveryRecordMapper orderDeliveryRecordMapper;


    @Resource
    private DeliveryFeeRuleRepository deliveryFeeRuleRepository;

    @Resource
    private TxMapClientService txMapClientService;

    @Resource
    private HemsClientService hemsClientService;


    @Resource
    private DsOnlineStoreRepo dsOnlineStoreRepo;

    @Resource
    private DsOnlineStoreDeliveryRepo dsOnlineStoreDeliveryRepo;

    @Resource
    private DsDeliveryStoreRepo dsDeliveryStoreRepo;

    @Resource
    private RouteClientService routeClientService;

    @Resource
    private DsMerchantGroupInfoRepo merchantGroupInfoRepo;

    @Resource
    private DsOnlineStoreManager dsOnlineStoreManager;

    @Resource
    @Qualifier("bestDeliveryFeeThreadPool")
    private ThreadPoolExecutor threadPoolExecutor;


    public OrderDeliveryRecord selectByOrderNo(Long orderNo) {
        return orderDeliveryRecordMapper.selectByOrderNo(orderNo);
    }








    private Map<String, BigDecimal> mapDeliveryFee(NewOrderParamFacade facade){
        List<DsDeliveryStore> deliveryStores  = getDeliveryStores(facade.getOrderInfo());
        if(CollUtil.isEmpty(deliveryStores)){
            return Maps.newHashMap();
        }
        DsMerchantGroupInfo merchantGroupInfo = merchantGroupInfoRepo.selectOne(new LambdaUpdateWrapper<DsMerchantGroupInfo>().eq(DsMerchantGroupInfo::getMerCode,MER_CODE_YXT));
        if(Objects.isNull(merchantGroupInfo) || Objects.isNull(merchantGroupInfo.getSessionKey())){
            return Maps.newHashMap();
        }
        //key:配送方式  value:配送费
        Map<String,Future<BigDecimal>> deliveryFeeFutureMap = new HashMap<>();
        deliveryStores.parallelStream().forEach(deliveryStore ->
                deliveryFeeFutureMap.put(deliveryStore.getPlatformCode(), getDeliveryFee(facade, deliveryStore, merchantGroupInfo.getSessionKey())));
        //转成Map<String,BigDecimal>
        Map<String,BigDecimal> deliveryFeeMap = new HashMap<>();
        deliveryFeeFutureMap.forEach((key, value)->{
            try {
                deliveryFeeMap.put(key, Objects.isNull(value)?null:value.get());
            }catch (Exception e){
                deliveryFeeMap.put(key, null);
            }
        });
        log.info("获取门店配送获取最优的配送方式,三方平台订单号:{},结果:{}",facade.getOrderInfo().getThirdOrderNo(), JSON.toJSONString(deliveryFeeMap));
        return deliveryFeeMap;
    }



    public List<DsDeliveryStore> getDeliveryStores( OrderInfo orderInfo) {
        OnlineStoreInfoRspDto onlineStoreInfoRspDto = dsOnlineStoreManager
                .checkValidityAndGet(orderInfo.getMerCode(), orderInfo.getThirdPlatformCode(), orderInfo.getClientCode(), orderInfo.getOnlineStoreCode());

        List<DsOnlineStoreDelivery> dsOnlineStoreDeliveries = dsOnlineStoreDeliveryRepo.selectList(
                new LambdaQueryWrapper<DsOnlineStoreDelivery>()
                        .eq(DsOnlineStoreDelivery::getOnlineStoreId, onlineStoreInfoRspDto.getId())
                        .eq(DsOnlineStoreDelivery::getStatus, DsConstants.INTEGER_ONE)
                        .notIn(DsOnlineStoreDelivery::getDeliveryType, Lists.newArrayList(DsDeliveryType.YGZS.getPlatformName(), DsDeliveryType.DDZT.getPlatformName(), DsDeliveryType.KDPS.getPlatformName(), DsDeliveryType.SJKDPS.getPlatformName())));
        if (CollUtil.isEmpty(dsOnlineStoreDeliveries)) {
            return Lists.newArrayList();
        }
        List<Long> deliveryStoreIds = dsOnlineStoreDeliveries.stream().map(DsOnlineStoreDelivery::getDeliveryStoreId).filter(Objects::nonNull).collect(Collectors.toList());
        List<DsDeliveryStore> dsDeliveryStores = dsDeliveryStoreRepo.listByIdsAndPlatformCode(MER_CODE_YXT, deliveryStoreIds, null);
        if (CollUtil.isEmpty(dsDeliveryStores)) {
            return Lists.newArrayList();
        }
        StoreInfoDataResDTO storeInfo = routeClientService.getStoreInfo(onlineStoreInfoRspDto.getOrganizationCode());
        if (Objects.nonNull(storeInfo)) {
            orderInfoDomain.setBizUnitOrgResDTOS(storeInfo.getBizUnitOrgList().stream().filter(bizUnitOrg -> bizUnitOrg.getLayer().equals(DsConstants.STRING_ONE)).collect(Collectors.toList()));
            dsDeliveryStores.forEach(e -> {
                e.setLongitude(storeInfo.getLongitude());
                e.setLatitude(storeInfo.getLatitude());
                e.setCity(storeInfo.getCity());
            });
        }
        return dsDeliveryStores;
    }


    /**
     * 异步获取配送费
     *
     * @param facade          订单信息领域对象
     * @param dsDeliveryStore 配送门店信息
     * @param sessionKey      会话密钥
     * @return 配送费用
     */
    private Future<BigDecimal> getDeliveryFee(NewOrderParamFacade facade, DsDeliveryStore dsDeliveryStore, String sessionKey) {
        return threadPoolExecutor.submit(() -> {
            String thirdOrderNo = facade.getOrderInfo().getThirdOrderNo();
            try {
                // 构建请求参数
                ReqCreateRiderOrderDto reqDto = buildReverseOrderAddReq(facade, dsDeliveryStore);
                String merCode = dsDeliveryStore.getMerCode();
                String platformCode = dsDeliveryStore.getPlatformCode();
                String clientCode = dsDeliveryStore.getDeliveryClientCode();

                // 先调用美团、达达等配送平台
                ResponseNet<CreateRiderOrderResp> responseNet = hemsClientService.riderPreCompare(
                        reqDto, merCode, sessionKey, platformCode, clientCode);

                log.info("调用三方平台获取配送费,三方平台订单号:{},返回结果：{}", thirdOrderNo, JSONObject.toJSONString(responseNet));

                // 检查响应是否成功
                if (responseNet.getCode() == 0 && responseNet.getData() != null) {
                    return NumberUtil.toBigDecimal(responseNet.getData().getTotalPrice());
                }

                // 请求失败，调用腾讯地图计算距离并根据规则计算配送费
                return getDeliveryFeeByRuleSet(facade.getOrderInfo(), dsDeliveryStore);
            } catch (Exception e) {
                log.warn("调用三方平台获取配送费异常,返回默认值,三方平台订单号:{}", thirdOrderNo, e);
                // 异常情况下，调用腾讯地图计算距离并根据规则计算配送费
                return getDeliveryFeeByRuleSet(facade.getOrderInfo(), dsDeliveryStore);
            }
        });
    }


    /**
     * 通过配置运费规则计算配送费
     *
     * @param orderInfo       订单信息领域对象
     * @param dsDeliveryStore 配送门店信息
     * @return 计算得出的配送费
     */
    private BigDecimal getDeliveryFeeByRuleSet(OrderInfo orderInfo, DsDeliveryStore dsDeliveryStore) {
        String thirdOrderNo = orderInfo.getThirdOrderNo();
        String city = dsDeliveryStore.getCity();
        String platformCode = dsDeliveryStore.getPlatformCode();

        // 1. 获取距离加价规则
        List<DeliveryFeeRuleAddprice> deliveryFeeRuleAddprices = deliveryFeeRuleRepository.listFeeRuleAddpriceByCityAndType(
                city, AddPriceTypeEnum.DISTANCE_ADD_PRICE.name());
        if (CollUtil.isEmpty(deliveryFeeRuleAddprices)) {
            log.warn("未找到城市[{}]的距离加价规则，无法计算配送费", city);
            return null;
        }

        // 2. 获取规则ID列表并根据平台代码筛选
        List<Long> ids = deliveryFeeRuleAddprices.stream()
                .map(DeliveryFeeRuleAddprice::getDeliveryRuleId)
                .collect(Collectors.toList());
        List<Long> ruleIds = deliveryFeeRuleRepository.listFeeRuleByIdsAndPlatFormCode(ids, platformCode)
                .stream()
                .map(DeliveryFeeRule::getId)
                .collect(Collectors.toList());
        if (CollUtil.isEmpty(ruleIds)) {
            log.warn("未找到平台[{}]的配送费规则，无法计算配送费", platformCode);
            return null;
        }

        // 3. 筛选并排序距离加价规则
        deliveryFeeRuleAddprices = deliveryFeeRuleAddprices.stream()
                .filter(x -> ruleIds.contains(x.getDeliveryRuleId()))
                .sorted(Comparator.comparingLong(DeliveryFeeRuleAddprice::getId).reversed())
                .collect(Collectors.toList());

        // 4. 调用腾讯地图获取距离
        Double distance = txMapClientService.getDistance(
                dsDeliveryStore.getLatitude(),
                dsDeliveryStore.getLongitude(),
                orderInfo.getReceiverLat(),
                orderInfo.getReceiverLng());
        if (Objects.isNull(distance)) {
            log.warn("腾讯地图获取距离失败，无法计算配送费");
            return null;
        }

        // 5. 获取配送费规则并计算基础配送费
        DeliveryFeeRule deliveryFeeRule = deliveryFeeRuleRepository.getDeliveryFeeRuleById(
                deliveryFeeRuleAddprices.get(0).getDeliveryRuleId());

        // 配送费 = 起步价 + 距离加价 + 时间加价
        BigDecimal deliveryFee = deliveryFeeRule.getStartPrice();

        // 6. 计算距离加价
        if (distance > Double.parseDouble(deliveryFeeRuleAddprices.get(0).getEndRange())) {
            // 如果配送距离大于最大区间，直接取最大区间对应的加价
            deliveryFee = deliveryFee.add(deliveryFeeRuleAddprices.get(0).getAddPrice());
        } else {
            // 配送距离在区间内，取区间对应的加价
            Optional<DeliveryFeeRuleAddprice> fitlerRuleOptional = deliveryFeeRuleAddprices.stream()
                    .filter(rule ->
                            Double.parseDouble(rule.getStartRange()) <= distance &&
                                    distance < Double.parseDouble(rule.getEndRange()))
                    .findFirst();
            if (fitlerRuleOptional.isPresent()) {
                deliveryFee = deliveryFee.add(fitlerRuleOptional.get().getAddPrice());
            }
        }

        // 7. 计算时间加价
        List<DeliveryFeeRuleAddprice> deliveryFeeRuleTimes = deliveryFeeRuleRepository.listFeeRuleAddpriceByCityAndType(
                city, AddPriceTypeEnum.TIME_ADD_PRICE.name());
        deliveryFeeRuleTimes = deliveryFeeRuleTimes.stream()
                .filter(x -> ruleIds.contains(x.getDeliveryRuleId()))
                .collect(Collectors.toList());

        Optional<DeliveryFeeRuleAddprice> anyTime = deliveryFeeRuleTimes.stream()
                .filter(time -> judgeTime(
                        orderInfo.getCreated(),
                        Time.valueOf(time.getStartRange()),
                        Time.valueOf(time.getEndRange())))
                .findAny();
        if (anyTime.isPresent()) {
            deliveryFee = deliveryFee.add(anyTime.get().getAddPrice());
        }

        log.info("通过心云配送费规则获取配送费,三方平台订单号:{},返回结果：{}", thirdOrderNo, deliveryFee);

        // 返回起步价加阶梯价
        return NumberUtil.toBigDecimal(deliveryFee.doubleValue());
    }


    /**
     * 请求参数组装
     *
     * @param facade
     * @param dsDeliveryStore
     * @return
     */
    private ReqCreateRiderOrderDto buildReverseOrderAddReq(NewOrderParamFacade facade, DsDeliveryStore dsDeliveryStore) {
        OrderPayInfo orderPayInfo = facade.getOrderPayInfo();
        OrderInfo orderInfo = facade.getOrderInfo();

        ReqCreateRiderOrderDto riderOrderAddReq = new ReqCreateRiderOrderDto();

        ReqCreateRiderOrderDto.RiderOrderInfoDto riderOrder = getRiderOrderInfoDto(dsDeliveryStore, orderInfo, orderPayInfo);
        riderOrderAddReq.setOrder(riderOrder);

        ReqCreateRiderOrderDto.RiderReceiverInfoDto receiver = getRiderReceiverInfoDto(facade, orderInfo);
        riderOrderAddReq.setReceiver(receiver);

        ReqCreateRiderOrderDto.RiderTransportInfoDto transport = getRiderTransportInfoDto(dsDeliveryStore);
        riderOrderAddReq.setTransport(transport);

        List<ReqCreateRiderOrderDto.RiderItemInfoDto> itemList = new ArrayList<>();
        for (OrderDetail orderDetail : facade.getOrderDetailList()) {
            ReqCreateRiderOrderDto.RiderItemInfoDto item = getRiderItemInfoDto(orderDetail);
            itemList.add(item);
        }
        riderOrderAddReq.setItems(itemList);
        return riderOrderAddReq;
    }

    private static ReqCreateRiderOrderDto.RiderItemInfoDto getRiderItemInfoDto(OrderDetail orderDetail) {
        ReqCreateRiderOrderDto.RiderItemInfoDto item = new ReqCreateRiderOrderDto.RiderItemInfoDto();
        item.setItemName(orderDetail.getCommodityName());
        item.setItemId(orderDetail.getErpCode());
        item.setItemPrice(orderDetail.getPrice().toString());
        item.setItemQuantity(orderDetail.getCommodityCount());
        item.setItemSize(1);
        item.setItemRemark("");
        item.setItemActualPrice(orderDetail.getPrice().toString());
        return item;
    }

    private static ReqCreateRiderOrderDto.RiderOrderInfoDto getRiderOrderInfoDto(DsDeliveryStore dsDeliveryStore, OrderInfo orderInfo, OrderPayInfo orderPayInfo) {
        ReqCreateRiderOrderDto.RiderOrderInfoDto riderOrder = new ReqCreateRiderOrderDto.RiderOrderInfoDto();
        riderOrder.setOrderid(orderInfo.getOrderNo().toString());
        riderOrder.setDeliveryid(orderInfo.getOrderNo());
        riderOrder.setShopid(dsDeliveryStore.getDeliveryStoreCode());
        riderOrder.setDeliveryServiceCode(dsDeliveryStore.getDefaultServiceCode());
        riderOrder.setOrderSource("101");
        riderOrder.setOrderWeight("0.1");
        riderOrder.setIsInsured(0);
        riderOrder.setOrderType(1);
        riderOrder.setIsPersonDirect(0);
        riderOrder.setOrderTotalAmount(orderPayInfo.getTotalAmount().toString());
        return riderOrder;
    }

    private static ReqCreateRiderOrderDto.RiderTransportInfoDto getRiderTransportInfoDto(DsDeliveryStore dsDeliveryStore) {
        ReqCreateRiderOrderDto.RiderTransportInfoDto transport = new ReqCreateRiderOrderDto.RiderTransportInfoDto();
        transport.setAddress(dsDeliveryStore.getAddress());
        transport.setLatitude(dsDeliveryStore.getLatitude());
        transport.setLongitude(dsDeliveryStore.getLongitude());
        transport.setName(dsDeliveryStore.getDeliveryStoreName());
        transport.setPositionSource("3");
        transport.setPhone(dsDeliveryStore.getContactPhone());
        return transport;
    }

    private static ReqCreateRiderOrderDto.RiderReceiverInfoDto getRiderReceiverInfoDto(NewOrderParamFacade facade, OrderInfo orderInfo) {
        ReqCreateRiderOrderDto.RiderReceiverInfoDto receiver = new ReqCreateRiderOrderDto.RiderReceiverInfoDto();
        OrderDeliveryAddress orderDeliveryAddress = facade.getOrderDeliveryAddress();
        receiver.setAddress(orderDeliveryAddress.getAddress());
        receiver.setName(orderDeliveryAddress.getReceiverName());
        receiver.setLatitude(orderInfo.getReceiverLat());
        receiver.setLongitude(orderInfo.getReceiverLng());
        receiver.setPhone(orderDeliveryAddress.getReceiverTelephone());
        receiver.setPositionSource("3");
        return receiver;
    }

    /**
     * 判断当前时间是否在指定的时间范围内
     *
     * @param nowDate   当前日期时间，如果为null则使用系统当前时间
     * @param startTime 开始时间
     * @param endTime   结束时间
     * @return 如果当前时间在指定范围内返回true，否则返回false
     */
    private boolean judgeTime(Date nowDate, Time startTime, Time endTime) {
        // 获取当前时间，如果未提供则使用系统当前时间
        LocalDateTime localDateTime = Objects.isNull(nowDate)
                ? LocalDateTime.now()
                : LocalDateTimeUtil.of(nowDate);

        // 将开始时间和结束时间转换为当天的LocalDateTime
        LocalDateTime startDate = startTime.toLocalTime().atDate(LocalDate.now());
        LocalDateTime endDate = endTime.toLocalTime().atDate(LocalDate.now());

        // 处理跨天的情况（如22:00-06:00）
        if (startTime.compareTo(endTime) > 0) {
            endDate = endDate.plusDays(1);
        }

        // 判断当前时间是否在时间范围内
        return localDateTime.isAfter(startDate) && localDateTime.isBefore(endDate);
    }
}
