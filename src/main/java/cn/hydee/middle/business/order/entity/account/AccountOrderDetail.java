package cn.hydee.middle.business.order.entity.account;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableLogic;
import com.baomidou.mybatisplus.annotation.TableName;
import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;
import lombok.Data;

/**
 * 销售单下账明细表
 * @TableName account_order_detail
 */
@TableName(value ="account_order_detail")
@Data
public class AccountOrderDetail implements Serializable {
    /**
     *
     */
    @TableId(type = IdType.AUTO)
    private Long id;

    /**
     * 系统订单号
     */
    private Long orderNo;

    /**
     * 商品编码
     */
    private String erpCode;

    /**
     * 商品名称
     */
    private String goodsName;

    /**
     * 商品类型, 普通商品- NORMAL、赠品-GIFT
     */
    private String goodsType;

    /**
     * 生产批号
     */
    private String batchNo;

    /**
     * 商品数量
     */
    private Integer goodsCount;

    /**
     * 商品单价
     */
    private BigDecimal price;

    /**
     * 商品进价
     */
    private BigDecimal purchasePrice;

    /**
     * 商品金额
     */
    private BigDecimal goodsAmount;

    /**
     * 分摊金额
     */
    private BigDecimal shareAmount;

    /**
     * 下账单价
     */
    private BigDecimal billPrice;

    /**
     * 创建时间
     */
    private Date createdTime;

    /**
     * 更新时间
     */
    private Date updatedTime;

    /**
     * 是否删除 0-未删除 时间戳-已删除
     */
    @TableLogic(value = "0")
    private Long deleted;

    /**
     *  数据版本，每次update+1
     */
    private Long version;

    @TableField(exist = false)
    private static final long serialVersionUID = 1L;


    /**
     * 获取商品下账总金额
     * */
    public BigDecimal obtainActualAmount() {
        return this.getBillPrice().multiply(new BigDecimal(this.getGoodsCount()));
    }

}