package cn.hydee.middle.business.order.yxtadapter.domainservice.assignment;

import cn.hydee.middle.business.order.yxtadapter.constant.AssignmentBizType;
import cn.hydee.middle.business.order.yxtadapter.domain.assignment.Assignment;
import cn.hydee.middle.business.order.yxtadapter.domain.assignment.JobStatus;
import cn.hydee.middle.business.order.yxtadapter.domain.middle.AssignmentDTO;

import java.util.Date;
import java.util.List;
import cn.hydee.middle.business.order.yxtadapter.infrastructure.db.mysql.model.AssignmentEngineAssignment;

/**
 * Created by Intellij IDEA.
 *
 * <AUTHOR>
 * Date:  2023/9/2
 */
public interface AssignmentEngine {
    /**
     * 执行单步骤作业
     *
     * @param assignment 作业上下文
     * @param task    作业fn
     */
    void executeAssignment(final Assignment assignment, final Task task);

    /**
     * 批量处理，返回失败的任务集合
     * */
    List<Assignment> executeBatchAssignment(List<Assignment> assignmentList,final BatchTask task);


    boolean createAssignment(AssignmentBizType assignmentBizType, String ukId, String reqContent, String resContent);

    void createAssignmentBatch(AssignmentBizType assignmentBizType, List<AssignmentDTO> assignmentReqList);

    boolean createAssignmentMissExist(AssignmentBizType assignmentBizType, String ukId, String reqContent, String resContent);

    void createAssignmentByNewRequires(AssignmentBizType assignmentBizType, String ukId, String reqContent, String resContent);

    boolean createAssignmentMissExistDate(AssignmentBizType assignmentBizType, String ukId, String reqContent, String resContent, Date date);

    boolean batchCreateAssignment(List<AssignmentEngineAssignment> list);


    AssignmentEngineAssignment selectAssignment(AssignmentBizType assignmentBizType, String businessId);




    @FunctionalInterface
    interface Task {
        /**
         * 执行内容
         *
         * @param jobStatus 作业状态控制
         * @return 接下来的作业
         */
        Assignment doJob(JobStatus jobStatus);
    }

    @FunctionalInterface
    interface NoResultTask {
        void doJob(JobStatus jobStatus);
    }

    @FunctionalInterface
    interface BatchTask {
        List<Assignment> doJob();
    }


}
