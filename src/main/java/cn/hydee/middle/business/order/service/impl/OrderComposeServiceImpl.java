package cn.hydee.middle.business.order.service.impl;

import cn.hydee.middle.business.order.Enums.*;
import cn.hydee.middle.business.order.dto.message.NetOrderCancelNotifyMessage;
import cn.hydee.middle.business.order.entity.OrderInfo;
import cn.hydee.middle.business.order.mapper.CloudPrintContentMapper;
import cn.hydee.middle.business.order.mapper.CloudSoundContentMapper;
import cn.hydee.middle.business.order.mapper.DbTableMapper;
import cn.hydee.middle.business.order.mapper.OrderInfoMapper;
import cn.hydee.middle.business.order.service.OrderComposeService;
import cn.hydee.middle.business.order.service.SupplierMessageConvert;
import cn.hydee.middle.business.order.service.rocket.MessageProducerService;
import cn.hydee.middle.business.order.service.suport.OrderBasicService;
import cn.hydee.middle.business.order.util.DateUtil;
import cn.hydee.middle.business.order.v2.manager.OrderBusinessToConsumerHandlerManager;
import cn.hydee.middle.business.order.yxtadapter.domainservice.bypass.BypassGateway;
import cn.hydee.middle.business.order.yxtadapter.domainservice.channel.ChannelGateway;
import com.alibaba.fastjson.JSON;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.Collections;
import java.util.Date;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2020/1/8 19:01
 */
@Slf4j
@Service
public class OrderComposeServiceImpl implements OrderComposeService {

    @Autowired
    private OrderBasicService orderBasicService;

    @Autowired
    private OrderInfoMapper orderInfoMapper;

    @Autowired
    private MessageProducerService messageProducerService;

    @Autowired
    private DbTableMapper dbTableMapper;

    @Autowired
    private CloudPrintContentMapper cloudPrintContentMapper;

    @Autowired
    private CloudSoundContentMapper cloudSoundContentMapper;

    @Autowired
    private SupplierMessageConvert supplierMessageConvert;

    @Autowired
    private BypassGateway bypassGateway;

    @Autowired
    private ChannelGateway channelGateway;

    @Override
    public void cancelOrder(NetOrderCancelNotifyMessage message) {
        //服务商模式消息校验并转换
        if (DsConstants.SUPPLIER_MER_CODE.equals(message.getGroupid())) {
            supplierMessageConvert.convertOtherMessage(message);
        }
        // 【*********】微商城仓库发货快递订单转B2C模块打面单发货 https://www.tapd.cn/61969829/prong/stories/view/1161969829001028205
        if (OrderBusinessToConsumerHandlerManager.businessToConsumerCancelOrder(message)) {
            return;
        }
        // 只有取消订单时，参数中的olorderno 表示表中的third_order_id
        OrderInfo orderInfo = orderBasicService.getOrderBaseByThirdNoWithCheck(message.getEctype(), message.getOlorderno());
//        //如果O2O订单是微商城 并且 未切店，转发到雨诺OMS
//        if (bypassGateway.needTransportByYdJia(orderInfo.getOrganizationCode(), orderInfo.getThirdPlatformCode())) {
//            if ("1".equals(message.getStatus())) {
//                log.info("cancelOrder Transport request {}", message.getOlorderno());
//                channelGateway.orderCancelPushYxtOldOmsStep1(message);
//            }
//        }


        // 1 申请 2拒绝 3同意 0 未知
        if ("1".equals(message.getStatus())) {
            if (orderInfo.getOrderState() < OrderStateEnum.COMPLETED.getCode()) {
                // 订单取消锁定
                OrderInfo update = new OrderInfo();
                update.setLockFlag(OrderLockFlagEnum.LOCK_CANCEL.getCode());
                update.setCancelReason(message.getRemark());
                update.setOrderNo(orderInfo.getOrderNo());
                if (message.getPushtime() != null) {
                    update.setCancelTime(DateUtil.parseStrToDate(message.getPushtime(), DateUtil.DATE_TIME_FORMAT_YYYY_MM_DD_HH_MI_SS));
                } else {
                    update.setCancelTime(new Date());
                }
                orderInfoMapper.updateOrder(update);
                // 声音广播
                messageProducerService.produceSoundBroadCastMessage(orderInfo, SoundTypeEnum.CANCEL_ORDER);
            }
        } else if (DsConstants.STRING_TWO.equals(message.getStatus())) {
            // 拒绝取消
            if (OrderStateEnum.COMPLETED.getCode() <= orderInfo.getOrderState()) {
                log.info("receive cancel refused message, but orderState not allow update,orderNo:[{}],orderState:[{}]"
                        , orderInfo.getOrderNo(), orderInfo.getOrderState());
                return;
            }
            if (!OrderLockFlagEnum.LOCK_CANCEL.getCode().equals(orderInfo.getLockFlag())) {
                log.info("receive cancel refused message, but lockFlag not allow update,orderNo:[{}],lockFlag:[{}]"
                        , orderInfo.getOrderNo(), orderInfo.getLockFlag());
                return;
            }
            OrderInfo update = new OrderInfo();
            update.setLockFlag(OrderLockFlagEnum.NOT_LOCK.getCode());
            update.setCancelReason("");
            update.setOrderNo(orderInfo.getOrderNo());
            orderInfoMapper.updateOrder(update);
        } else if ("3".equals(message.getStatus())) {
            // 同意取消消息先不接
            /*if (orderInfo.getOrderState() < OrderStateEnum.COMPLETED.getCode()) {
                //解锁erp库存
                refundHandlerService.undoErpForCancel(orderInfo, "system");

                OrderInfo update = new OrderInfo();
                update.setCancelReason(message.getRemark());
                update.setOrderState(OrderStateEnum.CANCEL.getCode());
                update.setOrderNo(orderInfo.getOrderNo());
                if (message.getPushtime() != null) {
                    update.setCancelTime(DateUtil.parseStrToDate(message.getPushtime(), DateUtil.DATE_TIME_FORMAT_YYYY_MM_DD_HH_MI_SS));
                } else {
                    update.setCancelTime(new Date());
                }
                int num = orderInfoMapper.updateOrder(update);
                if (num > 0){
                    // 订单日志
                    orderBasicService.saveOrderInfoLog(orderInfo, OrderStateEnum.CANCEL.getCode(), "system",
                            "订单取消", "三方消息取消",orderInfo.getErpState());

                }
            }*/
        }
        //订单更新异步消费
        messageProducerService.produceUpdateOrderMessage(Collections.singletonList(orderInfo.getOrderNo()), OrderUpdateCodeEnum.MESSAGE_CANCEL.getCode());
    }

    @Override
    public void init(String merCode) {

        // 云打印内容表
        //【ID1020998】打印声音分表合并
        String printTableName = DsConstants.DB_TABLE_PRINT_CONTENT;

        int printTableCount = dbTableMapper.countTable(printTableName);
        if (printTableCount < 1) {
            cloudPrintContentMapper.createNewTable(printTableName);
        }

        // 订单语音消息表
        //【ID1020998】打印声音分表合并
        String soundTableName = DsConstants.DB_TABLE_SOUND_CONTENT;

        int soundTableCount = dbTableMapper.countTable(soundTableName);
        if (soundTableCount < 1) {
            cloudSoundContentMapper.createTable(merCode);
        }

    }
}
