package cn.hydee.middle.business.order.batch.export.dto;

import com.alibaba.excel.annotation.ExcelProperty;
import com.alibaba.excel.annotation.write.style.ColumnWidth;
import com.baomidou.mybatisplus.annotation.TableField;
import lombok.Data;

import java.io.Serializable;

/**
 * <AUTHOR>
 */
@Data
public class OrganizationOrderInfoSnapshotDTO implements Serializable {

    private static final long serialVersionUID = 6449758072812176974L;

    @ColumnWidth(30)
    @ExcelProperty(value = "上级机构", index = 0)
    private String parentOrganizationName;
    @ColumnWidth(30)
    @ExcelProperty(value = "机构名称", index = 1)
    private String organizationName;
    @ColumnWidth(20)
    @ExcelProperty(value = "机构ID", index = 2)
    private String organizationCode;
    @ColumnWidth(17)
    @ExcelProperty(value = "销售待下账", index = 3)
    private Long waitSale;
    @ColumnWidth(17)
    @ExcelProperty(value = "退款待下账", index = 4)
    private Long refundWaitSale;
    @ColumnWidth(10)
    @ExcelProperty(value = "待拣货", index = 5)
    private Long waitPick;
    @ColumnWidth(10)
    @ExcelProperty(value = "待配送", index = 6)
    private Long waitPost;
    @TableField(exist = false)
    @ColumnWidth(10)
    @ExcelProperty(value = "配送中", index = 7)
    private Long posting;
    @ColumnWidth(10)
    @ExcelProperty(value = "异常", index = 8)
    private Long exception;
    @ColumnWidth(10)
    @ExcelProperty(value = "退款中", index = 9)
    private Long refunding;

    @ColumnWidth(30)
    @ExcelProperty(value = "是否开启全自动下账", index = 10)
    private String autoEnterAccountOpenFlag;
}
