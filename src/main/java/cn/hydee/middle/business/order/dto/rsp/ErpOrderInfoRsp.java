package cn.hydee.middle.business.order.dto.rsp;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

@Data
public class ErpOrderInfoRsp{

    @ApiModelProperty(value ="系统单号")
    private String orderNo;

    @ApiModelProperty(value = "订单状态")
    private Integer orderState;

    @ApiModelProperty(value = "下账状态")
    private Integer erpState;

    @ApiModelProperty(value = "平台订单号")
    private String thirdOrderNo;

    @ApiModelProperty(value ="调整单号")
    private String erpAdjustNo;

    @ApiModelProperty(value ="下单时间")
    private String created;

    @ApiModelProperty(value = "线下门店编码")
    private String organizationCode;
}
