package cn.hydee.middle.business.order.route.application.command;

import io.swagger.annotations.ApiModelProperty;
import java.util.List;
import lombok.Data;

@Data
public class SceneOperateCommand {

  @ApiModelProperty(value = "场景Id",name = "id")
  private Long id;

  @ApiModelProperty(value = "场景名称",name = "sceneName")
  private String sceneName;

  @ApiModelProperty(value = "场景类型",name = "sceneType")
  private String sceneType;

  @ApiModelProperty(value = "场景说明",name = "mark")
  private String mark;

  @ApiModelProperty(value = "创建人id",name = "createdBy")
  private String createdBy;

  @ApiModelProperty(value = "创建人")
  private String createdName;

  @ApiModelProperty(value = "更新人id",name = "updatedBy")
  private String updatedBy;

  @ApiModelProperty(value = "场景规则id集合",name = "ruleIds")
  private List<Long> ruleIds;

  @ApiModelProperty(value = "版本号")
  private Long version;
}
