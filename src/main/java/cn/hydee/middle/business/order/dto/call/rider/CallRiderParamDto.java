package cn.hydee.middle.business.order.dto.call.rider;

import cn.hydee.middle.business.order.dto.rsp.baseinfo.DsStoreDeliveryInfoResDTO;
import cn.hydee.middle.business.order.entity.OrderInfo;
import cn.hydee.unified.model.rider.RiderOrderAddReq;
import lombok.Data;

/**
 * <AUTHOR>
 * @date 2021/12/07
 */
@Data
public class CallRiderParamDto {
    private OrderInfo orderInfo;
    private DsStoreDeliveryInfoResDTO deliveryStore;
    private Boolean beRepeat;
    private String modifyDeliveryPlatformName;
    private RiderOrderAddReq addReq;
    private RiderOrderAddReq.Order riderOrder;
    private boolean appointCallRightNowFlag;

    public CallRiderParamDto(OrderInfo orderInfo, DsStoreDeliveryInfoResDTO deliveryStore, Boolean beRepeat, String modifyDeliveryPlatformName
        ,boolean appointCallRightNowFlag) {
        this.orderInfo = orderInfo;
        this.deliveryStore = deliveryStore;
        this.beRepeat = beRepeat;
        this.modifyDeliveryPlatformName = modifyDeliveryPlatformName;
        this.appointCallRightNowFlag = appointCallRightNowFlag;
    }

}
