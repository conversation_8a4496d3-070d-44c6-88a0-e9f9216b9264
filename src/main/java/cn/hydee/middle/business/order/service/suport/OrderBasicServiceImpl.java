package cn.hydee.middle.business.order.service.suport;

import cn.hutool.extra.spring.SpringUtil;
import cn.hydee.middle.business.order.Enums.*;
import cn.hydee.middle.business.order.domain.OrderInfoAllDomain;
import cn.hydee.middle.business.order.dto.MemberNoQueryDto;
import cn.hydee.middle.business.order.dto.OrderNoTotalFee;
import cn.hydee.middle.business.order.dto.req.GetMemberConsumeInfoReqDto;
import cn.hydee.middle.business.order.dto.req.OrderHandleReqDto;
import cn.hydee.middle.business.order.dto.req.asyn.UnlockStockBatchReqDto;
import cn.hydee.middle.business.order.dto.rsp.GetMemberConsumeInfoRsqDto;
import cn.hydee.middle.business.order.dto.rsp.baseinfo.DsStoreDeliveryInfoResDTO;
import cn.hydee.middle.business.order.dto.rsp.baseinfo.SysEmployeeResDTO;
import cn.hydee.middle.business.order.entity.*;
import cn.hydee.middle.business.order.mapper.*;
import cn.hydee.middle.business.order.service.EsEnhanceService;
import cn.hydee.middle.business.order.service.OrderDeliveryIdRelationService;
import cn.hydee.middle.business.order.service.OrderDeliveryLogService;
import cn.hydee.middle.business.order.service.baseinfo.DsDeliveryStoreService;
import cn.hydee.middle.business.order.util.ExceptionUtil;
import cn.hydee.middle.business.order.v2.feign.hems.HemsCommonClient;
import cn.hydee.middle.business.order.v2.manager.DeliveryManager;
import cn.hydee.middle.business.order.v2.manager.base.BaseInfoManager;
import cn.hydee.starter.dto.ErrorType;
import cn.hydee.starter.dto.ResponseBase;
import cn.hydee.unified.model.rider.RiderOrderCancelReq;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Lazy;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;

import java.math.BigDecimal;
import java.util.List;
import java.util.Objects;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2019/12/27 9:47
 */
@Service
@Slf4j
public class OrderBasicServiceImpl implements OrderBasicService {
    @Autowired
    private OrderInfoMapper orderInfoMapper;
    @Autowired
    private OrderDetailMapper orderDetailMapper;
    @Autowired
    private OrderDeliveryRecordMapper orderDeliveryRecordMapper;
    @Autowired
    private OrderDeliveryAddressMapper orderDeliveryAddressMapper;
    @Autowired
    private OrderLogMapper orderLogMapper;
    @Autowired
    private OrderPayInfoMapper orderPayInfoMapper;
    @Autowired
    private HemsCommonClient hemsCommonClient;
    @Autowired
    private DsDeliveryStoreService dsDeliveryStoreService;
    @Autowired
    private OrderDeliveryIdRelationService orderDeliveryIdRelationService;
    @Autowired
    private BaseInfoManager baseInfoManager;
    @Autowired
    private ErpBillInfoMapper erpBillInfoMapper;
    @Autowired
    private DeliveryManager deliveryManager;
    @Autowired
    @Lazy
    private EsEnhanceService esEnhanceService;

    // OrderDeliveryAddress
    @Override
    public OrderDeliveryAddress getOrderDeliveryAddress(Long orderNo){
        OrderDeliveryAddress orderDeliveryAddress = orderDeliveryAddressMapper.selectByOrderNo(orderNo);
        if (orderDeliveryAddress == null){
            throw ExceptionUtil.getWarnException(DsErrorType.ORDER_DATA_ERROR.getCode(), "订单收货信息不存在");
        }
        return orderDeliveryAddress;
    }

    // 获取订单delivery_record
    @Override
    public OrderDeliveryRecord getOrderDeliveryRecordWithCheck(Long orderNo){
        OrderDeliveryRecord orderDeliveryRecord = orderDeliveryRecordMapper.selectByOrderNo(orderNo);
        if (orderDeliveryRecord == null){
            throw ExceptionUtil.getWarnException(DsErrorType.ORDER_DATA_ERROR.getCode(), "订单配送信息不存在");
        }
        return orderDeliveryRecord;
    }

    // 获取订单商品列表信息
    @Override
    public List<OrderDetail> getOrderDetailListWithCheck(Long orderNo){
        List<OrderDetail> detailList = orderDetailMapper.selectListByOrderNo(orderNo);
        if (detailList == null){
            throw ExceptionUtil.getWarnException(DsErrorType.ORDER_DATA_ERROR.getCode(), "订单商品信息不存在");
        }
        return detailList;
    }

    // 查询订单基本信息
    @Override
    public OrderInfo getOrderBaseWithCheck(Long orderNo){
        OrderInfo orderInfo = getOrderBaseInfo(orderNo);
        if (orderInfo == null) {
            throw ExceptionUtil.getWarnException(DsErrorType.ORDER_NOT_EXIST);
        }
        return orderInfo;
    }

    // 查询订单总信息
    @Override
    public OrderInfoAllDomain getOrderAllWithCheck(Long orderNo){
        OrderInfoAllDomain orderInfo = orderInfoMapper.selectOrderInfoDetail(orderNo);
        if (orderInfo == null) {
            throw ExceptionUtil.getWarnException(DsErrorType.ORDER_NOT_EXIST);
        }
        return orderInfo;
    }

    // 通过订单号查询订单基本信息
    @Override
    public OrderInfo getOrderBaseInfo(Long orderNo){
        QueryWrapper<OrderInfo> queryWrapper = new QueryWrapper<>();
        LambdaQueryWrapper<OrderInfo> lambda = queryWrapper.lambda();
        lambda.eq(OrderInfo::getOrderNo, orderNo);
        return orderInfoMapper.selectOne(queryWrapper);
    }

    // 通过订单号和商户编码查询订单基本信息
    @Override
    public OrderInfo getOrderBaseInfo(Long orderNo, String merCode){
        QueryWrapper<OrderInfo> queryWrapper = new QueryWrapper<>();
        LambdaQueryWrapper<OrderInfo> lambda = queryWrapper.lambda();
        lambda.eq(OrderInfo::getOrderNo, orderNo);
        lambda.eq(OrderInfo::getMerCode,merCode);
        return orderInfoMapper.selectOne(queryWrapper);
    }

    @Override
    public OrderPayInfo getOrderPayInfoWithCheck(Long orderNo) {
        OrderPayInfo orderPayInfo = orderPayInfoMapper.selectByOrderNo(orderNo);
        if (orderPayInfo == null){
            throw ExceptionUtil.getWarnException(DsErrorType.ORDER_NOT_EXIST);
        }
        return orderPayInfo;
    }

    /** 通过三方平台与三方订单号查询订单基本信息 */
    @Override
    public OrderInfo getOrderBaseByThirdNoWithCheck(String thirdPlatformCode, String thirdOrderNo){
        OrderInfo orderInfo = orderInfoMapper.selectBaseByUnique(thirdOrderNo, thirdPlatformCode);
        if (orderInfo == null){
            log.info("OrderInfo not found for third platform thirdOrderNo:{}, thirdPlatformCode:{}",thirdOrderNo,thirdPlatformCode);
            throw ExceptionUtil.getWarnException(DsErrorType.ORDER_NOT_EXIST);
        }
        return orderInfo;
    }

    @Override
    public OrderInfo getOrderBaseByThirdNo( String thirdPlatformCode, String thirdOrderNo){
        OrderInfo orderInfo = orderInfoMapper.selectBaseByUnique(thirdOrderNo, thirdPlatformCode);
        return orderInfo;
    }

    @Override
    public OrderInfo checkOrderLock(String userId, OrderHandleReqDto orderHandleReqDto, boolean verifyLock){
        OrderInfo orderInfo = this.getOrderBaseWithCheck(orderHandleReqDto.getOrderNo());
        if (verifyLock == false){
            return orderInfo;
        }
        if ( !OrderLockFlagEnum.NOT_LOCK.getCode().equals(orderInfo.getLockFlag()) ){
            throw ExceptionUtil.getWarnException(DsErrorType.ORDER_LOCKED);
        }
        return orderInfo;
    }

    @Override
    public OrderInfoAllDomain checkOrderAllLock(Long orderNo, boolean verifyLock){
        OrderInfoAllDomain orderInfo = this.getOrderAllWithCheck(orderNo);
        if (verifyLock == false){
            return orderInfo;
        }
        if ( !OrderLockFlagEnum.NOT_LOCK.getCode().equals(orderInfo.getLockFlag()) ){
            throw ExceptionUtil.getWarnException(ErrorType.STATUS_ERROR);
        }
        return orderInfo;
    }

    /** 获取订单配送类型名称 */
    @Override
    public String getDeliveryPlatName(String thirdPlatformCode, String deliveryType){
        String platformName = "";
        /*if (DeliveryTypeEnum.PLAT.getCode().equals(deliveryType) ||
            DeliveryTypeEnum.PLAT_THIRD.getCode().equals(deliveryType)){
            if (PlatformCodeEnum.MEITUAN.getCode().equals(thirdPlatformCode)){
                platformName = DeliveryPlatformEnum.MEITUAN.getName();
            } else if (PlatformCodeEnum.JD_DAO_JIA.getCode().equals(thirdPlatformCode)){
                platformName = DeliveryPlatformEnum.DADA.getName();
            } else if (PlatformCodeEnum.E_BAI.getCode().equals(thirdPlatformCode)){
                platformName = DeliveryPlatformEnum.HUMMINGBIRD.getName();
            }
        }  */
        if (DeliveryTypeEnum.BUYER_SELF.getCode().equals(deliveryType)){
            platformName = DeliveryPlatformEnum.BUYER.getName();
        } else if (DeliveryTypeEnum.EXPRESS.getCode().equals(deliveryType)){
            platformName = DeliveryPlatformEnum.EXPRESS.getName();
        }
        return platformName;
    }

    /** 设置订单商品状态 商品不存在*/
    @Override
    @Transactional(rollbackFor = Exception.class)
    public void setOrderDetailNotExist(List<String> erpCodeList, OrderInfo orderInfo,List<OrderDetail> orderDetailList){
        for (String erpStr: erpCodeList){
            OrderDetail detailUpdate = new OrderDetail();
            detailUpdate.setOrderNo(orderInfo.getOrderNo());
            detailUpdate.setErpCode(erpStr);
            detailUpdate.setStatus(OrderDetailStatusEnum.NOT_EXIST.getCode());
            orderDetailMapper.updateByOrderNoErpCode(detailUpdate);
        }
        OrderInfo updateEx = new OrderInfo();
        //TODO 该接口暂未使用，不进行商品不存在异常分类改造
        updateEx.setLockFlag(OrderLockFlagEnum.LOCK_COMMODITY_NOT_EXIST.getCode());
        updateEx.setOrderNo(orderInfo.getOrderNo());
        orderInfoMapper.updateOrder(updateEx);
    }

    @Override
    public void saveOrderInfoLog(OrderInfo orderInfo, Integer stateNew, String operatorId,
        String operateDesc, String extraInfo,Integer erpState,String operatorName){
        if (Objects.isNull(orderInfo)) {
            log.warn("saveOrderInfoLog para orderInfo is null");
            return;
        }
        OrderLog orderLog = new OrderLog();
        orderLog.setOrderNo(orderInfo.getOrderNo());
        orderLog.setOperatorId(operatorId);
        orderLog.setOperateDesc(operateDesc);
        orderLog.setStateBefore(orderInfo.getOrderState());
        orderLog.setExtraInfo(extraInfo);
        orderLog.setState(stateNew);
        orderLog.setErpState(erpState);
        orderLog.setOperatorName(operatorName);
        orderLogMapper.insert(orderLog);
    }

    @Override
    public void saveOrderInfoLog(OrderInfo orderInfo, Integer stateBefore, String operatorId, String operateDesc, String extraInfo,String operatorName) {
        if (Objects.isNull(orderInfo)) {
            log.warn("saveOrderInfoLog para orderInfo is null");
            return;
        }
        OrderLog orderLog = new OrderLog();
        orderLog.setOrderNo(orderInfo.getOrderNo());
        orderLog.setOperatorId(operatorId);
        orderLog.setOperateDesc(operateDesc);
        orderLog.setStateBefore(stateBefore);
        orderLog.setExtraInfo(extraInfo);
        orderLog.setState(orderInfo.getOrderState());
        orderLog.setErpState(orderInfo.getErpState());
        orderLog.setOperatorName(operatorName);
        orderLogMapper.insert(orderLog);
    }

    /**
     * ID1014723 检查是否已呼叫骑手,已呼叫不允许修改门店
     * @param orderInfo
     */
    @Override
    public void whetherCalledRider(OrderInfo orderInfo){
        OrderDeliveryRecord orderDeliveryRecord = orderDeliveryRecordMapper.selectByOrderNo(orderInfo.getOrderNo());
        if(orderDeliveryRecord == null){
            return;
        }
        if(!DeliveryTypeEnum.SELLER_SELF.getCode().equals(orderDeliveryRecord.getDeliveryType())){
            return;
        }
        if (!DeliveryPlatformEnum.checkRider(orderDeliveryRecord.getDeliveryPlatName())) {
            return;
        }

        if(orderDeliveryRecord.getState() > DeliveryStateEnum.UN_CALL.getCode() && orderDeliveryRecord.getState() < DeliveryStateEnum.COMPLETED.getCode()){
            throw ExceptionUtil.getWarnException(DsErrorType.CHECK_RIDER_ERROR.getCode(), DsErrorType.CHECK_RIDER_ERROR.getMsg());
        }
    }

    @Override
    public SysEmployeeResDTO getEmployeeInfoForErp(String userId) {
        return baseInfoManager.getEmployeeInfoForErpNoEx(userId);
    }

    @Override
    public GetMemberConsumeInfoRsqDto getMemberConsumeInfo(GetMemberConsumeInfoReqDto req) {
        GetMemberConsumeInfoRsqDto getMemberConsumeInfoRsqDto = new GetMemberConsumeInfoRsqDto();
        List<Long> orderNos = esEnhanceService.queryMemberConsumerData(MemberNoQueryDto.buildBean(req));
        if (CollectionUtils.isEmpty(orderNos)) {
            return getMemberConsumeInfoRsqDto;
        }
        List<OrderNoTotalFee> orderNoTotalFeeList = erpBillInfoMapper.selectOrderNoTotalFee(orderNos);
        getMemberConsumeInfoRsqDto.setConsumeNum(orderNoTotalFeeList.size());
        getMemberConsumeInfoRsqDto.setConsumeAmounts(orderNoTotalFeeList.stream().map(OrderNoTotalFee::getTotalFee).reduce(BigDecimal.ZERO, BigDecimal::add));
        return getMemberConsumeInfoRsqDto;
    }

    @Override
    public void orderCancelUpdateDeliveryRecord(OrderInfo orderInfo,String source) {
        try {
            if(null == orderInfo){
                return;
            }
            OrderDeliveryRecord orderDeliveryRecord = orderDeliveryRecordMapper.selectByOrderNo(orderInfo.getOrderNo());
            if(orderDeliveryRecord == null){
                return;
            }
            if(!DeliveryTypeEnum.SELLER_SELF.getCode().equals(orderDeliveryRecord.getDeliveryType())){
                return;
            }
            if (!DeliveryPlatformEnum.checkRider(orderDeliveryRecord.getDeliveryPlatName())) {
                return;
            }

            if(orderDeliveryRecord.getState()<= DeliveryStateEnum.UN_CALL.getCode() || orderDeliveryRecord.getState() > DeliveryStateEnum.DELIVERY_ON.getCode()){
                return;
            }

            DsStoreDeliveryInfoResDTO deliveryStore = dsDeliveryStoreService.getDeliveryStoreByOnlineStoreId(orderInfo.getMerCode(),
                orderInfo.getOnlineStoreCode(),orderInfo.getThirdPlatformCode(),orderDeliveryRecord.getDeliveryPlatName());
            if (deliveryStore == null){
                log.error("OrderDelivery:modifyDeliveryPlatform, deliveryStore null, orderNo:{}, platName:{}",
                    orderInfo.getOrderNo(), orderDeliveryRecord.getDeliveryPlatName());
                throw ExceptionUtil.getWarnException(DsErrorType.ORDER_RIDER_ERROR.getCode(),
                    "获取不到配送门店信息");
            }

            Long deliveryId = orderDeliveryIdRelationService.queryDeliveryIdByOrderNo(orderInfo.getOrderNo());

            RiderOrderCancelReq cancelRiderReqDto = new RiderOrderCancelReq();
            this.setCancelRiderNewReason(orderDeliveryRecord,cancelRiderReqDto);
            cancelRiderReqDto.setDeliveryId(deliveryId);
            cancelRiderReqDto.setRiderOrderId(orderDeliveryRecord.getRiderOrderNo());
            //蜂鸟即配需要指定取消来源
            if(DeliveryPlatformEnum.checkCancelSource(orderDeliveryRecord.getDeliveryPlatName())){
                cancelRiderReqDto.setCancelFrom(source);
            }

            ResponseBase<Object> result = deliveryManager.cancelRider(orderInfo, cancelRiderReqDto, deliveryStore, orderDeliveryRecord, deliveryId);
            if (!DsConstants.ALL_SUCCESS.equals(result.getCode())) {
                orderDeliveryRecord.setCancelFrom(CancelRiderEnum.OrderMsgCancel.getCancelFrom());
                orderDeliveryRecord.setCancelReason(cancelRiderReqDto.getCancelReason().toString());
                orderDeliveryRecord.setExceptionReason(result.getMsg());
                orderDeliveryRecordMapper.updateDeliveryRecord(orderDeliveryRecord);
                throw ExceptionUtil.getWarnException(DsErrorType.CANCEL_RIDER_ERROR.getCode(), result.getMsg());
            }

            orderDeliveryRecord.setCancelReason(String.valueOf(cancelRiderReqDto.getCancelReason()));
            orderDeliveryRecord.setCancelFlag(DsConstants.INTEGER_ONE);
            orderDeliveryRecord.setCancelDetail(cancelRiderReqDto.getCancelDetail());
            orderDeliveryRecord.setState(DeliveryStateEnum.CANCELED.getCode());
            orderDeliveryRecord.setRiderOrderNo("");
            int num = orderDeliveryRecordMapper.updateDeliveryRecord(orderDeliveryRecord);
            if(num > 0){
                //记录订单配送日志
                SpringUtil.getBean(OrderDeliveryLogService.class).saveOrderDeliveryLog(orderDeliveryRecord.getOrderNo(),orderDeliveryRecord.getState(),
                    orderDeliveryRecord.getDeliveryPlatName(),orderDeliveryRecord.getRiderName(),orderDeliveryRecord.getRiderPhone(),"订单取消");
            }
        }  catch (Exception e){
            log.error("cancel rider exception, orderNo:{}", orderInfo.getOrderNo(), e);
            return;
        }

    }

    @Override
    public void setCancelRiderNewReason(OrderDeliveryRecord orderDeliveryRecord, RiderOrderCancelReq cancelRiderReqDto) {
        Integer cancelReason = 36;
        String cancelDetail = "我不需要配送了";
        //蜂鸟
        if (DeliveryPlatformEnum.HUMMINGBIRD.getName().equals(orderDeliveryRecord.getDeliveryPlatName())){
            cancelReason = 9;
            cancelDetail = "顾客下错单/临时不想要了";
        }
        //美团
        if (DeliveryPlatformEnum.MEITUAN_RIDER.getName().equals(orderDeliveryRecord.getDeliveryPlatName())){
            cancelReason = 101;
            cancelDetail = "顾客主动取消";
        }
        //达达
        if(DeliveryPlatformEnum.DADA.getName().equals(orderDeliveryRecord.getDeliveryPlatName())){
            cancelReason = 4;
            cancelDetail = "顾客取消订单";
        }
        //顺丰同城
        if(DeliveryPlatformEnum.SFTC.getName().equals(orderDeliveryRecord.getDeliveryPlatName())){
            cancelReason = 0;
            cancelDetail = "顾客取消订单";
        }
        cancelRiderReqDto.setCancelReason(cancelReason);
        cancelRiderReqDto.setCancelDetail(cancelDetail);
    }

    @Override
    public int updateOrderInfo(OrderInfo orderInfo) {
        return orderInfoMapper.updateOrder(orderInfo);
    }

    @Override
    public IPage<OrderInfo> selectOrderInfoUpdateBatch(Page<UnlockStockBatchReqDto> page, UnlockStockBatchReqDto reqDto, List<String> organizationList) {
        return orderInfoMapper.selectOrderInfoUpdateBatchWithLock(page,reqDto,organizationList);
    }
}
