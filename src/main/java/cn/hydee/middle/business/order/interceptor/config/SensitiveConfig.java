package cn.hydee.middle.business.order.interceptor.config;

import lombok.Data;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.context.annotation.Configuration;

import java.util.ArrayList;
import java.util.List;

@Configuration
@ConfigurationProperties(prefix = "sensitive.scope")
@Data
public class SensitiveConfig {
    /***
     * 脱敏作用范围-添加和更新
     */
    private List<String> addUpdates = new ArrayList<>();
    /***
     * 脱敏作用范围-查询
     */
    private List<String> selects = new ArrayList<>();
}
