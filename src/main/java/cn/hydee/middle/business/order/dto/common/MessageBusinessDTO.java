package cn.hydee.middle.business.order.dto.common;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import org.apache.commons.lang3.StringUtils;

/**
 * <AUTHOR>
 * @date 2023/06/01
 */
@Data
public class MessageBusinessDTO {

    @ApiModelProperty("商户编号")
    private String merCode;

    @ApiModelProperty("线下门店编码（oms订单需要区分门店）")
    private String organizationCode;

    @ApiModelProperty("用户id")
    private String userId;

    public static MessageBusinessDTO buildBean(String merCode,String organizationCode,String userId){
        MessageBusinessDTO pushObject = new MessageBusinessDTO();
        pushObject.setMerCode(merCode);
        pushObject.setOrganizationCode(organizationCode);
        pushObject.setUserId(userId);
        if(StringUtils.isNotBlank(userId)){
            pushObject.setOrganizationCode(null);
        }
        return pushObject;
    }

}
