package cn.hydee.middle.business.order.dto;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotNull;

/**
 * <AUTHOR>
 * @date 2024/8/28
 * @since 1.0
 */
@Data
public class PrescriptionAuditDto {

    @ApiModelProperty(value = "订单号")
    private String orderNo;

    @ApiModelProperty(value = "审核状态码 0：审核不通过，1：审核通过，2：待审核")
    private Integer status;
}
