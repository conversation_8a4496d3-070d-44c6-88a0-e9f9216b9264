package cn.hydee.middle.business.order.dto.req.baseinfo;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.List;

@Data
public class DsOnlineStoreTreeReqDto {

    @ApiModelProperty(value = "商户号")
    private String merCode;
    @ApiModelProperty(value = "平台编码")
    private String platformCode;
    @ApiModelProperty(value = "网店编码")
    private String clientCode;
    @ApiModelProperty(value = "门店名称")
    private String storeName;
    @ApiModelProperty(value = "服务模式")
    private String serviceMode;
    @ApiModelProperty(value = "选中的机构")
    private List<CheckedOrg> orgList;
    @ApiModelProperty(value = "业务类型，1-批量修改店铺配置，2-批量修改营业状态，3-批量修改营业时间")
    private Integer bizType;
    @ApiModelProperty(value = "查询类型 1-平台，2-网店，3-门店，4-店铺名称搜索")
    private Integer qryType;

}
