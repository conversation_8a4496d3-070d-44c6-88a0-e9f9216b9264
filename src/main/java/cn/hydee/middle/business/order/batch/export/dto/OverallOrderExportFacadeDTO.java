package cn.hydee.middle.business.order.batch.export.dto;


import lombok.Data;

import java.math.BigDecimal;

/**
 * <AUTHOR>
 * @date 2021/4/28 下午7:17
 */
@Data
public class OverallOrderExportFacadeDTO extends OverallOrderExportDTO {

    /**
     * 第三方平台名称
     */
    private String thirdPlatformName;
    /**
     * 订单类型
     */
    private String orderType;
    /**
     * 是否处方单
     */
    private String prescriptionDesc;
    /**
     * 转仓发货描述
     */
    private String transferDeliveryDesc;
    /**
     * 送达方式
     */
    private String deliveryTimeTypeDesc;
    /**
     * 配送状态描述
     */
    private String deliveryStateDesc;
    /**
     * 订单明细状态描述
     */
    private String detailStatusDesc;
    /**
     * 拣货时长
     */
    private String pickDuration;
    /**
     * 配送出库时长
     */
    private String deliveryOutDuration;
    /**
     * 是否有换货
     */
    private String hasSwap;
    /**
     * 毛利额
     */
    private BigDecimal grossProfitAmount;
    /**
     * 毛利率
     */
    private String grossProfitPercent;
    /**
     * 综合毛利额
     */
    private BigDecimal grossProfitAmountComprehensive;
    /**
     * 综合毛利率
     */
    private String grossProfitPercentComprehensive;
    /**
     * 订单状态描述
     */
    private String orderStateDesc;
    /**
     * 订单下账状态描述
     */
    private String erpStateDesc;
    /**
     * 是否预约单
     */
    private String appointmentDesc;
    //orderDetail start
    /**
     * 商品三方平台编码
     */
    private String platformSkuId;
    /**
     * 商品erp编码
     */
    private String erpCode;
    /**
     * 商品条形编码
     */
    private String barCode;
    /**
     * 商品名称
     */
    private String commodityName;
    /**
     * 商品规格
     */
    private String commoditySpec;
    /**
     * 明细状态，0正常，1已换货，2已退货
     */
    private Integer detailStatus;
    /**
     * 替换该商品的detailId
     */
    private Long swapId;
    /**
     * 商品数量
     */
    private Integer commodityCount;
    /**
     * 商品单价
     */
    private BigDecimal price;
    /**
     * 商品金额 小计金额售价*数量 mapping orderDetail.totalAmount
     */
    private BigDecimal detailTotalAmount;
    /**
     * 促销优惠金额
     */
    private BigDecimal discountAmount;
    /**
     * 优惠分摊
     */
    private BigDecimal discountShare;
    /**
     * 下账金额
     */
    private BigDecimal actualNetAmount;
    /**
     * 下账价格
     */
    private BigDecimal billPrice;
    /**
     * 换货价差
     */
    private BigDecimal modifyPriceDiff;
    //orderDetail end

    private String couponInfo;

    //是否换门店
    private String changeOrganizationFlagDesc;

    private String medicalInsuranceDesc;

    private String integralFlagDesc;
    /**
     * 来源渠道类型 1-京东渠道
     */
    private Integer sourceChannelType;
    /**
     * 京东渠道
     */
    private String jdChannelFlag;

}
