package cn.hydee.middle.business.order.service.impl;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.StrUtil;
import cn.hydee.middle.business.order.Enums.DsConstants;
import cn.hydee.middle.business.order.Enums.OrderOverSoldEnum;
import cn.hydee.middle.business.order.Enums.PlatformCodeEnum;
import cn.hydee.middle.business.order.dto.order.over.sold.OrderOverSoldReqDto;
import cn.hydee.middle.business.order.dto.order.over.sold.OrderOverSoldRespDto;
import cn.hydee.middle.business.order.dto.req.alarm.AlarmExceptionOrderReq;
import cn.hydee.middle.business.order.dto.rsp.alarm.AlarmExceptionOrderResp;
import cn.hydee.middle.business.order.dto.rsp.baseinfo.MerchantRegionResp;
import cn.hydee.middle.business.order.entity.CommodityExceptionOrder;
import cn.hydee.middle.business.order.entity.OrderDetail;
import cn.hydee.middle.business.order.feign.MiddleBaseInfoClient;
import cn.hydee.middle.business.order.mapper.CommodityExceptionOrderMapper;
import cn.hydee.middle.business.order.service.CommodityExceptionOrderService;
import cn.hydee.middle.business.order.service.OrderDetailService;
import cn.hydee.middle.business.order.util.DateUtil;
import cn.hydee.starter.dto.ResponseBase;
import cn.hydee.starter.util.ExLogger;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.dao.DuplicateKeyException;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;
import org.springframework.util.StringUtils;

import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @date 2021/4/7 下午2:20
 */
@Service
@Slf4j
public class CommodityExceptionOrderServiceImpl extends ServiceImpl<CommodityExceptionOrderMapper, CommodityExceptionOrder> implements CommodityExceptionOrderService {

    private final static String FORMAT ="%s_%s";

    @Autowired
    private CommodityExceptionOrderMapper commodityExceptionOrderMapper;

    @Autowired
    private OrderDetailService orderDetailService;

    @Autowired
    private MiddleBaseInfoClient middleBaseInfoClient;

    @Override
    public void insert(CommodityExceptionOrder commodityExceptionOrder) {
        try {
            commodityExceptionOrderMapper.insert(commodityExceptionOrder);
        } catch (DuplicateKeyException e) {
            log.info("DuplicateKeyException commodityExceptionOrder:{}.", commodityExceptionOrder, e);
        } catch (Exception e) {
            log.info("insert commodityExceptionOrder exception.", e);
        }
    }

    @Override
    public List<Long> batchQryLack(List<Long> orderNoList) {
        return commodityExceptionOrderMapper.batchQryLack(orderNoList);
    }

    @Override
    public List<String> getErpCodeByOrderNo(Long orderNo, Integer bizType) {
        LambdaQueryWrapper<CommodityExceptionOrder> queryWrapper = new LambdaQueryWrapper<CommodityExceptionOrder>()
                .eq(CommodityExceptionOrder::getOrderNo, orderNo)
                .eq(CommodityExceptionOrder::getBizType, bizType);
        List<CommodityExceptionOrder> commodityExceptionOrders = commodityExceptionOrderMapper.selectList(queryWrapper);
        if (CollUtil.isEmpty(commodityExceptionOrders)) {
            return null;
        }
        return commodityExceptionOrders.stream().map(CommodityExceptionOrder::getErpCode).collect(Collectors.toList());
    }

    @Override
    public List<CommodityExceptionOrder> getListByOrderNo(Long orderNo, Integer bizType) {
        LambdaQueryWrapper<CommodityExceptionOrder> queryWrapper = new LambdaQueryWrapper<CommodityExceptionOrder>()
                .eq(CommodityExceptionOrder::getOrderNo, orderNo)
                .eq(CommodityExceptionOrder::getBizType, bizType);
        return commodityExceptionOrderMapper.selectList(queryWrapper);
    }

    @Override
    public List<CommodityExceptionOrder> getListByOrderNo(Long orderNo, List<Integer> bizTypes) {
        LambdaQueryWrapper<CommodityExceptionOrder> queryWrapper = new LambdaQueryWrapper<CommodityExceptionOrder>()
                .eq(CommodityExceptionOrder::getOrderNo, orderNo)
                .in(CommodityExceptionOrder::getBizType, bizTypes);
        return commodityExceptionOrderMapper.selectList(queryWrapper);
    }

    @Override
    public CommodityExceptionOrder getById(Long id) {
        return commodityExceptionOrderMapper.selectById(id);
    }

    @Override
    public void updateReasonById(Long id, Integer reasonType,String reason) {
        reason = StrUtil.maxLength(reason, 100);
        commodityExceptionOrderMapper.updateReasonById(id,reasonType, reason);
    }

    @Override
    public IPage<OrderOverSoldRespDto> orderOverSoldQuery(OrderOverSoldReqDto reqDto){
        Page<OrderOverSoldRespDto> page = new Page<>(reqDto.getCurrentPage(),reqDto.getPageSize());
        IPage<OrderOverSoldRespDto> pageData = commodityExceptionOrderMapper.orderOverSoldQuery(page,reqDto);
        if(CollectionUtils.isEmpty(pageData.getRecords())){
            return pageData;
        }
        // 补充返参数据
        List<Long> orderNoList = pageData.getRecords().stream().map(dto->Long.valueOf(dto.getOrderNo())).collect(Collectors.toList());
        List<String> erpCodeList = pageData.getRecords().stream().map(OrderOverSoldRespDto::getErpCode).collect(Collectors.toList());
        List<OrderDetail> orderDetailList = orderDetailService.queryByOrderNoErpCodes(orderNoList,erpCodeList);
        Map<String,OrderDetail> commodityInfoMap = orderDetailList.stream().collect(Collectors.toMap(OrderDetail::uniqueKeyOrderNoErpCode, Function.identity(),(o1, o2)->o1));
        pageData.getRecords().forEach(data -> {
            // 平台编码名称
            Optional.ofNullable(PlatformCodeEnum.getByCode(data.getThirdPlatformCode())).ifPresent(info -> {
                data.setThirdPlatformName(info.getType());
            });
            // 超卖原因
            Optional.ofNullable(OrderOverSoldEnum.getByCode(data.getReasonType())).ifPresent(info -> {
                data.setReason(info.getMsg());
            });
            // 商品名称、商品图片（明细存在，取订单明细里的）
            Optional.ofNullable(commodityInfoMap.get(uniqueKey(data.getOrderNo(),data.getErpCode()))).ifPresent(info -> {
                if(!StringUtils.isEmpty(info.getCommodityName())) {
                    data.setErpName(info.getCommodityName());
                }
                data.setMainPic(info.getMainPic());
            });
        });
        return pageData;
    }

    @Override
    public void saveOrUpdate(List<CommodityExceptionOrder> dataList) {
        if(CollectionUtils.isEmpty(dataList)){
            return;
        }
        List<Long> orderNoList = dataList.stream().map(CommodityExceptionOrder::getOrderNo).collect(Collectors.toList());
        QueryWrapper<CommodityExceptionOrder> queryWrapper = new QueryWrapper<>();
        queryWrapper.lambda().in(CommodityExceptionOrder::getOrderNo,orderNoList);
        List<CommodityExceptionOrder> existDataList = baseMapper.selectList(queryWrapper);
        if (CollectionUtils.isEmpty(existDataList)) {
            existDataList = Collections.emptyList();
        }
        // 需要校验已有数据不处理
        Map<String,CommodityExceptionOrder> existDataMap = existDataList.stream().collect(Collectors.toMap(CommodityExceptionOrder::uniqueKey,c->c,(c1,c2)->c1));
        List<CommodityExceptionOrder> updateDataList = dataList.stream().filter(data -> existDataMap.containsKey(data.uniqueKey())).collect(Collectors.toList());
        List<CommodityExceptionOrder> insertDataList = dataList.stream().filter(data -> !existDataMap.containsKey(data.uniqueKey())).collect(Collectors.toList());
        if (!CollectionUtils.isEmpty(updateDataList)) {
            updateDataList.forEach(data -> {
                data.setId(existDataMap.get(data.uniqueKey()).getId());
            });
            updateBatchById(updateDataList);
        }
        if (!CollectionUtils.isEmpty(insertDataList)) {
            insertDataList.forEach(data -> {
                data.setCreateTime(new Date());
            });
            saveBatch(insertDataList);
        }
    }

    @Override
    public void alarmExceptionOrderByType(AlarmExceptionOrderReq req){
        OrderOverSoldEnum orderOverSoldEnum = checkAlarmReq(req);
        if (Objects.isNull(orderOverSoldEnum)){
            return;
        }
        List<AlarmExceptionOrderResp> alarmList = queryAlarmData(req);
        if(CollectionUtils.isEmpty(alarmList)){
            return;
        }

        // 按大区分组并打印告警日志
        Map<String,List<AlarmExceptionOrderResp>> deptAlarmMap = alarmList.stream().collect(Collectors.groupingBy(AlarmExceptionOrderResp::getDept));
        String model = orderOverSoldEnum.getAlarmInfo().getModel();
        String function = orderOverSoldEnum.getAlarmInfo().getFunction();
        String alarmLevel = orderOverSoldEnum.getAlarmInfo().getAlarmLevel();
        String alarmMsgFormat = orderOverSoldEnum.getAlarmInfo().getAlarmMsg();
        deptAlarmMap.entrySet().forEach(alarmMap -> {
            StringBuilder stringBuilder = new StringBuilder();
            alarmMap.getValue().forEach(alarmInfo -> {
                stringBuilder.append(String.format(alarmMsgFormat,alarmInfo.getMerCode(),alarmInfo.getOrderNums()));
            });
            String alarmMsg = stringBuilder.toString();
            ExLogger.logger("BusinessAlarm").field(alarmMap.getValue().get(0).getMerCode()).field(model).field(function).field(alarmLevel).warn(alarmMsg);
        });
    }

    private List<AlarmExceptionOrderResp> queryAlarmData(AlarmExceptionOrderReq req) {
        // 获取告警数据
        List<AlarmExceptionOrderResp> alarmList = baseMapper.queryAlarmInfo(req);
        if(CollectionUtils.isEmpty(alarmList)){
            return Collections.emptyList();
        }
        // 过滤告警数大于1 的数据
        alarmList = alarmList.stream().filter(alarmInfo -> Objects.nonNull(alarmInfo.getOrderNums()) && alarmInfo.getOrderNums().compareTo(DsConstants.INTEGER_ONE) > 0)
                .collect(Collectors.toList());
        if(CollectionUtils.isEmpty(alarmList)){
            return Collections.emptyList();
        }
        // 获取商户大区类型
        List<String> merCodeList = alarmList.stream().map(AlarmExceptionOrderResp::getMerCode).distinct().collect(Collectors.toList());
        ResponseBase<List<MerchantRegionResp>> result = middleBaseInfoClient.queryMerchantRegionList(merCodeList);
        if(Objects.isNull(result) || !result.checkSuccess() || CollectionUtils.isEmpty(result.getData())){
            return Collections.emptyList();
        }
        Map<String,String> merChantDeptMap = result.getData().stream().collect(Collectors.toMap(MerchantRegionResp::getMerCode,MerchantRegionResp::getBedept,(a1, a2)->a1));
        alarmList.forEach(alarmInfo -> {
            // 给默认值
            alarmInfo.setDept(DsConstants.STRING_ONE);
            Optional.ofNullable(merChantDeptMap.get(alarmInfo.getMerCode())).ifPresent(dept -> {
                alarmInfo.setDept(dept);
            });
        });
        return alarmList;
    }

    private OrderOverSoldEnum checkAlarmReq(AlarmExceptionOrderReq req) {
        // 校验
        OrderOverSoldEnum orderOverSoldEnum = OrderOverSoldEnum.getByCode(req.getOrderOverSoldType());
        if(Objects.isNull(orderOverSoldEnum)){
            return null;
        }
        // 处理时间
        Date now = new Date();
        Date yesterday = DateUtil.getDayBefore(now,1);
        if(Objects.isNull(req.getStartTime())){
            req.setStartTime(DateUtil.getDayBeginTime(yesterday));
        }
        if(Objects.isNull(req.getEndTime())){
            req.setEndTime(DateUtil.getDayEndTime(yesterday));
        }
        return orderOverSoldEnum;
    }

    private String uniqueKey(String orderNo,String erpCode){
        return String.format(FORMAT,orderNo,erpCode);
    }
}
