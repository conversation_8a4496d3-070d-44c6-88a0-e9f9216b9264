package cn.hydee.middle.business.order.yxtadapter.infrastructure.domainserviceimpl.hdpos;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.extra.spring.SpringUtil;
import cn.hydee.middle.business.order.Enums.*;
import cn.hydee.middle.business.order.domain.OrderDetailDomain;
import cn.hydee.middle.business.order.dto.req.SaleOutBillParamDTO;
import cn.hydee.middle.business.order.entity.*;
import cn.hydee.middle.business.order.exception.OmsException;
import cn.hydee.middle.business.order.mapper.*;
import cn.hydee.middle.business.order.module.orderdiff.handler.OrderDiffManager;
import cn.hydee.middle.business.order.route.domain.AllotDomainService;
import cn.hydee.middle.business.order.route.domain.enums.AllotStatusEnum;
import cn.hydee.middle.business.order.service.MedicalTraceCodeService;
import cn.hydee.middle.business.order.service.async.HydeeEsSyncClientAsync;
import cn.hydee.middle.business.order.util.DateUtil;
import cn.hydee.middle.business.order.util.ExceptionUtil;
import cn.hydee.middle.business.order.v2.annotation.OrderLock;
import cn.hydee.middle.business.order.v2.manager.ErpBillInfoManager;
import cn.hydee.middle.business.order.v2.manager.base.OrderDetailManager;
import cn.hydee.middle.business.order.v2.manager.base.OrderInfoManager;
import cn.hydee.middle.business.order.v2.manager.base.OrderPayInfoManager;
import cn.hydee.middle.business.order.v2.manager.base.StoreBillConfigManager;
import cn.hydee.middle.business.order.yxtadapter.constant.AssignmentBizType;
import cn.hydee.middle.business.order.yxtadapter.domain.dictionary.InnerStoreDictionary;
import cn.hydee.middle.business.order.yxtadapter.domain.pos.hdpos.AllotCallBackRequest;
import cn.hydee.middle.business.order.yxtadapter.domain.pos.hdpos.HdPosOrderPrescriptionCallBackResponse;
import cn.hydee.middle.business.order.yxtadapter.domain.pos.hdpos.OrderBillRequest;
import cn.hydee.middle.business.order.yxtadapter.domain.pos.hdpos.OrderBillResponse;
import cn.hydee.middle.business.order.yxtadapter.domain.pos.hdpos.OrderPrescriptionRequest;
import cn.hydee.middle.business.order.yxtadapter.domain.pos.hdpos.HdOrderPrescriptionDTO;
import cn.hydee.middle.business.order.yxtadapter.domain.pos.hdpos.OrderPrescriptionRequest.QueryOrderPrescriptionParam;
import cn.hydee.middle.business.order.yxtadapter.domainservice.assignment.AssignmentEngine;
import cn.hydee.middle.business.order.yxtadapter.domainservice.hdpos.HdPosGateway;
import cn.hydee.middle.business.order.yxtadapter.domainservice.hdpos.RefundProcessor;
import cn.hydee.middle.business.order.yxtadapter.infrastructure.domainserviceimpl.hdpos.context.HdPosBaseInfoContext;
import cn.hydee.middle.business.order.yxtadapter.infrastructure.domainserviceimpl.hdpos.context.RefundProcessorFactory;
import cn.hydee.middle.business.order.yxtadapter.infrastructure.domainserviceimpl.hdpos.convert.HdPosOrderSimpleInfoConvert;
import cn.hydee.middle.business.order.yxtadapter.infrastructure.domainserviceimpl.hdpos.refund.RefundAccountingEcodeHelper;
import cn.hydee.middle.business.order.yxtadapter.infrastructure.feign.PosInterfaceClient;
import cn.hydee.starter.dto.ResponseBase;
import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.google.common.collect.Lists;
import java.util.stream.Collectors;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.jetbrains.annotations.Nullable;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Lazy;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.transaction.support.TransactionTemplate;
import org.springframework.util.CollectionUtils;
import org.springframework.util.ObjectUtils;

import java.util.ArrayList;
import java.util.List;
import java.util.Objects;
import java.util.Optional;
import java.util.concurrent.Executor;

import static cn.hydee.middle.business.order.configuration.ThreadPoolConfig.ORDER_BUSINESS_ASYNC_THREAD_POOL;

/**
 * <AUTHOR>
 * @date 2023/12/20
 * @since 1.0
 */
@Slf4j
@Component
public class HdPosGatewayImpl implements HdPosGateway {


    @Autowired
    private OrderInfoManager orderInfoManager;
    @Autowired
    private OrderPayInfoManager orderPayInfoManager;
    @Autowired
    private ErpBillInfoManager erpBillInfoManager;
    @Autowired
    private StoreBillConfigManager storeBillConfigManager;
    @Autowired
    private HdPosOrderAccountingAdapter hdPosOrderAccountingAdapter;
    @Autowired
    private PosInterfaceClient hdPosClient;
    @Autowired
    private RefundOrderMapper refundOrderMapper;
    @Autowired
    private OrderDetailMapper orderDetailMapper;
    @Autowired
    private OrderPickInfoMapper orderPickInfoMapper;
    @Autowired
    private ErpRefundInfoMapper erpRefundInfoMapper;
    @Autowired
    private HdPosOrderSimpleInfoConvert hdPosOrderSimpleInfoConvert;
    @Autowired
    private RefundDetailMapper refundDetailMapper;
    @Autowired
    private AssignmentEngine assignmentEngine;
    @Autowired
    private OrderDetailManager orderDetailManager;
    @Autowired
    @Lazy
    private OrderDiffManager orderDiffManager;
    @Autowired
    private InnerStoreDictionaryMapper innerStoreDictionaryMapper;
    @Autowired
    private TransactionTemplate transactionTemplate;

    @Autowired
    private AllotDomainService allotDomainService;

    @Autowired
    private MedicalTraceCodeMapper medicalTraceCodeMapper;

    @Autowired
    private MedicalTraceCodeService medicalTraceCodeService;

    @Autowired
    private OrderInfoMapper orderInfoMapper;

    @Autowired
    private OrderPrescriptionMapper orderPrescriptionMapper;


    @Qualifier(ORDER_BUSINESS_ASYNC_THREAD_POOL)
    @Autowired
    private Executor hdPosAccountingExecutor;

    /* 重复全额退款校验开关 */
    @Value("${eBaiRepeatRefundFlag:true}")
    private Boolean eBaiRepeatRefundFlag;

    /* 是否是上海Pos切换 */
    @Value("${is.pos.switch.shanghai:false}")
    private Boolean isPosSwitchShanghai;

    //医保订单下账门店编码
    @Value("#{'${business.order.hdpos.h2.medicalInsuranceBillStoreCode}'.split(',')}")
    private List<String> medicalInsuranceBillStoreCodes;

    @Async("hdPosAccountingExecutor")
    @Override
    public Boolean orderAccountingAsync(String orderNo) {
        return orderAccounting(orderNo);
    }
    @Async("hdPosAccountingExecutor")
    @Override
    public Boolean refundOrderAccountingAsync(String refundNo, Integer businessFlag) {
        return refundOrderAccounting(refundNo,businessFlag);
    }

    @Override
    public void routeAllotCallback(AllotCallBackRequest request) {
        log.info("海典调拨回调入参：request:{}", JSONObject.toJSON(request));
        if(AllotStatusEnum.SUCCESS.name().equals(request.getAllotStatus()) && StringUtils.isEmpty(request.getPosAllotNo())){
            log.error("调拨成功时，pos调拨单号不能为空 omsAllotNo:{}", request.getOmsAllotNo());
            throw ExceptionUtil.getWarnException(DsErrorType.PARAM_VALID_ERROR.getCode(), "调拨成功时，pos调拨单号不能为空");
        }
        if(AllotStatusEnum.FAIL.name().equals(request.getAllotStatus()) && StringUtils.isEmpty(request.getFailMessage())){
            log.error("调拨失败时，失败原因不能为空 omsAllotNo:{}", request.getOmsAllotNo());
            throw ExceptionUtil.getWarnException(DsErrorType.PARAM_VALID_ERROR.getCode(), "调拨失败时，失败原因不能为空");
        }
        allotDomainService.routeAllotCallback(request);
    }

    @Override
    public HdPosOrderPrescriptionCallBackResponse orderPrescriptionQueryCallback(OrderPrescriptionRequest request) {
        log.info("海典订单处方信息查询回调参数内容：{}",JSONObject.toJSONString(request));
        if(Objects.isNull(request) || CollUtil.isEmpty(request.getParams())){
            return HdPosOrderPrescriptionCallBackResponse.fail("参数错误，检查参数后重试");
        }
        try {
            List<Long> orderNos = request.getParams().stream().map(QueryOrderPrescriptionParam::getOrderNo).filter(StrUtil::isNotBlank).map(Long::parseLong).collect(Collectors.toList());
            List<OrderPrescription> orderPrescriptions = new ArrayList<>();
            int batchSize = 100;
            for (int i = 0; i < orderNos.size(); i += batchSize) {
                int end = Math.min(i + batchSize, orderNos.size());
                List<Long> subOrderNos = orderNos.subList(i, end);
                LambdaQueryWrapper<OrderPrescription> queryWrapper = new LambdaQueryWrapper<OrderPrescription>()
                    .eq(OrderPrescription::getStatus, DsConstants.INTEGER_ONE);
                if(DsConstants.INTEGER_ONE.equals(request.getOrderType())){
                    //b2c处方信息查询
                    queryWrapper.in(OrderPrescription::getOmsOrderNo, subOrderNos);
                }else {
                    queryWrapper.in(OrderPrescription::getOrderNo, subOrderNos);
                }
                List<OrderPrescription> orderPrescriptionList = orderPrescriptionMapper.selectList(queryWrapper);
                if(CollUtil.isNotEmpty(orderPrescriptionList)){
                    orderPrescriptions.addAll(orderPrescriptionList);
                }
            }
            if(CollUtil.isEmpty(orderPrescriptions)){
                return HdPosOrderPrescriptionCallBackResponse.success(Lists.newArrayList());
            }
            List<HdOrderPrescriptionDTO> orderPrescriptionDTOS = Lists.newArrayList();
            orderPrescriptions.forEach(prescription -> {
                HdOrderPrescriptionDTO hdOrderPrescriptionDTO = new HdOrderPrescriptionDTO();
                BeanUtils.copyProperties(prescription, hdOrderPrescriptionDTO);
                if(Objects.nonNull(prescription.getLastPushTime())){
                    hdOrderPrescriptionDTO.setLastPushTime(DateUtil.parseDateToStr(prescription.getLastPushTime(), "yyyy-MM-dd HH:mm:ss"));
                }
                if(DsConstants.INTEGER_ONE.equals(request.getOrderType())){
                    //b2c
                    hdOrderPrescriptionDTO.setOrderNo(String.valueOf(prescription.getOmsOrderNo()));
                }else {
                    //o2o
                    hdOrderPrescriptionDTO.setOrderNo(String.valueOf(prescription.getOrderNo()));
                }

                hdOrderPrescriptionDTO.setCfPicUrl(prescription.getCfpicurl());
                hdOrderPrescriptionDTO.setCfId(prescription.getId().toString());
                orderPrescriptionDTOS.add(hdOrderPrescriptionDTO);
            });
            return HdPosOrderPrescriptionCallBackResponse.success(orderPrescriptionDTOS);
        }catch (Exception e){
            return HdPosOrderPrescriptionCallBackResponse.fail(e.getMessage());
        }
    }

    /**
     * 海典正向单下账入口
     *
     * @param orderNo
     * @return void
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    //@OrderLock
    public Boolean orderAccounting(String orderNo) {
        try{
            log.info("HdPosGatewayImpl orderAccounting orderNo:{}", orderNo);
            //1、查询数据
            long longOrderNo = Long.parseLong(orderNo);
            OrderInfo orderInfo = Optional.ofNullable(orderInfoManager.queryOrderInfo(longOrderNo)).orElseThrow(()-> new OmsException(String.format("订单 %s OrderInfo 信息不存在")));

            String storeCode = orderInfo.getSourceOrganizationCode();
            if(StringUtils.isEmpty(storeCode)){
                storeCode= orderInfo.getOrganizationCode();
                if(orderInfo.getServiceMode().equals(DsConstants.O2O)){
                    log.error("订单{}  SourceOrganizationCode门店编码为空 请开发介入!!!!!!!",orderNo);
                }
            }
            InnerStoreDictionary dictionary = innerStoreDictionaryMapper.selectOne(new QueryWrapper<InnerStoreDictionary>()
                    .lambda().eq(InnerStoreDictionary::getOrganizationCode, storeCode));
            if (!checkHdPos(dictionary.getPosMode())) {
                log.info("非海典下账门店 {}",storeCode);
                return true;
            }
            if(dictionary.getPosUrl().equals("http://101.91.219.131:9398") && isPosSwitchShanghai){
                log.warn("上海H1 Pos切换过程，暂停下账");
                return true;
            }
            if (!beforeAccountCheck(orderInfo)) {
                return true;
            }

            OrderPayInfo  orderPayInfo = Optional.ofNullable(orderPayInfoManager.selectByOrderNo(longOrderNo)).orElseThrow(()-> new OmsException(String.format("订单 %s orderPayInfo 信息不存在",longOrderNo)));
            StoreBillConfig storeBillConfig = Optional.ofNullable(storeBillConfigManager.getBillConfigById(orderInfo.getClientConfId())).orElseThrow(()-> new OmsException(String.format("订单 %s StoreBillConfig 信息不存在",longOrderNo)));
            // 新增海典正单下账时机校验
            if (!checkAutoEnterAccountFlag(storeBillConfig, orderInfo)) return true;
            //2、数据校验(有涉及到数据校验不通过的会修改数据，不调用pos下账)(有一次重算下账金额)
            boolean autoAccountFlag = hdPosOrderAccountingAdapter.orderAccountValidate(orderInfo, orderPayInfo);
            if (!autoAccountFlag) return true;
            // 有运费单运费单下账
            if(Objects.nonNull(orderInfo.getFreightOrderNo())){
                orderAccountingAsync(orderInfo.getFreightOrderNo().toString());
            }

            //3、重算下账金额
            SaleOutBillParamDTO paramDTO = new SaleOutBillParamDTO();
            paramDTO.setOrderInfo(orderInfo);
            List<OrderDetailDomain> orderDetailDomainList = orderDetailManager.selectDetailDomainListByOrderNo(orderInfo.getOrderNo());
            paramDTO.setOrderDetailDomainList(orderDetailDomainList);
            // 【【订单金额下账复核】：下账时核对平台订单金额后再下账】 https://www.tapd.cn/********/prong/stories/view/11********001032886
            orderDiffManager.amountCompareAndReBuildParam(paramDTO);
            //重算金额后重新查询数据拼接报文
            orderPayInfo = Optional.ofNullable(orderPayInfoManager.selectByOrderNo(longOrderNo)).orElseThrow(()-> new OmsException(String.format("订单 %s orderPayInfo 信息不存在",longOrderNo)));
            ErpBillInfo  erpBillInfo = Optional.ofNullable(erpBillInfoManager.getErpBillInfoByOrderNo(longOrderNo)).orElseThrow(()-> new OmsException(String.format("订单 %s erpBillInfo 信息不存在",longOrderNo)));


            //发送海典调拨单
            if(!sendHdCreateAllot(orderInfo, orderDetailDomainList)){
                log.error("推送海典调拨单失败 orderNo:{}", orderInfo.getOrderNo());
                return false;
            }
            //4、拼接报文
            OrderBillRequest request;
            try{
                request = hdPosOrderSimpleInfoConvert.toHdOrderBillRequestConvert(orderInfo, erpBillInfo, orderPayInfo, storeBillConfig,orderDetailDomainList,dictionary);
            }catch (MedicalTraceCodeException zhuiShuMaException){
                ResponseBase responseBase = new ResponseBase();
                responseBase.setMsg(zhuiShuMaException.getMessage());
                hdPosOrderAccountingAdapter.afterOrderAccountingFail(orderInfo, responseBase);
                assignmentEngine.createAssignmentMissExist(AssignmentBizType.ORDER_BILL_ACCOUNTING,orderNo,orderNo, responseBase.getMsg());
                return true;
            }
            //5、调用海典POS下账
            ResponseBase<OrderBillResponse> responseBase = null;
            if(PosModeEnum.POS_HD_H2.getCode().equals(dictionary.getPosMode()) && medicalInsuranceBillStoreCodes.contains(request.getOrganizationCode()) && DsConstants.INTEGER_ONE.equals(request.getMedicalInsurance())){
                //海典H2的指定门店的医保订单走新下账接口
                responseBase = hdPosClient.medicalInsuranceOrderAccounting(request, dictionary);
            }else {
                responseBase = hdPosClient.orderAccounting(request, dictionary);
            }
            if (DsConstants.STRING_ONE.equals(responseBase.getCode()) && !ObjectUtils.isEmpty(responseBase.getData())){
                orderInfo.setErpSaleNo(responseBase.getData().getSaleno());
                hdPosOrderAccountingAdapter.afterOrderAccountingSuccess(orderInfo, request);
            }else{
                hdPosOrderAccountingAdapter.afterOrderAccountingFail(orderInfo, responseBase);
                assignmentEngine.createAssignmentMissExist(AssignmentBizType.ORDER_BILL_ACCOUNTING,orderNo,orderNo, responseBase.getMsg());
            }
            return true;
        }catch (Exception e){
            assignmentEngine.createAssignmentByNewRequires(AssignmentBizType.ORDER_BILL_ACCOUNTING,orderNo,orderNo, "海典pos订单下账异常");
            log.error("正向单下账处理失败 原因 {}",e);
            throw new OmsException(e.getMessage());
        }
    }

    /** 校验下账时机 true 允许下账 false 不允许下账
     * <AUTHOR>
     * @Description
     * @date 2024/3/14 17:19
     */
    @Nullable
    private static Boolean checkAutoEnterAccountFlag(StoreBillConfig storeBillConfig, OrderInfo orderInfo) {
        if(ErpStateEnum.HAS_SALE.getCode().equals(orderInfo.getErpState())){return false;}

        // 下账时机校验添加
        Integer autoEnterAccountFlag = storeBillConfig.getAutoEnterAccountFlag();

        Integer orderState = orderInfo.getOrderState();
        // 拣货完成后下账 订单状态>待拣货
        // 允许下账
        if(AutoEnterAccountFlagEnum.PICK.getCode().equals(autoEnterAccountFlag) && orderState>OrderStateEnum.UN_PICK.getCode()){
            return true;
        }
        // 配送出库后下账 订单状态>待配送
        // 允许下账
        if(AutoEnterAccountFlagEnum.DELIVERY_OUT.getCode().equals(autoEnterAccountFlag) && orderState>OrderStateEnum.UN_DELIVERY.getCode()){
            return true;
        }
        return false;
    }

    /**
     * 发送海典创建调拨单
     * */
    private Boolean sendHdCreateAllot(OrderInfo orderInfo,List<OrderDetailDomain> orderDetailDomain){
        if (DsConstants.B2C.equals(orderInfo.getServiceMode()) && StringUtils.isEmpty(orderInfo.getSourceOrganizationCode())){
            return true;
        }
        if (Objects.equals(orderInfo.getOrganizationCode(), orderInfo.getSourceOrganizationCode())
                || StringUtils.isEmpty(orderInfo.getSourceOrganizationCode()) || StringUtils.isEmpty(orderInfo.getOrganizationCode())) {
            return true;
        }
        List<OrderPickInfo> orderPickInfoList = new ArrayList<>();
        orderDetailDomain.stream().map(OrderDetailDomain::getOrderPickInfoList).forEach(orderPickInfoList::addAll);
        List<OrderDetail> orderDetails = BeanUtil.copyToList(orderDetailDomain, OrderDetail.class);
        return allotDomainService.sendHdCreateOrderAllot(orderInfo, orderDetails, orderPickInfoList);
    }

    /**
     * 正单下账前置校验
     * */
    private boolean beforeAccountCheck(OrderInfo orderInfo){
        if(orderInfo.getOrderState() <= OrderStateEnum.UN_PICK.getCode()){
            log.info("订单需拣货后才可海典POS下账: orderNo:{}, thirdOrderNo:{}",orderInfo.getOrderNo(), orderInfo.getThirdOrderNo());
            return false;
        }
        //是否调用ERP接口标识校验
        if (!IntYesNoEnum.YES.getCode().equals(orderInfo.getCallErpFlag())) {
            log.info("订单无需通过ERP进行海典POS下账：orderNo:{}, thirdOrderNo:{}", orderInfo.getOrderNo(), orderInfo.getThirdOrderNo());
            return false;
        }
        //已下账、已取消、已退款无需下账校验
        if (orderInfo.getErpState() >= ErpStateEnum.HAS_SALE.getCode()) {
            log.info("订单已无需海典POS下账：orderNo:{}, thirdOrderNo:{}", orderInfo.getOrderNo(), orderInfo.getThirdOrderNo());
            return false;
        }
        return true;
    }

    /**
     * 海典退款单下账入口
     *
     * @param refundNo 退款单号
     * @return void
     */
    @OrderLock
    @Override
    public Boolean refundOrderAccounting(String refundNo, Integer businessFlag) {
        try {
            return refundOrderAccountingException(refundNo,businessFlag);
        }catch (Exception e){
            log.error("海典下账失败refundNo:{},businessFlag:{} error:{}",refundNo,businessFlag,e);
            return false;
        }
    }

    public Boolean refundOrderAccountingException(String refundNo, Integer businessFlag){
        log.info("HdPosGatewayImpl refundOrderAccounting refundNo:{}", refundNo);
        //查询订单 相关信息 填入上下文
        RefundOrder refundOrder = Optional.ofNullable(refundOrderMapper.selectByRefundNo(Long.valueOf(refundNo)))
                .orElseThrow(()->new OmsException(String.format("退款单不存在,请检查 退款单号 %s", refundNo)));
        if (refundOrder.getErpState() > RefundErpStateEnum.REFUND_FAIL.getCode()) {
            log.info("退款单已下账或已取消 refundNo {}",refundNo);
            return true;
        }
        if(!RefundStateEnum.SUCCESS.getCode().equals(refundOrder.getState())){
            log.info("退款单状态不是已经完成不允许下账 refundNo {}",refundNo);
            return true;
        }
        OrderInfo orderInfo = orderInfoManager.queryOrderInfo(refundOrder.getOrderNo());
        String storeCode = orderInfo.getSourceOrganizationCode();
        if(StringUtils.isEmpty(storeCode)){
            storeCode= orderInfo.getOrganizationCode();
            if(orderInfo.getServiceMode().equals(DsConstants.O2O)){
                log.error("退款单{}  SourceOrganizationCode门店编码为空 请开发介入!!!!!!!",refundNo);
            }
        }

        if (ErpStateEnum.HAS_SALE.getCode().equals(orderInfo.getErpState())) {
            RefundAccountingEcodeHelper refundAccountingEcodeHelper = new RefundAccountingEcodeHelper(medicalTraceCodeMapper,orderInfoMapper,refundDetailMapper, medicalTraceCodeService);
            try {
                boolean flag = refundAccountingEcodeHelper.checkMedicalTraceCode(refundOrder);
                if (!flag) {
                    log.warn("退款单没有录入追溯码,不允许下账, orderNo {} refundNo {}", refundOrder.getOrderNo(), refundOrder.getRefundNo());
                    return true;
                }
            } catch (MedicalTraceCodeException e) {
                HydeeEsSyncClientAsync.refundLogToES(DsConstants.INTEGER_ONE, refundOrder, RefundLogEnum.REFUND_BILL,businessFlag, e.getMessage(), null);
                return true;
            }


        }
        InnerStoreDictionary dictionary = innerStoreDictionaryMapper.selectOne(new QueryWrapper<InnerStoreDictionary>()
                .lambda().eq(InnerStoreDictionary::getOrganizationCode, storeCode));
        if (!checkHdPos(dictionary.getPosMode())) {
            log.info("非海典下账门店 {}",storeCode);
            return true;
        }
        if(dictionary.getPosUrl().equals("http://101.91.219.131:9398") && isPosSwitchShanghai){
            log.warn("上海H1 Pos切换过程，暂停下账");
            return true;
        }
        //部分退款校验
        if(!partRefundCheck(refundOrder)){
            log.info("海典部分退款单校验未通过： refundNo:{}", refundNo);
            return true;
        };
        if(eBaiRepeatRefundFlag){
            if(!eBaiRefundCheck(orderInfo, refundOrder)){
                log.info("海典重复退款: thirdRefundNo:{}, refundNo:{}", refundOrder.getThirdRefundNo(), refundOrder.getRefundNo());
                return true;
            }
        }

        List<OrderDetail> orderDetailList = Optional.ofNullable(orderDetailMapper.selectDetailListByOrderNo(orderInfo.getOrderNo()))
                .orElseThrow(()-> new OmsException(String.format("订单 %s orderDetail 不存在",orderInfo.getOrderNo())));

        OrderPayInfo orderPayInfo = Optional.ofNullable(orderPayInfoManager.selectByOrderNo(orderInfo.getOrderNo()))
                    .orElseThrow(()-> new OmsException(String.format("订单 %s orderPayInfo 不存在",orderInfo.getOrderNo())));

        ErpRefundInfo erpRefundInfo = Optional.ofNullable(erpRefundInfoMapper.selectByRefundNo(Long.valueOf(refundNo)))
                .orElseThrow(()-> new OmsException(String.format("退款单 %s ErpRefundInfo 不存在",refundNo)));
        StoreBillConfig storeBillConfig = Optional.ofNullable(storeBillConfigManager.getBillConfigById(orderInfo.getClientConfId()))
                .orElseThrow(()-> new OmsException(String.format("订单 %s 下账配置不存在",orderInfo.getOrderNo())));
        List<OrderPickInfo> orderPickInfoList = Optional.ofNullable(orderPickInfoMapper.selectPickInfoListByOrderNo(orderInfo.getOrderNo()))
                .orElseThrow(()-> new OmsException(String.format("订单 %s 拣货信息不存在",orderInfo.getOrderNo())));
        List<RefundDetail> refundDetailList = Optional.ofNullable(refundDetailMapper.selectListByRefundNo(Long.valueOf(refundNo)))
                .orElseThrow(()-> new OmsException(String.format("退款单 %s 退款明细不存在",refundNo)));

        HdPosBaseInfoContext context = new HdPosBaseInfoContext(orderInfo, orderPayInfo, orderDetailList, refundOrder);
        context.setStoreBillConfig(storeBillConfig);
        context.setOrderPickInfoList(orderPickInfoList);
        context.setErpRefundInfo(erpRefundInfo);
        context.setRefundDetailList(refundDetailList);
        context.setPosMode(dictionary.getPosMode());
        context.setInnerStoreDictionary(dictionary);


        // 如果正单含有追溯码 则需要正单重新下账
        if(ErpStateEnum.WAIT_SALE.getCode().equals(orderInfo.getErpState())) {
            Integer count = medicalTraceCodeMapper.queryOrderTraceCodeCount(orderInfo.getOrderNo());
            if (count > 0) {
                try {
                    orderAccounting(orderInfo.getOrderNo().toString());
                } catch (Exception e) {
                    log.error("退款审核后，正单下账失败,orderNo:{},refundNo:{}",refundOrder.getOrderNo(),refundOrder.getRefundNo(),e);
                }
            }
        }
        Boolean isSucc = transactionTemplate.execute(transactionStatus -> {
            try {
                RefundProcessor processor = RefundProcessorFactory.getProcessor(orderInfo.getErpState());
                if (Objects.isNull(processor)) {
                    processor = RefundProcessorFactory.getProcessor(DsConstants.INTEGER_ZERO);
                }
                boolean b = processor.handleRefund(context, businessFlag);
                if (!b) {
                    transactionStatus.setRollbackOnly();
                }
                return b;
            }catch (Exception e) {
                log.error("退款单下账失败 原因 {}",e);
                transactionStatus.setRollbackOnly();
            }
            return false;
        });

        //调拨单创建
        hdPosAccountingExecutor.execute(() -> {
            SpringUtil.getBean(AllotDomainService.class)
                    .sendHdCreateRefundAllot(context.getOrderInfo(), context.getRefundOrder());

        });
        if (!isSucc.booleanValue()) {
            //创建退款调拨单
            assignmentEngine.createAssignmentMissExist(AssignmentBizType.REFUND_ORDER_BILL_ACCOUNTING,refundOrder.getRefundNo().toString(),businessFlag.toString(),null);
        }
        return isSucc;
    }

    private boolean checkHdPos(Integer posMode){
        return !PosModeEnum.POS_KC.getCode().equals(posMode);
    }

    /**
     * 解决重复推送海典H1下账问题
     *
     * @param orderInfo
     * @param refundOrder
     * @return boolean
     */
    public boolean eBaiRefundCheck(OrderInfo orderInfo, RefundOrder refundOrder){
        if(!refundOrder.getThirdRefundNo().equals(refundOrder.getRefundNo().toString())){
            return true;
        }
        //判断是否有多个相同退款单的情况，有的话，正单改为已取消时产生的退款单不推送海典下账
        List<RefundOrder> refundOrders = refundOrderMapper.selectList(new QueryWrapper<RefundOrder>().lambda()
                .eq(RefundOrder::getType, DsConstants.STRING_ONE).eq(RefundOrder::getThirdOrderNo, orderInfo.getThirdOrderNo()));
        if(refundOrders.size() == 1) return true;
        for (RefundOrder refund:refundOrders) {
            if(refund.getThirdRefundNo().equals(refund.getRefundNo().toString())){
                continue;
            }
            if(refund.getErpState().equals(RefundErpStateEnum.WAIT_REFUND.getCode())){
                //有用户发起退款产生的mq消息未下账，不把此正单改为已取消时产生的退款单推送海典
                log.info("重复退款，用户发起退款产生的退款单未下帐，不把已取消时产生的退款单推送海典：refundNo:{}", refund.getRefundNo());
                return false;
            } else if (refund.getErpState().equals(RefundErpStateEnum.HAS_REFUND.getCode())) {
                //有用户发起退款产生的mq消息已下账，把正单改为已取消时产生的退款消息下账状态改为已取消
                log.info("重复退款，用户发起退款产生的退款单已下账，正单已取消时产生的退款单改为已取消：refundNo:{}", refund.getRefundNo());
                RefundOrder updateRefund = new RefundOrder();
                updateRefund.setErpState(RefundErpStateEnum.CANCELED.getCode());
                refundOrderMapper.update(updateRefund, new QueryWrapper<RefundOrder>().lambda().eq(RefundOrder::getRefundNo, refundOrder.getRefundNo()));
                return false;
            }
        }
        return true;
    }

    public boolean partRefundCheck(RefundOrder refundOrder){
        if(!RefundTypeEnum.PART.getCode().equals(refundOrder.getType())){
            return true;
        }
        List<RefundOrder> refundOrders = refundOrderMapper.selectList(new QueryWrapper<RefundOrder>().lambda()
                .eq(RefundOrder::getType, RefundTypeEnum.ALL.getCode()).eq(RefundOrder::getThirdOrderNo, refundOrder.getThirdOrderNo()));
        if (!CollectionUtils.isEmpty(refundOrders)){
            for (RefundOrder refund:refundOrders) {
                if(RefundErpStateEnum.WAIT_REFUND.getCode().equals(refund.getErpState()) || RefundErpStateEnum.REFUND_FAIL.getCode().equals(refund.getErpState())){
                    log.info("海典部分退款单下账需等全额退款单下账完成: refundNo:{}", refund.getRefundNo());
                    return false;
                }
                if(RefundErpStateEnum.HAS_REFUND.getCode().equals(refund.getErpState())){
                    log.info("海典全额退款单下账完成，部分退款单更新为已取消，无需下账: refundNo:{}", refund.getRefundNo());
                    RefundOrder updateRefund = new RefundOrder();
                    updateRefund.setErpState(RefundErpStateEnum.CANCELED.getCode());
                    refundOrderMapper.update(updateRefund, new QueryWrapper<RefundOrder>().lambda().eq(RefundOrder::getRefundNo, refundOrder.getRefundNo()));
                    return false;
                }
            }
        }
        return true;
    }
}
