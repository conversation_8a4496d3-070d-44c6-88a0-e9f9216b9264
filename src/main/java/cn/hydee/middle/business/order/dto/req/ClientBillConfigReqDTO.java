package cn.hydee.middle.business.order.dto.req;

import cn.hydee.starter.dto.PageBase;
import lombok.Data;
import lombok.EqualsAndHashCode;

import javax.validation.constraints.NotBlank;

/**
 * <AUTHOR>
 */
@Data
@EqualsAndHashCode(callSuper = true)
public class ClientBillConfigReqDTO extends PageBase {
    /**
     * 企业编码
     */
    @NotBlank(message = "企业编码不能为空")
    private String merCode;

    /**
     * 平台编码
     */
    @NotBlank(message = "平台编码不能为空")
    private String platformCode;

    /**
     * 网店编码
     */
    @NotBlank(message = "网店编码不能为空")
    private String clientCode;

    /**
     * 门店编码
     */
    @NotBlank(message = "门店编码不能为空")
    private String storeCode;

}
