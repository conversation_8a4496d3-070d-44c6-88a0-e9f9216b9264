package cn.hydee.middle.business.order.client.ws.handler;

import cn.hydee.middle.business.order.client.ws.dto.ReceiveFromWsMessageDTO;
import cn.hydee.middle.business.order.service.TemplateInfoService;
import cn.hydee.middle.business.order.util.JsonUtil;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.util.StringUtils;

@Service("wsPrintTemplateMessageHandler")
@Slf4j
public class WsPrintTemplateMessageHandler extends BaseWsMessageHandler {

    @Autowired
    private TemplateInfoService templateInfoService;

    @Override
    public void handle(ReceiveFromWsMessageDTO message, String validMsg) {

        // 校验
        validMsg = checkParam(message, validMsg);
        if (!StringUtils.isEmpty(validMsg)) {
            log.warn("[{}]====>wsPrintTemplateMessageHandler message valid,cause:{},data:{}",message.getOmsId(), validMsg,
                    JsonUtil.object2Json(message));
            return;
        }
        // 推送一次全量打印模板数据（异步）
        templateInfoService.pushAllPrintTemplateDataAsync(message.getMerCode(), message.getStoreCode());
    }

}
