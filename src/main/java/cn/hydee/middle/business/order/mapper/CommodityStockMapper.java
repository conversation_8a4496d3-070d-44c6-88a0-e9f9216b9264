package cn.hydee.middle.business.order.mapper;

import cn.hydee.middle.business.order.Enums.DsConstants;
import cn.hydee.middle.business.order.dto.req.CommodityStockReqDto;
import cn.hydee.middle.business.order.dto.req.OrderPageReqDto;
import cn.hydee.middle.business.order.dto.rsp.OrderInfoPageRsp;
import cn.hydee.middle.business.order.entity.CommodityStock;
import com.baomidou.dynamic.datasource.annotation.DS;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import org.apache.ibatis.annotations.Param;
import org.springframework.stereotype.Repository;

import java.util.List;

/**
 * @author: xin.tu
 * @date: 2023/1/6 10:09
 * @menu:
 */
@Repository
public interface CommodityStockMapper extends BaseMapper<CommodityStock> {




    @DS(DsConstants.DB_ORDER_SLAVE)
    int getUnLockStockJobCount(@Param("param")CommodityStockReqDto dto);




    /**
     * 正常状态订单列表
     */
    @DS(DsConstants.DB_ORDER_SLAVE)
    IPage<CommodityStock> selectCommodityStockPage(Page<CommodityStock> page, @Param("param") CommodityStockReqDto dto);

}
