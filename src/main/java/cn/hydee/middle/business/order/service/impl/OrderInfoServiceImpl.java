package cn.hydee.middle.business.order.service.impl;

import static cn.hydee.middle.business.order.Enums.DsConstants.B2C;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.map.MapUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.extra.spring.SpringUtil;
import cn.hydee.middle.business.order.Enums.*;
import cn.hydee.middle.business.order.Enums.oborder.*;
import cn.hydee.middle.business.order.configuration.MerCodesConfig;
import cn.hydee.middle.business.order.domain.*;
import cn.hydee.middle.business.order.doris.service.OrderDorisService;
import cn.hydee.middle.business.order.dto.*;
import cn.hydee.middle.business.order.dto.commodity.CommodityStockInfoDto;
import cn.hydee.middle.business.order.dto.erp.BatchStockReqDto;
import cn.hydee.middle.business.order.dto.erp.inner.BatchStock;
import cn.hydee.middle.business.order.dto.message.NetOrderRemindNotifyMessage;
import cn.hydee.middle.business.order.dto.net.NetOrderGetDetailReqDto;
import cn.hydee.middle.business.order.dto.order.forced_refresh.req.ForcedRefreshDto;
import cn.hydee.middle.business.order.dto.req.*;
import cn.hydee.middle.business.order.dto.req.BaseHemsReqDto;
import cn.hydee.middle.business.order.dto.req.CommodityCountReqDto;
import cn.hydee.middle.business.order.dto.req.ErpOrderQueryReqDto;
import cn.hydee.middle.business.order.dto.req.LogisticCallbackReqDto;
import cn.hydee.middle.business.order.dto.req.MatchingStoreReqDto;
import cn.hydee.middle.business.order.dto.req.NewOrderPartRefundReqDto;
import cn.hydee.middle.business.order.dto.req.OrderAuditCheckHandleReq;
import cn.hydee.middle.business.order.dto.req.OrderAuditHandleReq;
import cn.hydee.middle.business.order.dto.req.OrderChangeSellerRemarkReqDto;
import cn.hydee.middle.business.order.dto.req.OrderChangeStoreDTO;
import cn.hydee.middle.business.order.dto.req.OrderCountSearchReqDto;
import cn.hydee.middle.business.order.dto.req.OrderHandleBaseReqDto;
import cn.hydee.middle.business.order.dto.req.OrderHandleExceptionReqDto;
import cn.hydee.middle.business.order.dto.req.OrderHandleReqDto;
import cn.hydee.middle.business.order.dto.req.OrderInfNoReq;
import cn.hydee.middle.business.order.dto.req.OrderInfoSnapshotReqDto;
import cn.hydee.middle.business.order.dto.req.OrderLedgerPageReqDto;
import cn.hydee.middle.business.order.dto.req.OrderPageOtherReqDto;
import cn.hydee.middle.business.order.dto.req.OrderPageReqDto;
import cn.hydee.middle.business.order.dto.req.OrderPartRefundReqDto;
import cn.hydee.middle.business.order.dto.req.OrderPrescriptionQueryReqDto;
import cn.hydee.middle.business.order.dto.req.OrderQueryBySelfCodeReqDto;
import cn.hydee.middle.business.order.dto.req.OrderQueryReqDto;
import cn.hydee.middle.business.order.dto.req.OrderReservationReqDto;
import cn.hydee.middle.business.order.dto.req.OrderSearchMoreStatusDto;
import cn.hydee.middle.business.order.dto.req.OrderVerifyCodeReqDto;
import cn.hydee.middle.business.order.dto.req.QueryOrganizationReqDto;
import cn.hydee.middle.business.order.dto.req.SearchOrderPageReqDto;
import cn.hydee.middle.business.order.dto.req.StoreConfigReqDTO;
import cn.hydee.middle.business.order.dto.req.ThirdOrderDetailReq;
import cn.hydee.middle.business.order.dto.req.UpdateOrderSysStoreReqDto;
import cn.hydee.middle.business.order.dto.req.VerifyReqDto;
import cn.hydee.middle.business.order.dto.req.asyn.CommodityCreateReqDto;
import cn.hydee.middle.business.order.dto.req.baseinfo.DsSynOnlineStoreQueryDTO;
import cn.hydee.middle.business.order.dto.req.erp.ErpWareReqDto;
import cn.hydee.middle.business.order.dto.req.memberRecords.MemberRecordsReqDto;
import cn.hydee.middle.business.order.dto.req.obOrder.CargoTransferSuccessReqDto;
import cn.hydee.middle.business.order.dto.req.obOrder.ObOrderPageReqDto;
import cn.hydee.middle.business.order.dto.rsp.*;
import cn.hydee.middle.business.order.dto.rsp.baseinfo.*;
import cn.hydee.middle.business.order.dto.rsp.AvailablePCAORspDTO;
import cn.hydee.middle.business.order.dto.rsp.BatchThirdStoreCommodityRspDto;
import cn.hydee.middle.business.order.dto.rsp.CommodityCountDetailResult;
import cn.hydee.middle.business.order.dto.rsp.CommodityRspDto;
import cn.hydee.middle.business.order.dto.rsp.ErpOrderInfoRsp;
import cn.hydee.middle.business.order.dto.rsp.MatchingStoreRsp;
import cn.hydee.middle.business.order.dto.rsp.MultiPayType;
import cn.hydee.middle.business.order.dto.rsp.OnlineStoreInfoRspDto;
import cn.hydee.middle.business.order.dto.rsp.OrderInfoPageRsp;
import cn.hydee.middle.business.order.dto.rsp.OrderRealTimeCountRsp;
import cn.hydee.middle.business.order.dto.rsp.OrderSearchCountRsp;
import cn.hydee.middle.business.order.dto.rsp.OrderStateCountRspDto;
import cn.hydee.middle.business.order.dto.rsp.OrderStoreTemplateRspDto;
import cn.hydee.middle.business.order.dto.rsp.OrgOrderInfoSnapshotPageRsp;
import cn.hydee.middle.business.order.dto.rsp.ReceiverDecryptInfoDto;
import cn.hydee.middle.business.order.dto.rsp.ReceiverInfoRsp;
import cn.hydee.middle.business.order.dto.rsp.SearchOrderRspDto;
import cn.hydee.middle.business.order.dto.rsp.StoreOrderInfoSnapshotPageRsp;
import cn.hydee.middle.business.order.dto.rsp.ThirdOrderDetailResDto;
import cn.hydee.middle.business.order.dto.rsp.ThirdOrderDetailResDto.OrderDetailDto;
import cn.hydee.middle.business.order.dto.rsp.ThirdOrderDetailRspDto;
import cn.hydee.middle.business.order.dto.rsp.ThirdOrderInfoAllRsp;
import cn.hydee.middle.business.order.dto.rsp.ThirdPlatCodeNameRsp;
import cn.hydee.middle.business.order.dto.rsp.VerifyRspDto;
import cn.hydee.middle.business.order.dto.rsp.baseinfo.DsOnlineStoreConfResDTO;
import cn.hydee.middle.business.order.dto.rsp.baseinfo.DsOnlineStoreResDTO;
import cn.hydee.middle.business.order.dto.rsp.baseinfo.StoreResDTO;
import cn.hydee.middle.business.order.dto.rsp.baseinfo.SysEmployeeResDTO;
import cn.hydee.middle.business.order.dto.rsp.memberRecords.CommodityInfoRspDto;
import cn.hydee.middle.business.order.dto.rsp.memberRecords.MemberRecordsRspDto;
import cn.hydee.middle.business.order.dto.rsp.obOrder.CommodityStockCheckRspDto;
import cn.hydee.middle.business.order.dto.rsp.obOrder.MerchantRspDto;
import cn.hydee.middle.business.order.dto.rsp.obOrder.OrganizationRspDto;
import cn.hydee.middle.business.order.dto.rsp.upload.OrderInfoStoreRsp;
import cn.hydee.middle.business.order.elasticsearch.config.EsUtils;
import cn.hydee.middle.business.order.elasticsearch.model.ErpOrderDetailModel;
import cn.hydee.middle.business.order.elasticsearch.model.ErpOrderInfoModel;
import cn.hydee.middle.business.order.elasticsearch.service.ElasticService;
import cn.hydee.middle.business.order.elasticsearch.util.ESUtils;
import cn.hydee.middle.business.order.entity.*;
import cn.hydee.middle.business.order.entity.CommodityExceptionOrder;
import cn.hydee.middle.business.order.entity.DeliveryFeeEconomizeRecord;
import cn.hydee.middle.business.order.entity.DeliveryLogisticsCompany;
import cn.hydee.middle.business.order.entity.DsMerchantGroupInfo;
import cn.hydee.middle.business.order.entity.DsOnlineStore;
import cn.hydee.middle.business.order.entity.ErpOrderDetail;
import cn.hydee.middle.business.order.entity.OmsHemsMap;
import cn.hydee.middle.business.order.entity.OrderCount;
import cn.hydee.middle.business.order.entity.OrderCountInfo;
import cn.hydee.middle.business.order.entity.OrderDeliveryAddress;
import cn.hydee.middle.business.order.entity.OrderDeliveryRecord;
import cn.hydee.middle.business.order.entity.OrderDetail;
import cn.hydee.middle.business.order.entity.OrderInfo;
import cn.hydee.middle.business.order.entity.OrderInfoExtendInfo;
import cn.hydee.middle.business.order.entity.OrderLockInfo;
import cn.hydee.middle.business.order.entity.OrderPayInfo;
import cn.hydee.middle.business.order.entity.OrderPickInfo;
import cn.hydee.middle.business.order.entity.OrderRemind;
import cn.hydee.middle.business.order.entity.OriThirdOrderDetail;
import cn.hydee.middle.business.order.entity.RefundDetail;
import cn.hydee.middle.business.order.entity.RefundOrder;
import cn.hydee.middle.business.order.entity.StoreBillConfig;
import cn.hydee.middle.business.order.entity.dto.PaySaleInfo;
import cn.hydee.middle.business.order.exception.ErpNetException;
import cn.hydee.middle.business.order.exception.OmsException;
import cn.hydee.middle.business.order.feign.MiddleBaseInfoClient;
import cn.hydee.middle.business.order.feign.MiddleMerchandiseClient;
import cn.hydee.middle.business.order.feign.MiddleOrderClient;
import cn.hydee.middle.business.order.http.NetHttpAdapter;
import cn.hydee.middle.business.order.interceptor.GlobalInterceptor;
import cn.hydee.middle.business.order.mapper.*;
import cn.hydee.middle.business.order.proxy.LockStockProxy;
import cn.hydee.middle.business.order.route.application.StrategyApplicationService;
import cn.hydee.middle.business.order.route.domain.repository.StrategyRepository;
import cn.hydee.middle.business.order.service.*;
import cn.hydee.middle.business.order.service.async.HydeeEsSyncClientAsync;
import cn.hydee.middle.business.order.service.baseinfo.DsMerchantGroupInfoService;
import cn.hydee.middle.business.order.service.baseinfo.DsOnlineStoreConfigService;
import cn.hydee.middle.business.order.service.baseinfo.DsOnlineStoreService;
import cn.hydee.middle.business.order.service.impl.base.BaseServiceInterface;
import cn.hydee.middle.business.order.service.ob.cloud.shelf.CloudShelfService;
import cn.hydee.middle.business.order.service.rabbit.delayConsumer.base.BaseDelayMqMsg;
import cn.hydee.middle.business.order.service.rabbit.delayConsumer.base.DelayProducer;
import cn.hydee.middle.business.order.service.rabbit.delayConsumer.base.MsgTypeServiceEnum;
import cn.hydee.middle.business.order.service.rocket.MessageProducerService;
import cn.hydee.middle.business.order.service.suport.BaseInfoService;
import cn.hydee.middle.business.order.service.suport.MerchandiseBaseService;
import cn.hydee.middle.business.order.service.suport.OrderBasicService;
import cn.hydee.middle.business.order.service.suport.RefundBasicService;
import cn.hydee.middle.business.order.service.thirdplatform.ThirdPlatformService;
import cn.hydee.middle.business.order.thirdbill.entity.JDHealthRefundRecordResult;
import cn.hydee.middle.business.order.util.*;
import cn.hydee.middle.business.order.util.AESUtils;
import cn.hydee.middle.business.order.util.DateUtil;
import cn.hydee.middle.business.order.util.ErrorMessageUtil;
import cn.hydee.middle.business.order.util.ExceptionUtil;
import cn.hydee.middle.business.order.util.JsonUtil;
import cn.hydee.middle.business.order.util.NotifyRedisHelper;
import cn.hydee.middle.business.order.util.Sequence;
import cn.hydee.middle.business.order.util.SpringBeanUtils;
import cn.hydee.middle.business.order.util.local.page.ListPageTool;
import cn.hydee.middle.business.order.util.redis.RedisStringUtil;
import cn.hydee.middle.business.order.v2.annotation.OrderLock;
import cn.hydee.middle.business.order.v2.feign.hems.HemsCommonClient;
import cn.hydee.middle.business.order.v2.manager.CommodityHandlerManager;
import cn.hydee.middle.business.order.v2.manager.OrderSaveHandlerManager;
import cn.hydee.middle.business.order.v2.manager.OrderSingleQueryManager;
import cn.hydee.middle.business.order.v2.manager.OrderUpdateStoreDeliveryMatchManager;
import cn.hydee.middle.business.order.v2.manager.base.BaseInfoManager;
import cn.hydee.middle.business.order.v2.manager.base.OrderBasicManager;
import cn.hydee.middle.business.order.v2.manager.base.OrderHandlerManager;
import cn.hydee.middle.business.order.v2.manager.base.OrderInfoRedisManager;
import cn.hydee.middle.business.order.yxtadapter.constant.AssignmentBizType;
import cn.hydee.middle.business.order.yxtadapter.domain.assignment.Assignment;
import cn.hydee.middle.business.order.yxtadapter.domain.dictionary.InnerStoreOpenAutoBillDto;
import cn.hydee.middle.business.order.yxtadapter.domain.middle.AssignmentDTO;
import cn.hydee.middle.business.order.yxtadapter.domain.pos.callback.getorderlist.KcPosGetOrderListCallBackResponse;
import cn.hydee.middle.business.order.yxtadapter.domainservice.assignment.AssignmentEngine;
import cn.hydee.middle.business.order.yxtadapter.domainservice.assignment.converter.AssignmentConverter;
import cn.hydee.middle.business.order.yxtadapter.domainservice.innerstoredictionary.InnerStoreDictionaryQrtGateway;
import cn.hydee.middle.business.order.yxtadapter.domainservice.mdm.MiddleBaseInfoClientAdapter;
import cn.hydee.middle.business.order.yxtadapter.infrastructure.db.mysql.model.AssignmentEngineAssignment;
import cn.hydee.middle.business.order.yxtadapter.infrastructure.domainserviceimpl.assignment.AssignmentReportGatewayImpl;
import cn.hydee.middle.business.order.yxtadapter.infrastructure.domainserviceimpl.assignment.converter.AssignmentEngineAssignmentConverter;
import cn.hydee.middle.business.order.yxtadapter.infrastructure.domainserviceimpl.hdpos.HdPosGatewayImpl;
import cn.hydee.middle.business.order.yxtadapter.infrastructure.domainserviceimpl.util.ThreadUtils;
import cn.hydee.starter.dto.ErrorType;
import cn.hydee.starter.dto.PageDTO;
import cn.hydee.starter.dto.ResponseBase;
import cn.hydee.starter.elasticsearch.service.HydeeRestHighLevelClient;
import cn.hydee.starter.exception.WarnException;
import cn.hydee.unified.model.HemsBaseData;
import cn.hydee.unified.model.base.BaseHemsResp;
import cn.hydee.unified.model.order.*;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.baomidou.dynamic.datasource.annotation.DS;
import com.baomidou.mybatisplus.core.conditions.Wrapper;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.google.common.collect.Lists;

import java.math.RoundingMode;
import java.time.Duration;
import java.util.concurrent.BlockingQueue;
import java.util.concurrent.ConcurrentHashMap;
import java.util.concurrent.ConcurrentMap;
import java.util.concurrent.Executor;
import java.util.concurrent.LinkedBlockingQueue;
import java.util.concurrent.ScheduledExecutorService;
import lombok.extern.slf4j.Slf4j;
import org.apache.logging.log4j.util.Strings;
import org.elasticsearch.action.search.SearchRequest;
import org.elasticsearch.index.query.BoolQueryBuilder;
import org.elasticsearch.index.query.QueryBuilder;
import org.elasticsearch.index.query.QueryBuilders;
import org.elasticsearch.search.builder.SearchSourceBuilder;
import org.elasticsearch.search.sort.SortOrder;
import org.jetbrains.annotations.NotNull;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.data.redis.core.StringRedisTemplate;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.Assert;
import org.springframework.util.CollectionUtils;
import org.springframework.util.ObjectUtils;
import org.springframework.util.StringUtils;

import javax.validation.Valid;
import java.io.IOException;
import java.math.BigDecimal;
import java.time.Duration;
import java.util.*;
import java.util.concurrent.*;
import java.util.concurrent.atomic.AtomicLong;
import java.util.stream.Collectors;
import java.util.stream.Stream;


/**
 * <p>
 * 基本订单信息表 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2019-12-16
 */
@Service
@Slf4j
public class OrderInfoServiceImpl implements OrderInfoService, BaseServiceInterface {
    @Autowired
    private OrderInfoMapper orderInfoMapper;
    @Autowired
    private OrderBasicService orderBasicService;
    @Autowired
    private OrderHandlerService orderHandler;
    @Autowired
    private OrderDetailMapper orderDetailMapper;
    @Autowired
    private BaseInfoService baseInfoService;
    @Autowired
    private HemsCommonClient hemsCommonClient;
    @Autowired
    private OrderRemindMapper orderRemindMapper;
    @Autowired
    private MerchandiseBaseService merchandiseBaseService;
    @Autowired
    private DsOnlineClientRepo dsOnlineClientRepo;
    @Autowired
    private DsOnlineStoreService dsOnlineStoreService;
    @Autowired
    private ErpBillService erpBillService;
    @Autowired
    private RefundOrderMapper refundOrderMapper;
    @Autowired
    private VerifyService verifyService;
    @Autowired
    private OrderPayInfoMapper orderPayInfoMapper;
    @Autowired
    private MiddleOrderClient middleOrderClient;
    @Autowired
    private StoreBillConfigService storeBillConfigService;
    @Autowired
    private LockStockProxy lockStockProxy;
    @Autowired
    private RefundDetailMapper refundDetailMapper;
    @Autowired
    private OrderBasicManager orderBasicManager;
    @Autowired
    private MiddleBaseInfoClient middleBaseInfoClient;
    @Autowired
    private MiddleBaseInfoClientAdapter middleBaseInfoClientAdapter;
    @Autowired
    private CloudShelfService cloudShelfService;
    @Autowired
    private ElasticService elasticService;
    @Autowired
    private HydeeRestHighLevelClient hydeeRestHighLevelClient;
    @Autowired
    private BaseInfoManager baseInfoManager;
    @Autowired
    private OrderDeliveryRecordMapper orderDeliveryRecordMapper;
    @Autowired
    private OrderHandlerManager orderHandlerManager;
    @Autowired
    private DeliveryLogisticsCompanyMapper deliveryLogisticsCompanyMapper;
    @Autowired
    private OmsHemsMapMapper omsHemsMapMapper;
    @Autowired
    private OrderDeliveryAddressMapper orderDeliveryAddressMapper;

    @Autowired
    private DsOnlineStoreRepo dsOnlineStoreRepo;
    @Autowired
    private MessageProducerService messageProducerService;
    @Autowired
    private DsOnlineStoreConfigService dsOnlineStoreConfigService;
    @Autowired
    private OrderInfoRedisManager orderInfoRedisManager;
    @Autowired
    private OrderDeliveryRecordService orderDeliveryRecordService;
    @Autowired
    private NetHttpAdapter netHttpAdapter;
    @Autowired
    private DsMerchantGroupInfoRepo merchantGroupInfoRepo;
    @Autowired
    private EsEnhanceService esEnhanceService;
    @Autowired
    private OrderSingleQueryManager orderSingleQueryManager;
    @Autowired
    private SupplierMessageConvert supplierMessageConvert;
    @Autowired
    private OrderMultiPayInfoService orderMultiPayInfoService;
    @Autowired
    private OrderUpdateStoreDeliveryMatchManager updateStoreDeliveryMatchManager;


    @Autowired
    private CommodityStockService commodityStockService;

    @Autowired
    private CommodityExceptionOrderService commodityExceptionOrderService;


    @Autowired
    private OrderRefundBillStatisticsService orderRefundBillStatisticsService;

    @Autowired
    private CommodityHandlerManager commodityHandlerManager;

    @Autowired
    private OrderDorisService orderDorisService;

    @Autowired
    private AssignmentEngine assignmentEngine;

    @Autowired
    private AssignmentReportGatewayImpl assignmentReportGateway;

    @Autowired
    InnerStoreDictionaryQrtGateway innerStoreDictionaryQrtGateway;

    @Autowired
    private OrderLockInfoMapper orderLockInfoMapper;

    @Autowired
    private MerCodesConfig merCodesConfig;

    @Autowired
    private DelayProducer delayProducer;

    @Autowired
    private MiddleMerchandiseClient middleMerchandiseClient;

    @Autowired
    private StrategyRepository strategyRepository;

    @Value("${order.limit.day.number:30}")
    private int limitDayNum = 30;

    @Value("${prescriptionPlatList}")
    private String prescriptionPlatList;

    @Qualifier("queryThreadPool")
    @Autowired
    private Executor queryThreadPool;

    @Value("${order.switch.countStatus:true}")
    private Boolean orderCountStatusSwitch;

    private Map<String, Object> map = new HashMap<>();

    @Autowired
    private DsMerchantGroupInfoService merchantGroupInfoService;

    @Autowired
    private StrategyApplicationService strategyApplicationService;

    //设置了转单门店的订单列表查询限制天数
    @Value("${order.route-store-search-limit:30}")
    private Long routeStoreSearchLimitDay;

    /**是否查询下单门店列表 关闭暂时只影响路由功能*/
    @Value("${route.isQueryOrderList:true}")
    private boolean isQueryRouteOrderList;

    /**查询下单门店列表 等待时间 超过时间放弃 单位 秒*/
    @Value("${route.queryOrderWaitTime:3}")
    private Long queryOrderTime;

    @Value("${order.receiver.view.num:30}")
    private Integer viewReceiverInfoNum;

    @Value("${forcedRefreshStartTime:7200000}") // 2h内
    private Long forcedRefreshStartTime;


    /**
     * 是否开启使用退款表中的机构进行搜索
     */
    @Value("${open-refund-organization-search:false}")
    private Boolean openRefundOrganizationSearch;

    @Autowired
    private OriThirdOrderDetailService oriThirdOrderDetailService;

    @Autowired
    private StringRedisTemplate stringRedisTemplate;

    @Value("${commodityCode.days:90}")
    private int days = 90;

    //批量处理 延时下账时长
    @Value("${batchPick.Account.DelayMinute:1}")
    private int batchPickAccountDelayMinute = 1;

    @Autowired
    private OrderPickInfoService orderPickInfoService;

    @Autowired
    private RedisTemplate<String, String> redisTemplate;

    @Autowired
    private ScheduledExecutorService batchAccountDelayThreadPool;

    @Autowired
    private DeliveryFeeEconomizeRecordService deliveryFeeEconomizeRecordService;

    @Autowired
    private ThirdPlatformService thirdPlatformService;

    @Override
    public OrderInfo queryOrderInfo(Long orderNo) {
        Wrapper<OrderInfo> queryWrapper = Wrappers.<OrderInfo>lambdaQuery().eq(OrderInfo::getOrderNo, orderNo);
        return orderInfoMapper.selectOne(queryWrapper);
    }

    @Override
    public OrderInfo queryYdjOrderInfo(String thirdOrderNo) {
        return orderBasicService.getOrderBaseByThirdNoWithCheck(PlatformCodeEnum.YD_JIA.getCode(), thirdOrderNo);
    }

    @Override
    @DS(DsConstants.DB_ORDER_SLAVE_TWO)
    public IPage<SearchOrderRspDto> searchOrderAllPage(OrderQuerySearchReqDto pageBase, List<String> organizationList, List<String> platformCodeList) {
        Page<SearchOrderRspDto> page = new Page<>(pageBase.getCurrentPage(), pageBase.getPageSize());
        pageBase.setAesReceiverPhone(AESUtils.encrypt(pageBase.getReceiverPhone()));
        buildAndOrSql(pageBase, "o");
        IPage<SearchOrderRspDto> res = esEnhanceService.searchOrderAllPage(page, pageBase, organizationList, platformCodeList);
        if (!CollectionUtils.isEmpty(res.getRecords())) {
            List<OrderStoreTemplateRspDto> configs = dsOnlineStoreRepo.getOrderConfigByList(res.getRecords());
            Map<String, OrderStoreTemplateRspDto> configMap = configs.stream().collect(Collectors.toMap(dto -> getKey(dto), data -> data, (v1, v2) -> v1));
            for (SearchOrderRspDto rsp : res.getRecords()) {
                OrderStoreTemplateRspDto config = configMap.get(getKey(rsp.getMerCode(), rsp.getThirdPlatformCode(), rsp.getClientCode(), rsp.getOnlineStoreCode()));
                if (null == config) {
                    rsp.setMultiPrintTemplate(false);
                } else if (null == config.getPrintTemplateId() || null == config.getSecondPrintTemplateId()) {
                    rsp.setMultiPrintTemplate(false);
                } else if (config.getPrintTemplateId().equals(config.getSecondPrintTemplateId())) {
                    rsp.setMultiPrintTemplate(false);
                } else {
                    rsp.setMultiPrintTemplate(true);
                }
                //订单类型描述赋值
                List<String> orderTypeDesc = new ArrayList<>();
                if (DsConstants.INTEGER_ONE.equals(rsp.getMedicalInsurance())) {
                    orderTypeDesc.add("医保订单");
                }
                if (DsConstants.INTEGER_ONE.equals(rsp.getPrescriptionFlag())) {
                    orderTypeDesc.add("处方订单");
                } else {
                    orderTypeDesc.add(OrderTypeEnum.getOrderType(rsp.getOrderType()).getMsg());
                }

                rsp.setOrderTypeDesc(StrUtil.join(",", orderTypeDesc));
            }
        }
        return res;
    }

    @Override
    public String forcedRefresh(ForcedRefreshDto forcedRefreshDto) {

        String refreshMessage = RedisStringUtil.getValue(forcedRefreshDto.refreshKey());
        if (!StringUtils.isEmpty(refreshMessage)) {
            return refreshMessage + "[重复点击]";
        }

        String message = "刷新成功,当前无订单";

       Date now = new Date();
        now.setTime(now.getTime() - forcedRefreshStartTime);
        Wrapper<OrderInfo> queryWrapper = Wrappers.<OrderInfo>lambdaQuery()
            .ge(OrderInfo::getCreated, now )
            .eq(OrderInfo::getMerCode, forcedRefreshDto.getMerCode() )
            .eq(OrderInfo::getOrganizationCode, forcedRefreshDto.getOrganizationCode() )
            .eq(OrderInfo::getOrderState, forcedRefreshDto.getOrderState())
            .eq(OrderInfo::getServiceMode,"O2O");
        List<OrderInfo> orderInfoList = orderInfoMapper.selectList(queryWrapper);
        if (!CollectionUtils.isEmpty(orderInfoList)) {
            orderInfoList.forEach(orderInfo -> {
                OrderInfo updateOrderInfo = new OrderInfo();
                updateOrderInfo.setId(orderInfo.getId());
                updateOrderInfo.setDataVersion(orderInfo.getDataVersion() + 1);
                orderInfoMapper.updateById(updateOrderInfo);
            });
            message = String.format("刷新成功,共刷新 %d 条订单", orderInfoList.size());
        }

        RedisStringUtil.setValue(forcedRefreshDto.refreshKey(), message, 1L, TimeUnit.MINUTES);
        return message;
    }


    public static void buildAndOrSql(OrderQuerySearchReqDto pageBase, String preTable) {
        if (CollectionUtils.isEmpty(pageBase.getAndList()) && CollectionUtils.isEmpty(pageBase.getOrList())) {
            //防注入 重设null
            pageBase.setOrSql(null);
            return;
        }
        if (!CollectionUtils.isEmpty(pageBase.getAndList())) {
            List<String> andList = pageBase.getAndList();
            if (andList.contains("changeOrganizationFlag")) {
                pageBase.setChangeOrganizationFlag(DsConstants.INTEGER_ONE);
            }
            if (andList.contains("medicalInsurance")) {
                pageBase.setMedicalInsurance(DsConstants.INTEGER_ONE);
            }
            if (andList.contains("appointment")) {
                pageBase.setAppointment(DsConstants.INTEGER_ONE);
            }
            if (andList.contains("integralFlag")) {
                pageBase.setIntegralFlag(DsConstants.STRING_ONE);
            }
            if (andList.contains("newCustomerFlag")) {
                pageBase.setNewCustomerFlag(DsConstants.STRING_ONE);
            }
            if (andList.contains("transferDelivery")) {
                pageBase.setTransferDelivery(DsConstants.INTEGER_ONE);
            }
            if (andList.contains("jdChannelFlag")) {
                pageBase.setSourceChannelType(SourceChannelType.JD_CHANNEL.getCode());
            }
        }
        if (!CollectionUtils.isEmpty(pageBase.getOrList())) {
            String andOrSql = "";
            List<String> orList = pageBase.getOrList();
            if (orList.contains("changeOrganizationFlag")) {
                andOrSql = andOrSql + "or  (source_organization_code is not null and source_organization_code != " + preTable + ".organization_code) ";
            }
            if (orList.contains("medicalInsurance")) {
                andOrSql = andOrSql + "or medical_insurance = 1 ";
            }
            if (orList.contains("appointment")) {
                andOrSql = andOrSql + "or appointment = 1 ";
            }
            if (orList.contains("integralFlag")) {
                andOrSql = andOrSql + "or integral_flag = '1' ";
            }
            if (orList.contains("newCustomerFlag")) {
                andOrSql = andOrSql + "or new_customer_flag = '1' ";
            }
            if (orList.contains("transferDelivery")) {
                andOrSql = andOrSql + "or transfer_delivery = 1 ";
            }
            if (orList.contains("jdChannelFlag")) {
                andOrSql = andOrSql + "or source_channel_type = 1 ";
            }
            if (andOrSql.startsWith("or")) {
                andOrSql = andOrSql.substring(2);
            }
            if (!StringUtils.isEmpty(andOrSql)) {
                andOrSql = "( " + andOrSql;
                andOrSql = andOrSql + " )";
                pageBase.setOrSql(andOrSql);
            }
        } else {
            pageBase.setOrSql(null);
        }
    }

    //两个获取config唯一键的方法
    private String getKey(String merCode, String platfromCode, String onlineClientCode, String onlineStoreCode) {
        return String.format("%s-%s-%s-%s", merCode, platfromCode, onlineClientCode, onlineStoreCode);
    }

    private String getKey(OrderStoreTemplateRspDto dto) {
        return getKey(dto.getMerCode(), dto.getPlatformCode(), dto.getOnlineClientCode(), dto.getOnlineStoreCode());
    }

    @Override
    public IPage<OrderInfoPageRsp> getOrderPage(OrderPageReqDto orderPageReqDto, List<String> platformCodeList) {
        VerifyRspDto verifyDto = verifyService.verifyOrganizationAndGet(VerifyReqDto.builder().merCode(orderPageReqDto.getMerCode()).organizationCode(orderPageReqDto.getOrganizationCode()).userId(orderPageReqDto.getUserId()).build());
        // 分页查询记录
        //过滤待审方状态
        Map<String, Integer> storePrescriptionMap = null;
        Integer orderState = null;
        if (DsConstants.INTEGER_FIVE.equals(orderPageReqDto.getOrderState())) {
            orderPageReqDto.setIsPrescription(DsConstants.INTEGER_ONE);
            orderPageReqDto.setPrescriptionStatus(DsConstants.INTEGER_ZERO);
            orderState = DsConstants.INTEGER_FIVE;
        }
        Page<OrderInfoPageRsp> page = new Page<>(orderPageReqDto.getCurrentPage(), orderPageReqDto.getPageSize());
        if (!StringUtils.isEmpty(orderPageReqDto.getReceiverTelephone())) {
            orderPageReqDto.setReceiverTelephoneEncrypted(AESUtils.encrypt(orderPageReqDto.getReceiverTelephone()));
        }
        //查询下单门店得列表 2024-05-16 路由导致慢sql临时修改
        CompletableFuture<IPage<OrderInfoPageRsp>> sourceListFuture = null;
        if (isQueryRouteOrderList) {
            sourceListFuture = CompletableFuture.supplyAsync(
                () -> {
                    Page<OrderInfoPageRsp> pageSource = new Page<>(orderPageReqDto.getCurrentPage(),
                        orderPageReqDto.getPageSize());
                    return esEnhanceService.selectOrderPageForSourceOrg(pageSource, orderPageReqDto,
                        verifyDto.getOrganizatioinList(), platformCodeList);
                }, queryThreadPool);

            CompletableFuture.allOf(
                sourceListFuture
            ).join();
        }




        IPage<OrderInfoPageRsp> iPage = esEnhanceService.selectOrderPage(page, orderPageReqDto, verifyDto.getOrganizatioinList(), platformCodeList);
        if (null != sourceListFuture) {
            try {
                IPage<OrderInfoPageRsp> orderInfoPageRspIPage = sourceListFuture.get(queryOrderTime, TimeUnit.SECONDS);
                if (CollectionUtils.isEmpty(iPage.getRecords())) {
                    iPage.setRecords(orderInfoPageRspIPage.getRecords());
                } else {
                    iPage.getRecords().addAll(orderInfoPageRspIPage.getRecords());
                }
                ArrayList<OrderInfoPageRsp> records = iPage.getRecords().stream().collect(Collectors.collectingAndThen(
                        Collectors.toCollection(() -> new TreeSet<>(Comparator.comparing(OrderInfoPageRsp::getOrderNo))),
                        ArrayList::new
                ));
                iPage.setRecords(records);
                iPage.setTotal(records.size());
            } catch (Exception e) {
                log.error("查询下单门店订单列表失败,{}",e.getMessage());
            }
        }
        // 懒加载检索优化
        orderSingleQueryManager.buildRespForOrderNormalQuery(iPage.getRecords());
        if (orderState != null && !CollectionUtils.isEmpty(iPage.getRecords())) {
            List<DsOnlineStorePriscriptionQryDto> storePriscriptionQryDtoList = new ArrayList<>();
            iPage.getRecords().forEach(item -> {
                DsOnlineStorePriscriptionQryDto dto = new DsOnlineStorePriscriptionQryDto();
                dto.setMerCode(item.getMerCode());
                dto.setPlatformCode(item.getThirdPlatformCode());
                dto.setOnlineClientCode(item.getClientCode());
                dto.setOnlineStoreCode(item.getOnlineStoreCode());
                storePriscriptionQryDtoList.add(dto);
            });
            storePrescriptionMap = getStorePrescriptionConf(storePriscriptionQryDtoList.stream().distinct().collect(Collectors.toList()));
        }
        convertTransFlag(iPage, verifyDto.getOrganizatioinList());
        convertDeliveryType(iPage, storePrescriptionMap);
        isNeedChangeSelfDelivery(iPage);
        //收货信息解密状态填充
        iPage.getRecords().forEach(OrderInfoPageRsp::obtainReceiverDecryptState);
        return iPage;
    }

    /**
     * 设置转单策略相关内容
     *
     * @param iPage
     * @return void
     */
    private void convertTransFlag(IPage<OrderInfoPageRsp> iPage, List<String> organizatioinList){
        List<OrderInfoPageRsp> records = iPage.getRecords();
        if (CollectionUtils.isEmpty(records)) return;
        records.forEach(item -> {
            if (!item.getOrganizationCode().equals(item.getSourceOrganizationCode())){
                item.setTransFlag(DsConstants.STRING_ONE);
                if(!StringUtils.isEmpty(item.getExtendInfo())){
                    OrderInfoExtendInfo extendInfo = JSONObject.parseObject(item.getExtendInfo(), OrderInfoExtendInfo.class);
                    item.setAcceptFlag(extendInfo.getAcceptFlag());
                    item.setAutoRefuseTimeRange(extendInfo.getAutoRefuseTimeRange());
                    item.setTransferTime(extendInfo.getRouteOrderTime());
                }
                //发货门店拥有修改权限
                if(organizatioinList.contains(item.getOrganizationCode())){
                    item.setTransUpdateFlag(DsConstants.STRING_ONE);
                }
            }
        });
    }

    /**
     * 查询处方单门店审方配置信息
     *
     * @param dsOnlineStorePriscriptionQryDtoList
     * @return
     */
    private Map<String, Integer> getStorePrescriptionConf(List<DsOnlineStorePriscriptionQryDto> dsOnlineStorePriscriptionQryDtoList) {
        List<DsOnlineStorePriscriptionDto> onlineStorePriscriptionDtos = dsOnlineStoreRepo.selectPrescriptionConf(dsOnlineStorePriscriptionQryDtoList);
        return onlineStorePriscriptionDtos.stream().collect(Collectors.toMap(item -> item.getMerCode() + "_" + item.getPlatformCode() + "_" + item.getOnlineClientCode() + "_" + item.getOnlineStoreCode(), item -> Optional.ofNullable(item.getCheckingType()).orElse(DsConstants.INTEGER_TWO), (k1, k2) -> k1));
    }


    private void isNeedChangeSelfDelivery(IPage<OrderInfoPageRsp> iPage) {
        for (OrderInfoPageRsp orderInfoPageRsp : iPage.getRecords()) {
            OrderDeliveryRecord odr = orderInfoPageRsp.getOrderDeliveryRecord();
            // 平台配送，拣货完成后，超15分钟未获取到平台下发的骑手信息，则显示
            if (DeliveryTypeEnum.PLAT.getCode().equals(odr.getDeliveryType()) && DateUtil.getDistanceMinutes(odr.getModifyTime(), new Date()) > 15) {
                odr.setChangeFlag(1);
            }
            // 预约单，则预约送达前15钟，显示转自配送按钮
            if (DsConstants.INTEGER_ONE.equals(orderInfoPageRsp.getDeliveryTimeType()) && DateUtil.getDistanceMinutes(odr.getModifyTime(), new Date()) > 15) {
                odr.setChangeFlag(1);
            }

            // 自配送，显示
            if (DeliveryTypeEnum.SELLER_SELF.getCode().equals(odr.getDeliveryType())) {
                odr.setChangeFlag(1);
            }
        }
    }

    private void convertDeliveryType(IPage<OrderInfoPageRsp> iPage, Map<String, Integer> params) {
        for (OrderInfoPageRsp orderInfoPageRsp : iPage.getRecords()) {
            if (params != null) {
                orderInfoPageRsp.setCheckingType(params.get(orderInfoPageRsp.getMerCode() + "_" + orderInfoPageRsp.getThirdPlatformCode() + "_" + orderInfoPageRsp.getClientCode() + "_" + orderInfoPageRsp.getOnlineStoreCode()));
            }
            OrderDeliveryRecord odr = orderInfoPageRsp.getOrderDeliveryRecord();
            switch (odr.getDeliveryType()) {
                case "1":
                case "2":
                    StringBuilder platformDeliveryDesc = new StringBuilder("平台配送");
                    if (OrderStateEnum.UN_TAKE.getCode().compareTo(orderInfoPageRsp.getOrderState()) < 0) {
                        platformDeliveryDesc.append("(").append(DeliveryStateEnum.getByCode(odr.getState())).append(")");
                    }
                    orderInfoPageRsp.setDeliveryDesc(platformDeliveryDesc.toString());
                    break;
                case "3":
                    StringBuilder selfDeliveryDesc = new StringBuilder("商家自配送");
                    if (!StringUtils.isEmpty(odr.getDeliveryPlatName())) {
                        selfDeliveryDesc.append("-").append(odr.getDeliveryPlatName());
                        if (OrderStateEnum.UN_TAKE.getCode().compareTo(orderInfoPageRsp.getOrderState()) < 0 && !DeliveryPlatformEnum.SJEXPRESS.getName().equals(odr.getDeliveryPlatName())) {
                            selfDeliveryDesc.append("(").append(DeliveryStateEnum.getByCode(odr.getState())).append(")");
                        }
                    }
                    orderInfoPageRsp.setDeliveryDesc(selfDeliveryDesc.toString());
                    break;
                case "4":
                    orderInfoPageRsp.setDeliveryDesc("到店自提");
                    break;
                case "5":
                    orderInfoPageRsp.setDeliveryDesc("快递配送");
                    break;
                default:
                    break;
            }
        }
    }


    @Transactional
    @Override
    public IPage<OrderInfoPageRsp> getOrderLedgerAllListPage(OrderLedgerPageReqDto req, List<String> organizatioinList, List<String> platformCodeList) {
        //更新已关闭，已取消的订单的下账状态为取消下账
//        CustomUpdateWrapper<OrderInfo> customUpdateWrapper = new CustomUpdateWrapper<>();
//        customUpdateWrapper.update(TableConstant.ORDER_INFO)
//                .set()
//                .eq(OrderInfo::getErpState,ErpStateEnum.CANCELED.getCode())
//                .where()
//                .eq(OrderInfo::getMerCode,req.getMerCode())
//                .and()
//                .ge(OrderInfo::getOrderState,OrderStateEnum.CLOSED.getCode())
//                .and()
//                .le(OrderInfo::getOrderState,OrderStateEnum.CANCEL.getCode())
//                .and()
//                .ge(OrderInfo::getErpState,ErpStateEnum.WAIT_PICK.getCode())
//                .and()
//                .le(OrderInfo::getErpState,ErpStateEnum.WAIT_SALE.getCode())
//                .semicolon();
//        SqlExecuteUtil.executeUpdateSql(customUpdateWrapper.getUpdateSql(),this);
//        //异步更新 待下账或待解锁库存 处理解锁已锁库存
//        UnlockStockBatchReqDto reqDto = UnlockStockBatchReqDto.builder()
//                .merCode(req.getMerCode()).build();
//        orderInfoHandlerAsync.finalStatusUpdateErpState(reqDto,null);
        //获取数据
        validateForceIndex(req);
        Page<OrderInfoPageRsp> page = new Page<>(req.getCurrentPage(), req.getPageSize());
        IPage<OrderInfoPageRsp> pageData = esEnhanceService.selectOrderAllLedgerPage(page, req, organizatioinList, platformCodeList);
        //已取消未下账订单不在待下账列表显示
        if (Objects.nonNull(pageData) && req.getBillState().getErpStateList().contains(ErpStateEnum.WAIT_SALE.getCode())) {
            List<OrderInfoPageRsp> records = pageData.getRecords().stream().filter(i -> !OrderStateEnum.CANCEL.getCode().equals(i.getOrderState())).collect(Collectors.toList());
            pageData.setRecords(records);
        }
        // 特殊处理 待下账订单 需要过滤下账时机的订单
        if(Objects.nonNull(pageData) && CollectionUtil.isNotEmpty(pageData.getRecords())){
            List<OrderInfoPageRsp> records= new ArrayList<>();
            for (OrderInfoPageRsp orderInfoPageRsp : pageData.getRecords()) {
                // 下账时机校验
                StoreBillConfig storeBillConfig = orderInfoPageRsp.getStoreBillConfig();
                Integer orderState = orderInfoPageRsp.getOrderState();
                if(Boolean.TRUE.equals(checkAutoEnterAccountFlag(storeBillConfig, orderState))){
                    records.add(orderInfoPageRsp);
                }
            }
            pageData.setRecords(records);
        }
        // 懒加载检索优化
        orderSingleQueryManager.buildRespForOrderNormalQueryV2(pageData.getRecords());
        if (req.getBillState().getErpStateList().contains(ErpStateEnum.HAS_SALE_FAIL.getCode())) {
            for (OrderInfoPageRsp record : pageData.getRecords()) {
                OrderInfoExtendInfo orderInfoExtendInfo = JSON.parseObject(record.getExtendInfo(), OrderInfoExtendInfo.class);
                if (null != orderInfoExtendInfo && !StringUtils.isEmpty(orderInfoExtendInfo.getOrderAccountFailReason()) && (orderInfoExtendInfo.getOrderAccountFailReason().contains("库存不足")
                        ||orderInfoExtendInfo.getOrderAccountFailReason().contains("批次")
                        ||orderInfoExtendInfo.getOrderAccountFailReason().contains("追溯码")
                )) {
                    record.setAllowUpdateBatchNo("1");
                }


            }
        }
        getStoreTimestamp(pageData.getRecords());
        return pageData;
    }


    private void validateForceIndex(OrderLedgerPageReqDto req) {
        //默认会走索引
        if (null != req.getOrderNo() || (!StringUtils.isEmpty(req.getThirdOrderNo()))) {
            req.setForceIndex("");
            return;
        }
        if (!StringUtils.isEmpty(req.getOnlineStoreCode())) {
            req.setForceIndex("");
            return;
        }
        if (req.getBillState().isSearchNotSale()) {
            //只搜索待下账
            req.setForceIndex(" force index(idx_code_erp_state) ");
            return;
        }
        if (null != req.getOrderBeginTime()) {
            //有下单开始时间 走时间索引
            req.setForceIndex(" force index(idx_created) ");
            return;
        }

        if (null != req.getBillEndTime()) {
            //有下账开始时间 走时间索引
            req.setForceIndex(" force index(idx_bill_time) ");
            return;
        }
        //默认忽略主键索引
        req.setForceIndex(" ");
    }

    @Override
    public Integer updateBatchSql(String sql) {
        return orderInfoMapper.orderUpdateBatch(sql);
    }

    @Override
    public Integer getOrderLedgerAllListCount(OrderLedgerPageReqDto req, List<String> organizatioinList, List<String> platformCodeList) {
        return esEnhanceService.selectOrderAllLedgerCount(req, organizatioinList, platformCodeList);
    }

    @Override
    public IPage<OrderInfoPageRsp> getCancelingOrderPage(OrderPageOtherReqDto orderPageOtherReqDto, List<String> platformCodeList) {
        VerifyRspDto verifyDto = verifyService.verifyOrganizationAndGet(VerifyReqDto.builder().merCode(orderPageOtherReqDto.getMerCode()).organizationCode(orderPageOtherReqDto.getOrganizationCode()).userId(orderPageOtherReqDto.getUserId()).build());
        Page<OrderInfoPageRsp> page = new Page<>(orderPageOtherReqDto.getCurrentPage(), orderPageOtherReqDto.getPageSize());
        if (!StringUtils.isEmpty(orderPageOtherReqDto.getReceiverTelephone())) {
            orderPageOtherReqDto.setReceiverTelephoneEncrypted(AESUtils.encrypt(orderPageOtherReqDto.getReceiverTelephone()));
        }
        IPage<OrderInfoPageRsp> pageData = orderInfoMapper.selectCancelingOrderPage(page, orderPageOtherReqDto, OrderLockFlagEnum.LOCK_CANCEL.getCode(), verifyDto.getOrganizatioinList(), platformCodeList);
        // 懒加载检索优化
        orderSingleQueryManager.buildRespForOrderNormalQuery(pageData.getRecords());
        return pageData;
    }

    @Override
    public IPage<OrderInfoPageRsp> getUrgeOrderPage(OrderPageOtherReqDto pageBase, List<String> platformCodeList) {
        VerifyRspDto verifyDto = verifyService.verifyOrganizationAndGet(VerifyReqDto.builder().merCode(pageBase.getMerCode()).organizationCode(pageBase.getOrganizationCode()).userId(pageBase.getUserId()).build());
        Page<OrderInfoPageRsp> page = new Page<>(pageBase.getCurrentPage(), pageBase.getPageSize());
        if (!StringUtils.isEmpty(pageBase.getReceiverTelephone())) {
            pageBase.setReceiverTelephoneEncrypted(AESUtils.encrypt(pageBase.getReceiverTelephone()));
        }
        // 懒加载检索优化
        IPage<OrderInfoPageRsp> pageData = orderInfoMapper.selectRemindOrderPage(page, pageBase, DsConstants.INTEGER_ONE, verifyDto.getOrganizatioinList(), platformCodeList);
        orderSingleQueryManager.buildRespForOrderNormalQuery(pageData.getRecords());
        return pageData;
    }

    @Override
    public IPage<OrderInfoPageRsp> getExceptionOrderPage(OrderPageOtherReqDto pageBase) {
        VerifyRspDto verifyDto = verifyService.verifyOrganizationAndGet(VerifyReqDto.builder().merCode(pageBase.getMerCode()).organizationCode(pageBase.getOrganizationCode()).userId(pageBase.getUserId()).build());
        Page<OrderInfoPageRsp> page = new Page<>(pageBase.getCurrentPage(), pageBase.getPageSize());
        if (!StringUtils.isEmpty(pageBase.getReceiverTelephone())) {
            pageBase.setReceiverTelephoneEncrypted(AESUtils.encrypt(pageBase.getReceiverTelephone()));
        }

        CompletableFuture<IPage<OrderInfoPageRsp>> sourceFuture = null;
        if (isQueryRouteOrderList){
            sourceFuture = CompletableFuture.supplyAsync(
                () -> {
                    Page<OrderInfoPageRsp> sourcePage = new Page<>(pageBase.getCurrentPage(),
                        pageBase.getPageSize());
                    return orderInfoMapper.selectExceptionOrderPageForSource(sourcePage, pageBase,
                        OrderLockFlagEnum.LOCK_EXCEPTION_MIN_CODE.getCode(),
                        OrderLockFlagEnum.LOCK_EXCEPTION_MAX_CODE.getCode(),
                        verifyDto.getOrganizatioinList());
                }, queryThreadPool);

            CompletableFuture.allOf(
                sourceFuture
            ).join();
        }

        IPage<OrderInfoPageRsp> pageData = orderInfoMapper.selectExceptionOrderPageV3(page, pageBase,
            OrderLockFlagEnum.LOCK_EXCEPTION_MIN_CODE.getCode(),
            OrderLockFlagEnum.LOCK_EXCEPTION_MAX_CODE.getCode(), verifyDto.getOrganizatioinList());

        if (null != sourceFuture) {
            try {
                IPage<OrderInfoPageRsp> orderInfoPageRspIPage = sourceFuture.get(queryOrderTime, TimeUnit.SECONDS);

                if (CollectionUtils.isEmpty(pageData.getRecords())) {
                    pageData.setRecords(orderInfoPageRspIPage.getRecords());
                } else {
                    pageData.getRecords().addAll(orderInfoPageRspIPage.getRecords());
                }
                pageData.getRecords().addAll(orderInfoPageRspIPage.getRecords());
                ArrayList<OrderInfoPageRsp> records = pageData.getRecords().stream().collect(Collectors.collectingAndThen(
                    Collectors.toCollection(() -> new TreeSet<>(Comparator.comparing(OrderInfoPageRsp::getOrderNo))),
                    ArrayList::new
                ));
                pageData.setRecords(records);
                pageData.setTotal(records.size());
            } catch (Exception e) {
                log.error("查询下单门店订单列表失败,{}",e.getMessage());
            }
        }

       /* //查询是否存在转单门店
        StrategyOpenDto strategy = strategyApplicationService.validOpenStrategy(verifyDto.getOrganizatioinList());
        IPage<OrderInfoPageRsp> pageData = null;
        //如果没有转单策略的门店，执行旧的sql
        if(BooleanUtil.isFalse(strategy.getIsOpendStrategy())){
            pageData = orderInfoMapper.selectExceptionOrderPage(page, pageBase, OrderLockFlagEnum.LOCK_EXCEPTION_MIN_CODE.getCode(), OrderLockFlagEnum.LOCK_EXCEPTION_MAX_CODE.getCode(), verifyDto.getOrganizatioinList());
        } else {
            LocalDateTime startTime = LocalDateTime.now().plusDays(-routeStoreSearchLimitDay);
            pageBase.setStartTime(LocalDateTimeUtil.formatNormal(startTime));
            pageData = orderInfoMapper.selectExceptionOrderPageV2(page, pageBase, OrderLockFlagEnum.LOCK_EXCEPTION_MIN_CODE.getCode(), OrderLockFlagEnum.LOCK_EXCEPTION_MAX_CODE.getCode(), verifyDto.getOrganizatioinList());
        }*/

        // 懒加载检索优化
        orderSingleQueryManager.buildRespForOrderNormalQuery(pageData.getRecords());
        convertTransFlag(pageData, verifyDto.getOrganizatioinList());
        return pageData;
    }

    @Override
    public IPage<OrderInfoPageRsp> getRefundOrderPage(OrderPageOtherReqDto pageBase) {
        Page<OrderInfoPageRsp> page = new Page<>(pageBase.getCurrentPage(), pageBase.getPageSize());
        return orderInfoMapper.selectRefundOrderPage(page, pageBase, OrderLockFlagEnum.LOCK_REFUND.getCode());
    }

    @Override
    public OrderInfoAllDomain getOrderInfoDetail(Long orderNo, String merCode) {
        return orderInfoMapper.selectOrderInfoDetail(orderNo);
    }

    @Override
    public OrderInfoAllDomainWithPickInfo getAllOrderDetail(Long orderNo, String merCode) {
        OrderInfoAllDomainWithPickInfo domain = orderInfoMapper.selectAllOrderDetail(orderNo);
        if (ObjectUtils.isEmpty(domain)) {
            log.error("订单信息不存在 orderNo:{}", orderNo);
            throw ExceptionUtil.getWarnException(DsErrorType.ORDER_NOT_EXIST);
        }
        if (PlatformCodeEnum.JD_DAOJIA.getCode().equals(domain.getThirdPlatformCode()) || PlatformCodeEnum.JD_HEALTH.getCode().equals(domain.getThirdPlatformCode())) {
            domain.setOrderDetailForRefundList(orderDetailMapper.selectDetailForRefund(orderNo));
        }
        if (ErpStateEnum.HAS_SALE_FAIL.getCode().intValue() == domain.getErpState()) {
            String extendInfo = domain.getExtendInfo();
            if (!StringUtils.isEmpty(extendInfo)) {
                try {
                    JSONObject jsonObject = JSONObject.parseObject(extendInfo);
                    String erpFailReason = jsonObject.getString("orderAccountFailReason");
                    domain.setOrderAccountFailReason(erpFailReason);
                } catch (Exception ignore) {
                    domain.setOrderAccountFailReason("下账失败,具体原因请联系系统管理员");
                }
            }
        }

        orderDeliveryRecordService.setSelfVerifyCode(domain.getOrderDeliveryRecord(), domain);
        StoreBillConfig config = storeBillConfigService.getBillConfigById(domain.getClientConfId());
        domain.setStoreBillConfig(config);
        OrderDeliveryRecord odr = domain.getOrderDeliveryRecord();
        switch (odr.getDeliveryType()) {
            case "1":
            case "2":
                StringBuilder platformDeliveryDesc = new StringBuilder("平台配送");
                if (OrderStateEnum.UN_TAKE.getCode().compareTo(domain.getOrderState()) < 0) {
                    platformDeliveryDesc.append("(").append(DeliveryStateEnum.getByCode(odr.getState())).append(")");
                }
                domain.setDeliveryDesc(platformDeliveryDesc.toString());
                break;
            case "3":
                StringBuilder selfDeliveryDesc = new StringBuilder("商家自配送");
                if (!StringUtils.isEmpty(odr.getDeliveryPlatName())) {
                    selfDeliveryDesc.append("-").append(odr.getDeliveryPlatName());
                    if (OrderStateEnum.UN_TAKE.getCode().compareTo(domain.getOrderState()) < 0 && !DeliveryPlatformEnum.SJEXPRESS.getName().equals(odr.getDeliveryPlatName())) {
                        selfDeliveryDesc.append("(").append(DeliveryStateEnum.getByCode(odr.getState())).append(")");
                    }
                }
                domain.setDeliveryDesc(selfDeliveryDesc.toString());
                break;
            case "4":
                domain.setDeliveryDesc("到店自提");
                break;
            case "5":
                domain.setDeliveryDesc("快递配送");
                break;
            default:
                break;
        }
        //订单详情 返回门店配置信息
        StoreConfigReqDTO storeConfigReqDTO = new StoreConfigReqDTO();
        storeConfigReqDTO.setClientCode(domain.getClientCode());
        storeConfigReqDTO.setMerCode(domain.getMerCode());
        storeConfigReqDTO.setOnlineStoreCode(domain.getOnlineStoreCode());
        storeConfigReqDTO.setPlatformCode(domain.getThirdPlatformCode());
        DsOnlineStoreConfResDTO dsOnlineStoreConfResDTO = dsOnlineStoreConfigService.queryStoreConfig(storeConfigReqDTO);
        DeliveryFeeEconomizeRecord deliveryFeeEconomizeRecord = deliveryFeeEconomizeRecordService.getDeliveryFeeEconomizeRecord(orderNo);
        if(Objects.nonNull(deliveryFeeEconomizeRecord)){
            //最优配送方式 订单入库比较配送费 会默认配送方式
            dsOnlineStoreConfResDTO.setBestDeliveryType(deliveryFeeEconomizeRecord.getPredictDeliveryPlatName());
        }
        domain.setDsOnlineStoreConfResDTO(dsOnlineStoreConfResDTO);

        //补充多支付方式下账金额
        if (!PlatformCodeEnum.YD_JIA.getCode().equals(domain.getThirdPlatformCode()) && domain.getStoreBillConfig() != null && DsConstants.INTEGER_ONE.equals(domain.getStoreBillConfig().getOnlinePayType())) {
            List<MultiPayType> orderMultiPayInfoList = orderMultiPayInfoService.queryPayList2Account(domain);
            MultiPayDataDto.buildOrderDetailMultiPay(domain, orderMultiPayInfoList);
        }

        //隐私收货人信息
        domain.getOrderDeliveryAddress().privacyReceiverInfo();

        // 截取处方图片链接
        domain.getOrderPrescriptionList().forEach(o ->o.subtractUrl());

        this.defaultBatchPick(domain);


        // 新增现金券金额
        if (Objects.nonNull(domain.getOrderPayInfo())) {
            List<PaySaleInfo> paySaleInfos = JSON.parseArray(domain.getOrderPayInfo().getPaySaleInfo(), PaySaleInfo.class);
            if(CollUtil.isNotEmpty(paySaleInfos)){
                domain.getOrderPayInfo().setAllCashCouponAmount(paySaleInfos.
                        stream().map(PaySaleInfo::getSaleAmount).filter(Objects::nonNull).reduce(BigDecimal.ZERO, BigDecimal::add).setScale(2, RoundingMode.HALF_UP));
            }
        }

        return domain;
    }

    /**
     * 待拣货时默认批次号拣货
     *
     * @param domain
     * @return void
     */
    private void defaultBatchPick(OrderInfoAllDomainWithPickInfo domain){
        if(!OrderStateEnum.UN_PICK.getCode().equals(domain.getOrderState())){
            return;
        }
        if(Objects.isNull(domain.getDsOnlineStoreConfResDTO()) ||
                Objects.isNull(domain.getDsOnlineStoreConfResDTO().getDefaultBatchNo()) || DsConstants.INTEGER_ZERO.equals(domain.getDsOnlineStoreConfResDTO().getDefaultBatchNo())){
            return;
        }
        List<OrderDetailDomain> orderDetailList = domain.getOrderDetailList();
        if(CollectionUtils.isEmpty(orderDetailList)){
            return;
        }

        List<OrderPickInfo> pickInfos = orderDetailList.stream()
                .filter(detail -> Objects.nonNull(detail.getOrderPickInfoList()))
                .flatMap(detail -> detail.getOrderPickInfoList().stream())
                .collect(Collectors.toList());
        //判断是否有拣货信息，没有的话才默认批号
        if(!CollectionUtils.isEmpty(pickInfos)){
            return;
        }

        List<String> erpCodes = orderDetailList.stream()
                .filter(detail -> !OrderDetailStatusEnum.REPLACE.getCode().equals(detail.getStatus()))
                .map(OrderDetail::getErpCode).distinct().collect(Collectors.toList());
        BatchStockReqDto batchStockReqDto = new BatchStockReqDto();
        batchStockReqDto.setOrderNo(domain.getOrderNo());
        batchStockReqDto.setErpCodeList(erpCodes);
        batchStockReqDto.setOrganCode(domain.getOrganizationCode());
        List<BatchStock> batchStockListFromHana = SpringUtil.getBean(OrderDetailService.class).getErpBatchStock(domain.getMerCode(), batchStockReqDto);
        if(CollectionUtils.isEmpty(batchStockListFromHana)){
            return;
        }
        for (BatchStock stock:batchStockListFromHana) {
            List<BatchStock.StockQty> filterStockQties = stock.getMake().stream()
                    /* 过滤过期时间不为空且大于30天，库存数量不为0 */
                    .filter(stockQty -> !ObjectUtils.isEmpty(stockQty.getRestEffectDays()) && stockQty.getRestEffectDays() > 30)
                    .filter(stockQty -> !stockQty.getWareqty().equals("0.0000"))
                    .sorted(Comparator.comparing(BatchStock.StockQty::getRestEffectDays)).collect(Collectors.toList());
            if(CollectionUtils.isEmpty(filterStockQties)){
                continue;
            }
            for (OrderDetailDomain detail:orderDetailList) {
                if(!detail.getErpCode().equals(stock.getWarecode())){
                    continue;
                }
                List<OrderPickInfo> orderPickInfoList = new ArrayList<>();
                Integer count = detail.getCommodityCount();
                for(BatchStock.StockQty stockQty : filterStockQties){
                    if(DsConstants.STRING_DOUBLE_ZERO.equals(stockQty.getWareqty())){
                        continue;
                    }
                    OrderPickInfo orderPickInfo = new OrderPickInfo();
                    orderPickInfo.setOrderDetailId(detail.getId());
                    orderPickInfo.setErpCode(stock.getWarecode());
                    int warty;
                    if(count >  Double.parseDouble(stockQty.getWareqty())){
                        warty = (int)Double.parseDouble(stockQty.getWareqty());
                        /* 会出现相同erpCode的多条明细信息，需要修改剩余库存为下条明细拣货做准备 */
                        stockQty.setWareqty(DsConstants.STRING_DOUBLE_ZERO);
                    }else{
                        warty = count;
                        double restCount = Double.parseDouble(stockQty.getWareqty()) - count;
                        stockQty.setWareqty(String.valueOf(restCount));
                    }
                    count -= warty;
                    orderPickInfo.setCommodityBatchNo(stockQty.getMakeno());
                    orderPickInfo.setCount(warty);
                    orderPickInfoList.add(orderPickInfo);
                    if(count <=0) break;
                }
                detail.setOrderPickInfoList(orderPickInfoList);
            }
        }
    }



    @Override
    public IPage<OrderInfoPageRsp> getOrderPageBySelfVerifyCode(@Valid OrderQueryBySelfCodeReqDto pageBase) {
        Page<OrderInfoPageRsp> page = new Page<>(pageBase.getCurrentPage(), pageBase.getPageSize());
        DsOnlineStoreResDTO dsOnlineStoreResDTO = dsOnlineStoreService.queryOnlineStoreById(pageBase.getOnlineStoreId());
        boolean platFlag = PlatformCodeEnum.PING_AN_CENTRAL.getCode().equals(pageBase.getThirdPlatFormCode()) || PlatformCodeEnum.PA_CITY.getCode().equals(pageBase.getThirdPlatFormCode()) || PlatformCodeEnum.PA_COMMON_O2O.getCode().equals(pageBase.getThirdPlatFormCode()) || PlatformCodeEnum.JD_DAOJIA.getCode().equals(pageBase.getThirdPlatFormCode());
        if (platFlag) {
            //平安平安
            //获取门店信息
            OnlineStoreInfoRspDto storeInfo = baseInfoService.getOnlineStoreInfo(pageBase.getMerCode(), pageBase.getThirdPlatFormCode(), dsOnlineStoreResDTO.getOnlineClientCode(), dsOnlineStoreResDTO.getOnlineStoreCode());
            //取货码校验
            HemsBaseData baseData = hemsCommonClient.constructHemsBaseData(pageBase.getMerCode(), storeInfo.getPlatformCode(), storeInfo.getOnlineClientCode(), storeInfo.getSessionKey());
            OrderCodeReq orderCodeReq = new OrderCodeReq();
            orderCodeReq.setOlShopId(dsOnlineStoreResDTO.getOnlineStoreCode());
            orderCodeReq.setVerifyCode(pageBase.getSelfVerifyCode());
            BaseHemsResp<OrderCodeResp> responseNet = hemsCommonClient.getOrderBySelfCode(orderCodeReq, baseData);
            if (null != responseNet && DsConstants.INTEGER_ZERO.equals(responseNet.getCode())) {
                //请求成功
                OrderPrescriptionQueryReqDto orderPrescriptionQueryReqDto = new OrderPrescriptionQueryReqDto();
                orderPrescriptionQueryReqDto.setThirdOrderNo(responseNet.getData().getOlOrderNo());
                orderPrescriptionQueryReqDto.setMerCode(pageBase.getMerCode());
                orderPrescriptionQueryReqDto.setOnlineCode(dsOnlineStoreResDTO.getOnlineStoreCode());
                orderPrescriptionQueryReqDto.setThirdPlatFormCode(pageBase.getThirdPlatFormCode());
                if (PlatformCodeEnum.JD_DAOJIA.getCode().equals(pageBase.getThirdPlatFormCode())) {
                    //京东到家更新自提码用于核销
                    orderInfoMapper.updateSelfVerifyCode(orderPrescriptionQueryReqDto, pageBase.getSelfVerifyCode());
                }
                return orderInfoMapper.selectOrderPageByOrderNo(page, orderPrescriptionQueryReqDto);

            } else if (null != responseNet && NetCode.CODE_1009.getCode().equals(responseNet.getCode())) {
                throw ExceptionUtil.getWarnException(String.valueOf(NetCode.CODE_1009.getCode()), responseNet.getMsg());
            } else {
                throw ExceptionUtil.getWarnException(DsErrorType.ORDER_NOT_EXIST);
            }
        }
        OrderVerifyCodeReqDto reqDto = new OrderVerifyCodeReqDto();
        reqDto.setMerCode(pageBase.getMerCode());
        reqDto.setOnlineCode(dsOnlineStoreResDTO.getOnlineStoreCode());
        reqDto.setSelfVerifyCode(pageBase.getSelfVerifyCode());
        reqDto.setThirdPlatFormCode(pageBase.getThirdPlatFormCode());
        return orderInfoMapper.selectOrderPageBySelfVerifyCode(page, reqDto);
    }

    @Override
    public List<OrderInfoStoreRsp> getUploadOrderPage(Page page, String userId, @Valid SearchOrderPageReqDto pageBase) {
        IPage<OrderInfoStoreRsp> iPage = orderInfoMapper.selectOrderUploadPage(page, pageBase);
        return iPage.getRecords();
    }

    @Override
    public List<OrderInfoStoreRsp> getUploadOrderAllPage(Page page, String userId, SearchOrderPageReqDto pageBase, List<String> organizationCodeList) {
        IPage<OrderInfoStoreRsp> iPage = orderInfoMapper.selectOrderUploadAllPage(page, pageBase, organizationCodeList);
        return iPage.getRecords();
    }

    @Override
    public IPage<OrderInfoPageRsp> getOrderReservationPage(OrderReservationReqDto pageBase) {
        if (DsConstants.INTEGER_ZERO.equals(pageBase.getDeliveryTimeType())) {
            throw ExceptionUtil.getWarnException(DsErrorType.NOT_ORDER_RESERVATION);
        }
        Page<OrderInfoPageRsp> page = new Page<>(pageBase.getCurrentPage(), pageBase.getPageSize());
        return orderInfoMapper.selectOrderReservationPage(page, pageBase);
    }

    @Override
    @DS(DsConstants.DB_ORDER_SLAVE)
    public OrderStateCountRspDto getOrderCountByOrganCode(String userId, String merCode, String organCode) throws ExecutionException, InterruptedException {
        if (orderInfoRedisManager.checkisRedis(merCode)) {
            //如果是version2版本，则调用新实现方式
            return getOrderCountByOrganCodeFromRedis(userId, merCode, organCode);
        }
        //根据配置，选择原有调用，还是调用redis控制的
        VerifyRspDto verifyDto = verifyService.verifyOrganizationAndGet(VerifyReqDto.builder().merCode(merCode).organizationCode(organCode).userId(userId).build());
        if ("-99".equals(organCode) && verifyDto.getOrganizatioinList().size() > 1) {
            if (orderCountStatusSwitch) {
                throw ExceptionUtil.getWarnException(DsErrorType.FORBIDDEN_QUERY_ALL.getCode(), "获取线下门店订单各状态数量禁止查询全部");
            }

            log.info("查询全部 userId: {}", userId);//如果是-99，表示查询所有门店
            Date temp = DateUtil.getDayBeginTime(new Date(System.currentTimeMillis()));
            Date dayEndTime = DateUtil.addDate(temp, 0, 0, 1, 0, 0, 0, 0);
            Date dayBeginTime = DateUtil.addDate(temp, 0, 0, -limitDayNum, 0, 0, 0, 0);
            verifyDto.setBeginTime(dayBeginTime);
            verifyDto.setEndTime(dayEndTime);
        }
        List<String> platformCodeList = verifyService.verifyPlatformCodeAndGet(merCode, userId, null);


        // 订单状态数统计
        CompletableFuture<List<OrderCountDomain>> orderCountDomainsFuture = CompletableFuture.supplyAsync(() -> orderInfoMapper.selectStoreOrderCount(merCode, organCode, verifyDto.getOrganizatioinList(), platformCodeList, verifyDto.getBeginTime(), verifyDto.getEndTime()), queryThreadPool);
        // 处方单
        CompletableFuture<Integer> uncheckNumFuture = CompletableFuture.supplyAsync(() -> orderInfoMapper.selectUncheckCount(merCode, organCode, verifyDto.getOrganizatioinList(), platformCodeList, verifyDto.getBeginTime(), verifyDto.getEndTime()), queryThreadPool);
        // 待拣货（非预约单 或 预约单已处理）
        CompletableFuture<Integer> unPickCountFuture = CompletableFuture.supplyAsync(() -> orderInfoMapper.selectStoreOrderUnPickCount(merCode, organCode, verifyDto.getOrganizatioinList(), platformCodeList, verifyDto.getBeginTime(), verifyDto.getEndTime()), queryThreadPool);
        // 取消数、异常数
        CompletableFuture<List<OrderCountDomain>> lockOrderCountDomainListFuture = CompletableFuture.supplyAsync(() -> orderInfoMapper.selectStoreOrderCancelExceptionCount(merCode, organCode, verifyDto.getOrganizatioinList(), platformCodeList, verifyDto.getBeginTime(), verifyDto.getEndTime()), queryThreadPool);
        // 退款数 退款中的订单必须有相应的退款单，才统计数据。即只统计退款单的记录数目
        CompletableFuture<RefundCountDomain> refundCountDomainFuture = null;
        if(openRefundOrganizationSearch){
            refundCountDomainFuture = CompletableFuture.supplyAsync(() -> refundOrderMapper.selectRefundingCountV2(merCode, organCode, verifyDto.getOrganizatioinList(), platformCodeList, verifyDto.getBeginTime(), verifyDto.getEndTime()), queryThreadPool);
        }else{
            refundCountDomainFuture = CompletableFuture.supplyAsync(() -> refundOrderMapper.selectRefundingCount(merCode, organCode, verifyDto.getOrganizatioinList(), platformCodeList, verifyDto.getBeginTime(), verifyDto.getEndTime()), queryThreadPool);
        }
        // 催单中
        CompletableFuture<Integer> urgeCountFuture = CompletableFuture.supplyAsync(() -> orderInfoMapper.selectStoreOrderRemindCount(merCode, organCode, verifyDto.getOrganizatioinList(), platformCodeList, verifyDto.getBeginTime(), verifyDto.getEndTime()), queryThreadPool);
        // 预约单
        CompletableFuture<Integer> appointmentCountFuture = CompletableFuture.supplyAsync(() -> orderInfoMapper.selectStoreOrderAppointmentCount(merCode, organCode, verifyDto.getOrganizatioinList(), platformCodeList, verifyDto.getBeginTime(), verifyDto.getEndTime()), queryThreadPool);

        CompletableFuture.allOf(
                orderCountDomainsFuture,
                uncheckNumFuture,
                unPickCountFuture,
                lockOrderCountDomainListFuture,
                refundCountDomainFuture,
                urgeCountFuture,
                appointmentCountFuture).join();

        List<OrderCountDomain> orderCountDomains = orderCountDomainsFuture.get();
        Integer uncheckNum = uncheckNumFuture.get();
        Integer unPickCount = unPickCountFuture.get();
        List<OrderCountDomain> lockOrderCountDomainList = lockOrderCountDomainListFuture.get();
        RefundCountDomain refundCountDomain = refundCountDomainFuture.get();
        Integer urgeCount = urgeCountFuture.get();
        Integer appointmentCount = appointmentCountFuture.get();

        // build response
        OrderStateCountRspDto orderStateCountRspDto = new OrderStateCountRspDto();

        Map<Integer, Integer> map = orderCountDomains.stream().collect(Collectors.toMap(OrderCountDomain::getStatus, OrderCountDomain::getNum));
        if (map.containsKey(OrderStateEnum.UN_TAKE.getCode())) {
            orderStateCountRspDto.setUnTakeNum(map.get(OrderStateEnum.UN_TAKE.getCode()));
        }
        if (map.containsKey(OrderStateEnum.UN_DELIVERY.getCode())) {
            orderStateCountRspDto.setUnDeliveryNum(map.get(OrderStateEnum.UN_DELIVERY.getCode()));
        }
        if (map.containsKey(OrderStateEnum.POSTING.getCode())) {
            orderStateCountRspDto.setUnReceiveNum(map.get(OrderStateEnum.POSTING.getCode()));
        }

        // 处方单
        orderStateCountRspDto.setUnCheckNum(uncheckNum);
//        if (map.containsKey(DsConstants.STRING_FIVE)) {
//            orderStateCountRspDto.setUnCheckNum(map.get(DsConstants.STRING_FIVE));
//        }
        orderStateCountRspDto.setUnPickNum(unPickCount);

        int cancelCount = 0;
        int exceptionCount = 0;
        for (OrderCountDomain orderCountDomain : lockOrderCountDomainList) {
            if (OrderLockFlagEnum.LOCK_CANCEL.getCode().equals(orderCountDomain.getStatus())) {
                cancelCount += orderCountDomain.getNum();
            } else if (orderCountDomain.getStatus() > OrderLockFlagEnum.LOCK_EXCEPTION_MIN_CODE.getCode() && orderCountDomain.getStatus() < OrderLockFlagEnum.LOCK_EXCEPTION_MAX_CODE.getCode()) {
                exceptionCount += orderCountDomain.getNum();
            }
        }

        // 取消中
        orderStateCountRspDto.setCancelingNum(cancelCount);
        // 异常中
        orderStateCountRspDto.setExceptionNum(exceptionCount);
        // 退款中
        orderStateCountRspDto.setRefundNum(refundCountDomain.getNum());
        // 催单中
        orderStateCountRspDto.setUrgeNum(urgeCount);
        // 预约单
        orderStateCountRspDto.setAppointmentNum(appointmentCount);

        // 待下账数量 = 销售单待下账数量 + 退款单待下账数量
//        OrderLedgerPageReqDto req = new OrderLedgerPageReqDto();
//        req.setMerCode(merCode);
//        ArrayList<Integer> billStates = new ArrayList<>();
//        billStates.add(ErpStateEnum.WAIT_PICK.getCode());
//        billStates.add(ErpStateEnum.WAIT_SALE.getCode());
//        OrderErpListDtoReq orderErpListDtoReq = new OrderErpListDtoReq();
//        orderErpListDtoReq.setErpStateList(billStates);
//        req.setBillState(orderErpListDtoReq);`
        int orderWaitSaleNum = -1;//getOrderLedgerAllListCount(req, verifyDto.getOrganizatioinList(), platformCodeList);
        orderStateCountRspDto.setWaitSaleNum(orderWaitSaleNum);
//
//        RefundLedgerReqDto rfReq = new RefundLedgerReqDto();
//        rfReq.setMerCode(merCode);
//        rfReq.setBillState(RefundStateEnum.NOT_RETURN_GOODS.getCode());
        int refundLedgerCount = -1;//refundOrderMapper.getRefundLedgerCount(rfReq, verifyDto.getOrganizatioinList(), platformCodeList);
        orderStateCountRspDto.setWaitRefundSaleNum(refundLedgerCount);

        return orderStateCountRspDto;
    }

    @Override
    public OrderStateCountRspDto getOrderCountByOrganCodeFromRedis(String userId, String merCode, String organCode) {
        VerifyRspDto verifyDto = verifyService.verifyOrganizationAndGet(VerifyReqDto.builder().merCode(merCode).organizationCode(organCode).userId(userId).build());
        List<String> platformCodeList = verifyService.verifyPlatformCodeAndGet(merCode, userId, null);
        verifyDto.setPlatformList(platformCodeList);
        //批量获取
        return orderInfoRedisManager.getOrderCountFromRedis(merCode, verifyDto);
    }

    @Override
    public void markException(String merCode, String userId, OrderHandleExceptionReqDto orderHandleReqDto) {
        OrderInfo orderInfo = orderBasicService.checkOrderLock(userId, orderHandleReqDto, true);
        SysEmployeeResDTO sysEmployeeResDTO = baseInfoService.getEmployeeInfo(userId);
        int num = makeExceptionWithTran(sysEmployeeResDTO, userId, orderInfo, orderHandleReqDto);
        if (num > 0) {
            //订单更新异步消费
            messageProducerService.produceUpdateOrderMessage(Collections.singletonList(orderInfo.getOrderNo()), OrderUpdateCodeEnum.ORDER_EX.getCode());
            // 订单日志
            HydeeEsSyncClientAsync.orderLogToES(DsConstants.INTEGER_ONE, merCode, orderInfo.getOrderNo(), orderInfo.getOrderState(), orderInfo.getErpState(), OrderLogEnum.COMMIT_EXCEPTION.getAction(), OrderLogEnum.getCommitExceptionInfo(Strings.join(orderHandleReqDto.getErpCodeList(), ',')), sysEmployeeResDTO);
        }
    }

    @Transactional(rollbackFor = Exception.class)
    public int makeExceptionWithTran(SysEmployeeResDTO sysEmployeeResDTO, String userId, OrderInfo orderInfo, OrderHandleExceptionReqDto orderHandleReqDto) {
        if (OrderStateEnum.UN_PICK.getCode().equals(orderInfo.getOrderState()) && OrderLockFlagEnum.NOT_LOCK.getCode().equals(orderInfo.getLockFlag())) {
            if (sysEmployeeResDTO == null) {
                throw ExceptionUtil.getWarnException(DsErrorType.USER_NOT_EXIST_ERROR);
            }
            //判断erpCodeList是否有数据，若无则抛erp不存在的异常
            if (orderHandleReqDto.getErpCodeList().isEmpty()) {
                throw ExceptionUtil.getWarnException(DsErrorType.REPLACE_DETAIL_ERP_CODE_ERROR);
            }
            OrderInfo updateOrder = new OrderInfo();
            updateOrder.setOrderNo(orderHandleReqDto.getOrderNo());
            updateOrder.setLockFlag(orderHandleReqDto.getLockFlag());
            updateOrder.setExOperatorId(sysEmployeeResDTO.getEmpId());
            updateOrder.setExOperatorName(sysEmployeeResDTO.getEmpName());
            updateOrder.setExOperatorTime(new Date());
            int num = orderInfoMapper.updateOrderWithState(updateOrder, orderInfo.getOrderState());
            if (num > 0) {
                LambdaQueryWrapper<OrderDetail> eq = new QueryWrapper<OrderDetail>().lambda().eq(OrderDetail::getOrderNo, orderHandleReqDto.getOrderNo()).in(OrderDetail::getErpCode, orderHandleReqDto.getErpCodeList());
                List<OrderDetail> oldOrderDetailList = orderDetailMapper.selectList(eq);
                // 修改
                orderDetailMapper.batchUpdateStatus(orderHandleReqDto.getOrderNo(), orderHandleReqDto.getErpCodeList(), OrderDetailStatusEnum.OUT_OF_STOCK.getCode());
                List<OrderDetail> orderDetailList = orderDetailMapper.selectListByOrderNo(orderInfo.getOrderNo());
                Map<String, String> commodityCodeNameMap = new HashMap<>();
                if (!CollectionUtils.isEmpty(orderDetailList)) {
                    commodityCodeNameMap = orderDetailList.stream().collect(Collectors.toMap(OrderDetail::getErpCode, OrderDetail::getCommodityName, (o1, o2) -> o1));
                }
                // 标记为库存不足 添加异常库存商品记录
                for (String erpCode : orderHandleReqDto.getErpCodeList()) {
                    CommodityExceptionOrder commodityExceptionOrder = new CommodityExceptionOrder();
                    commodityExceptionOrder.setMerCode(orderInfo.getMerCode());
                    commodityExceptionOrder.setOrderNo(orderInfo.getOrderNo());
                    commodityExceptionOrder.setErpCode(erpCode);
                    commodityExceptionOrder.setStatus(OrderDetailStatusEnum.OUT_OF_STOCK.getCode());
                    Date now = new Date();
                    commodityExceptionOrder.setCreateTime(now);
                    commodityExceptionOrder.setModifyTime(now);
                    commodityExceptionOrder.setBizType(DsConstants.INTEGER_THREE);
                    // 增加商品名称快照
                    Optional.ofNullable(commodityCodeNameMap.get(erpCode)).ifPresent(commodityName -> {
                        commodityExceptionOrder.setCommodityName(commodityName);
                    });
                    commodityExceptionOrderService.insert(commodityExceptionOrder);
                }
                // 释放原商品
                commodityStockService.unLockStockNew(orderInfo, oldOrderDetailList, "拣货复核标记为商品为异常释放商品库存", UnLockStockTypeEnum.PART);
            }
            return num;
        } else {
            throw ExceptionUtil.getWarnException(DsErrorType.STATE_NOT_PERMITTED.getCode(), "非拣货状态或锁定状态不允许标记异常");
        }
    }

    @Override
    public void merchantCancelOrder(String merCode, String userId, OrderAuditHandleReq orderHandleReqDto) {
        OrderInfo orderInfo = orderBasicService.getOrderBaseWithCheck(orderHandleReqDto.getOrderNo());
        GlobalInterceptor.tObject.set(orderInfo);
        // 判断订单状态
        if (orderInfo.getOrderState() >= OrderStateEnum.COMPLETED.getCode() || ErpStateEnum.HAS_SALE.getCode() <= orderInfo.getErpState()) {
            throw ExceptionUtil.getWarnException(DsErrorType.STATE_NOT_PERMITTED_TO_CANCEL);
        }

        // 判断异常    31-37类型的异常能取消
        if (!OrderLockFlagEnum.LOCK_LACK_STOCK.getCode().equals(orderInfo.getLockFlag()) && !OrderLockFlagEnum.LOCK_COMMODITY_NOT_EXIST.getCode().equals(orderInfo.getLockFlag()) && !OrderLockFlagEnum.LOCK_MONEY_EX_UN_TAKE.getCode().equals(orderInfo.getLockFlag()) && !OrderLockFlagEnum.LOCK_DELIVERY_EX_UN_TAKE.getCode().equals(orderInfo.getLockFlag()) && !OrderLockFlagEnum.LOCK_SHOP_EX.getCode().equals(orderInfo.getLockFlag()) && !OrderLockFlagEnum.CFCHECK_NOT_PASSS.getCode().equals(orderInfo.getLockFlag()) && !OrderLockFlagEnum.LOCK_COMMODITY_NOT_SALL.getCode().equals(orderInfo.getLockFlag()) && !OrderLockFlagEnum.ROBOT_AUTO_PICK_ERROR.getCode().equals(orderInfo.getLockFlag()) &&
                //商品不存在异常分类
                !OrderLockFlagEnum.LOCK_ERP_CODE_NULL.getCode().equals(orderInfo.getLockFlag()) && !OrderLockFlagEnum.LOCK_THIRD_COMMODITY_UNMATCH.getCode().equals(orderInfo.getLockFlag()) && !OrderLockFlagEnum.LOCK_OFFLINE_COMMODITY_NOT_EXSIST.getCode().equals(orderInfo.getLockFlag())) {
            throw ExceptionUtil.getWarnException(DsErrorType.STATE_NOT_PERMITTED.getCode(), "该异常状态不允许取消");
        }

        //【*********】 第一次弹框的确定操作，判断 “订单是否为第三方骑手配送，且配送单未完成；” 是 就要取消骑手，取消失败抛异常
        // DoubleCheck 为 1 时 是第二次弹框的确认，直接走老逻辑
        if (orderHandleReqDto.getDoubleCheck().equals(DsConstants.INTEGER_ZERO)) {
            orderBasicManager.orderCancelWithEx(orderInfo, CancelRiderEnum.ExceptionOrderCancel);
        }

        // 调用订单取消接口
        OnlineStoreInfoRspDto storeInfo = baseInfoService.getOnlineStoreInfo(orderInfo.getMerCode(), orderInfo.getThirdPlatformCode(), orderInfo.getClientCode(), orderInfo.getOnlineStoreCode());
        OrderCancelReq netAuditReq = new OrderCancelReq();
        netAuditReq.setOlOrderNo(orderInfo.getThirdOrderNo());
        netAuditReq.setReasonCode(orderHandleReqDto.getReasonCode());
        netAuditReq.setReason(orderHandleReqDto.getReason());
        netAuditReq.setOlShopId(orderInfo.getOnlineStoreCode());
        netAuditReq.setShopId(storeInfo.getOutShopId());
        HemsBaseData baseData = hemsCommonClient.constructHemsBaseData(merCode, storeInfo.getPlatformCode(), storeInfo.getOnlineClientCode(), storeInfo.getSessionKey());
        hemsCommonClient.orderCancel(netAuditReq, baseData);
        // 释放库存往前提
        commodityStockService.unLockStockNew(orderInfo, null, "商家取消异常订单释放库存", UnLockStockTypeEnum.ALL);
        // 解锁erp库存，更新下账状态为【取消下账】
        erpBillService.undoErpForCancel(orderInfo, userId, "商家取消异常订单");

        // 取消锁定到订单取消
        orderHandler.modifyOrderToCancel(userId, orderInfo, DsConstants.CANCEL_ORDER, "商家取消");
        SysEmployeeResDTO employee = baseInfoManager.getEmployeeInfoForErpNoEx(userId);

        //订单更新异步消费
        messageProducerService.produceUpdateOrderMessage(Collections.singletonList(orderInfo.getOrderNo()), OrderUpdateCodeEnum.CANCEL_ORDER.getCode());

        OrderInfo curOrderInfo = orderInfoMapper.selectBaseInfoByOrderNo(orderInfo.getOrderNo());
        HydeeEsSyncClientAsync.orderLogToES(DsConstants.INTEGER_ONE, orderInfo.getMerCode(), orderInfo.getOrderNo(), curOrderInfo.getOrderState(), curOrderInfo.getErpState(), OrderLogEnum.CANCEL_ORDER.getAction(), OrderLogEnum.getCancelOrderInfo("异常订单-取消"), employee);
    }

    @Override
    @OrderLock
    public String merchantCheckCancelOrder(String merCode, String userId, OrderAuditCheckHandleReq orderHandleReqDto, String orderNo) {
        String message = "";
        OrderInfo orderInfo = orderBasicService.getOrderBaseWithCheck(orderHandleReqDto.getOrderNo());
        // 医保云订单只允许当天退款
        checkMedicalInsurance(orderInfo);
        // 【*********】【优化】订单查询微商城订单操作全部退款优化
        orderHavePartRefundActive(orderInfo);
        GlobalInterceptor.tObject.set(orderInfo);
        // 判断订单状态
        if (orderInfo.getOrderState() > OrderStateEnum.COMPLETED.getCode()) {
            throw ExceptionUtil.getWarnException(DsErrorType.STATE_NOT_PERMITTED_TO_REFUND);
        }
        if (orderInfo.getOrderState() >= 40 && orderInfo.getOrderState() <= 100 && (orderHandleReqDto.getType() == 2 || orderHandleReqDto.getType() == 1)) {

        } else if (orderHandleReqDto.getType() == 2 && ErpStateEnum.HAS_SALE.getCode().equals(orderInfo.getErpState())) {

        } else if (orderHandleReqDto.getType() == 1) {

        } else {
            throw ExceptionUtil.getWarnException(DsErrorType.ORDER_STATE_NOT_MATCH_TYPE);
        }

        //【*********】 第一次弹框的确定操作，判断 “订单是否为第三方骑手配送，且配送单未完成；” 是 就要取消骑手，取消失败抛异常
        // DoubleCheck 为 1 时 是第二次弹框的确认，直接走老逻辑
        if (orderHandleReqDto.getDoubleCheck().equals(DsConstants.INTEGER_ZERO)) {
            orderBasicManager.orderCancelWithEx(orderInfo, CancelRiderEnum.MerchantRefund);
        }

        //调用订单取消接口
        OnlineStoreInfoRspDto storeInfo = baseInfoService.getOnlineStoreInfo(orderInfo.getMerCode(), orderInfo.getThirdPlatformCode(), orderInfo.getClientCode(), orderInfo.getOnlineStoreCode());
        OrderCancelReq netAuditReq = new OrderCancelReq();
        netAuditReq.setOlOrderNo(orderInfo.getThirdOrderNo());
        netAuditReq.setReasonCode(orderHandleReqDto.getReasonCode());
        netAuditReq.setReason(orderHandleReqDto.getReason());
        netAuditReq.setOlShopId(orderInfo.getOnlineStoreCode());
        netAuditReq.setShopId(storeInfo.getOutShopId());
        HemsBaseData baseData = hemsCommonClient.constructHemsBaseData(merCode, storeInfo.getPlatformCode(), storeInfo.getOnlineClientCode(), storeInfo.getSessionKey());
        hemsCommonClient.orderCancel(netAuditReq, baseData);

        // 更新订单状态为已关闭
        OrderInfo updateOrder = new OrderInfo();
        updateOrder.setOrderNo(orderHandleReqDto.getOrderNo());
        updateOrder.setOrderState(OrderStateEnum.CLOSED.getCode());
        updateOrder.setLockFlag(OrderLockFlagEnum.NOT_LOCK.getCode());
        updateOrder.setRemindFlag(DsConstants.INTEGER_ZERO);
        updateOrder.setCancellerId(userId);
        // 新增逆向运费单流程
        undoErpForCancel(updateOrder,orderInfo.getFreightOrderNo());
        //订单取消下账
        message = erpBillService.undoErpForCancelRefundEnterAccountNoException(orderInfo, userId, orderHandleReqDto.getType());


        // C端操作,状态为待拣货的预约订单且业务类型为【代发】的订单，订单变更为【已取消、已关闭】状态时,推送代发订单取消发货消息给服务商平台
        cloudShelfService.cancelDeliverGoodsWhenStateChange(updateOrder);


        int num = orderInfoMapper.updateOrder(updateOrder);
        if (num > 0) {
            //订单更新异步消费
            messageProducerService.produceUpdateOrderMessage(Collections.singletonList(updateOrder.getOrderNo()), OrderUpdateCodeEnum.QUERY_CANCEL_ORDER.getCode());
            // 订单日志
            SysEmployeeResDTO employeePickOperator = baseInfoManager.getEmployeeInfoForErpNoEx(userId);
            HydeeEsSyncClientAsync.orderLogToES(DsConstants.INTEGER_ONE, merCode, orderInfo.getOrderNo(), updateOrder.getOrderState(), orderInfo.getErpState(), OrderLogEnum.REFUND.getAction(), OrderLogEnum.getRefundInfo("商家主动发起退款:成功"), employeePickOperator);
        }
        NotifyRedisHelper.removeAllNotify(orderInfo, storeInfo);
        return message;
    }

    @Override
    public void exOrderForceAudit(String merCode, String userId, OrderHandleReqDto orderHandleReqDto) {
        SysEmployeeResDTO sysEmployeeResDTO = baseInfoService.getEmployeeInfo(userId);
        if (sysEmployeeResDTO == null || sysEmployeeResDTO.getEmpId() == null) {
            throw ExceptionUtil.getWarnException(DsErrorType.USER_NOT_EXIST_ERROR);
        }
        OrderInfo orderInfo = orderBasicService.getOrderBaseWithCheck(orderHandleReqDto.getOrderNo());
        // 商品不存在 不允许强审核
        if (OrderLockFlagEnum.LOCK_COMMODITY_NOT_EXIST.getCode().equals(orderInfo.getLockFlag())
                || OrderLockFlagEnum.LOCK_COMMODITY_NOT_SALL.getCode().equals(orderInfo.getLockFlag())
                || OrderLockFlagEnum.LOCK_ERP_CODE_NULL.getCode().equals(orderInfo.getLockFlag())
                || OrderLockFlagEnum.LOCK_THIRD_COMMODITY_UNMATCH.getCode().equals(orderInfo.getLockFlag())
                || OrderLockFlagEnum.LOCK_OFFLINE_COMMODITY_NOT_EXSIST.getCode().equals(orderInfo.getLockFlag()) ) {
            throw ExceptionUtil.getWarnException(DsErrorType.ERP_MERCHANDISE_NOT_EXISTS_ERROR.getCode(), "商品不存在或者未对码不允许强审核！");
        }
        // 强审的异常类型： 库存不足、商品不存在
        if (!OrderLockFlagEnum.LOCK_LACK_STOCK.getCode().equals(orderInfo.getLockFlag())
                && !OrderLockFlagEnum.LOCK_MONEY_EX_UN_TAKE.getCode().equals(orderInfo.getLockFlag())
                && !OrderLockFlagEnum.LOCK_DELIVERY_EX_UN_TAKE.getCode().equals(orderInfo.getLockFlag())
                && !OrderLockFlagEnum.LOCK_SHOP_EX.getCode().equals(orderInfo.getLockFlag())
                && !OrderLockFlagEnum.SYS_ERROR.getCode().equals(orderInfo.getLockFlag())
                && !OrderLockFlagEnum.ROBOT_AUTO_PICK_ERROR.getCode().equals(orderInfo.getLockFlag())  ) {
            throw ExceptionUtil.getWarnException(DsErrorType.STATE_NOT_PERMITTED.getCode(), "该异常状态不允许强审");
        }

        List<OrderDetail> detailList = orderBasicService.getOrderDetailListWithCheck(orderInfo.getOrderNo());
        // 强审锁库存
        //v3.10.3改动，需要把异常信息更新&包装返回给前端的错误
        // 机器自动拣货异常不需要操作库存
        if (!OrderLockFlagEnum.ROBOT_AUTO_PICK_ERROR.getCode().equals(orderInfo.getLockFlag())) {
            try {
                orderHandler.commodityStockDeductOther(orderInfo, detailList, DsConstants.NO_NEED_RETRY, "异常订单强审锁库");
            } catch (ErpNetException e) {
                //            log.info("exOrderForceAudit fail due to ErpNetException");
                throw ExceptionUtil.getWarnException(DsErrorType.HTTP_ERP_NET_ERROR.getCode(), DsErrorType.HTTP_ERP_NET_ERROR.getMsg());
            } catch (WarnException e) {
                log.info("exOrderForceAuditError:", e);
                //更新异常信息
                //MERCHANDISE_STOCK_DEDUCT_ERROR  商品中台扣减库存异常
                //ERP_LOCK_STOCK_ERROR  ERP锁库存异常
                OrderInfo update = new OrderInfo();
                update.setOrderNo(orderInfo.getOrderNo());
                update.setLockFlag(OrderLockFlagEnum.LOCK_LACK_STOCK.getCode());
                OrderLockFlagEnum exCode = ErrorMessageUtil.getJiedanMsgCode(e.getMessage());
                if (null != exCode) {
                    //商品错误消息
                    update.setLockFlag(exCode.getCode());
                }
                update.setLockMsg(e.getMessage());
                // 【*********】 写入LOCK信息时判断是否已完成，已完成则不写入
                OrderInfo nowOrderInfo = orderBasicService.getOrderBaseWithCheck(orderHandleReqDto.getOrderNo());
                if (OrderStateEnum.COMPLETED.getCode().equals(nowOrderInfo.getOrderState())) {
                    log.info("have lock message but no update because order COMPLETED ,orderNo:[{}],olorderNo:[{}] ", nowOrderInfo.getOrderNo(), nowOrderInfo.getThirdOrderNo());
                    throw ExceptionUtil.getWarnException(DsErrorType.ORDER_AUDIT_ERP_ERROR);
                }
                orderInfoMapper.updateOrder(update);
                String resMsg = e.getMessage();
                if (!StringUtils.isEmpty(resMsg)) {
                    resMsg = resMsg.replace("javax.script.ScriptException: ", "");
                }
                // 如果是商品不存在的异常
                if(OrderLockFlagEnum.LOCK_OFFLINE_COMMODITY_NOT_EXSIST.equals(exCode)){
                    throw ExceptionUtil.getWarnException(DsErrorType.ERP_MERCHANDISE_NOT_EXISTS_ERROR.getCode(),DsErrorType.ERP_MERCHANDISE_NOT_EXISTS_ERROR.getMsg());
                }
                throw ExceptionUtil.getWarnException(DsErrorType.ORDER_AUDIT_ERP_ERROR.getCode(), resMsg);
            }
        }
        //更新商品状态
        for (OrderDetail temp : detailList) {
            orderDetailMapper.updateById(new OrderDetail().setId(temp.getId()).setStatus(OrderDetailStatusEnum.NORMAL.getCode()));
        }

        // 更新订单信息
        OrderInfo update = new OrderInfo();
        update.setOrderNo(orderInfo.getOrderNo());
        update.setLockFlag(OrderLockFlagEnum.NOT_LOCK.getCode());
        if (!OrderLockFlagEnum.ROBOT_AUTO_PICK_ERROR.getCode().equals(orderInfo.getLockFlag())) {
            update.setLockMsg("强审通过");
        }
        // 强审过后 不需要改订单状态，容易破坏原来的业务流程
//        update.setOrderState(OrderStateEnum.UN_PICK.getCode());
//        if(DsConstants.INTEGER_ONE.equals(orderInfo.getPrescriptionFlag())){
//            DsOnlineStoreConfig storeConfig = dsOnlineStoreConfigService.getStoreConfig(orderInfo.getMerCode(), orderInfo.getThirdPlatformCode(),
//                    orderInfo.getClientCode(), orderInfo.getOnlineStoreCode());
//            if(DsConstants.INTEGER_ONE.equals(storeConfig.getWhetherNeedPrescription())){
//                update.setOrderState(OrderStateEnum.UN_CHECK.getCode());
//            }
//        }
        if (orderInfo.getLockFlag() >= OrderLockFlagEnum.LOCK_EXCEPTION_MIN_CODE.getCode()) {
            update.setExOperatorId(sysEmployeeResDTO.getEmpId());
            update.setExOperatorName(sysEmployeeResDTO.getEmpName());
            update.setExOperatorTime(new Date());
        }
        int num = orderInfoMapper.updateOrderWithState(update, orderInfo.getOrderState());
        //日志打印
        if (num > 0) {
            //订单更新异步消费
            messageProducerService.produceUpdateOrderMessage(Collections.singletonList(orderInfo.getOrderNo()), OrderUpdateCodeEnum.FORCE_AUDIT.getCode());
            // 订单日志
            SysEmployeeResDTO employeePickOperator = baseInfoManager.getEmployeeInfoForErpNoEx(userId);
            HydeeEsSyncClientAsync.orderLogToES(DsConstants.INTEGER_ONE, merCode, orderInfo.getOrderNo(), orderInfo.getOrderState(), orderInfo.getErpState(), OrderLogEnum.STRONG_AUDIT.getAction(), OrderLogEnum.STRONG_AUDIT.getInfo(), employeePickOperator);
        }

    }

    @Override
    public void lockStockRetryByMQ(String merCode, OrderHandleReqDto orderHandleReqDto) {
        OrderInfo orderInfo = orderBasicService.getOrderBaseWithCheck(orderHandleReqDto.getOrderNo());

        // 1. 订单下账状态为待拣货或待下账且订单未锁库存 2 订单状态不为已完成、已关闭 、已取消
        if (!((ErpStateEnum.WAIT_PICK.getCode().equals(orderInfo.getErpState()) || ErpStateEnum.WAIT_SALE.getCode().equals(orderInfo.getErpState())) && (StringUtils.isEmpty(orderInfo.getErpAdjustNo()))) || (OrderStateEnum.COMPLETED.getCode().equals(orderInfo.getOrderState()) || OrderStateEnum.CLOSED.getCode().equals(orderInfo.getOrderState()) || OrderStateEnum.CANCEL.getCode().equals(orderInfo.getOrderState()))) {
            log.info("lockStockRetryByMQ 该订单有货位调整单或者状态不需锁库存重试 orderNo {} ", orderInfo.getOrderNo());
            throw ExceptionUtil.getWarnException(DsErrorType.STATE_NOT_PERMITTED.getCode(), "该订单有货位调整单或者状态不需锁库存重试");
        }

        List<OrderDetail> detailList = orderBasicService.getOrderDetailListWithCheck(orderInfo.getOrderNo());
        // 强审锁库存
        //v3.10.3改动，需要把异常信息更新&包装返回给前端的错误
        try {
            //这里重试是指erp重试 不扣商品库存
//            orderHandler.commodityStockDeductOther(orderInfo, detailList, DsConstants.IS_RETRY);
            if (DsConstants.CALLERPFLAG_CALL.equals(orderInfo.getCallErpFlag())) {
                erpBillService.erpTakeWithEx(orderInfo, detailList, DsConstants.IS_RETRY);
            }
            OrderInfo unlockStockOrderInfo = new OrderInfo();
            BeanUtils.copyProperties(orderInfo, unlockStockOrderInfo);
            unlockStockOrderInfo.setErpAdjustNo(null);
//            localStockService.unlockStock(unlockStockOrderInfo, detailList, StockSourceEnum.RECEIVE_LOCK_UNLOCK);
            commodityStockService.unLockStockNew(orderInfo, null, "收到延迟消费消息释放库存", UnLockStockTypeEnum.ALL);
        } catch (ErpNetException e) {
            // 网络异常  不锁订单
            throw e;
        } catch (WarnException e) {
            log.error("lockStockRetryByMQ Error:", e);
            //更新异常信息
            //MERCHANDISE_STOCK_DEDUCT_ERROR  商品中台扣减库存异常
            //ERP_LOCK_STOCK_ERROR  ERP锁库存异常
            OrderInfo update = new OrderInfo();
            update.setOrderNo(orderInfo.getOrderNo());
            update.setLockFlag(OrderLockFlagEnum.LOCK_LACK_STOCK.getCode());
            OrderLockFlagEnum exCode = ErrorMessageUtil.getJiedanMsgCode(e.getMessage());
            if (null != exCode) {
                //商品错误消息
                update.setLockFlag(exCode.getCode());
            }
            update.setLockMsg(e.getMessage());
            // 【*********】 写入LOCK信息时判断是否已完成，已完成则不写入
            OrderInfo nowOrderInfo = orderBasicService.getOrderBaseWithCheck(orderHandleReqDto.getOrderNo());
            if (OrderStateEnum.COMPLETED.getCode().equals(nowOrderInfo.getOrderState())) {
                log.info("lockStockRetryByMQ have lock message but no update because order COMPLETED ,orderNo:[{}],olorderNo:[{}] ", nowOrderInfo.getOrderNo(), nowOrderInfo.getThirdOrderNo());
                throw ExceptionUtil.getWarnException(DsErrorType.ORDER_AUDIT_ERP_ERROR);
            }
            orderInfoMapper.updateOrder(update);
            throw ExceptionUtil.getWarnException(DsErrorType.ORDER_AUDIT_ERP_ERROR);
        }
        //更新商品状态
        for (OrderDetail temp : detailList) {
            orderDetailMapper.updateById(new OrderDetail().setId(temp.getId()).setStatus(OrderDetailStatusEnum.NORMAL.getCode()));
        }

        // 更新订单信息
        OrderInfo update = new OrderInfo();
        update.setOrderNo(orderInfo.getOrderNo());
        update.setLockFlag(OrderLockFlagEnum.NOT_LOCK.getCode());
        update.setLockMsg("通过");
        update.setOrderState(OrderStateEnum.UN_PICK.getCode());
        if (orderInfo.getLockFlag() >= OrderLockFlagEnum.LOCK_EXCEPTION_MIN_CODE.getCode()) {
            update.setExOperatorId(DsConstants.SYSTEM);
            update.setExOperatorName(DsConstants.SYSTEM);
            update.setExOperatorTime(new Date());
        }
        orderInfoMapper.updateOrderWithState(update, orderInfo.getOrderState());

    }

    @Override
    public void merchantAgreeCancel(String merCode, String userId, OrderHandleBaseReqDto orderHandleBaseReqDto) {
        OrderInfo orderInfo = orderBasicService.getOrderBaseWithCheck(orderHandleBaseReqDto.getOrderNo());
        // 判断锁定状态
        if (!OrderLockFlagEnum.LOCK_CANCEL.getCode().equals(orderInfo.getLockFlag())) {
            throw ExceptionUtil.getWarnException(DsErrorType.STATE_NOT_PERMITTED);
        }

        //【*********】 第一次弹框的确定操作，判断 “订单是否为第三方骑手配送，且配送单未完成；” 是 就要取消骑手，取消失败抛异常
        // DoubleCheck 为 1 时 是第二次弹框的确认，直接走老逻辑
        if (orderHandleBaseReqDto.getDoubleCheck().equals(DsConstants.INTEGER_ZERO)) {
            orderBasicManager.orderCancelWithEx(orderInfo, CancelRiderEnum.MerchantAgreeCancel);
        }

        // 调用同意取消接口
        OnlineStoreInfoRspDto storeInfo = baseInfoService.getOnlineStoreInfo(orderInfo.getMerCode(), orderInfo.getThirdPlatformCode(), orderInfo.getClientCode(), orderInfo.getOnlineStoreCode());
        OrderCancelAgreeReq netAuditReq = new OrderCancelAgreeReq();
        //京东健康需要传退款单id
        if (PlatformCodeEnum.JD_HEALTH.getCode().equals(orderInfo.getThirdPlatformCode())) {
            DsMerchantGroupInfo merchantGroupInfo = merchantGroupInfoRepo.querySessionKeyByMerCode(orderInfo.getMerCode());
            String sessionKey = merchantGroupInfo.getSessionKey();
            ResponseBody<JDHealthRefundRecordResult> refundRecord = netHttpAdapter.getJdHealthRefundRecord(orderInfo.getMerCode(), orderInfo.getOnlineStoreCode(), orderInfo.getThirdPlatformCode(), orderInfo.getClientCode(), sessionKey, orderInfo.getThirdOrderNo());
            if (!CollectionUtils.isEmpty(refundRecord.getData().getResult())) {
                netAuditReq.setOlOrderNo(String.valueOf(refundRecord.getData().getResult().get(0).getId()));
            } else {
                throw ExceptionUtil.getWarnException(DsErrorType.GET_JDHEALTH_REFUND_RECORD_FAILED.getCode(), "未查询到京东健康退款单");
            }
        } else {
            netAuditReq.setOlOrderNo(orderInfo.getThirdOrderNo());
        }
        netAuditReq.setReason("商家同意取消");
        //操作人 【京东健康必填】
        netAuditReq.setOperateMan(userId);
        netAuditReq.setOlShopId(orderInfo.getOnlineStoreCode());
        netAuditReq.setShopId(storeInfo.getOutShopId());
        HemsBaseData baseData = hemsCommonClient.constructHemsBaseData(merCode, storeInfo.getPlatformCode(), storeInfo.getOnlineClientCode(), storeInfo.getSessionKey());
        hemsCommonClient.merchantAgreeCancel(netAuditReq, baseData);
        // 释放库存往前提
        commodityStockService.unLockStockNew(orderInfo, null, "商家同意取消订单释放库存", UnLockStockTypeEnum.ALL);
        //解锁erp库存
        erpBillService.undoErpForCancel(orderInfo, userId, "商家同意取消订单");
        // 同意后生成对应运费的下账单

        // 修改订单
        String extra = "商家同意第三方取消";
        orderHandler.modifyOrderToCancel(userId, orderInfo, DsConstants.CANCEL_ORDER, extra);

        //订单更新异步消费
        messageProducerService.produceUpdateOrderMessage(Collections.singletonList(orderInfo.getOrderNo()), OrderUpdateCodeEnum.AGREE_CANCEL.getCode());

        SysEmployeeResDTO employee = baseInfoManager.getEmployeeInfoForErpNoEx(userId);
        HydeeEsSyncClientAsync.orderLogToES(DsConstants.INTEGER_ONE, orderInfo.getMerCode(), orderInfo.getOrderNo(), orderInfo.getOrderState(), orderInfo.getErpState(), OrderLogEnum.CANCEL_ORDER.getAction(), extra, employee);
    }

    @Override
    public void merchantRefuseCancel(String merCode, String userId, OrderHandleBaseReqDto orderHandleBaseReqDto) {
        OrderInfo orderInfo = orderBasicService.getOrderBaseWithCheck(orderHandleBaseReqDto.getOrderNo());
        if (!OrderLockFlagEnum.LOCK_CANCEL.getCode().equals(orderInfo.getLockFlag())) {
            // 判断锁定状态
            throw ExceptionUtil.getWarnException(DsErrorType.STATE_NOT_PERMITTED);
        }
        // 调用拒绝取消接口
        OnlineStoreInfoRspDto storeInfo = baseInfoService.getOnlineStoreInfo(orderInfo.getMerCode(), orderInfo.getThirdPlatformCode(), orderInfo.getClientCode(), orderInfo.getOnlineStoreCode());
        OrderCancelRejectReq netAuditReq = new OrderCancelRejectReq();
        //京东健康需要传退款单id
        if (PlatformCodeEnum.JD_HEALTH.getCode().equals(orderInfo.getThirdPlatformCode())) {
            DsMerchantGroupInfo merchantGroupInfo = merchantGroupInfoRepo.querySessionKeyByMerCode(orderInfo.getMerCode());
            String sessionKey = merchantGroupInfo.getSessionKey();
            ResponseBody<JDHealthRefundRecordResult> refundRecord = netHttpAdapter.getJdHealthRefundRecord(orderInfo.getMerCode(), orderInfo.getOnlineStoreCode(), orderInfo.getThirdPlatformCode(), orderInfo.getClientCode(), sessionKey, orderInfo.getThirdOrderNo());
            if (!CollectionUtils.isEmpty(refundRecord.getData().getResult())) {
                netAuditReq.setOlOrderNo(String.valueOf(refundRecord.getData().getResult().get(0).getId()));
            } else {
                throw ExceptionUtil.getWarnException(DsErrorType.GET_JDHEALTH_REFUND_RECORD_FAILED.getCode(), "未查询到京东健康退款单");
            }
        } else {
            netAuditReq.setOlOrderNo(orderInfo.getThirdOrderNo());
        }
        netAuditReq.setReason("商家拒绝取消");

        //是否出库： true：已出库 false：未出库 【京东健康必填】
        netAuditReq.setOutOfDeptActual(false);
        //驳回类型 【京东健康必填】  具体取值等产品经理安排！！！
        netAuditReq.setRejectType("5");
        //操作人【京东健康必填】
        netAuditReq.setOperateMan(userId);
        netAuditReq.setOlShopId(orderInfo.getOnlineStoreCode());
        netAuditReq.setShopId(storeInfo.getOutShopId());
        HemsBaseData baseData = hemsCommonClient.constructHemsBaseData(merCode, storeInfo.getPlatformCode(), storeInfo.getOnlineClientCode(), storeInfo.getSessionKey());
        hemsCommonClient.merchantRefuseCancel(netAuditReq, baseData);

        // 修改订单为未锁定状态
        String extra = "商家拒绝第三方取消";
        orderHandler.modifyOrderToUnlock(userId, orderInfo, DsConstants.CANCEL_REFUSE, extra);

        //订单更新异步消费
        messageProducerService.produceUpdateOrderMessage(Collections.singletonList(orderInfo.getOrderNo()), OrderUpdateCodeEnum.REFUSE_CANCEL.getCode());

        SysEmployeeResDTO employee = baseInfoManager.getEmployeeInfoForErpNoEx(userId);
        HydeeEsSyncClientAsync.orderLogToES(DsConstants.INTEGER_ONE, orderInfo.getMerCode(), orderInfo.getOrderNo(), orderInfo.getOrderState(), orderInfo.getErpState(), OrderLogEnum.CANCEL_ORDER.getAction(), extra, employee);
    }

    /*@Override
    public void rePick(String merCode, String userId, OrderHandlePickConfirmReqDto orderHandleReqDto) {
        OrderInfo orderInfo = orderBasicService.getOrderBaseInfo(orderHandleReqDto.getOrderNo());
        if (!OrderLockFlagEnum.LOCK_LACK_STOCK.getCode().equals(orderInfo.getLockFlag())){
            throw ExceptionUtil.getWarnException(DsErrorType.STATE_NOT_PERMITTED);
        }
        if (!OrderStateEnum.UN_PICK.getCode().equals(orderInfo.getOrderState())){
            throw ExceptionUtil.getWarnException(DsErrorType.STATE_NOT_PERMITTED);
        }
        // 带批号重锁定
        ResponseBase<PickLockStockRspDto> base = erpHttpAdapter.lockStockWithBatchNo(merCode, userId, orderInfo, orderHandleReqDto.getPickDetailList());
        if (!ErpRspUtil.checkSuccess(base)){
            throw ExceptionUtil.getWarnException(DsErrorType.ERP_RE_LOCK_STOCK_ERROR);
        }

        PickLockStockRspDto reLockWithBatchNoRsp = base.getData();
        // 修改adjust no
        OrderInfo update = new OrderInfo();
        update.setOrderNo(orderInfo.getOrderNo());
        update.setErpAdjustNo( reLockWithBatchNoRsp.getAdjustno());
        update.setErpState(ErpStateEnum.WAIT_SALE.getCode());
        orderInfoMapper.updateOrder(update);

        // 修改订单为未锁定
        orderHandler.modifyOrderToUnlock(userId, orderInfo, DsConstants.RE_PICK, null);
    }*/

    @Override
    public void partRefund(String merCode, String userId, OrderPartRefundReqDto orderHandleReqDto) {
        OrderInfo orderInfo = orderBasicService.getOrderBaseInfo(orderHandleReqDto.getOrderNo());
        // 缺货
        boolean flag = OrderLockFlagEnum.LOCK_LACK_STOCK.getCode().equals(orderInfo.getLockFlag()) || OrderLockFlagEnum.LOCK_COMMODITY_NOT_EXIST.getCode().equals(orderInfo.getLockFlag())
                //商品不存在异常分类
                || OrderLockFlagEnum.LOCK_ERP_CODE_NULL.getCode().equals(orderInfo.getLockFlag()) || OrderLockFlagEnum.LOCK_THIRD_COMMODITY_UNMATCH.getCode().equals(orderInfo.getLockFlag()) || OrderLockFlagEnum.LOCK_OFFLINE_COMMODITY_NOT_EXSIST.getCode().equals(orderInfo.getLockFlag());
        if (!flag) {
            throw ExceptionUtil.getWarnException(DsErrorType.STATE_NOT_PERMITTED);
        }
        // 部分退货锁库存
        List<OrderDetail> orderDetailList = orderDetailMapper.selectListByOrderNo(orderInfo.getOrderNo());
        Map<Long, OrderDetail> mapDetail = orderDetailList.stream().collect(Collectors.toMap(OrderDetail::getId, p -> p));
        for (OrderPartRefundReqDto.RefundItem e : orderHandleReqDto.getRefundItemList()) {
            if (!mapDetail.containsKey(e.getDetailId())) {
                throw ExceptionUtil.getWarnException(DsErrorType.ERROR_ERP_CODE);
            }
            OrderDetail temp = mapDetail.get(e.getDetailId());
            if (e.getPartCount() > temp.getPickCount()) {
                throw ExceptionUtil.getWarnException(DsErrorType.PART_REFUND_COUNT_ERROR);
            }
        }

        // 调用三方部分退款接口
        OnlineStoreInfoRspDto storeInfo = baseInfoService.getOnlineStoreInfo(orderInfo.getMerCode(), orderInfo.getThirdPlatformCode(), orderInfo.getClientCode(), orderInfo.getOnlineStoreCode());
        OrderPartRefundReq netPartRefundReqDto = new OrderPartRefundReq();
        netPartRefundReqDto.setOlOrderNo(orderInfo.getThirdOrderNo());
        netPartRefundReqDto.setReason(orderHandleReqDto.getReason());
        List<OrderPartRefundReq.Refund> refundItemList = new ArrayList<>();
        for (OrderPartRefundReqDto.RefundItem e : orderHandleReqDto.getRefundItemList()) {
            OrderPartRefundReq.Refund refundItem = new OrderPartRefundReq.Refund();
            refundItem.setOutSkuId(e.getPlatformSkuId());
            refundItem.setSkuId(e.getErpCode());
            refundItem.setCount(e.getPartCount().toString());
            refundItemList.add(refundItem);
        }
        netPartRefundReqDto.setRefundList(refundItemList);
        netPartRefundReqDto.setOlShopId(orderInfo.getOnlineStoreCode());
        netPartRefundReqDto.setShopId(storeInfo.getOutShopId());
        HemsBaseData baseData = hemsCommonClient.constructHemsBaseData(merCode, storeInfo.getPlatformCode(), storeInfo.getOnlineClientCode(), storeInfo.getSessionKey());
        hemsCommonClient.merchantPartRefund(netPartRefundReqDto, baseData);

        // 更新订单明细数据
        for (OrderPartRefundReqDto.RefundItem e : orderHandleReqDto.getRefundItemList()) {
            OrderDetail temp = mapDetail.get(e.getDetailId());
            OrderDetail odUpdate = new OrderDetail();
            odUpdate.setId(temp.getId());
            int refundCount = temp.getRefundCount();
            refundCount += e.getPartCount();
            odUpdate.setRefundCount(refundCount);
            if (refundCount == temp.getCommodityCount()) {
                odUpdate.setStatus(OrderDetailStatusEnum.REFUND.getCode());
            }
            orderDetailMapper.updateById(odUpdate);
            temp.setRefundCount(refundCount);
        }

        // 锁库存
        orderHandler.commodityStockDeductOther(orderInfo, orderDetailList, DsConstants.NEED_RETRY, "异常订单部分退款锁库");
        // WO

        // 修改订单
        orderHandler.modifyOrderToUnlock(userId, orderInfo, DsConstants.PART_REFUND, null);
    }

    @Override
    public Boolean newPartRefund(String merCode, String userId, NewOrderPartRefundReqDto orderHandleReqDto) {
        log.info("发起部分退款 orderNo:{} request:{}",orderHandleReqDto.getOrderNo(),orderHandleReqDto);
        OrderInfo orderInfo = orderBasicService.getOrderBaseInfo(orderHandleReqDto.getOrderNo());

        SysEmployeeResDTO employeePickOperator = baseInfoManager.getEmployeeInfoForErpNoEx(userId);
        HydeeEsSyncClientAsync.orderLogToES(DsConstants.INTEGER_ONE, merCode, orderInfo.getOrderNo(), orderInfo.getOrderState(), orderInfo.getErpState(),
                OrderLogEnum.PART_REFUND.getAction(), OrderLogEnum.getPartRefundInfo(orderHandleReqDto.getRefundItemList()), employeePickOperator);
        //校验当前订单状态
        partRefundOrderStatusCheck(orderInfo);
        //校验得剩余最后一个商品
        partRefundOrderGoodsCountCheck(orderInfo.getOrderNo(),orderHandleReqDto);
        merchantPartRefund(merCode, orderHandleReqDto, orderInfo);
        return Boolean.TRUE;
    }

    @Override
    public void orderRemind(NetOrderRemindNotifyMessage message) {
        //服务商模式消息校验并转换
        if (DsConstants.SUPPLIER_MER_CODE.equals(message.getGroupid())) {
            supplierMessageConvert.convertOtherMessage(message);
        }
        OrderInfo orderInfo = orderBasicService.getOrderBaseByThirdNoWithCheck(message.getEctype(), message.getOlorderno());
        //目前暂定b2c丢弃提醒消息
        if (DsConstants.B2C.equals(orderInfo.getServiceMode())) {
            log.info("b2c remind message,thirdOrderNo:{}", message.getOlorderno());
            return;
        }
        orderRemindWithTran(orderInfo, message);
        //订单更新异步消费
        messageProducerService.produceUpdateOrderMessage(Collections.singletonList(orderInfo.getOrderNo()), OrderUpdateCodeEnum.MESSAGE_REMIND.getCode());
        messageProducerService.produceSoundBroadCastMessage(orderInfo, SoundTypeEnum.URGE_ORDER);
    }

    @Transactional
    public void orderRemindWithTran(OrderInfo orderInfo, NetOrderRemindNotifyMessage message) {

        QueryWrapper<OrderRemind> queryWrapper = new QueryWrapper<>();
        queryWrapper.lambda().eq(OrderRemind::getOrderNo, orderInfo.getOrderNo()).eq(OrderRemind::getRemindId, message.getRemindid());
        List<OrderRemind> orderRemindList = orderRemindMapper.selectList(queryWrapper);
        if (orderRemindList != null && !orderRemindList.isEmpty()) {
            return;
        }
        OrderRemind orderRemind = new OrderRemind();
        orderRemind.setOrderNo(orderInfo.getOrderNo());
        orderRemind.setThirdOrderNo(message.getOlorderno());
        orderRemind.setRemindId(message.getRemindid());
        orderRemind.setRemindTime(message.getRemindtime());
        orderRemind.setStatus(DsConstants.INTEGER_ZERO);
        orderRemindMapper.insert(orderRemind);

        // 更新订单状态
        OrderInfo update = new OrderInfo();
        update.setRemindFlag(DsConstants.INTEGER_ONE);
        update.setOrderNo(orderInfo.getOrderNo());
        orderInfoMapper.updateOrder(update);
    }

    @Override
    public void merchantCancelInitiative(String merCode, String userId, OrderAuditHandleReq orderHandleReq) {
        OrderInfo orderInfo = orderBasicService.getOrderBaseWithCheck(orderHandleReq.getOrderNo());
        // 判断锁定状态
        if (OrderStateEnum.CLOSED.getCode().equals(orderInfo.getOrderState()) || OrderStateEnum.CANCEL.getCode().equals(orderInfo.getOrderState())) {
            throw ExceptionUtil.getWarnException(DsErrorType.STATE_CANCEL_CLOSED);
        }
        // 调用取消接口
        OnlineStoreInfoRspDto storeInfo = baseInfoService.getOnlineStoreInfo(orderInfo.getMerCode(), orderInfo.getThirdPlatformCode(), orderInfo.getClientCode(), orderInfo.getOnlineStoreCode());
        OrderCancelReq netAuditReq = new OrderCancelReq();
        netAuditReq.setOlOrderNo(orderInfo.getThirdOrderNo());
        netAuditReq.setReasonCode(orderHandleReq.getReasonCode());
        netAuditReq.setReason(orderHandleReq.getReason());
        netAuditReq.setOlShopId(orderInfo.getOnlineStoreCode());
        netAuditReq.setShopId(storeInfo.getOutShopId());
        HemsBaseData baseData = hemsCommonClient.constructHemsBaseData(merCode, storeInfo.getPlatformCode(), storeInfo.getOnlineClientCode(), storeInfo.getSessionKey());
        hemsCommonClient.orderCancel(netAuditReq, baseData);
        // 释放库存往前提
        commodityStockService.unLockStockNew(orderInfo, null, "商家取消（平安）释放库存", UnLockStockTypeEnum.ALL);
        //解锁erp库存
        erpBillService.undoErpForCancel(orderInfo, userId, "商家取消（平安）");

        // 修改订单
        String extra = "商家取消订单";
        orderHandler.modifyOrderToCancel(userId, orderInfo, DsConstants.CANCEL_ORDER, extra);
    }

    @Override
    public MatchingStoreRsp matchingStores(String merCode, OrderHandleReqDto req) {
        //返回数据
        MatchingStoreRsp resp = new MatchingStoreRsp();
        // 设置默认状态
        OrderLockFlagEnum lockFlagEnum = OrderLockFlagEnum.NOT_LOCK;
        OrderInfo orderCheck = orderBasicService.getOrderBaseWithCheck(req.getOrderNo());
        //获取平台名称
        List<ThirdPlatCodeNameRsp> plat = dsOnlineClientRepo.getThirdPlatFormCodeName();
        Map<String, String> platNameMap = plat.stream().collect(Collectors.toMap(ThirdPlatCodeNameRsp::getPlatformCode, ThirdPlatCodeNameRsp::getPlatformName, (value1, value2) -> {
            return value2;
        }));
        resp.setThirdPlatFormCode(orderCheck.getThirdPlatformCode());
        resp.setThirdPlatFormName(platNameMap.get(orderCheck.getThirdPlatformCode()));
        resp.setOnlineStoreCode(orderCheck.getOnlineStoreCode());
        resp.setOnlineStoreName(orderCheck.getOnlineStoreName());
        // 获取线上门店信息，包含对应的线下门店信息
        OnlineStoreInfoRspDto onlineStoreInfoRspDto = baseInfoService.getOnlineStoreInfoInner(orderCheck.getMerCode(), orderCheck.getThirdPlatformCode(), orderCheck.getClientCode(), orderCheck.getOnlineStoreCode());
//        log.info("saveOrder: getOnlineStoreInfo, thirdOrderNo:{}, onlineStoreInfo:{}", orderCheck.getThirdOrderNo(), JSON.toJSONString(onlineStoreInfoRspDto));
        if (onlineStoreInfoRspDto == null) {
            //线上门店不存在
            resp.setCode(DsErrorType.NO_STORE_FAIL_NO_ONLINESHOP.getCode());
            resp.setMsg(DsErrorType.NO_STORE_FAIL_NO_ONLINESHOP.getMsg());
            return resp;
        }

        if (orderCheck.getOnlineStoreCode() != null && !StringUtils.isEmpty(onlineStoreInfoRspDto.getOrganizationCode())) {
            //更新订单线上门店信息
            OrderInfo orderInfo = new OrderInfo();
            orderInfo.setOrderNo(orderCheck.getOrderNo());
            orderInfo.setOnlineStoreName(onlineStoreInfoRspDto.getOnlineStoreName());
            orderInfo.setOrganizationCode(onlineStoreInfoRspDto.getOrganizationCode());
            orderInfo.setOrganizationName(onlineStoreInfoRspDto.getOrganizationName());
            orderInfo.setLockFlag(lockFlagEnum.getCode());
            //更新接口不允许状态回退，需要单独更新
            orderInfoMapper.updateOrder(orderInfo);
            //匹配门店后修改订单状态为待接单
            orderInfo.setOrderState(OrderStateEnum.UN_TAKE.getCode());
            orderInfoMapper.updateOrderState(orderInfo);

            List<OrderDetail> orderDetailList = orderDetailMapper.selectList(new QueryWrapper<OrderDetail>().lambda().eq(OrderDetail::getOrderNo, orderCheck.getOrderNo()));
            if (!OrderLockFlagEnum.LOCK_SHOP_EX.getCode().equals(lockFlagEnum.getCode())) {
                //商品校验
                checkCommodity(orderDetailList, orderCheck, lockFlagEnum, orderInfo);
            }
            resp.setCode(DsErrorType.NO_STORE_SUCCESS.getCode());
            resp.setMsg(DsErrorType.NO_STORE_SUCCESS.getMsg());
            resp.setOrganizationCode(onlineStoreInfoRspDto.getOrganizationCode());
            resp.setOrganizationName(onlineStoreInfoRspDto.getOrganizationName());
            resp.setOnlineStoreName(onlineStoreInfoRspDto.getOnlineStoreName());
            resp.setOnlineStoreCode(onlineStoreInfoRspDto.getOnlineClientCode());
        } else {
            //线上门店存在，线下门店不存在（组织机构不存在）
            resp.setCode(DsErrorType.NO_STORE_FAIL_NO_ORGANIZATION.getCode());
            resp.setMsg(DsErrorType.NO_STORE_FAIL_NO_ORGANIZATION.getMsg());
            resp.setOnlineStoreName(onlineStoreInfoRspDto.getOnlineStoreName());
            resp.setOnlineStoreCode(onlineStoreInfoRspDto.getOnlineClientCode());
            return resp;
        }
        return resp;
    }

    @Override
    public MatchingStoreRsp matchingStoresByCode(String merCode, MatchingStoreReqDto req,String userId) {
        //返回数据
        MatchingStoreRsp resp = new MatchingStoreRsp();
        // 设置默认状态
        OrderLockFlagEnum lockFlagEnum = OrderLockFlagEnum.NOT_LOCK;
        OrderInfo orderCheck = orderBasicService.getOrderBaseWithCheck(req.getOrderNo());
        if (!orderCheck.getLockFlag().equals(OrderLockFlagEnum.LOCK_SHOP_EX.getCode())) {
            resp.setCode(DsErrorType.ORDER_NORMAL.getCode());
            resp.setMsg(DsErrorType.ORDER_NORMAL.getMsg());
            return resp;
        }

        //获取平台名称
        List<ThirdPlatCodeNameRsp> plat = dsOnlineClientRepo.getThirdPlatFormCodeName();
        Map<String, String> platNameMap = plat.stream().collect(Collectors.toMap(ThirdPlatCodeNameRsp::getPlatformCode, ThirdPlatCodeNameRsp::getPlatformName, (value1, value2)  -> value1));
        DsSynOnlineStoreQueryDTO dsSynOnlineStoreQueryDTO = new DsSynOnlineStoreQueryDTO();
        dsSynOnlineStoreQueryDTO.setPlatformCode(orderCheck.getThirdPlatformCode());
        dsSynOnlineStoreQueryDTO.setClientCode(orderCheck.getClientCode());
        dsSynOnlineStoreQueryDTO.setOrganizationCode(req.getOrganizationCode());
        dsSynOnlineStoreQueryDTO.setMerCode(orderCheck.getMerCode());
        DsOnlineStore openStatusByCode = baseInfoService.getOpenStatusByCode(dsSynOnlineStoreQueryDTO);
        if (openStatusByCode == null) {
            //线上门店不存在
            resp.setCode(DsErrorType.NO_STORE_FAIL_NO_ONLINESHOP.getCode());
            resp.setMsg(String.format("该门店未开通%s平台的店铺,请重新选择保存",PlatformCodeEnum.getNameByCode(orderCheck.getThirdPlatformCode())));
            return resp;
        }
        // 选择下单门店
        String onlineStoreCode = openStatusByCode.getOnlineStoreCode();
        resp.setThirdPlatFormCode(orderCheck.getThirdPlatformCode());
        resp.setThirdPlatFormName(platNameMap.get(orderCheck.getThirdPlatformCode()));
        resp.setOnlineStoreCode(onlineStoreCode);
        resp.setOnlineStoreName(orderCheck.getOnlineStoreName());
        // 根据选择的下单门店获取线上门店信息,包含对应的线下门店信息
        OnlineStoreInfoRspDto onlineStoreInfoRspDto = baseInfoService.getOnlineStoreInfoInner(orderCheck.getMerCode(), orderCheck.getThirdPlatformCode(), orderCheck.getClientCode(), onlineStoreCode);
        // 获取线上门店信息，包含对应的线下门店信息
        if (onlineStoreInfoRspDto == null) {
            //线上门店不存在
            resp.setCode(DsErrorType.NO_STORE_FAIL_NO_ONLINESHOP.getCode());
            resp.setMsg(String.format("该门店未开通%s平台的店铺,请重新选择保存",PlatformCodeEnum.getNameByCode(orderCheck.getThirdPlatformCode())));
            return resp;
        }
        if (StringUtils.isEmpty(onlineStoreInfoRspDto.getOrganizationCode())) {
            //线上门店存在，线下门店不存在（组织机构不存在）
            resp.setCode(DsErrorType.NO_STORE_FAIL_NO_ORGANIZATION.getCode());
            resp.setMsg(DsErrorType.NO_STORE_FAIL_NO_ORGANIZATION.getMsg());
            resp.setOnlineStoreName(onlineStoreInfoRspDto.getOnlineStoreName());
            resp.setOnlineStoreCode(onlineStoreInfoRspDto.getOnlineClientCode());
            return resp;
        }

        // 线上线下门店都存在 更新订单线上门店信息
        OrderInfo updateOrder = new OrderInfo();
        updateOrder.setOrderNo(orderCheck.getOrderNo());
        updateOrder.setOnlineStoreCode(onlineStoreCode);
        updateOrder.setOnlineStoreName(onlineStoreInfoRspDto.getOnlineStoreName());
        updateOrder.setOrganizationCode(onlineStoreInfoRspDto.getOrganizationCode());
        updateOrder.setOrganizationName(onlineStoreInfoRspDto.getOrganizationName());
        updateOrder.setSourceOnlineStoreCode(onlineStoreCode);
        updateOrder.setSourceOnlineStoreName(onlineStoreInfoRspDto.getOnlineStoreName());
        updateOrder.setSourceOrganizationCode(onlineStoreInfoRspDto.getOrganizationCode());
        updateOrder.setSourceOrganizationName(onlineStoreInfoRspDto.getOrganizationName());
        updateOrder.setLockFlag(lockFlagEnum.getCode());
        // 更新接口不允许状态回退，需要单独更新
        orderInfoMapper.updateOrder(updateOrder);
        // 匹配门店后修改订单状态为待接单
        if(orderCheck.getOrderState()<=OrderStateEnum.COMPLETED.getCode()){
            updateOrder.setOrderState(OrderStateEnum.UN_TAKE.getCode());
        }else {
            updateOrder.setOrderState(orderCheck.getOrderState());
            orderInfoMapper.updateOrderState(updateOrder);
            resp.setCode(DsErrorType.NO_STORE_SUCCESS.getCode());
            resp.setMsg(DsErrorType.NO_STORE_SUCCESS.getMsg());
            resp.setOrganizationCode(onlineStoreInfoRspDto.getOrganizationCode());
            resp.setOrganizationName(onlineStoreInfoRspDto.getOrganizationName());
            resp.setOnlineStoreName(onlineStoreInfoRspDto.getOnlineStoreName());
            resp.setOnlineStoreCode(onlineStoreCode);
            return resp;
        }
        orderInfoMapper.updateOrderState(updateOrder);


        OrderInfoAllDomain orderInfoAllDomain = orderInfoMapper.selectOrderInfoDetail(req.getOrderNo());
        SysEmployeeResDTO employeePickOperator = baseInfoManager.getEmployeeInfoForErpNoEx(userId);
        HydeeEsSyncClientAsync.orderLogToES(DsConstants.INTEGER_ONE, orderInfoAllDomain.getMerCode(), req.getOrderNo(), orderInfoAllDomain.getOrderState(), orderInfoAllDomain.getErpState(),
                OrderLogEnum.MATCHING_STORES.getAction(), OrderLogEnum.MATCHING_STORES.getInfo(), employeePickOperator);

        List<OrderDetail> orderDetailList = orderInfoAllDomain.getOrderDetailList();
        if(CollectionUtils.isEmpty(orderDetailList)){return null;}
        // 商品校验
        checkCommodityV2(orderDetailList, orderCheck, lockFlagEnum, orderInfoAllDomain);

        Long orderNo = orderInfoAllDomain.getOrderNo();

        List<OrderDetail> orderDetaiNormallList = orderDetailMapper.selectList(new QueryWrapper<OrderDetail>().lambda().eq(OrderDetail::getOrderNo, orderNo).eq(OrderDetail::getStatus, OrderDetailStatusEnum.NORMAL.getCode()));
        // 按照配置进行处理 如果配置了创建新订单自动锁库存 则进行扣减库存操作 1:创建新单自动锁库存  0：不锁库存
        // 预约单不锁库存; 转仓到 HEMS的不锁库存
        boolean isCheckErp = false;

        if (DsConstants.INTEGER_ONE.equals(onlineStoreInfoRspDto.getWhetherInventoryLocked())) {
            //返回是否扣商品库存成功  扣失败的不通知
            isCheckErp = orderHandlerManager.commodityStockDeductV2(orderInfoAllDomain, orderDetaiNormallList);
        }

        // 创建新订单成功后如果设置了自动打印小票 则打印小票
        if (DsConstants.INTEGER_ONE.equals(onlineStoreInfoRspDto.getAutoPrintReceipt())) {
            // 保存打印信息
            List<OrderLockInfo> lockList = orderLockInfoMapper.selectByOrderNo(orderNo);
            SpringUtil.getBean(OrderSaveHandlerManager.class).producePrintMessageContent(orderInfoAllDomain, lockList, onlineStoreInfoRspDto,DsConstants.INTEGER_ONE);
        }

        // net新订单通知，呼叫骑手
        // 流程优化：方法命名上没有呼叫骑手，理论上应该接单后才会呼叫骑手
        messageProducerService.produceNewOrderMessage(orderNo);

        // client新订单声音通知，websocket
        messageProducerService.produceSoundBroadCastMessage(orderInfoAllDomain, getSoundTypeOnSaveOrder(orderInfoAllDomain));

        if (OrderThirdStateEnum.WAIT_TAKE.getCode().equals(orderInfoAllDomain.getOffState().toString())
                && !DsConstants.INTEGER_ONE.equals(onlineStoreInfoRspDto.getAutoAcceptFlag())) {
            orderInfoMapper.updateAcceptTimeNull(orderNo);
            orderInfoAllDomain.setAcceptTime(null);
        }

        // 1015205 拣货、发货提醒与时效管控
        NotifyRedisHelper.addPickNotify(orderInfoAllDomain, onlineStoreInfoRspDto);

        // 判断订单状态是否为待接单且设置为自动接单，如果是待接单，则自动调用接单接口接单
        if (OrderStateEnum.UN_TAKE.getCode().equals(orderInfoAllDomain.getOrderState())
                && DsConstants.INTEGER_ONE.equals(onlineStoreInfoRspDto.getAutoAcceptFlag())) {
            //4.8.1 非 （处方单 & 配置了当前平台）  不自动接单
            if (!(DsConstants.INTEGER_ONE.equals(orderInfoAllDomain.getPrescriptionFlag()) && isContainsPlatform(orderInfoAllDomain.getThirdPlatformCode()))) {
                HemsBaseData baseData = hemsCommonClient.constructHemsBaseData(orderInfoAllDomain.getMerCode(), orderInfoAllDomain.getThirdPlatformCode(),
                        orderInfoAllDomain.getClientCode(), onlineStoreInfoRspDto.getSessionKey());
                OrderConformReq orderConfirmReq = new OrderConformReq();
                orderConfirmReq.setConfirmType(DsConstants.STRING_THREE);
                orderConfirmReq.setDeliveryId("");
                orderConfirmReq.setOlOrderNo(orderInfoAllDomain.getThirdOrderNo());
                orderConfirmReq.setOlShopId(onlineStoreCode);
                orderConfirmReq.setShopId(onlineStoreInfoRspDto.getOutShopId());
                //操作人 【京东健康必填】
                orderConfirmReq.setOperateMan("system");
                try {
                    hemsCommonClient.orderConfirm(orderConfirmReq, baseData);
                    OrderInfo updateOrderNew = new OrderInfo();
                    updateOrderNew.setOrderNo(orderNo);
                    updateOrderNew.setOrderState(OrderStateEnum.UN_PICK.getCode());
                    orderInfoMapper.updateOrder(updateOrderNew);
                    HydeeEsSyncClientAsync.orderLogToES(DsConstants.INTEGER_ONE, orderInfoAllDomain.getMerCode(), orderNo, orderInfoAllDomain.getOrderState(), orderInfoAllDomain.getErpState(),
                            OrderLogEnum.RECEIVE_ORDER.getAction(), OrderLogEnum.getReceveOrderInfo(orderNo, orderInfoAllDomain.getOrderState()), null);
                } catch (Exception e) {
                    log.error("matchingstore orderconfirm failed,orderNo:{}", orderNo, e);
                }
            }
        }

        // 暂无自动拣货功能 代码先放着
        try{
            //3分钟后通知自动拣货  硬编码万和521220   美团饿百  会进入队列
            if(merCodesConfig.needPick2platformFlag(orderInfoAllDomain.getMerCode())) {
                //美团平台订单且配送方式为美团平台配送，或饿百平台订单
                OrderDeliveryRecord orderDeliveryRecord = orderInfoAllDomain.getOrderDeliveryRecord();
                if(PlatformCodeEnum.E_BAI.getCode().equals(orderInfoAllDomain.getThirdPlatformCode()) ||
                        (PlatformCodeEnum.MEITUAN.getCode().equals(orderInfoAllDomain.getThirdPlatformCode()) && DeliveryTypeEnum.checkPlatformRider(orderDeliveryRecord.getDeliveryType()))){
                    JSONObject mqParam = new JSONObject();
                    mqParam.put("orderNo", orderNo);
                    BaseDelayMqMsg message = new BaseDelayMqMsg(MsgTypeServiceEnum.ORDER_PICK.getType(), mqParam);
                    delayProducer.sendMessage2RedayFirst(message);
                }
            }
        }catch (Exception e){
            log.error("pick2platform error,orderNo:{},e:{}", orderNo,e);
        }

        // 重新获取订单信息， 商品不存在 & 库存不足 ， 则通知商品中台   校验了erp库存的才返回这个
        if(isCheckErp){
            notifyComm(orderInfoAllDomain, orderNo);
        }

        resp.setCode(DsErrorType.NO_STORE_SUCCESS.getCode());
        resp.setMsg(DsErrorType.NO_STORE_SUCCESS.getMsg());
        resp.setOrganizationCode(onlineStoreInfoRspDto.getOrganizationCode());
        resp.setOrganizationName(onlineStoreInfoRspDto.getOrganizationName());
        resp.setOnlineStoreName(onlineStoreInfoRspDto.getOnlineStoreName());
        resp.setOnlineStoreCode(onlineStoreCode);
        return resp;
    }

    public boolean isContainsPlatform(String thirdPlatformCode) {
        //配置空  则默认全部
        if(StringUtils.isEmpty(prescriptionPlatList)){
            return true;
        }
        String[] codeList = prescriptionPlatList.split(",");
        for(int i=0; i<codeList.length; i++){
            if(codeList[i].equals(thirdPlatformCode)){
                return true;
            }
        }
        return false;
    }

    public SoundTypeEnum getSoundTypeOnSaveOrder(OrderInfo orderInfo) {
        SoundTypeEnum soundTypeEnum = SoundTypeEnum.NEW_ORDER;

        // 待审方订单
        if (DsConstants.INTEGER_ONE.equals(orderInfo.getIsPrescription()) && DsConstants.INTEGER_ZERO.equals(orderInfo.getPrescriptionStatus())) {
            // 设置订单语音消息播报类型为："待审方订单"
            soundTypeEnum = SoundTypeEnum.UN_CHECK_ORDER;
        }

        // 预约单
        if (ObOrderTypeEnum.APPOINTMENT_ORDER.getCode().equals(orderInfo.getAppointment())) {
            soundTypeEnum = SoundTypeEnum.APPOINTMENT_ORDER;
        }

        if (orderInfo.getLockFlag() > OrderLockFlagEnum.LOCK_EXCEPTION_MIN_CODE.getCode() &&
                orderInfo.getLockFlag() < OrderLockFlagEnum.LOCK_EXCEPTION_MAX_CODE.getCode()) {
            soundTypeEnum = SoundTypeEnum.EXCEPTION_ORDER;
        }

        return soundTypeEnum;
    }

    private void notifyComm(OrderInfoAllDomain orderInfoAllDomain,Long orderNo){
        try{
            OrderInfo orderInfo = orderInfoMapper.selectOrderInfo(orderNo);
            if(OrderLockFlagEnum.LOCK_LACK_STOCK.getCode().equals(orderInfo.getLockFlag())
                    || OrderLockFlagEnum.LOCK_COMMODITY_NOT_EXIST.getCode().equals(orderInfo.getLockFlag())
                    || OrderLockFlagEnum.LOCK_COMMODITY_NOT_SALL.getCode().equals(orderInfo.getLockFlag())
                    //商品不存在异常分类
                    || OrderLockFlagEnum.LOCK_ERP_CODE_NULL.getCode().equals(orderInfo.getLockFlag())
                    || OrderLockFlagEnum.LOCK_THIRD_COMMODITY_UNMATCH.getCode().equals(orderInfo.getLockFlag())
                    || OrderLockFlagEnum.LOCK_OFFLINE_COMMODITY_NOT_EXSIST.getCode().equals(orderInfo.getLockFlag())){
                //erp无法判断具体某个商品，传全量商品给商品中台
                List<OrderDetail> orderDetailList = orderInfoAllDomain.getOrderDetailList();
                if(!CollectionUtils.isEmpty(orderDetailList)){
                    String[] erpCodes = new String[orderDetailList.size()];
                    for(int i = 0;i< orderDetailList.size();i++){
                        erpCodes[i] = orderDetailList.get(i).getErpCode();
                    }
                    ResponseBase<PageDTO<CommodityStockInfoDto>> result =  middleMerchandiseClient.notifyStockEx(erpCodes,orderInfo.getMerCode(),orderInfo.getOrganizationCode());
                    log.info("saveOrderBehindnotifyComm orderNo:{},erpCodes:{},result:{}",orderNo, Arrays.toString(erpCodes), JSON.toJSONString(result));
                }
            }
        }catch (Exception e){
            log.error("saveOrderBehindnotifyComm error: orderNo:{},",orderNo,e);
        }
    }

    private void checkCommodity(List<OrderDetail> orderDetailList, OrderInfo orderCheck, OrderLockFlagEnum lockFlagEnum, OrderInfo orderInfo) {
        long emptyCodeNum = orderDetailList.stream().map(item -> org.apache.commons.lang3.StringUtils.isBlank(item.getErpCode())).count();
        if (emptyCodeNum > 0) {
            lockFlagEnum = OrderLockFlagEnum.LOCK_ERP_CODE_NULL;
            //更新订单异常信息
            orderInfo = new OrderInfo().setOrderNo(orderCheck.getOrderNo()).setLockFlag(lockFlagEnum.getCode());
            orderInfoMapper.updateOrder(orderInfo);
            return;
        }
        // 从商品中台获取商品信息
        Set<String> erpCodeSet = orderDetailList.stream().map(OrderDetail::getErpCode).filter(p -> p != null).collect(Collectors.toSet());
        //增加三方门店商品校验
        validThirdStoreCommodity(orderDetailList, orderCheck, erpCodeSet, orderInfo.getOrganizationCode());
        //查询商品信息
        ResponseBase<List<CommodityRspDto>> baseCommodity = merchandiseBaseService.batchGetCommodityInfoInner(orderCheck.getMerCode(), erpCodeSet);
        if (baseCommodity != null && baseCommodity.checkSuccess() && baseCommodity.getData() != null) {
            List<CommodityRspDto> commodityRspDtoList = baseCommodity.getData();
            //更新商品信息
            for (CommodityRspDto temp : commodityRspDtoList) {
                if (DsConstants.INTEGER_ZERO.equals(temp.getExist())) {
                    orderDetailMapper.updateByOrderNoErpCode(new OrderDetail().setOrderNo(orderCheck.getOrderNo()).setErpCode(temp.getErpCode()).setStatus((OrderDetailStatusEnum.NOT_EXIST.getCode())));
                    lockFlagEnum = OrderLockFlagEnum.LOCK_THIRD_COMMODITY_UNMATCH;
                } else {
                    orderDetailMapper.updateByOrderNoErpCode(new OrderDetail().setOrderNo(orderCheck.getOrderNo()).setErpCode(temp.getErpCode()).setMainPic(temp.getMainPic()).setManufacture(temp.getManufacture()).setApprovalNumber(temp.getApprovalNumber()).setBarCode(temp.getBarCode()).setStatus(OrderDetailStatusEnum.NORMAL.getCode()));
                }
            }
            //更新订单线上门店信息
            orderInfo = new OrderInfo().setOrderNo(orderCheck.getOrderNo()).setLockFlag(lockFlagEnum.getCode());
            orderInfoMapper.updateOrder(orderInfo);

            //订单更新异步消费
            messageProducerService.produceUpdateOrderMessage(Collections.singletonList(orderInfo.getOrderNo()), OrderUpdateCodeEnum.MATCH_STORE.getCode());
        } else {
            if (baseCommodity == null) {
                log.error("matchingStores: getCommodityInfoFromMiddle result is null, thirdOrderNo:{},", orderCheck.getOrderNo());
            } else {
                log.error("matchingStores: getCommodityInfoFromMiddle, thirdOrderNo:{}, code:{}, msg:{}", orderCheck.getOrderNo(), baseCommodity.getCode(), baseCommodity.getMsg());
            }
        }
    }

    private void checkCommodityV2(List<OrderDetail> orderDetailList, OrderInfo orderCheck, OrderLockFlagEnum lockFlagEnum, OrderInfo orderInfo) {
        long emptyCodeNum = orderDetailList.stream().filter(item -> org.apache.commons.lang3.StringUtils.isBlank(item.getErpCode())).count();
        if (emptyCodeNum > 0) {
            lockFlagEnum = OrderLockFlagEnum.LOCK_ERP_CODE_NULL;
            //更新订单异常信息
            orderInfo = new OrderInfo().setOrderNo(orderCheck.getOrderNo()).setLockFlag(lockFlagEnum.getCode());
            orderInfoMapper.updateOrder(orderInfo);
            return;
        }
        // 从商品中台获取商品信息
        Set<String> erpCodeSet = orderDetailList.stream().map(OrderDetail::getErpCode).filter(Objects::nonNull).collect(Collectors.toSet());
        //增加三方门店商品校验
        validThirdStoreCommodity(orderDetailList, orderCheck, erpCodeSet, orderInfo.getOrganizationCode());
        // 查询商品信息
        ResponseBase<List<CommodityRspDto>> baseCommodity = merchandiseBaseService.batchGetCommodityInfoInner(orderCheck.getMerCode(), erpCodeSet);
        if (baseCommodity != null && baseCommodity.checkSuccess() && baseCommodity.getData() != null) {
            List<CommodityRspDto> commodityRspDtoList = baseCommodity.getData();
            //更新商品信息
            for (CommodityRspDto temp : commodityRspDtoList) {
                if (DsConstants.INTEGER_ZERO.equals(temp.getExist())) {
                    orderDetailMapper.updateByOrderNoErpCode(new OrderDetail().setOrderNo(orderCheck.getOrderNo()).setErpCode(temp.getErpCode()).setStatus((OrderDetailStatusEnum.NOT_EXIST.getCode())));
                    lockFlagEnum = OrderLockFlagEnum.LOCK_THIRD_COMMODITY_UNMATCH;
                } else {
                    orderDetailMapper.updateByOrderNoErpCode(new OrderDetail().setOrderNo(orderCheck.getOrderNo()).setErpCode(temp.getErpCode()).setMainPic(temp.getMainPic()).setManufacture(temp.getManufacture()).setApprovalNumber(temp.getApprovalNumber()).setBarCode(temp.getBarCode()).setStatus(OrderDetailStatusEnum.NORMAL.getCode()));
                }
            }
            //更新订单线上门店信息
            orderInfo = new OrderInfo().setOrderNo(orderCheck.getOrderNo()).setLockFlag(lockFlagEnum.getCode());
            orderInfoMapper.updateOrder(orderInfo);

            //订单更新异步消费
            messageProducerService.produceUpdateOrderMessage(Collections.singletonList(orderInfo.getOrderNo()), OrderUpdateCodeEnum.MATCH_STORE.getCode());
        } else {
            if (baseCommodity == null) {
                log.error("matchingStores: getCommodityInfoFromMiddle result is null, thirdOrderNo:{},", orderCheck.getOrderNo());
            } else {
                log.error("matchingStores: getCommodityInfoFromMiddle, thirdOrderNo:{}, code:{}, msg:{}", orderCheck.getOrderNo(), baseCommodity.getCode(), baseCommodity.getMsg());
            }
        }


    }

    private void validThirdStoreCommodity(List<OrderDetail> orderDetailList, OrderInfo orderInfo, Set<String> erpCodeSet, String organizationCode) {
        //微商城订单直接返回
        if (PlatformCodeEnum.YD_JIA.getCode().equals(orderInfo.getThirdPlatformCode())) {
            return;
        }
        Map<String, OrderDetail> nonExistentOrderDetailMap = orderDetailList.stream().filter(item -> DsConstants.INTEGER_ZERO.equals(item.getGoodsType())).collect(Collectors.toMap(OrderDetail::getErpCode, item -> item, (a, b) -> a));
        //忽略赠品
//        if(!CollectionUtils.isEmpty(addOrderInfoReqDto.getOrdergiftlist())){
//            orderDetailMap.putAll(addOrderInfoReqDto.getOrdergiftlist().stream().collect(Collectors.toMap(AddOrderGiftReqDto::getGift_outer_iid)));
//        }
        // 先查三方门店的商品，不存在的商品调用异步创建商品接口
        ResponseBase<BatchThirdStoreCommodityRspDto> thirdStoreCommodity = merchandiseBaseService.batchGetThirdStoreCommodity(orderInfo.getMerCode(), orderInfo.getThirdPlatformCode(), orderInfo.getOnlineStoreCode(), organizationCode, new ArrayList<>(erpCodeSet));
        if (thirdStoreCommodity == null || !thirdStoreCommodity.checkSuccess()) {
            return;
        }
        if (thirdStoreCommodity.getData() != null && thirdStoreCommodity.getData().getRecords() != null) {
            thirdStoreCommodity.getData().getRecords().forEach(nonExistentOrderDetailMap::remove);
        }
        //如果查询全部返回，直接返回
        if (nonExistentOrderDetailMap.isEmpty()) {
            return;
        }
        //不存在商品信息集合
        List<CommodityCreateReqDto.EccProductInfo> unExistsCommoditys = new ArrayList<>();
        List<CommodityCreateReqDto.EccProductInfo> finalUnExistsCommoditys = unExistsCommoditys;
        nonExistentOrderDetailMap.forEach((erpCode, detail) -> {
            CommodityCreateReqDto.EccProductInfo productInfo = new CommodityCreateReqDto.EccProductInfo();
            productInfo.setErpCode(erpCode);
            productInfo.setTid(detail.getPlatformSkuId());
            productInfo.setName(detail.getCommodityName());
            productInfo.setUcp(detail.getBarCode());
            finalUnExistsCommoditys.add(productInfo);
        });
        unExistsCommoditys = finalUnExistsCommoditys.stream().distinct().collect(Collectors.toList());
        //增加异步创建商品调用
        if (!CollectionUtils.isEmpty(unExistsCommoditys)) {
            CommodityCreateReqDto commodityCreateReqDto = new CommodityCreateReqDto();
            commodityCreateReqDto.setMerCode(orderInfo.getMerCode());
            commodityCreateReqDto.setPlatformCode(orderInfo.getThirdPlatformCode());
            commodityCreateReqDto.setThirdOrderNo(orderInfo.getThirdOrderNo());
            commodityCreateReqDto.setClientCode(orderInfo.getClientCode());
            commodityCreateReqDto.setOnlineStoreCode(orderInfo.getOnlineStoreCode());
            commodityCreateReqDto.setOrgCode(organizationCode);
            commodityCreateReqDto.setUserName(DsConstants.O2O);
            commodityCreateReqDto.setEccProductInfos(unExistsCommoditys);
            //商品详情
            merchandiseBaseService.commodityCreateAsync(commodityCreateReqDto);
        }
    }

    @Override
    public List<String> getYdjOrderNoList(List<String> reqYdjOrderNoList) {
        return orderInfoMapper.getYdjOrderNoList(reqYdjOrderNoList);
    }

    @Override
    @DS(DsConstants.DB_ORDER_SLAVE_TWO)
    public OrderCount getOrderCountBySearch(OrderCountSearchReqDto reqDto, List<String> organizationCodeList) {
        OrderCount orderCount = new OrderCount();
        List<OrderCount> list = orderInfoMapper.getOrderCount(reqDto, organizationCodeList);
        if (!list.isEmpty()) {
            orderCount.AddOrderCountList(list);
        }
        return orderCount;
    }


    /**
     * 处理机构编码为空的情况
     *
     * @param reqDto
     * @return
     */
    @Override
    @DS(DsConstants.DB_ORDER_SLAVE_TWO)
    public OrderCount getOrderCountBySearch(OrderCountSearchReqDto reqDto) {
        QueryOrganizationReqDto queryOrganizationReqDto = QueryOrganizationReqDto.builder().merCode(reqDto.getMerCode()).userId(reqDto.getUserId()).province(reqDto.getProvince()).city(reqDto.getCity()).area(reqDto.getArea()).build();
        List<AvailablePCAORspDTO.Organization> organizationList = verifyService.getOrganizationsWithPCA(reqDto.getMerCode(), reqDto.getUserId(), queryOrganizationReqDto);

        if (CollectionUtils.isEmpty(organizationList)) {
            return new OrderCount();
        }
        List organizationCodes = organizationList.stream().map((organization -> organization.getOrganizationCode())).collect(Collectors.toList());
        return getOrderCountBySearch(reqDto, organizationCodes);
    }

    Sequence<OrderCountInfo> comp = Sequence.create(new Sequence<>(), (v) -> {
        v.initComp(OrderCountInfo::getTotalAmount, Comparator.comparing(OrderCountInfo::getTotalAmount));
        v.initComp(OrderCountInfo::getMerchantActualAmount, Comparator.comparing(OrderCountInfo::getMerchantActualAmount));
        v.initComp(OrderCountInfo::getCommodityBillTotal, Comparator.comparing(OrderCountInfo::getCommodityBillTotal));
        v.initComp(OrderCountInfo::getGrossProfitAmount, Comparator.comparing(OrderCountInfo::getGrossProfitAmount));
        v.initComp(OrderCountInfo::getGrossProfitPercent, Comparator.comparing(OrderCountInfo::convertGrossProfitPercent));
        v.initComp(OrderCountInfo::getGrossProfitAmountComprehensive, Comparator.comparing(OrderCountInfo::getGrossProfitAmountComprehensive));
        v.initComp(OrderCountInfo::getGrossProfitPercentComprehensive, Comparator.comparing(OrderCountInfo::convertGrossProfitPercentComprehensive));
    });

    @Override
    @DS(DsConstants.DB_ORDER_SLAVE_TWO)
    public IPage<OrderCountInfo> getOrderCountInfoBySearch(OrderCountSearchReqDto reqDto, List<StoreResDTO> storeResDTOs) {
        Page<OrderSearchCountRsp> page = new Page<>(reqDto.getCurrentPage(), reqDto.getPageSize());
        List<String> organizationCodeList = storeResDTOs.stream().map((s) -> s.getStCode()).collect(Collectors.toList());
        IPage<OrderCountInfo> orderCountInfo = orderInfoMapper.getOrderCountInfo(page, reqDto, organizationCodeList);
        Map<String, StoreResDTO> collect = storeResDTOs.stream().collect(Collectors.toMap(p -> p.getStCode(), p -> p, (v1, v2) -> v1));

        if (!orderCountInfo.getRecords().isEmpty()) {
            List<OrderCountInfo> infoList = orderCountInfo.getRecords().stream().map((info) -> {
                StoreResDTO storeResDTO = collect.get(info.getOrganizationCode());
                info.setProvince(storeResDTO.getProvince());
                info.setCity(storeResDTO.getCity());
                info.setArea(storeResDTO.getArea());
                // 重新计算各类业务
                info.calculateAll();
                return info;
            }).collect(Collectors.toList());
            ;
            //选择的排序字段和需要重新计算的字段不同，不用重新排序
            if (comp.getSequence(reqDto.getSortField(), reqDto.getSortSequence()) != null) {
                infoList = infoList.stream().sorted(comp.getSequence(reqDto.getSortField(), reqDto.getSortSequence())).collect(Collectors.toList());
            }
            orderCountInfo.setRecords(infoList);
            /*for(OrderCountInfo info:orderCountInfo.getRecords()){
                info.setTotalAmount(info.getTotalAmount().add(info.getMerchantDeliveryFee()).add(info.getMerchantPackFee()));
                info.setMerchantActualAmount(info.getTotalAmount().subtract(info.getBrokerageAmount())
                .subtract(info.getMerchantDiscountSum()).subtract(info.getDiscountFeeDtl()));
            }*/
        }
        return orderCountInfo;
    }

    /**
     * // 省、市、区 有一项不为空, 机构编码为空
     *
     * @param reqDto
     * @return
     */
    @Override
    @DS(DsConstants.DB_ORDER_SLAVE_TWO)
    public IPage<OrderCountInfo> getOrderCountInfoBySearch(OrderCountSearchReqDto reqDto) {
        QueryOrganizationReqDto queryOrganizationReqDto = QueryOrganizationReqDto.builder().merCode(reqDto.getMerCode()).userId(reqDto.getUserId()).province(reqDto.getProvince()).city(reqDto.getCity()).area(reqDto.getArea()).build();
        List<AvailablePCAORspDTO.Organization> organizationList = verifyService.getOrganizationsWithPCA(reqDto.getMerCode(), reqDto.getUserId(), queryOrganizationReqDto);
        if (CollectionUtils.isEmpty(organizationList)) {
            return new Page<OrderCountInfo>();
        }
        List<String> organizationCodeList = organizationList.stream().map((org) -> org.getOrganizationCode()).collect(Collectors.toList());
        Page<OrderSearchCountRsp> page = new Page<>(reqDto.getCurrentPage(), reqDto.getPageSize());
        IPage<OrderCountInfo> orderCountInfo = orderInfoMapper.getOrderCountInfo(page, reqDto, organizationCodeList);
        Map<String, AvailablePCAORspDTO.Organization> collect = organizationList.stream().collect(Collectors.toMap(p -> p.getOrganizationCode(), p -> p, (p1, p2) -> p1));

        if (!CollectionUtils.isEmpty(orderCountInfo.getRecords())) {
            List<OrderCountInfo> infoList = orderCountInfo.getRecords().stream().map((info) -> {
                AvailablePCAORspDTO.Organization storeinfo = collect.get(info.getOrganizationCode());
                if (Objects.nonNull(storeinfo)) {
                    info.setProvince(storeinfo.getProvince());
                    info.setCity(storeinfo.getCity());
                    info.setArea(storeinfo.getArea());
                }
                // 重新计算各类业务
                info.calculateAll();
                return info;
            }).collect(Collectors.toList());
            ;

            //选择的排序字段和需要重新计算的字段不同，不用重新排序
            if (comp.getSequence(reqDto.getSortField(), reqDto.getSortSequence()) != null) {
                infoList = infoList.stream().sorted(comp.getSequence(reqDto.getSortField(), reqDto.getSortSequence())).collect(Collectors.toList());
            }
            orderCountInfo.setRecords(infoList);
        }
        return orderCountInfo;
    }

    @Override
    public List<AvailablePCAORspDTO.Organization> getOrganizationList(OrderCountSearchReqDto reqDto) {
        List<AvailablePCAORspDTO.Organization> organizationList = new ArrayList();
        AvailablePCAORspDTO pca = verifyService.getPCAO(reqDto.getMerCode(), reqDto.getUserId());
        //只有省不为空
        if (reqDto.onlyProvince()) {
            for (int i = 0; i < reqDto.getProvince().size(); i++) {
                Iterator<AvailablePCAORspDTO.Province> iterator = pca.getProvinces().iterator();
                while (iterator.hasNext()) {
                    AvailablePCAORspDTO.Province province = iterator.next();
                    if (province.getName().equals(reqDto.getProvince().get(i))) {
                        List<AvailablePCAORspDTO.Organization> collectOrganization = province.getCitys().stream().flatMap((city -> city.getAreas().stream())).flatMap((area -> area.getOrganizations().stream())).collect(Collectors.toList());
                        organizationList.addAll(collectOrganization);
                    }
                }
            }
        }
        // 只有地区是空
        else if (reqDto.onlyAreaNull()) {
            List<AvailablePCAORspDTO.City> collectCity = pca.getProvinces().stream().flatMap((province -> province.getCitys().stream())).collect(Collectors.toList());
            reqDto.getCity().forEach((city) -> {
                Iterator<AvailablePCAORspDTO.City> iterator = collectCity.iterator();
                while (iterator.hasNext()) {
                    AvailablePCAORspDTO.City next = iterator.next();
                    if (next.getName().equals(city)) {
                        List<AvailablePCAORspDTO.Organization> collectOrganization = next.getAreas().stream().flatMap((area -> area.getOrganizations().stream())).collect(Collectors.toList());
                        organizationList.addAll(collectOrganization);
                    }
                }
            });
        }
        // 区域不为空
        else if (reqDto.areaNotNull()) {
            Set<AvailablePCAORspDTO.Area> collectAreas = pca.getProvinces().stream().flatMap((province -> province.getCitys().stream())).flatMap((city -> city.getAreas().stream())).collect(Collectors.toSet());

            reqDto.getArea().forEach(area -> {
                Iterator<AvailablePCAORspDTO.Area> iterator = collectAreas.iterator();
                while (iterator.hasNext()) {
                    AvailablePCAORspDTO.Area next = iterator.next();
                    if (next.getName().equals(area)) {
                        organizationList.addAll(next.getOrganizations().stream().collect(Collectors.toList()));
                    }
                }
            });
        } else {
            log.error("getOrderCountBySearch fatal error, OrderCountSearchReqDto {}", JSON.toJSONString(reqDto));
        }
        return organizationList;
    }

    @Override
    public OrderInfo getOrderInfoByThirdOrderNo(String thirdOrderNo) {
        return orderInfoMapper.selectOrderByThirdOrderNo(thirdOrderNo);
    }

    @Override
    public OrderInfo getSimpleOrderDetail(Long orderNo) {
        return orderInfoMapper.selectOrderInfo(orderNo);
    }

    @Override
    @DS(DsConstants.DB_ORDER_SLAVE)
    public List<OrderCountInfo> uploadOrderCountInfoBySearch(Page<OrderCountSearchReqDto> page, OrderCountSearchReqDto reqDto, List<StoreResDTO> storeResDTOs) {
        List<String> organizationCodeList = storeResDTOs.stream().map((s) -> s.getStCode()).collect(Collectors.toList());
        Map<String, StoreResDTO> collect = storeResDTOs.stream().collect(Collectors.toMap(p -> p.getStCode(), p -> p, (v1, v2) -> v1));
        List<OrderCountInfo> list = orderInfoMapper.uploadOrderCountInfo(page, reqDto, organizationCodeList);

        if (!list.isEmpty()) {
            List<OrderCountInfo> infoList = list.stream().map((info) -> {
                StoreResDTO storeResDTO = collect.get(info.getOrganizationCode());
                info.setProvince(storeResDTO.getProvince());
                info.setCity(storeResDTO.getCity());
                info.setArea(storeResDTO.getArea());
                // 重新计算各类业务
                info.calculateAll();
                return info;
            }).collect(Collectors.toList());
            ;
            //选择的排序字段和需要重新计算的字段不同，不用重新排序
            if (comp.getSequence(reqDto.getSortField(), reqDto.getSortSequence()) != null) {
                infoList = infoList.stream().sorted(comp.getSequence(reqDto.getSortField(), reqDto.getSortSequence())).collect(Collectors.toList());
            }
            list = infoList;
        }
        return list;
    }

    @Override
    public OrderRealTimeCountRsp getOrderAllCount(OrderSearchMoreStatusDto searchOrderPageReqDto, String userId) {
        VerifyRspDto verifyDto = verifyService.verifyTimeAndOrganization(VerifyReqDto.builder().merCode(searchOrderPageReqDto.getMerCode()).organizationCode(searchOrderPageReqDto.getOrganizationCode()).userId(userId).storeFlag(0).verifyFlag(1).beginTime(searchOrderPageReqDto.getBeginTime()).endTime(searchOrderPageReqDto.getEndTime()).build());
        searchOrderPageReqDto.setBeginTime(verifyDto.getBeginTime());
        searchOrderPageReqDto.setEndTime(verifyDto.getEndTime());
        if (searchOrderPageReqDto.getOrderState() != null && searchOrderPageReqDto.getOrderState().getOrderStateList().isEmpty()) {
            searchOrderPageReqDto.setOrderState(null);
        }
        List<OrderCountDomain> countList = orderInfoMapper.getOrderAllCount(searchOrderPageReqDto, verifyDto.getOrganizatioinList());
        OrderRealTimeCountRsp rsp = new OrderRealTimeCountRsp();
        for (OrderCountDomain countDomain : countList) {
            if (countDomain.getStatus().equals(OrderStateEnum.UN_TAKE.getCode())) {
                rsp.setUnTakeNum(rsp.getUnTakeNum() + countDomain.getNum());
            }
            //待审方不作为单独状态
//            if(countDomain.getStatus().equals(OrderStateEnum.UN_CHECK.getCode())){
//                rsp.setUnCheckNum(rsp.getUnCheckNum()+countDomain.getNum());
//            }
            if (countDomain.getStatus().equals(OrderStateEnum.UN_DELIVERY.getCode())) {
                rsp.setUnDeliveryNum(rsp.getUnDeliveryNum() + countDomain.getNum());
            }
            if (countDomain.getStatus().equals(OrderStateEnum.UN_PICK.getCode())) {
                rsp.setUnPickNum(rsp.getUnPickNum() + countDomain.getNum());
            }
            if (countDomain.getStatus().equals(OrderStateEnum.POSTING.getCode())) {
                rsp.setUnReceiveNum(rsp.getUnReceiveNum() + countDomain.getNum());
            }
        }
        return rsp;
    }

    @Override
    public OrderInfo getOrderByOnlineStore(String merCode, String onlineStoreCode) {
        return orderInfoMapper.getOrderByOnlineStore(merCode, onlineStoreCode);
    }

    @Override
    @OrderLock
    public void updateOrderStore(String merCode, String userId, UpdateOrderSysStoreReqDto req, String orderNo) {

        SysEmployeeResDTO employeePickOperator = baseInfoManager.getEmployeeInfoForErpNoEx(userId);
        /** 1. 修改OMS订单的线上门店和线下门店为更换后的门店，并重新按新门店的配置初始化订单*/
        OrderInfo orderInfo = orderBasicService.getOrderBaseWithCheck(req.getOrderNo());
        // ID1014723 检查是否已呼叫骑手,已呼叫不允许修改门店
        orderBasicService.whetherCalledRider(orderInfo);
        String oldOrCode = orderInfo.getOrganizationCode();

        OnlineStoreInfoRspDto onlineStoreInfoRspDto = baseInfoService.getOnlineStoreInfoInner(orderInfo.getMerCode(), orderInfo.getThirdPlatformCode(), orderInfo.getClientCode(), req.getOnlineStoreCode());
        log.info("updateOrderStore: getOnlineStoreInfo, thirdOrderNo:{}, onlineStoreInfo:{}", orderInfo.getOrderNo(), JSON.toJSONString(onlineStoreInfoRspDto));

        //解锁库存提前
        undoOrder(orderInfo);
        //执行门店修改并锁库存  含事务
        OrderInfo orderUpdate = updateStore(orderInfo, req, onlineStoreInfoRspDto, merCode, employeePickOperator);
        //订单更新异步消费
        messageProducerService.produceUpdateOrderMessage(Collections.singletonList(orderInfo.getOrderNo()), OrderUpdateCodeEnum.UPDATE_STORE.getCode(), oldOrCode);

        HydeeEsSyncClientAsync.orderLogToES(DsConstants.INTEGER_ONE, merCode, orderInfo.getOrderNo(), orderInfo.getOrderState(), orderInfo.getErpState(), OrderLogEnum.MODIFY_ORDER_STORE.getAction(), OrderLogEnum.getModifyOrderSTtoreInfo(orderInfo.getOrganizationName(), req.getOrganizationName()), employeePickOperator);

        //20211207  再次加上调用微商城接口
        notifyWscStoreChange(merCode, orderInfo, req);
    }

    //非事务方式执行解锁库存

    private void notifyWscStoreChange(String merCode, OrderInfo orderInfo, UpdateOrderSysStoreReqDto req) {
        // 5、修改微商城订单的门店为更换后的门店（微商城提供修改门店接口）
        // 【ID1013941】【Bug转需求】拣货时修改发货门店失败后，不要锁定被改门店商品库存
        try {
            if (!PlatformCodeEnum.YD_JIA.getCode().equals(orderInfo.getThirdPlatformCode())) {
                return;
            }
            OrderChangeStoreDTO orderChangeStoreDTO = new OrderChangeStoreDTO();
            orderChangeStoreDTO.setMerCode(merCode);
            orderChangeStoreDTO.setOrderId(Long.valueOf(orderInfo.getThirdOrderNo()));
            orderChangeStoreDTO.setNewStCode(req.getOrganizationCode());
            ResponseBase<Boolean> base = middleOrderClient.orderChangeStore(orderChangeStoreDTO);
            HydeeEsSyncClientAsync.sendLogToES(ServiceTypeEnum.YDJIA_MERCHANT_MANAGER, OperateEnum.OrderChangeStore, orderInfo, orderChangeStoreDTO, base, Boolean.class);
            log.info("orderChangeStore updateMiddleOrderStore, requestParam:{},result:{}", JSON.toJSON(orderChangeStoreDTO), JSON.toJSON(base));
        } catch (Exception e) {
            log.error("notifyWscStoreChange error,orderNo:{}", orderInfo.getOrderNo(), e);
        }
    }

    public void undoOrder(OrderInfo orderInfo) {
        commodityStockService.unLockStockNew(orderInfo, null, "修改订单门店信息释放库存", UnLockStockTypeEnum.ALL);
//        localStockService.unlockStock(orderInfo, null, StockSourceEnum.CHECKOUT_ORG_UNLOCK);
        if (!StringUtils.isEmpty(orderInfo.getErpAdjustNo())) {
            // 解锁原ERP库存
            try {
                erpBillService.undoErpForCancel(orderInfo, "", "修改订单门店信息");
                //解锁成功后，将订单拣货单号置为空,还原待锁定状态
                OrderInfo updatesOrder = new OrderInfo();
                updatesOrder.setOrderNo(orderInfo.getOrderNo());
                updatesOrder.setErpAdjustNo("");
                updatesOrder.setErpState(ErpStateEnum.WAIT_PICK.getCode());
                orderInfoMapper.updateOrder(updatesOrder);
            } catch (WarnException ex) {
                if (DsErrorType.ERP_UNLOCK_STOCK_ERROR.getCode().equals(ex.getCode()) && ex.getMessage().contains("是否存在或者已经作废")) {
                    log.error("call erp undoErpForCancel error orderNo:{},code:{},msg:{}", orderInfo.getOrderNo(), ex.getCode(), ex.getMessage());
                }
            }
        }
    }

    @Transactional(rollbackFor = Exception.class)
    public OrderInfo updateStore(OrderInfo orderInfo, UpdateOrderSysStoreReqDto req, OnlineStoreInfoRspDto onlineStoreInfoRspDto, String merCode, SysEmployeeResDTO employeePickOperator) {
        OrderInfo updateOrder = new OrderInfo();
        updateOrder.setOrderNo(orderInfo.getOrderNo());
        updateOrder.setOrganizationCode(req.getOrganizationCode());
        updateOrder.setOrganizationName(req.getOrganizationName());
        int num = orderInfoMapper.updateOrder(updateOrder);

        OrderInfo orderUpdate = null;
        if (num > 0) {
            /** 3.如果有锁库存，解锁原门店库存，锁新门店库存 */
            List<OrderDetail> orderDetailList = orderBasicService.getOrderDetailListWithCheck(orderInfo.getOrderNo());
//            OrderInfo getOrderInfo = orderBasicService.getOrderBaseWithCheck(req.getOrderNo());
            //拣货单号置空后再查询最新的单据
            orderUpdate = orderBasicService.getOrderBaseWithCheck(orderInfo.getOrderNo());
            //存在脏读，此处正常
            if (!StringUtils.isEmpty(orderInfo.getErpAdjustNo())) {
                // 解锁原ERP库存提前执行
                try {
                    //锁新门店库存
                    lockStockProxy.switchStoreLockStock(onlineStoreInfoRspDto, null, orderUpdate, orderDetailList);
                } catch (WarnException e) {
                    if (DsErrorType.MERCHANDISE_STOCK_DEDUCT_ERROR.getCode().equals(e.getCode())) {
                        String msg = String.format("%s门店商品库存不足，不可修改，请更换其它门店！", req.getOnlineStoreName());
                        HydeeEsSyncClientAsync.orderLogToES(DsConstants.INTEGER_ZERO, merCode, orderUpdate.getOrderNo(), orderUpdate.getOrderState(), orderUpdate.getErpState(), OrderLogEnum.MODIFY_ORDER_STORE.getAction(), msg, employeePickOperator);
                        throw ExceptionUtil.getWarnException(DsErrorType.MERCHANDISE_STOCK_DEDUCT_ERROR.getCode(), msg);
                    } else if (DsErrorType.ERP_LOCK_STOCK_ERROR.getCode().equals(e.getCode())) {
                        HydeeEsSyncClientAsync.orderLogToES(DsConstants.INTEGER_ZERO, merCode, orderUpdate.getOrderNo(), orderUpdate.getOrderState(), orderUpdate.getErpState(), OrderLogEnum.MODIFY_ORDER_STORE.getAction(), "换新门店失败:" + e.getMessage(), employeePickOperator);
                        throw ExceptionUtil.getWarnException(e.getCode(), e.getMessage());
                    } else {
                        throw e;
                    }
                }
            } else {
                //锁新门店库存
                lockStockProxy.switchStoreLockStock(onlineStoreInfoRspDto, null, orderUpdate, orderDetailList);
                //拣货单号置空后再查询最新的单据
                orderUpdate = orderBasicService.getOrderBaseWithCheck(orderInfo.getOrderNo());
            }

            //增加修改门店后骑手呼叫校验逻辑
            if (orderUpdate.isUpdateStore()) {
                updateStoreDeliveryMatchManager.updateStoreDeliveryProcess(orderUpdate);
            }
            //异常订单换门店后订单要修改为正常，订单明细状态也修改为正常
            if (orderUpdate.getLockFlag() >= OrderLockFlagEnum.LOCK_EXCEPTION_MIN_CODE.getCode()) {
                OrderInfo update = new OrderInfo();
                update.setOrderNo(orderInfo.getOrderNo());
                update.setLockFlag(OrderLockFlagEnum.NOT_LOCK.getCode());
                orderInfoMapper.updateOrder(update);
            }
            for (OrderDetail orderDetail : orderDetailList) {
                if (OrderDetailStatusEnum.OUT_OF_STOCK.getCode().equals(orderDetail.getStatus()) || OrderDetailStatusEnum.NOT_EXIST.getCode().equals(orderDetail.getStatus())) {
                    OrderDetail updateOrderDetail = new OrderDetail();
                    updateOrderDetail.setId(orderDetail.getId());
                    updateOrderDetail.setStatus(OrderDetailStatusEnum.NORMAL.getCode());
                    orderDetailMapper.updateById(updateOrderDetail);
                }
            }
        }
        return updateOrder;
    }

    @Override
    public void updateOrder(OrderInfo updateOrder) {
        if (updateOrder == null) {
            return;
        }
        orderInfoMapper.updateOrder(updateOrder);
    }

    @Override
    public void updateOrderSellerRemark(OrderChangeSellerRemarkReqDto remarkReqDto) {
        OrderInfo orderInfo = orderInfoMapper.selectBaseInfoByOrderNo(remarkReqDto.getOrderNo());
        if (orderInfo == null) {
            throw ExceptionUtil.getWarnException(DsErrorType.ORDER_NOT_EXIST);
        }
        OrderInfo updateOrder = new OrderInfo();
        updateOrder.setOrderNo(orderInfo.getOrderNo());
        updateOrder.setExtendInfo(setSellerRemarkDate(orderInfo.getExtendInfo()));
        updateOrder.setSellerRemark(remarkReqDto.getSellerRemark());
        this.updateOrder(updateOrder);
    }

    private static String setSellerRemarkDate(String extendInfo) {
        OrderInfoExtendInfo orderInfoExtendInfo;
        if(org.apache.commons.lang3.StringUtils.isNotBlank(extendInfo)){
            orderInfoExtendInfo = JSONObject.parseObject(extendInfo, OrderInfoExtendInfo.class);
        }else {
            orderInfoExtendInfo = new OrderInfoExtendInfo();
        }
        orderInfoExtendInfo.setSellerRemarkTime(new Date());
        extendInfo = JSONObject.toJSONString(orderInfoExtendInfo);
        return extendInfo;
    }

    private List<ErpOrderInfoModel> queryErpOrderRecodFromEs(MemberRecordsReqDto reqDto, IPage<MemberRecordsRspDto> onlineOrderData) {
        String beginTime = reqDto.getBeginTime();
        String endTime = reqDto.getEndTime();
        if (StringUtils.isEmpty(beginTime)) {
            return Collections.emptyList();
        }
        Date startDate = DateUtil.parseStrToDate(beginTime, DateUtil.DATE_TIME_FORMAT_YYYY_MM_DD_HH_MI_SS);
        Date endDate = new Date();
        if (!StringUtils.isEmpty(endTime)) {
            endDate = DateUtil.parseStrToDate(endTime, DateUtil.DATE_TIME_FORMAT_YYYY_MM_DD_HH_MI_SS);
        }
        String beginDateStr = DateUtil.parseDateToStr(startDate, DateUtil.DATE_FORMAT_YYYY_MM);
        String endDateStr = DateUtil.parseDateToStr(endDate, DateUtil.DATE_FORMAT_YYYY_MM);
        // 获取月份间隔
        List<String> yearMonthList = DateUtil.getMonthListOfDate(beginDateStr, endDateStr);
        List<ErpOrderInfoModel> dataList = new ArrayList<>();
        String erpOrderInfoIndex = null;
        SearchRequest searchRequest = null;
        PageDTO<ErpOrderInfoModel> pageResult = null;
        for (String yearMonthStr : yearMonthList) {
            // 获取匹配的索引
            erpOrderInfoIndex = ESUtils.getErpOrderInfoIndexByTime(DateUtil.parseStrToDate(yearMonthStr, DateUtil.DATE_FORMAT_YYYY_MM));
            if (!ESUtils.isExistsIndex(hydeeRestHighLevelClient, erpOrderInfoIndex)) {
                continue;
            }
            // 构建检索条件
            searchRequest = assemblyRequest(erpOrderInfoIndex, startDate, endDate, reqDto);
            try {
                pageResult = elasticService.queryModelFromEs(searchRequest, ErpOrderInfoModel.class);
                log.info("queryErpOrderRecodFromEs info,request source:{},result:{}", JSONObject.toJSONString(searchRequest.source()), JSONObject.toJSONString(pageResult));
            } catch (IOException e) {
                log.error("queryErpOrderRecodFromEs error,request source:{},cause:{}", JSONObject.toJSONString(searchRequest.source()), e);
            }
            if (null == pageResult || CollectionUtils.isEmpty(pageResult.getData())) {
                continue;
            }
            onlineOrderData.setTotal(onlineOrderData.getTotal() + Long.valueOf(pageResult.getTotalCount()));
            dataList.addAll(pageResult.getData());
        }
        return dataList;
    }

    private SearchRequest assemblyRequest(String erpOrderInfoIndex, Date startDate, Date endDate, MemberRecordsReqDto reqDto) {
//        SearchRequest request = new SearchRequest(erpOrderInfoIndex);
        SearchRequest request = EsUtils.searchIndex(erpOrderInfoIndex);
        request.source(buildSearchSource(startDate, endDate, reqDto));
        return request;
    }

    private SearchSourceBuilder buildSearchSource(Date startDate, Date endDate, MemberRecordsReqDto reqDto) {
        SearchSourceBuilder searchSourceBuilder = new SearchSourceBuilder();
        // 设置分页查询
        searchSourceBuilder.size(reqDto.getCurrentPage() * reqDto.getPageSize());
        searchSourceBuilder.from(0);
        // 设置排序
        searchSourceBuilder.sort("tradeTime", SortOrder.DESC);
        // 搜索过滤条件
        searchSourceBuilder.query(buildBoolQuery(startDate, endDate, reqDto));
        return searchSourceBuilder;
    }

    private QueryBuilder buildBoolQuery(Date startDate, Date endDate, MemberRecordsReqDto reqDto) {
        BoolQueryBuilder boolBuilder = QueryBuilders.boolQuery();
        // 门店编码
        if (!StringUtils.isEmpty(reqDto.getMerCode())) {
            boolBuilder.filter(QueryBuilders.termQuery("merCode", reqDto.getMerCode()));
        }
        // 会员卡号
        if (!StringUtils.isEmpty(reqDto.getMemberNo())) {
            boolBuilder.filter(QueryBuilders.termQuery("memberCardNo", reqDto.getMemberNo()));
        }
        // 交易时间
        boolBuilder.filter(QueryBuilders.rangeQuery("tradeTime").from(startDate.getTime() / 1000).to(endDate.getTime() / 1000));

        return boolBuilder;
    }

    private void memberRecordsBuild(Page<MemberRecordsRspDto> data, Map<Long, List<OrderDetail>> orderDetailMap, Map<Long, List<OrderPayInfo>> orderPayInfoMap, Map<Long, List<ErpOrderDetail>> erpOrderDetailMap, Map<Long, List<RefundDetail>> refundDetailMap, int i) {
        Long orderNo = data.getRecords().get(i).getOrderNo();
        List<ErpOrderDetail> erpOrderDetailDataList = erpOrderDetailMap.get(orderNo);
        List<OrderDetail> orderDetailDataList = orderDetailMap.get(orderNo);
        List<RefundDetail> refundDetailDataList = refundDetailMap.get(orderNo);
        // 线下单明细
        if (!CollectionUtils.isEmpty(erpOrderDetailDataList)) {
            BigDecimal totalAmount = new BigDecimal(0);
            for (ErpOrderDetail erpOrderDetail2 : erpOrderDetailDataList) {
                totalAmount = totalAmount.add(erpOrderDetail2.getTotalAmount());
            }
            String commodityNames = erpOrderDetailDataList.stream().map(ErpOrderDetail::getCommodityName).collect(Collectors.joining(","));
            List<CommodityInfoRspDto> commodityInfoList = erpOrderDetailDataList.stream().map(erpOrderDetail -> new CommodityInfoRspDto(erpOrderDetail.getCommodityName(), erpOrderDetail.getCommodityCount() < 0.0 ? -erpOrderDetail.getCommodityCount() : erpOrderDetail.getCommodityCount(), Optional.ofNullable(erpOrderDetail.getChinaMedicineFlag()).orElse(DsConstants.INTEGER_ZERO), erpOrderDetail.getPrice(), null, erpOrderDetail.getApprovalNumber(), erpOrderDetail.getSpecDesc())).collect(Collectors.toList());
            data.getRecords().get(i).setTotalAmount(totalAmount);
            data.getRecords().get(i).setCommodityNames(commodityNames);
            data.getRecords().get(i).setCommodityInfo(commodityInfoList);

        } else if (!CollectionUtils.isEmpty(orderDetailDataList)) {
            // 线上单明细
            BigDecimal totalAmount = new BigDecimal(0);
            List<OrderPayInfo> orderPayInfoDataList = orderPayInfoMap.get(orderNo);
            if (!CollectionUtils.isEmpty(orderPayInfoDataList)) {
                // 货到付款 已完成的单子  金额取买家到付金额
                if (OrderStateEnum.COMPLETED.getCode().equals(data.getRecords().get(i).getOrderState()) && DsConstants.STRING_TWO.equals(orderPayInfoDataList.get(0).getPayType())) {
                    totalAmount = orderPayInfoDataList.get(0).getBuyerCodAmount();
                } else {
                    totalAmount = orderPayInfoDataList.get(0).getBuyerActualAmount();
                }
            }
            String commodityNames = orderDetailDataList.stream().map(OrderDetail::getCommodityName).collect(Collectors.joining(","));
            List<CommodityInfoRspDto> commodityInfoList = orderDetailDataList.stream().map(orderDetail -> new CommodityInfoRspDto(orderDetail.getCommodityName(), orderDetail.getCommodityCount(), DsConstants.INTEGER_ZERO, orderDetail.getPrice(), orderDetail.getMainPic())).collect(Collectors.toList());
            data.getRecords().get(i).setTotalAmount(totalAmount);
            data.getRecords().get(i).setCommodityNames(commodityNames);
            data.getRecords().get(i).setCommodityInfo(commodityInfoList);
        } else if (!CollectionUtils.isEmpty(refundDetailDataList)) {
            // 线上单退款
            BigDecimal totalAmount = new BigDecimal(0);
            totalAmount = data.getRecords().get(i).getTotalAmount().negate();
            String commodityNames = refundDetailDataList.stream().map(RefundDetail::getCommodityName).collect(Collectors.joining(","));
            List<CommodityInfoRspDto> commodityInfoList = refundDetailDataList.stream().map(refundDetail -> new CommodityInfoRspDto(refundDetail.getCommodityName(), refundDetail.getRefundCount(), DsConstants.INTEGER_ZERO)).collect(Collectors.toList());
            data.getRecords().get(i).setTotalAmount(totalAmount);
            data.getRecords().get(i).setCommodityNames(commodityNames);
            data.getRecords().get(i).setCommodityInfo(commodityInfoList);
        }
    }

    private void selectMemberRecords(Page<MemberRecordsRspDto> data, List<ErpOrderInfoModel> erpOrderInfoModelList) {
        Map<Long, List<OrderDetail>> orderDetailMap = new HashMap<>();
        Map<Long, List<OrderPayInfo>> orderPayInfoMap = new HashMap<>();
        Map<Long, List<ErpOrderDetail>> erpOrderDetailMap = new HashMap<>();
        Map<Long, List<RefundDetail>> refundDetailMap = new HashMap<>();
        List<Long> orderNoList = data.getRecords().stream().map(MemberRecordsRspDto::getOrderNo).collect(Collectors.toList());
        // 线上订单明细
        QueryWrapper<OrderDetail> orderDetailQueryWrapper = new QueryWrapper<>();
        orderDetailQueryWrapper.lambda().in(OrderDetail::getOrderNo, orderNoList);
        List<OrderDetail> orderDetailList = orderDetailMapper.selectList(orderDetailQueryWrapper);
        orderDetailMap = orderDetailList.stream().collect(Collectors.groupingBy(OrderDetail::getOrderNo));
        // 线上订单财务数据
        QueryWrapper<OrderPayInfo> orderPayInfoQueryWrapper = new QueryWrapper<>();
        orderPayInfoQueryWrapper.lambda().in(OrderPayInfo::getOrderNo, orderNoList);
        List<OrderPayInfo> orderPayInfoList = orderPayInfoMapper.selectList(orderPayInfoQueryWrapper);
        orderPayInfoMap = orderPayInfoList.stream().collect(Collectors.groupingBy(OrderPayInfo::getOrderNo));
        // 线上订单退款明细
        QueryWrapper<RefundDetail> refundDetailQueryWrapper = new QueryWrapper<>();
        refundDetailQueryWrapper.lambda().in(RefundDetail::getRefundNo, orderNoList);
        List<RefundDetail> refundDetailList = refundDetailMapper.selectList(refundDetailQueryWrapper);
        refundDetailMap = refundDetailList.stream().collect(Collectors.groupingBy(RefundDetail::getRefundNo));
        // 线下订单明细
        if (!CollectionUtils.isEmpty(erpOrderInfoModelList)) {
            List<ErpOrderDetailModel> detailList = new ArrayList<>();
            for (ErpOrderInfoModel erpOrder : erpOrderInfoModelList) {
                String detailStr = erpOrder.getDetailList();
                if (StringUtils.isEmpty(detailStr)) {
                    continue;
                }
                List<ErpOrderDetailModel> erpOrderDetailModels = JSONObject.parseArray(detailStr, ErpOrderDetailModel.class);
                erpOrderDetailModels.forEach(item -> {
                    if (!StringUtils.isEmpty(item.getOrderId()) && item.getOrderId().contains(ErpOrderInfoServiceImpl.idRule)) {
                        item.setOrderId(erpOrder.getThirdOrderNo());
                    }
                });
                detailList.addAll(erpOrderDetailModels);
            }
            List<ErpOrderDetail> erpOrderDetailList = detailList.stream().map(detailModel -> {
                {
                    ErpOrderDetail detail = new ErpOrderDetail();
                    BeanUtils.copyProperties(detailModel, detail);
                    detail.setOrderId(getSafeLong(detailModel.getOrderId()));
                    return detail;
                }
            }).collect(Collectors.toList());
            if (!CollectionUtils.isEmpty(erpOrderDetailList)) {
                erpOrderDetailMap = erpOrderDetailList.stream().filter(item -> item.getCommodityCount() != null).collect(Collectors.groupingBy(ErpOrderDetail::getOrderId));
            }
        }

        for (int i = 0; i < data.getRecords().size(); i++) {
            memberRecordsBuild(data, orderDetailMap, orderPayInfoMap, erpOrderDetailMap, refundDetailMap, i);
        }
    }

    @Override
    public IPage<MemberRecordsRspDto> selectMemberRecords(MemberRecordsReqDto reqDto) {
        if (CollectionUtils.isEmpty(reqDto.getStates())) {
            List<Integer> states = Stream.of(OrderStateEnum.values()).map(OrderStateEnum::getCode).collect(Collectors.toList());
            reqDto.setStates(states);
        }
        List<MemberRecordsRspDto> dataList = new ArrayList<>();
        // 获取线上订单、退款单
        Page<MemberRecordsRspDto> page = new Page<>(1, (long) reqDto.getCurrentPage() * reqDto.getPageSize());
        // 切换到从es中查询订单号
        List<Long> orderNoList = esEnhanceService.queryMemberConsumerData(MemberNoQueryDto.buildBean(reqDto));
        IPage<MemberRecordsRspDto> onlineOrderData = new Page<>();
        if (!CollectionUtils.isEmpty(orderNoList)) {
            onlineOrderData = orderInfoMapper.selectMemberRecords(page, orderNoList);
            try {
                if (CollUtil.isNotEmpty(onlineOrderData.getRecords())) {
                    onlineOrderData.getRecords().forEach(o -> {
                            if (!StringUtils.isEmpty(o.getWscExtJson())) {
                                AddOrderInfoReqDto.ExtJson extJson = JSON.parseObject(o.getWscExtJson(), AddOrderInfoReqDto.ExtJson.class);
                                if (DsConstants.B2C_ONLINE_STORE_CODE.equals(extJson.getB2cOnlineStore())) {
                                    o.setStoreName(DsConstants.B2C_STORE_CODE);
                                }
                            }
                    });
                }
            } catch (Exception e) {
                log.error("wsc extjson error,json:{}", JSON.toJSONString(onlineOrderData.getRecords()), e);
            }
            if (!CollectionUtils.isEmpty(onlineOrderData.getRecords())) {
                dataList.addAll(onlineOrderData.getRecords());
            }
        }
        // 获取线下订单、退款单
        List<ErpOrderInfoModel> erpOrderInfoModelList = new ArrayList<>();
        if (reqDto.getStates().contains(OrderStateEnum.COMPLETED.getCode()) || reqDto.getStates().contains(OrderStateEnum.CLOSED.getCode())) {
            erpOrderInfoModelList = this.queryErpOrderRecodFromEs(reqDto, onlineOrderData);
        }
        if (!CollectionUtils.isEmpty(erpOrderInfoModelList)) {
            dataList.addAll(erpOrderInfoModelList.stream().map(erpOrder -> {
                {
                    try {
                        String erpOrderNo = erpOrder.getId();
                        if (!StringUtils.isEmpty(erpOrder.getId()) && erpOrder.getId().contains(ErpOrderInfoServiceImpl.idRule)) {
                            erpOrderNo = erpOrder.getThirdOrderNo();
                        }
                        return MemberRecordsRspDto.builder().orderNo(getSafeLong(erpOrderNo)).storeName(erpOrder.getStoreName()).orderState(BigDecimal.ZERO.compareTo(erpOrder.getTotalFee()) < 0 ? OrderStateEnum.COMPLETED.getCode() : OrderStateEnum.CLOSED.getCode()).totalAmount(erpOrder.getTotalFee()).refundFlag(BigDecimal.ZERO.compareTo(erpOrder.getTotalFee()) < 0 ? 2 : 1).onLineOrder(DsConstants.INTEGER_TWO).shopTime(DateUtil.parseDateToStr(new Date(erpOrder.getTradeTime() * 1000), DateUtil.DATE_TIME_FORMAT_YYYY_MM_DD_HH_MI_SS)).build();
                    } catch (Exception e) {
                        log.error("buildErpOrder error", e);
                        return null;
                    }
                }
            }).collect(Collectors.toList()));
        }
        // 内存分页
        ListPageTool<MemberRecordsRspDto> pageToolData = new ListPageTool<MemberRecordsRspDto>(dataList, reqDto.getCurrentPage(), reqDto.getPageSize(), "desc", "shopTime");
        Page<MemberRecordsRspDto> data = new Page<>(reqDto.getCurrentPage(), reqDto.getPageSize(), onlineOrderData.getTotal());
        data.setRecords(pageToolData.getData());
        if (CollectionUtils.isEmpty(data.getRecords())) {
            return data;
        }
        selectMemberRecords(data, erpOrderInfoModelList);
        return data;
    }

    // 安全转换long型
    private Long getSafeLong(String erpOrderNo) {
        try {
            return Long.valueOf(erpOrderNo);
        } catch (Exception e) {
            return Math.abs(new Random(Integer.MAX_VALUE).nextLong());
        }
    }

    @Override
    public IPage<OrderInfoPageRsp> getObOrderPage(ObOrderPageReqDto obOrderPageReqDto, List<String> platformCodeList) {
        VerifyRspDto verifyDto = verifyService.verifyOrganizationAndGet(VerifyReqDto.builder().merCode(obOrderPageReqDto.getMerCode()).organizationCode(obOrderPageReqDto.getOrganizationCode()).userId(obOrderPageReqDto.getUserId()).build());
        // 分页查询记录
        Page<OrderInfoPageRsp> page = new Page<>(obOrderPageReqDto.getCurrentPage(), obOrderPageReqDto.getPageSize());
        IPage<OrderInfoPageRsp> pageData = orderInfoMapper.selectObOrderPage(page, obOrderPageReqDto, verifyDto.getOrganizatioinList(), platformCodeList);
        // 懒加载检索优化
        orderSingleQueryManager.buildRespForOrderNormalQuery(pageData.getRecords());
        // 预约单描述处理
        if (CollectionUtils.isEmpty(pageData.getRecords())) {
            return pageData;
        }
        Set<String> dcRecordSet = new HashSet<>();
        Set<String> cloudRecordSet = new HashSet<>();
        for (OrderInfoPageRsp record : pageData.getRecords()) {
            if (CollectionUtils.isEmpty(record.getOrderDetailList())) {
                continue;
            }
            Integer originType = record.getOrderDetailList().get(0).getOriginType();
            String orCode = record.getOrderDetailList().get(0).getStCode();
            if (ObOrderOriginTypeEnum.DC.getCode().equals(originType)) {
                dcRecordSet.add(orCode);
            }
            if (ObOrderOriginTypeEnum.CLOUD.getCode().equals(originType)) {
                cloudRecordSet.add(orCode);
            }
        }
        // 与hydee-middle-baseinfo交互
        Map<String, String> dcOrCodeNameMap = new HashMap<>();
        dcRecordSet.forEach(orCode -> {
            {
                ResponseBase<OrganizationRspDto> queryOrgByCodeResult = middleBaseInfoClientAdapter.queryOrgByCode(obOrderPageReqDto.getMerCode(), orCode);
                if (queryOrgByCodeResult.checkSuccess() && null != queryOrgByCodeResult.getData()) {
                    String orName = queryOrgByCodeResult.getData().getOrName();
                    dcOrCodeNameMap.put(orCode, orName);
                }
            }
        });
        Map<String, String> cloudOrCodeNameMap = new HashMap<>();
        cloudRecordSet.forEach(stCode -> {
            {
                ResponseBase<MerchantRspDto> queryMerchantByCodeResult = middleBaseInfoClient.queryMerchantByCode(stCode);
                if (queryMerchantByCodeResult.checkSuccess() && null != queryMerchantByCodeResult.getData()) {
                    String orName = queryMerchantByCodeResult.getData().getMerName();
                    cloudOrCodeNameMap.put(stCode, orName);
                }
            }
        });
        String appointmentDesc = "%s:%s-请于%s 前送达";
        pageData.getRecords().forEach(record -> {
            {
                if (!CollectionUtils.isEmpty(record.getOrderDetailList())) {
                    Integer originType = record.getOrderDetailList().get(0).getOriginType();
                    String orCode = record.getOrderDetailList().get(0).getStCode();

                    String originName = ObOrderOriginTypeEnum.key2Value(originType);
                    String orName = "";
                    if (ObOrderOriginTypeEnum.DC.getCode().equals(originType)) {
                        orName = dcOrCodeNameMap.get(orCode);
                    }
                    if (ObOrderOriginTypeEnum.CLOUD.getCode().equals(originType)) {
                        orName = cloudOrCodeNameMap.get(orCode);
                    }
                    orName = Optional.ofNullable(orName).orElse("未知");
                    String expectDeliveryTime = "";
                    if (null != record.getOrderDetailList().get(0).getExpectDeliveryTime()) {
                        expectDeliveryTime = DateUtil.parseDateToStr(record.getOrderDetailList().get(0).getExpectDeliveryTime(), DateUtil.DATE_TIME_FORMAT_YYYY_MM_DD_HH_MI_SS);
                    }
                    record.setAppointmentDesc(String.format(appointmentDesc, originName, orName, expectDeliveryTime));
                    // 代发标识
                    Integer directDeliveryType = record.getOrderDetailList().get(0).getDirectDeliveryType();
                    record.setDirectDeliveryType(Optional.ofNullable(directDeliveryType).orElse(ObOrderDirectDeliveryTypeEnum.NOT_DIRECT_DELIVERY.getCode()));
                }
            }
        });
        convertDeliveryType(pageData, null);
        return pageData;
    }


    @Transactional(rollbackFor = Exception.class)
    @Override
    public void updateObOrderStore(String userId, String merCode, UpdateOrderSysStoreReqDto req) {
        // 1、业务校验
        OrderDeliveryRecord orderDeliveryRecord = orderBasicManager.getOrderDeliveryRecordWithCheck(req.getOrderNo());
        // 非快递配送
        if (!DeliveryTypeEnum.EXPRESS.getCode().equals(orderDeliveryRecord.getDeliveryType())) {
            throw ExceptionUtil.getWarnException(DsErrorType.ORDER_OB_MODIFY_STORE_DELIVERY_TYPE);
        }
        OrderInfo orderInfo = orderBasicService.getOrderBaseWithCheck(req.getOrderNo());
        String oldOrCode = orderInfo.getOrganizationCode();
        // 代发校验
        cloudShelfService.obOrderAllowOperateCheck(orderInfo);
        List<OrderDetail> orderDetailList = orderBasicService.getOrderDetailListWithCheck(orderInfo.getOrderNo());
        List<ErpWareReqDto> erpCodeList = orderDetailList.stream().map(orderDetail -> {
            {
                return ErpWareReqDto.builder().wareCode(orderDetail.getErpCode()).goodsType(orderDetail.getGoodsType()).chailing(orderDetail.getChailing()).build();
            }
        }).collect(Collectors.toList());
        // 库存是否充足
        OrderInfo orderInfoCheck = new OrderInfo();
        orderInfoCheck.setOrderNo(orderInfo.getOrderNo());
        orderInfoCheck.setErpAdjustNo(orderInfo.getErpAdjustNo());
        orderInfoCheck.setOrganizationCode(req.getOrganizationCode());
        orderInfoCheck.setMerCode(merCode);
        if (!orderHandler.commodityStockCheck(orderInfoCheck, erpCodeList).getCheckFlag()) {
            String msg = String.format("%s门店商品库存不足，不可修改，请更换其它门店！", req.getOnlineStoreCode());
            throw ExceptionUtil.getWarnException(DsErrorType.OUT_OF_STOCK.getCode(), msg);
        }

        // 2、修改OMS订单的线上门店和线下门店为更换后的门店，并重新按新门店的配置初始化订单
        OnlineStoreInfoRspDto onlineStoreInfoRspDto = baseInfoService.getOnlineStoreInfoInner(orderInfo.getMerCode(), orderInfo.getThirdPlatformCode(), orderInfo.getClientCode(), req.getOnlineStoreCode());
        log.info("updateObOrderStore: getOnlineStoreInfo, thirdOrderNo:{}, onlineStoreInfo:{}", orderInfo.getOrderNo(), JSON.toJSONString(onlineStoreInfoRspDto));
        OrderInfo updateOrder = new OrderInfo();
        updateOrder.setOrderNo(orderInfo.getOrderNo());
        updateOrder.setOrganizationCode(req.getOrganizationCode());
        updateOrder.setOrganizationName(req.getOrganizationName());
        int num = orderInfoMapper.updateOrder(updateOrder);
        if (num <= 0) {
            throw ExceptionUtil.getWarnException(DsErrorType.ORDER_OB_MODIFY_STORE_EXCEPTION);
        }

        // 3、解锁原门店库存，锁新门店库存
        OrderInfo getOrderInfo = orderBasicService.getOrderBaseWithCheck(req.getOrderNo());
        Boolean lockInventoryFlag = Boolean.FALSE;
        if (StringUtils.isEmpty(orderInfo.getErpAdjustNo())) {
            // 锁新门店库存
            lockInventoryFlag = lockStockProxy.switchStoreLockStockObOrder(onlineStoreInfoRspDto, null, getOrderInfo, orderDetailList);
        } else {
            // 解锁ERP库存
            undoErpForCancelAndUpdateState(orderInfo);
            // 解锁成功后，将订单拣货单号置为空,还原待锁定状态
            OrderInfo updatesOrder = new OrderInfo();
            updatesOrder.setOrderNo(orderInfo.getOrderNo());
            updatesOrder.setErpAdjustNo("");
            updatesOrder.setErpState(ErpStateEnum.WAIT_PICK.getCode());
            orderInfoMapper.updateOrder(updatesOrder);
            // 拣货单号置空后再查询最新的单据
            OrderInfo orderUpdate = orderBasicService.getOrderBaseWithCheck(orderInfo.getOrderNo());
            // 锁新门店库存
            lockInventoryFlag = lockStockProxy.switchStoreLockStockObOrder(onlineStoreInfoRspDto, null, orderUpdate, orderDetailList);
        }

        try {
            // 4、异常订单换门店后订单要修改为正常，订单明细状态也修改为正常
            if (getOrderInfo.getLockFlag() >= OrderLockFlagEnum.LOCK_EXCEPTION_MIN_CODE.getCode()) {
                OrderInfo update = new OrderInfo();
                update.setOrderNo(orderInfo.getOrderNo());
                update.setLockFlag(OrderLockFlagEnum.NOT_LOCK.getCode());
                orderInfoMapper.updateOrder(update);
            }
            for (OrderDetail orderDetail : orderDetailList) {
                if (OrderDetailStatusEnum.OUT_OF_STOCK.getCode().equals(orderDetail.getStatus()) || OrderDetailStatusEnum.NOT_EXIST.getCode().equals(orderDetail.getStatus())) {
                    OrderDetail updateOrderDetail = new OrderDetail();
                    updateOrderDetail.setId(orderDetail.getId());
                    updateOrderDetail.setStatus(OrderDetailStatusEnum.NORMAL.getCode());
                    orderDetailMapper.updateById(updateOrderDetail);
                }
            }

            //订单更新异步消费
            messageProducerService.produceUpdateOrderMessage(Collections.singletonList(orderInfo.getOrderNo()), OrderUpdateCodeEnum.OB_UPDATE_STORE.getCode(), oldOrCode);

            // 6、后置处理：调用微商城接口，将待发货状态回传微商城，该订单从预约单列表消失，并展示在待拣货列表
            updateObOrderStoreBehindHandel(merCode, req.getOrderNo(), ObOrderBusinessTypeEnum.MODIFY_STORE.getCode());
        } catch (Exception e) {
            // 解锁本事务已锁库存
            if (lockInventoryFlag) {
                OrderInfo newOrderInfo = orderBasicService.getOrderBaseWithCheck(req.getOrderNo());
                undoErpForCancelAndUpdateState(newOrderInfo);
            }
            throw e;
        }

        //预约单列表-修改门店后记录订单日志
        String msg = "线上门店从【%s】切换到【%s】";
        String message = String.format(msg, orderInfo.getOnlineStoreName(), req.getOnlineStoreName());
        SysEmployeeResDTO employeePickOperator = baseInfoManager.getEmployeeInfoForErpNoEx(userId);
        HydeeEsSyncClientAsync.orderLogToES(DsConstants.INTEGER_ONE, merCode, orderInfo.getOrderNo(), orderInfo.getOrderState(), orderInfo.getErpState(), OrderLogEnum.UPDATE_OB_ORDERSTORE.getAction(), message, employeePickOperator);


        //20220329  再次加上调用微商城接口
        notifyWscStoreChange(merCode, orderInfo, req);
    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public void obOrderGoodsMakeSure(String merCode, @Valid OrderHandleReqDto req) {
        // 1、校验处理
        OrderInfo orderInfo = orderBasicService.getOrderBaseWithCheck(req.getOrderNo());
        // 代发校验
        cloudShelfService.obOrderAllowOperateCheck(orderInfo);
        List<OrderDetail> orderDetailList = orderBasicService.getOrderDetailListWithCheck(orderInfo.getOrderNo());
        List<ErpWareReqDto> erpCodeList = orderDetailList.stream().map(orderDetail -> {
            {
                return ErpWareReqDto.builder().wareCode(orderDetail.getErpCode()).goodsType(orderDetail.getGoodsType()).chailing(orderDetail.getChailing()).build();
            }
        }).collect(Collectors.toList());
        Map<String, String> erpCodeNameMap = orderDetailList.stream().collect(Collectors.toMap(OrderDetail::getErpCode, OrderDetail::getCommodityName));
        // 库存是否充足
        CommodityStockCheckRspDto checkResult = orderHandler.commodityStockCheck(orderInfo, erpCodeList);
        if (!checkResult.getCheckFlag()) {
            String msg = String.format("商品%s库存不足，请确认门店库存量，再操作！", erpCodeNameMap.get(checkResult.getCheckErpCodeList().get(0)));
            throw ExceptionUtil.getWarnException(DsErrorType.OUT_OF_STOCK.getCode(), msg);
        }
        Boolean lockInventoryFlag = Boolean.FALSE;
        // 2、门店设置接单锁库存，则锁门店库存
        OnlineStoreInfoRspDto onlineStoreInfoRspDto = baseInfoService.getOnlineStoreInfoInner(orderInfo.getMerCode(), orderInfo.getThirdPlatformCode(), orderInfo.getClientCode(), orderInfo.getOnlineStoreCode());
//  没有ERP，暂时不做锁库存   lockInventoryFlag = lockStockProxy.switchStoreLockStockObOrder(onlineStoreInfoRspDto, null, orderInfo, orderDetailList);
        try {
            // 3、后置处理：调用微商城接口，将待发货状态回传微商城，该订单从预约单列表消失，并展示在待拣货列表
            updateObOrderStoreBehindHandel(merCode, req.getOrderNo(), ObOrderBusinessTypeEnum.REPLENISHMENT.getCode());
        } catch (Exception e) {
            // 解锁本事务已锁库存
            if (lockInventoryFlag) {
                OrderInfo newOrderInfo = orderBasicService.getOrderBaseWithCheck(req.getOrderNo());
                undoErpForCancelAndUpdateState(newOrderInfo);
            }
            throw e;
        }
        //订单更新异步消费
        messageProducerService.produceUpdateOrderMessage(Collections.singletonList(orderInfo.getOrderNo()), OrderUpdateCodeEnum.YUYUE_CONFIRM.getCode());

    }

    @Override
    public List<CommodityCountDetailResult> getCommodityCount(CommodityCountReqDto req) {
        log.info("getCommodityCount,req:{}", JSONObject.toJSONString(req));
        Date startTime = new Date(req.getBeginTime());
        Date endTime = new Date(req.getEndTime());
        //获取商品统计信息
        List<CommodityCountDetailResult> results = orderInfoMapper.getCommodityCount(req, startTime, endTime);
        return results;
    }

    /**
     * undoErpForCancelAndUpdateState:解锁ERP库存. <br/>
     *
     * @param orderInfo
     * <AUTHOR>
     * @date 2020年10月9日 下午4:25:20
     */
    private void undoErpForCancelAndUpdateState(OrderInfo orderInfo) {
        // 解锁原ERP库存
        try {
            commodityStockService.unLockStockNew(orderInfo, null, "OB预约订单释放库存", UnLockStockTypeEnum.ALL);
            erpBillService.undoErpForCancel(orderInfo, "", "OB预约订单");
        } catch (WarnException ex) {
            if (DsErrorType.ERP_UNLOCK_STOCK_ERROR.getCode().equals(ex.getCode()) && ex.getMessage().contains("是否存在或者已经作废")) {
                log.error("call erp undoErpForCancel error orderNo:{},code:{},msg:{}", orderInfo.getOrderNo(), ex.getCode(), ex.getMessage());
            }
        }
    }

    /**
     * updateObOrderStoreBehindHandel:后置处理：调用微商城接口，将待发货状态回传微商城，该订单从预约单列表消失，并展示在待拣货列表. <br/>
     *
     * <AUTHOR>
     * @date 2020年9月29日 下午12:02:12
     */
    private void updateObOrderStoreBehindHandel(String merCode, Long orderNo, Integer obOrderBusinessType) {
        OrderInfo orderInfo = orderBasicService.getOrderBaseWithCheck(orderNo);
        // 调用微商城接口，将待发货状态回传微商城
        CargoTransferSuccessReqDto cargoTransferSuccessDTO = new CargoTransferSuccessReqDto();
        cargoTransferSuccessDTO.setMerCode(merCode);
        cargoTransferSuccessDTO.setOrderId(Long.valueOf(orderInfo.getThirdOrderNo()));
        cargoTransferSuccessDTO.setStatus(CargoTransferStatusEnum.GOODS_WAIT_TAKE.getCode());
        ResponseBase<Boolean> base = middleOrderClient.cargoTransferSuccess(cargoTransferSuccessDTO);
        HydeeEsSyncClientAsync.sendLogToES(ServiceTypeEnum.YDJIA_MERCHANT_MANAGER, OperateEnum.UpdateObOrderStoreBehindHandel, orderInfo, cargoTransferSuccessDTO, base, Boolean.class);
        log.info("updateMiddleOrderState cargoTransferSuccess, requestParam:{},result:{}", JSON.toJSON(cargoTransferSuccessDTO), JSON.toJSON(base));
        if (!base.checkSuccess()) {
            log.error("updateObOrderState:updateMiddleOrderState, cargoTransferSuccess is error, requestParam:{},result:{}", JSON.toJSON(cargoTransferSuccessDTO), JSON.toJSON(base));
            throw ExceptionUtil.getWarnException(DsErrorType.MIDDLE_ORDER_CARGO_TRANSFER_ERROR);
        }
        // 操作失败
        if (!base.getData()) {
            log.error("updateObOrderState:updateMiddleOrderState, cargoTransferSuccess is error, requestParam:{},result:{}", JSON.toJSON(cargoTransferSuccessDTO), JSON.toJSON(base));
            throw ExceptionUtil.getWarnException(DsErrorType.MIDDLE_ORDER_CARGO_TRANSFER_ERROR.getCode(), DsErrorType.MIDDLE_ORDER_CARGO_TRANSFER_ERROR.getMsg() + ":修改状态失败");
        }

        // 该订单从预约单列表消失，并展示在待拣货列表
        OrderInfo update = new OrderInfo();
        update.setOrderNo(orderNo);
        update.setAppointmentBusinessFlag(ObOrderBusinessFlagEnum.HANDELED.getCode());
        update.setAppointmentBusinessType(obOrderBusinessType);
        orderInfoMapper.updateOrder(update);
    }

    @Override
    public ResponseHEMS<Void> logisticCallback(BaseHemsReqDto req) {
        log.info("logisticCallback requestParam {}", JSON.toJSONString(req));

        OmsHemsMap omsHemsMap = omsHemsMapMapper.selectOne(new QueryWrapper<OmsHemsMap>().lambda().eq(OmsHemsMap::getHemsMercode, req.getGroupid()).eq(OmsHemsMap::getHemsPlatformCode, req.getEccode()).eq(OmsHemsMap::getHemsClientCode, req.getOlshopid()));
        if (omsHemsMap == null) {
            return ResponseHEMS.invalidData("企业编码-平台编号-门店id在omsHemsMap找不到配置");
        }

        String sign = orderHandlerManager.buildSign(req, omsHemsMap.getAppsecret());
        if (!req.getSign().equals(sign)) {
            return ResponseHEMS.failed("签名无效");
        }

        LogisticCallbackReqDto logisticCallbackReqDto = JSON.parseObject(req.getBody(), LogisticCallbackReqDto.class);
        if (logisticCallbackReqDto == null || org.apache.commons.lang.StringUtils.isEmpty(logisticCallbackReqDto.getOlorderno()) || org.apache.commons.lang.StringUtils.isEmpty(req.getEccode())) {
            return ResponseHEMS.invalidData("请求入参不对或网店订单号为空或平台编码为空");
        }

        DeliveryLogisticsCompany deliveryLogisticsCompany = deliveryLogisticsCompanyMapper.queryOneByRelatePlatformCode(PlatformCodeEnum.TY_O2O.getCode(), logisticCallbackReqDto.getExpcmpids(), omsHemsMap.getOmsPlatformCode());
        if (deliveryLogisticsCompany == null) {
            return ResponseHEMS.invalidData("快递企业编码在OMS找不到配置");
        }

        OrderInfo orderInfo = orderBasicManager.getOrderBaseByThirdNo(omsHemsMap.getOmsPlatformCode(), logisticCallbackReqDto.getOlorderno());
        if (orderInfo == null) {
            return ResponseHEMS.invalidData("订单不存在");
        }

        if (!DsConstants.INTEGER_ONE.equals(orderInfo.getTransferDelivery())) {
            return ResponseHEMS.invalidData("非转仓发货订单");
        }
        //同步信息给平台
        try {
            GlobalInterceptor.tObject.set(orderInfo);
            notifyPlatForm(orderInfo, deliveryLogisticsCompany, logisticCallbackReqDto.getExpids());
        } catch (Exception e) {
            log.warn("hems 物流回调，oms 同步信息给平台 发送错误 {}", e.getMessage());
            //调平台发货返回错误时也返回给HEMS
            throw e;
        }

        OrderInfo update = new OrderInfo();
        update.setOrderNo(orderInfo.getOrderNo());
        update.setErpState(ErpStateEnum.CANCELED.getCode());
        if (orderInfo.getOrderState().intValue() < OrderStateEnum.POSTING.getCode()) {
            update.setOrderState(OrderStateEnum.POSTING.getCode());
        }
        int num = orderInfoMapper.updateOrderWithState(update, OrderStateEnum.UN_PICK.getCode());
        if (num > 0) {
            OrderDeliveryRecord updateDelivery = new OrderDeliveryRecord();
            updateDelivery.setOrderNo(orderInfo.getOrderNo());
            updateDelivery.setLogisticsName(deliveryLogisticsCompany.getLogisticsName());
            updateDelivery.setPickTime(new Date());
            updateDelivery.setLogisticsNo(logisticCallbackReqDto.getExpids());
            updateDelivery.setLogisticsCompany(deliveryLogisticsCompany.getLogisticsCode());
            updateDelivery.setDeliveryType(DeliveryTypeEnum.EXPRESS.getCode());
            orderDeliveryRecordMapper.updateDeliveryRecord(updateDelivery);
            HydeeEsSyncClientAsync.orderLogToES(DsConstants.INTEGER_ONE, orderInfo.getMerCode(), orderInfo.getOrderNo(), OrderStateEnum.POSTING.getCode(), ErpStateEnum.CANCELED.getCode(), OrderLogEnum.LOGISTIC_CALLBACK.getAction(), OrderLogEnum.getSupplierInfo("回调物流操作成功"), null);
            //订单更新异步消费
            messageProducerService.produceUpdateOrderMessage(Collections.singletonList(orderInfo.getOrderNo()), OrderUpdateCodeEnum.HEMS_LOGISTICS_CALLBACK.getCode());
            return ResponseHEMS.success();
        }
        HydeeEsSyncClientAsync.orderLogToES(DsConstants.INTEGER_ZERO, orderInfo.getMerCode(), orderInfo.getOrderNo(), OrderStateEnum.POSTING.getCode(), ErpStateEnum.CANCELED.getCode(), OrderLogEnum.LOGISTIC_CALLBACK.getAction(), OrderLogEnum.getSupplierInfo("回调物流操作失败"), null);
        return ResponseHEMS.failed("订单状态非待拣货，未能保存物流信息到OMS");
    }

    //通知三方平台发货
    private void notifyPlatForm(OrderInfo orderInfo, DeliveryLogisticsCompany deliveryLogisticsCompany, String expids) {
        // 调用发货接口
        OnlineStoreInfoRspDto storeInfo = baseInfoManager.getOnlineStoreInfo(orderInfo.getMerCode(), orderInfo.getThirdPlatformCode(), orderInfo.getClientCode(), orderInfo.getOnlineStoreCode());
        HemsBaseData baseData = hemsCommonClient.constructHemsBaseData(orderInfo.getMerCode(), storeInfo.getPlatformCode(), storeInfo.getOnlineClientCode(), storeInfo.getSessionKey());

        OrderDeliveryAddress orderDeliveryAddress = orderDeliveryAddressMapper.selectByOrderNo(orderInfo.getOrderNo());
        OrderDeliveryExpressReq expressReq = new OrderDeliveryExpressReq();
        expressReq.setOrderId(orderInfo.getThirdOrderNo());
        expressReq.setOlshopId(orderInfo.getOnlineStoreCode());
        //【京东健康必填】
        DeliveryLogisticsCompany relateCompany = null;
        if (null != deliveryLogisticsCompany.getPlatRelateId()) {
            relateCompany = deliveryLogisticsCompanyMapper.selectById(deliveryLogisticsCompany.getPlatRelateId());
        } else {
            relateCompany = deliveryLogisticsCompany;
        }
        expressReq.setLogisticsProviderCode(relateCompany.getLogisticsCode());
        //【京东健康必填】
        expressReq.setLogisticsCode(expids);
        if (null != orderDeliveryAddress) {
            if (!org.apache.commons.lang3.StringUtils.isEmpty(orderDeliveryAddress.getReceiverTelephone())) {
                expressReq.setRecipientPhone(orderDeliveryAddress.getReceiverTelephone());
            } else {
                expressReq.setRecipientPhone(orderDeliveryAddress.getReceiverMobile());
            }
        }
        //操作人【京东健康必填】
        expressReq.setOperateMan("168");
        //三方物流公司名称（和公司编号对应）【京东健康必填】
        expressReq.setLogisticsProviderName(relateCompany.getLogisticsName());
        //操作人【饿百固定必填】   DELIVERY_SHIPPING /  COMPLETED
        expressReq.setStatusCode(DsConstants.DELIVERY_STATUS_POSTING);
        expressReq.setWaybillId(orderInfo.getThirdOrderNo());
        expressReq.setShopId(storeInfo.getOutShopId());
        //饿百物流新接口
        if (PlatformCodeEnum.E_BAI.getCode().equals(orderInfo.getThirdPlatformCode())) {
            OrderDeliveryExpressLogisticsReq logisticsReq = new OrderDeliveryExpressLogisticsReq();
            logisticsReq.setLogisticsCode(expids);
            logisticsReq.setLogisticsProviderCode(relateCompany.getLogisticsCode());
            expressReq.setLogisticsList(Collections.singletonList(logisticsReq));

            expressReq.setVersion(DsConstants.STRING_ONE_POINT_ONE);
        }
        hemsCommonClient.orderB2CDeliveryExpress(expressReq, baseData);
    }

    @DS(DsConstants.DB_ORDER_SLAVE_TWO)
    @Override
    public IPage<OrgOrderInfoSnapshotPageRsp> getOrgSnapshot(OrderInfoSnapshotReqDto req) {
        HashMap<String, OrgOrderInfoSnapshotPageRsp> resultHashMap = new HashMap<>();
        // 销售单待下账
        OrgOrderInfoSnapshotDto orgOrderInfoSnapshotDto = orderInfoRedisManager.buildOrgOrderInfoSnapshotData(req.getMerCode(), req.getOrganizationCodeList());
        req.setTotalData(orgOrderInfoSnapshotDto.calculateTotalData());
        List<StringNumDto> orgWaitSaleList = orgOrderInfoSnapshotDto.getWaitBillData();
        if (!CollectionUtils.isEmpty(orgWaitSaleList)) {
            for (StringNumDto map : orgWaitSaleList) {
                String organizationCode = map.getKey();
                Long num = map.getValue();
                resultHashMap.compute(organizationCode, (k, v) -> {
                    if (v == null) {
                        v = new OrgOrderInfoSnapshotPageRsp();
                        v.setOrganizationCode(organizationCode);
                    }
                    v.setWaitSale(num);
                    return v;
                });
            }
        }
        // 退款单待下账
        List<StringNumDto> orgRefundWaitSaleList = orgOrderInfoSnapshotDto.getWaitRefundBillData();
        if (!CollectionUtils.isEmpty(orgRefundWaitSaleList)) {
            for (StringNumDto map : orgRefundWaitSaleList) {
                String organizationCode = map.getKey();
                Long num = map.getValue();
                resultHashMap.compute(organizationCode, (k, v) -> {
                    if (v == null) {
                        v = new OrgOrderInfoSnapshotPageRsp();
                        v.setOrganizationCode(organizationCode);
                    }
                    v.setRefundWaitSale(num);
                    return v;
                });
            }
        }
        //待拣货
        List<StringNumDto> orgWaitPickList = orgOrderInfoSnapshotDto.getWaitPickData();
        if (!CollectionUtils.isEmpty(orgWaitPickList)) {
            for (StringNumDto map : orgWaitPickList) {
                String organizationCode = map.getKey();
                Long num = map.getValue();
                resultHashMap.compute(organizationCode, (k, v) -> {
                    if (v == null) {
                        v = new OrgOrderInfoSnapshotPageRsp();
                        v.setOrganizationCode(organizationCode);
                    }
                    v.setWaitPick(num);
                    return v;
                });
            }
        }
        //待配送
        List<StringNumDto> orgWaitPostList = orgOrderInfoSnapshotDto.getWaitPostData();
        if (!CollectionUtils.isEmpty(orgWaitPostList)) {
            for (StringNumDto map : orgWaitPostList) {
                String organizationCode = map.getKey();
                Long num = map.getValue();
                resultHashMap.compute(organizationCode, (k, v) -> {
                    if (v == null) {
                        v = new OrgOrderInfoSnapshotPageRsp();
                        v.setOrganizationCode(organizationCode);
                    }
                    v.setWaitPost(num);
                    return v;
                });
            }
        }
        // 配送中
        List<StringNumDto> orgPostingList = orgOrderInfoSnapshotDto.getPostingData();
        if (!CollectionUtils.isEmpty(orgPostingList)) {
            for (StringNumDto map : orgPostingList) {
                String organizationCode = map.getKey();
                Long num = map.getValue();
                resultHashMap.compute(organizationCode, (k, v) -> {
                    if (v == null) {
                        v = new OrgOrderInfoSnapshotPageRsp();
                        v.setOrganizationCode(organizationCode);
                    }
                    v.setPosting(num);
                    return v;
                });
            }
        }
        // 异常
        List<StringNumDto> orgExceptionList = orgOrderInfoSnapshotDto.getExceptionData();
        if (!CollectionUtils.isEmpty(orgExceptionList)) {
            for (StringNumDto map : orgExceptionList) {
                String organizationCode = map.getKey();
                Long num = map.getValue();
                resultHashMap.compute(organizationCode, (k, v) -> {
                    if (v == null) {
                        v = new OrgOrderInfoSnapshotPageRsp();
                        v.setOrganizationCode(organizationCode);
                    }
                    v.setException(num);
                    return v;
                });
            }
        }
        // 退款中
        List<StringNumDto> orgRefundingList = orgOrderInfoSnapshotDto.getRefundingData();
        if (!CollectionUtils.isEmpty(orgRefundingList)) {
            for (StringNumDto map : orgRefundingList) {
                String organizationCode = map.getKey();
                Long num = map.getValue();
                resultHashMap.compute(organizationCode, (k, v) -> {
                    if (v == null) {
                        v = new OrgOrderInfoSnapshotPageRsp();
                        v.setOrganizationCode(organizationCode);
                    }
                    v.setRefunding(num);
                    return v;
                });
            }
        }

        // 没查到的机构 返回空数据
        List<MerOrganizationCode> merOrgs = dsOnlineStoreRepo.selectDistinctMerOrg(req.getMerCode(), req.getOrganizationCodeList());
        Map<String, MerOrganizationCode> merOrgMap = merOrgs.stream().filter((item) -> org.apache.commons.lang3.StringUtils.isNotEmpty(item.getOrganizationCode())).collect(Collectors.toMap(MerOrganizationCode::getOrganizationCode, item -> item, (a, b) -> a));
        List<String> orgList = merOrgs.stream().map(MerOrganizationCode::getOrganizationCode).collect(Collectors.toList());
        if (!CollectionUtils.isEmpty(orgList)) {
            for (String organizationCode : orgList) {
                resultHashMap.compute(organizationCode, (k, v) -> {
                    if (v == null) {
                        v = new OrgOrderInfoSnapshotPageRsp();
                        v.setOrganizationCode(organizationCode);
                    }
                    if (merOrgMap.get(organizationCode) != null) {
                        v.setOrganizationName(merOrgMap.get(organizationCode).getOrganizationName());
                    }
                    return v;
                });
            }
        }

        Stream<OrgOrderInfoSnapshotPageRsp> stream = resultHashMap.values().stream().filter(item -> !StringUtils.isEmpty(item.getOrganizationCode()));

        if (req.getSortField() != null && req.getSortSequence() != null && snapshotComp.getSequence(req.getSortField(), req.getSortSequence()) != null) {
            stream = stream.sorted(snapshotComp.getSequence(req.getSortField(), req.getSortSequence()));
        } else {
            stream = stream.sorted(snapshotComp.getSequence("waitSale", "desc"));
        }

        // 保底
        AtomicLong id = new AtomicLong(0);
        stream = stream.map((item) -> {
            item.setId(id.incrementAndGet());
            return item;
        });

        List<OrgOrderInfoSnapshotPageRsp> collect = stream.collect(Collectors.toList());

        Page<OrgOrderInfoSnapshotPageRsp> response = new Page<>();
        response.setTotal(collect.size());
        List<OrgOrderInfoSnapshotPageRsp> result = collect.stream().skip((req.getCurrentPage() - 1) * req.getPageSize()).limit(req.getPageSize()).collect(Collectors.toList());
        response.setSize(req.getPageSize());
        response.setCurrent(req.getCurrentPage());
        response.setSearchCount(true);
        response.setRecords(result);
        return response;
    }

    @DS(DsConstants.DB_ORDER_SLAVE_TWO)
    @Override
    public IPage<StoreOrderInfoSnapshotPageRsp> getStoreSnapshot(OrderInfoSnapshotReqDto req) {
        HashMap<String, StoreOrderInfoSnapshotPageRsp> resultHashMap = new HashMap<>();
        // 销售单待下账
        List<Map<String, Object>> storeWaitSaleList = orderInfoMapper.getStoreWaitSaleNum(req.getMerCode(), req.getOrganizationCodeList());
        if (!CollectionUtils.isEmpty(storeWaitSaleList)) {
            for (Map<String, Object> map : storeWaitSaleList) {
                String orgPlatStoreClient = (String) map.get("orgPlatStoreClient");
                Long num = (Long) map.get("num");
                resultHashMap.compute(orgPlatStoreClient, (k, v) -> {
                    if (v == null) {
                        v = new StoreOrderInfoSnapshotPageRsp();
                        String[] orgPlatStoreSplit = orgPlatStoreClient.split("-");
                        v.setOrganizationCode(orgPlatStoreSplit[0]);
                        v.setThirdPlatformCode(orgPlatStoreSplit[1]);
                        v.setOnlineStoreCode(orgPlatStoreSplit[2]);
                        v.setClientCode(orgPlatStoreSplit[3]);
                    }
                    v.setWaitSale(num);
                    return v;
                });
            }
        }
        // 退款单待下账
        List<Map<String, Object>> storeRefundWaitSaleList = orderInfoMapper.getStoreRefundWaitSaleNum(req.getMerCode(), req.getOrganizationCodeList());
        if (!CollectionUtils.isEmpty(storeRefundWaitSaleList)) {
            for (Map<String, Object> map : storeRefundWaitSaleList) {
                String orgPlatStoreClient = (String) map.get("orgPlatStoreClient");
                Long num = (Long) map.get("num");
                resultHashMap.compute(orgPlatStoreClient, (k, v) -> {
                    if (v == null) {
                        v = new StoreOrderInfoSnapshotPageRsp();
                        String[] orgPlatStoreSplit = orgPlatStoreClient.split("-");
                        v.setOrganizationCode(orgPlatStoreSplit[0]);
                        v.setThirdPlatformCode(orgPlatStoreSplit[1]);
                        v.setOnlineStoreCode(orgPlatStoreSplit[2]);
                        v.setClientCode(orgPlatStoreSplit[3]);
                    }
                    v.setRefundWaitSale(num);
                    return v;
                });
            }
        }
        //待拣货
        List<Map<String, Object>> storeWaitPickList = orderInfoMapper.getStoreWaitPickNum(req.getMerCode(), req.getOrganizationCodeList());
        if (!CollectionUtils.isEmpty(storeWaitPickList)) {
            for (Map<String, Object> map : storeWaitPickList) {
                String orgPlatStoreClient = (String) map.get("orgPlatStoreClient");
                Long num = (Long) map.get("num");
                resultHashMap.compute(orgPlatStoreClient, (k, v) -> {
                    if (v == null) {
                        v = new StoreOrderInfoSnapshotPageRsp();
                        String[] orgPlatStoreSplit = orgPlatStoreClient.split("-");
                        v.setOrganizationCode(orgPlatStoreSplit[0]);
                        v.setThirdPlatformCode(orgPlatStoreSplit[1]);
                        v.setOnlineStoreCode(orgPlatStoreSplit[2]);
                        v.setClientCode(orgPlatStoreSplit[3]);
                    }
                    v.setWaitPick(num);
                    return v;
                });
            }
        }
        //待配送、配送中
        List<Map<String, Object>> storeWaitPostList = orderInfoMapper.getStorePostNum(req.getMerCode(), req.getOrganizationCodeList());
        if (!CollectionUtils.isEmpty(storeWaitPostList)) {
            for (Map<String, Object> map : storeWaitPostList) {
                String orgPlatStoreClientState = (String) map.get("orgPlatStoreClientState");
                String orgPlatStoreClient = orgPlatStoreClientState.substring(0, orgPlatStoreClientState.lastIndexOf('-'));
                Integer status = Integer.valueOf(orgPlatStoreClientState.substring(orgPlatStoreClientState.lastIndexOf('-') + 1));
                Long num = (Long) map.get("num");
                resultHashMap.compute(orgPlatStoreClient, (k, v) -> {
                    if (v == null) {
                        v = new StoreOrderInfoSnapshotPageRsp();
                        String[] orgPlatStoreSplit = orgPlatStoreClient.split("-");
                        v.setOrganizationCode(orgPlatStoreSplit[0]);
                        v.setThirdPlatformCode(orgPlatStoreSplit[1]);
                        v.setOnlineStoreCode(orgPlatStoreSplit[2]);
                        v.setClientCode(orgPlatStoreSplit[3]);
                    }
                    if (OrderStateEnum.UN_DELIVERY.getCode().equals(status)) {
                        v.setWaitPost(num);
                    }
                    if (OrderStateEnum.POSTING.getCode().equals(status)) {
                        v.setPosting(num);
                    }
                    return v;
                });
            }
        }
        // 异常
        List<Map<String, Object>> storeExceptionList = orderInfoMapper.getStoreExceptionNum(req.getMerCode(), req.getOrganizationCodeList());
        if (!CollectionUtils.isEmpty(storeExceptionList)) {
            for (Map<String, Object> map : storeExceptionList) {
                String orgPlatStoreClient = (String) map.get("orgPlatStoreClient");
                Long num = (Long) map.get("num");
                resultHashMap.compute(orgPlatStoreClient, (k, v) -> {
                    if (v == null) {
                        v = new StoreOrderInfoSnapshotPageRsp();
                        String[] orgPlatStoreSplit = orgPlatStoreClient.split("-");
                        v.setOrganizationCode(orgPlatStoreSplit[0]);
                        v.setThirdPlatformCode(orgPlatStoreSplit[1]);
                        v.setOnlineStoreCode(orgPlatStoreSplit[2]);
                        v.setClientCode(orgPlatStoreSplit[3]);
                    }
                    v.setException(num);
                    return v;
                });
            }
        }
        // 退款中
        List<Map<String, Object>> orgRefundingList = orderInfoMapper.getStoreRefundingNum(req.getMerCode(), req.getOrganizationCodeList());
        if (!CollectionUtils.isEmpty(orgRefundingList)) {
            for (Map<String, Object> map : orgRefundingList) {
                String orgPlatStoreClient = (String) map.get("orgPlatStoreClient");
                Long num = (Long) map.get("num");
                resultHashMap.compute(orgPlatStoreClient, (k, v) -> {
                    if (v == null) {
                        v = new StoreOrderInfoSnapshotPageRsp();
                        String[] orgPlatStoreSplit = orgPlatStoreClient.split("-");
                        v.setOrganizationCode(orgPlatStoreSplit[0]);
                        v.setThirdPlatformCode(orgPlatStoreSplit[1]);
                        v.setOnlineStoreCode(orgPlatStoreSplit[2]);
                        v.setClientCode(orgPlatStoreSplit[3]);
                    }
                    v.setRefunding(num);
                    return v;
                });
            }
        }

        // 没查到的机构 返回空数据
        List<DsOnlineStore> merOrgPlatStoreList = dsOnlineStoreRepo.selectDistinctMerOrgPlatStore(req.getMerCode(), req.getOrganizationCodeList());
        Map<String, DsOnlineStore> integrateMap = merOrgPlatStoreList.stream().collect(Collectors.toMap(item -> item.getOrganizationCode() + "-" + item.getPlatformCode() + "-" + item.getOnlineStoreCode() + "-" + item.getOnlineClientCode(), item -> item, (a, b) -> a));
        List<String> orgPlatStoreClientList = integrateMap.keySet().stream().collect(Collectors.toList());
        if (!CollectionUtils.isEmpty(orgPlatStoreClientList)) {
            for (String orgPlatStoreClient : orgPlatStoreClientList) {
                resultHashMap.compute(orgPlatStoreClient, (k, v) -> {
                    DsOnlineStore store = integrateMap.get(orgPlatStoreClient);
                    if (v == null) {
                        v = new StoreOrderInfoSnapshotPageRsp();
                        v.setOrganizationCode(store.getOrganizationCode());
                        v.setOrganizationName(store.getOrganizationName());
                        v.setThirdPlatformCode(store.getPlatformCode());
                        v.setThirdPlatformName(PlatformCodeEnum.getByCode(store.getPlatformCode()).getType());
                        v.setOnlineStoreCode(store.getOnlineStoreCode());
                        v.setOnlineStoreName(store.getOnlineStoreName());
                    } else {
                        v.setOrganizationName(store.getOrganizationName());
                        v.setOnlineStoreName(store.getOnlineStoreName());
                        v.setThirdPlatformName(PlatformCodeEnum.getByCode(store.getPlatformCode()).getType());
                    }
                    return v;
                });
            }
        }

        Stream<StoreOrderInfoSnapshotPageRsp> stream = resultHashMap.values().stream().filter(item -> !StringUtils.isEmpty(item.getOnlineStoreCode()));

        if (req.getSortField() != null && req.getSortSequence() != null && snapshotComp.getSequence(req.getSortField(), req.getSortSequence()) != null) {
            stream = stream.sorted(snapshotComp.getSequence(req.getSortField(), req.getSortSequence()));
        } else {
            stream = stream.sorted(snapshotComp.getSequence("waitSale", "desc"));
        }

        // 保底查
        AtomicLong id = new AtomicLong(0);
        stream = stream.map((item) -> {
            if (StringUtils.isEmpty(item.getOrganizationName())) {
                DsOnlineStore dsOnlineStore = dsOnlineStoreRepo.selectStore(req.getMerCode(), item.getThirdPlatformCode(), item.getOnlineStoreCode(), item.getClientCode());
                if (dsOnlineStore != null) {
                    item.setThirdPlatformName(dsOnlineStore.getPlatformName());
                    item.setOnlineStoreName(dsOnlineStore.getOnlineStoreName());
                }
            }
            item.setId(id.incrementAndGet());
            return item;
        });

        List<StoreOrderInfoSnapshotPageRsp> collect = stream.collect(Collectors.toList());

        Page<StoreOrderInfoSnapshotPageRsp> response = new Page<>();
        response.setTotal(collect.size());
        List<StoreOrderInfoSnapshotPageRsp> result = collect.stream().skip((req.getCurrentPage() - 1) * req.getPageSize()).limit(req.getPageSize()).collect(Collectors.toList());
        response.setSize(req.getPageSize());
        response.setCurrent(req.getCurrentPage());
        response.setSearchCount(true);
        response.setRecords(result);
        return response;
    }

    Sequence<OrgOrderInfoSnapshotPageRsp> snapshotComp = Sequence.create(new Sequence<>(), (v) -> {
        v.initComp(OrgOrderInfoSnapshotPageRsp::getWaitSale, Comparator.comparing(OrgOrderInfoSnapshotPageRsp::getWaitSale));
        v.initComp(OrgOrderInfoSnapshotPageRsp::getWaitPick, Comparator.comparing(OrgOrderInfoSnapshotPageRsp::getWaitPick));
        v.initComp(OrgOrderInfoSnapshotPageRsp::getWaitPost, Comparator.comparing(OrgOrderInfoSnapshotPageRsp::getWaitPost));
        v.initComp(OrgOrderInfoSnapshotPageRsp::getPosting, Comparator.comparing(OrgOrderInfoSnapshotPageRsp::getPosting));
        v.initComp(OrgOrderInfoSnapshotPageRsp::getException, Comparator.comparing(OrgOrderInfoSnapshotPageRsp::getException));
        v.initComp(OrgOrderInfoSnapshotPageRsp::getRefunding, Comparator.comparing(OrgOrderInfoSnapshotPageRsp::getRefunding));
        v.initComp(OrgOrderInfoSnapshotPageRsp::getRefundWaitSale, Comparator.comparing(OrgOrderInfoSnapshotPageRsp::getRefundWaitSale));
    });

    private void orderHavePartRefundActive(OrderInfo orderInfo) {
        if (!PlatformCodeEnum.YD_JIA.getCode().equals(orderInfo.getThirdPlatformCode())) {
            return;
        }
        QueryWrapper<RefundOrder> refundOrderQueryWrapper = new QueryWrapper<>();
        // 已拒绝、已取消
        List<Integer> refundStateList = Arrays.asList(RefundStateEnum.REFUSED.getCode(), RefundStateEnum.CANCEL.getCode());
        refundOrderQueryWrapper.lambda().eq(RefundOrder::getOrderNo, orderInfo.getOrderNo()).notIn(RefundOrder::getState, refundStateList);
        List<RefundOrder> refundOrderList = refundOrderMapper.selectList(refundOrderQueryWrapper);
        if (CollectionUtils.isEmpty(refundOrderList)) {
            return;
        }
        throw ExceptionUtil.getWarnException(DsErrorType.ALL_REFUND_HAVE_PART_REFUND_ACTIVE_ERROR);
    }

    private void checkMedicalInsurance(OrderInfo orderInfo) {
        boolean medicalInsuranceNotCurrentFlagBoolean = DsConstants.INTEGER_ONE.equals(orderInfo.getMedicalInsurance()) && !DateUtil.isToday(orderInfo.getCreated());
        if (medicalInsuranceNotCurrentFlagBoolean) {
            throw ExceptionUtil.getWarnException(DsErrorType.PARAM_VALID_ERROR.getCode(), "医保云订单只允许当天退款！");
        }
    }

    @Override
    public List<Long> searchOrderForPrintBatch(OrderQuerySearchReqDto orderPageReqDto, List<String> organizationCodeList, List<String> platformCodeList) {
        return orderInfoMapper.searchOrderForPrintBatch(orderPageReqDto, organizationCodeList, platformCodeList);
    }

    @Override
    public void exOrderForceAuditStatus(String merCode, String userId, OrderHandleReqDto orderHandleReqDto) {
        SysEmployeeResDTO sysEmployeeResDTO = baseInfoService.getEmployeeInfo(userId);
        if (sysEmployeeResDTO == null || sysEmployeeResDTO.getEmpId() == null) {
            throw ExceptionUtil.getWarnException(DsErrorType.USER_NOT_EXIST_ERROR);
        }
        OrderInfo orderInfo = orderBasicService.getOrderBaseWithCheck(orderHandleReqDto.getOrderNo());
        /*if (!OrderStateEnum.UN_PICK.getCode().equals(orderInfo.getOrderState())) {
            throw ExceptionUtil.getWarnException(DsErrorType.STATE_NOT_PERMITTED.getCode(), "非待拣货状态不允许强审");
        }*/
        boolean isLockStock = OrderLockFlagEnum.LOCK_LACK_STOCK.getCode().equals(orderInfo.getLockFlag());
      // 强审的异常类型： 库存不足、商品不存在
        if (!OrderLockFlagEnum.LOCK_LACK_STOCK.getCode().equals(orderInfo.getLockFlag())
                && !OrderLockFlagEnum.LOCK_COMMODITY_NOT_EXIST.getCode().equals(orderInfo.getLockFlag())
                && !OrderLockFlagEnum.LOCK_MONEY_EX_UN_TAKE.getCode().equals(orderInfo.getLockFlag())
                && !OrderLockFlagEnum.LOCK_DELIVERY_EX_UN_TAKE.getCode().equals(orderInfo.getLockFlag())
                && !OrderLockFlagEnum.LOCK_SHOP_EX.getCode().equals(orderInfo.getLockFlag())
                && !OrderLockFlagEnum.CFCHECK_NOT_PASSS.getCode().equals(orderInfo.getLockFlag())
                && !OrderLockFlagEnum.LOCK_COMMODITY_NOT_SALL.getCode().equals(orderInfo.getLockFlag())
                && !OrderLockFlagEnum.SYS_ERROR.getCode().equals(orderInfo.getLockFlag())
                //商品不存在异常分类
                && !OrderLockFlagEnum.LOCK_ERP_CODE_NULL.getCode().equals(orderInfo.getLockFlag())
                && !OrderLockFlagEnum.LOCK_THIRD_COMMODITY_UNMATCH.getCode().equals(orderInfo.getLockFlag())
                && !OrderLockFlagEnum.LOCK_OFFLINE_COMMODITY_NOT_EXSIST.getCode().equals(orderInfo.getLockFlag())
        ) {
            throw ExceptionUtil.getWarnException(DsErrorType.STATE_NOT_PERMITTED.getCode(), "该异常状态不允许强审");
        }

        List<OrderDetail> detailList = orderBasicService.getOrderDetailListWithCheck(orderInfo.getOrderNo());
        //更新商品状态
        for (OrderDetail temp : detailList) {
            orderDetailMapper.updateById(new OrderDetail().setId(temp.getId()).setStatus(OrderDetailStatusEnum.NORMAL.getCode()));
        }

        // 更新订单信息
        OrderInfo update = new OrderInfo();
        update.setOrderNo(orderInfo.getOrderNo());
        update.setLockFlag(OrderLockFlagEnum.NOT_LOCK.getCode());
        update.setLockMsg("强审通过");
        // 强审通过是 不要改变订单状态，会使得处方单状态错乱
        //update.setOrderState(OrderStateEnum.UN_PICK.getCode());
        if (orderInfo.getLockFlag() >= OrderLockFlagEnum.LOCK_EXCEPTION_MIN_CODE.getCode()) {
            update.setExOperatorId(sysEmployeeResDTO.getEmpId());
            update.setExOperatorName(sysEmployeeResDTO.getEmpName());
            update.setExOperatorTime(new Date());
        }
        int num = orderInfoMapper.updateOrderWithState(update, orderInfo.getOrderState());
        //如果是微商城的自提库存不足订单 需要做提货通知
        OrderDeliveryRecord orderDeliveryRecord = orderDeliveryRecordMapper.selectByOrderNo(orderInfo.getOrderNo());
        if(Objects.nonNull(orderDeliveryRecord) && PlatformCodeEnum.YD_JIA.getCode().equals(orderInfo.getThirdPlatformCode()) && DeliveryTypeEnum.BUYER_SELF.getCode().equals(orderDeliveryRecord.getDeliveryType()) && isLockStock){
            thirdPlatformService.storeSelfPickMsg(orderInfo.getMerCode(), orderInfo.getThirdPlatformCode(), orderInfo.getClientCode(), orderInfo.getThirdOrderNo(), orderDeliveryRecord.getDeliveryType());
        }
        //日志打印
        if (num > 0) {
            //订单更新异步消费
            messageProducerService.produceUpdateOrderMessage(Collections.singletonList(orderInfo.getOrderNo()), OrderUpdateCodeEnum.FORCE_AUDIT.getCode());
            // 订单日志
            SysEmployeeResDTO employeePickOperator = baseInfoManager.getEmployeeInfoForErpNoEx(userId);
            HydeeEsSyncClientAsync.orderLogToES(DsConstants.INTEGER_ONE, merCode, orderInfo.getOrderNo(), orderInfo.getOrderState(), orderInfo.getErpState(), OrderLogEnum.STRONG_AUDIT.getAction(), "强制" + OrderLogEnum.STRONG_AUDIT.getInfo(), employeePickOperator);
        }
        try {
            commodityStockService.lockStockNew(orderInfo, detailList, "异常订单强审再次锁定6,7库存");
        } catch (Exception e) {
            log.info("异常订单强审再次锁定6,7库存失败{}", e.getMessage());

        }
    }

    @Override
    public List<OrderStoreRiderResulDto> getAllInDistributionOrder(List<Long> orderNos) {
        return orderInfoMapper.getAllInDistributionOrder(orderNos);
    }

    @Override
    public List<ErpOrderInfoRsp> getErpQueryInfo(ErpOrderQueryReqDto req) {
        if (req.getOrderNoList().size() > 200) {
            throw ExceptionUtil.getWarnException(DsErrorType.ERP_ORDER_OVER_ERROR);
        }
        List<Long> orderNoList = new ArrayList<>();
        List<String> merCodeList = new ArrayList<>();
        req.getOrderNoList().forEach(item -> {
            try {
                orderNoList.add(Long.parseLong(item.getOrderNo()));
                merCodeList.add(item.getMerCode());
            } catch (Exception e) {
                log.error("trans orderNo error ,req:{}", JSONObject.toJSONString(req), e);
            }
        });
        List<OrderInfo> orderInfoList = orderInfoMapper.getErpQueryInfo(req, orderNoList, merCodeList);
        return buildResult(orderInfoList);
    }

    @Override
    public List<ErpOrderInfoRsp> queryOrderBaseInfo(OrderQueryReqDto req) {
        if (null != req.getOrderNoList() && req.getOrderNoList().size() > 200) {
            throw ExceptionUtil.getWarnException(DsErrorType.ERP_ORDER_OVER_ERROR);
        }
        if (null != req.getThirdOrderNoList() && req.getThirdOrderNoList().size() > 200) {
            throw ExceptionUtil.getWarnException(DsErrorType.ERP_ORDER_OVER_ERROR);
        }
        QueryWrapper<OrderInfo> qw = new QueryWrapper<>();
        qw.lambda().eq(OrderInfo::getMerCode, req.getMerCode());
        //非A即B  防止没有传查询数据过多
        if (!CollectionUtils.isEmpty(req.getThirdOrderNoList())) {
            qw.lambda().in(OrderInfo::getThirdOrderNo, req.getThirdOrderNoList());
        } else {
            qw.lambda().in(OrderInfo::getOrderNo, req.getOrderNoList());
        }
        List<OrderInfo> orderInfoList = orderInfoMapper.selectList(qw);
        return buildResult(orderInfoList);
    }

    //构建返回结果集
    private List<ErpOrderInfoRsp> buildResult(List<OrderInfo> orderInfoList) {
        List<ErpOrderInfoRsp> results = new ArrayList<>();
        if (!CollectionUtils.isEmpty(orderInfoList)) {
            for (OrderInfo orderInfo : orderInfoList) {
                ErpOrderInfoRsp rsp = new ErpOrderInfoRsp();
                if (null != orderInfo.getCreated()) {
                    rsp.setCreated(DateUtil.parseDateToStr(orderInfo.getCreated(), DateUtil.DATE_TIME_FORMAT_YYYY_MM_DD_HH_MI_SS));
                }
                rsp.setErpAdjustNo(orderInfo.getErpAdjustNo());
                rsp.setErpState(orderInfo.getErpState());
                rsp.setOrderNo(String.valueOf(orderInfo.getOrderNo()));
                rsp.setOrderState(orderInfo.getOrderState());
                rsp.setThirdOrderNo(orderInfo.getThirdOrderNo());
                rsp.setOrganizationCode(orderInfo.getOrganizationCode());
                results.add(rsp);
            }
        }
        return results;
    }

    @Override
    public OrderInfo getOrderByClinetCode(String merCode, String clientCode) {
        return dsOnlineClientRepo.validClientOrderExists(merCode, clientCode);
    }


    /**
     * 基于订单号同步经营分析数据
     */
    @Override
    public boolean analysisPayInfoData(Long orderNo) {

        List<RefundOrder> refundOrderList = refundOrderMapper.selectAllRefundByOrderNo(orderNo);

        //同步order_refund_statistics数据
        if (CollectionUtil.isNotEmpty(refundOrderList)) {
            SpringUtil.getBean(OrderRefundStatisticsServiceImpl.class).statisticsHasSaleRefundOrderRefundAmount(refundOrderList);
            //orderRefundStatisticsService.statisticsHasSaleRefundOrderRefundAmount(refundOrderList);
            List<Long> orderNos = new ArrayList<>();
            orderNos.add(orderNo);
            orderRefundBillStatisticsService.calculateBillRefundOrderRefundAmountByOrderNo(orderNos);
            commodityHandlerManager.saveCommodityAveragePriceAfterRefundBill(orderNo);
        }
        //经营数据同步到Kafka
        return orderDorisService.pushMessage2KafkaOMSByOrderNo(orderNo);

    }


    public void importStoreOrderToAssigmentV2(List<String> storeCodeList, String startCreateDate, String endCreateDate) {

        ThreadUtils.doSempColl(storeCodeList, storeCode -> {
            int i = 1;
            try {
                Integer totalCount = orderInfoMapper.queryCountForAnalysis(storeCode, startCreateDate, endCreateDate);
                if (totalCount == 0) {
                    log.info("门店{}没有适合同步的订单", storeCode);
                    return;
                }

                int stepLength = 500;
                int totalPages = totalCount / stepLength + 1;

                for (; i <= totalPages; i++) {
                    Page<OrderInfo> page = new Page<>(i, stepLength);
                    List<AssignmentDTO> assignmentDTOList = orderInfoMapper.pageQueryOrderInforForAnalysis(page, storeCode, startCreateDate, endCreateDate).getRecords()
                            .stream().map(p -> AssignmentDTO.builder()
                                    .ukId(p.getOnlineStoreCode() + "_" + p.getOrderNo().toString())
                                    .reqContent(p.getOrderNo().toString())
                                    .build())
                            .collect(Collectors.toList());

                    if (CollectionUtil.isEmpty(assignmentDTOList)) {
                        break;
                    }

                    assignmentEngine.createAssignmentBatch(AssignmentBizType.OPERATE_ANALYSIS_TO_DORIS, assignmentDTOList);

                }

                log.info("已完成门店{}的assignment数据落库，总数量：{}", storeCode, totalCount);
            } catch (Exception e) {
                log.error("经营分析同步，门店抓取订单异常：{}，Exception message：{}，currentPageIndex：{}", storeCode, e.getMessage(), i);
            }

        }, 10);

    }

    /**
     * 获取骑手轨迹展示的H5页面URL
     *
     * @param orderNo 订单号，用于查询订单信息
     * @return 返回骑手轨迹展示的H5页面的URL。如果无法获取相关信息，则返回空字符串。
     */
    public String getRiderTrailH5Url(Long orderNo) {
        // 获取订单信息
        List<OrderStoreRiderResulDto> riderResultDtoList = getAllInDistributionOrder(Collections.singletonList(orderNo));
        if (CollectionUtils.isEmpty(riderResultDtoList)) {
            throw new OmsException("无效信息");
        }
        // 只有 美团 达达 顺丰 才有轨迹展示页面
        List<String> vaildDelivery= new ArrayList<>();
        vaildDelivery.add(DeliveryPlatformEnum.DADA.getCode());
        vaildDelivery.add(DeliveryPlatformEnum.SFTC.getCode());
        vaildDelivery.add(DeliveryPlatformEnum.MEITUAN_RIDER.getCode());

        OrderStoreRiderResulDto riderResultDto = riderResultDtoList.get(0);

        if(!vaildDelivery.contains(DeliveryPlatformEnum.getByName(riderResultDto.getDeliveryPlatName()).getCode())){
            throw new OmsException("目前只支持美团骑手、达达、顺丰查看骑手轨迹。");
        }

        // 获取接口参数加密密钥
        Map<String, String> sessionKeyMap = merchantGroupInfoService.querySessionKeyByMerCodeList(Collections.singletonList(riderResultDto.getMerCode()));
        if (sessionKeyMap.isEmpty()) {
            throw new OmsException("无效信息");
        }
        String sessionKey = sessionKeyMap.get(riderResultDto.getMerCode());


        // 获取骑手轨迹展示的H5 页面 url
        String url= netHttpAdapter.getRiderTrailH5Url(riderResultDto.getRiderOrderNo(), riderResultDto.getMerCode(),
                DeliveryPlatformEnum.getByName(riderResultDto.getDeliveryPlatName()).getCode(), riderResultDto.getDeliveryClientCode(), sessionKey,riderResultDto.getOnlineStoreCode());

        if(StringUtils.isEmpty(url)){
            throw new OmsException("轨迹信息获取失败");
        }
        return url;

    }

    @Transactional
    @Override
    public ReceiverInfoRsp getReceiverInfo(String userId,Long orderNo) {
        SysEmployeeResDTO sysEmployeeResDTO = baseInfoService.getEmployeeInfo(userId);
        //查询收货信息
        OrderDeliveryAddress orderDeliveryAddress = orderDeliveryAddressMapper.selectByOrderNo(orderNo);
        if (Objects.isNull(orderDeliveryAddress)) {
            throw ExceptionUtil.getWarnException(ErrorType.OPERATOR_ERROR.getCode(), "订单收货信息不存在");
        }
        OrderInfo orderInfo = orderInfoMapper.selectOrderInfo(orderNo);
        try {
            if (!orderDeliveryAddress.checkReceiveDecryptExist() && PlatformCodeEnum.MEITUAN.getCode()
                .equals(orderInfo.getThirdPlatformCode())) {
                //不存在请求解密接口
                Map<String, String> sessionKeyMap = merchantGroupInfoService.querySessionKeyByMerCodeList(
                    Collections.singletonList(DsConstants.MER_CODE_YXT));
                if (sessionKeyMap.isEmpty()) {
                    throw new OmsException("获取sessionKey失败");
                }
                String sessionKey = sessionKeyMap.get(DsConstants.MER_CODE_YXT);
                List<String> privacyList = Arrays.asList(orderDeliveryAddress.getReceiverNamePrivacy(),
                    orderDeliveryAddress.getReceiverAddressPrivacy(), orderDeliveryAddress.getReceiverPhonePrivacy());
                List<ReceiverDecryptInfoDto> receiverDecryptInfoDtos = netHttpAdapter.decryptReceiverInfo(privacyList,
                    orderNo, OrderChangeSelfDeliveryTypeEnum.MANUAL.name(), DsConstants.MER_CODE_YXT,
                    orderInfo.getThirdPlatformCode(), orderInfo.getClientCode(), sessionKey, orderInfo.getOnlineStoreCode(), orderInfo.getThirdOrderNo());
                orderDeliveryAddress.setDecryptInfo(receiverDecryptInfoDtos);
                //保存入库
                orderDeliveryAddressMapper.updateRecord(orderDeliveryAddress);
                //orderInfo解密状态
                upReceiverDecryptState(CommonStateEnum.SUCCESS.name(), orderNo);
                //解密 系统自己的Aes加密  更新操作会将更新值覆盖
                orderDeliveryAddress.setReceiverName(AESUtils.decrypt(orderDeliveryAddress.getReceiverName()));
                orderDeliveryAddress.setReceiverMobile(AESUtils.decrypt(orderDeliveryAddress.getReceiverMobile()));
                orderDeliveryAddress.setReceiverTelephone(
                    AESUtils.decrypt(orderDeliveryAddress.getReceiverTelephone()));
                orderDeliveryAddress.setFullAddress(AESUtils.decrypt(orderDeliveryAddress.getFullAddress()));
                orderDeliveryAddress.setAddress(AESUtils.decrypt(orderDeliveryAddress.getAddress()));

            }
            //获取redis 中改用户查看次数
            Integer viewReceiverNum = orderInfoRedisManager.getViewReceiverNum(userId);
            if (viewReceiverNum > this.viewReceiverInfoNum) {
                throw ExceptionUtil.getWarnException(ErrorType.OPERATOR_ERROR.getCode(),
                    "您已查看过" + this.viewReceiverInfoNum + "次，请明天再查看");
            }
            //记录操作日志
            HydeeEsSyncClientAsync.orderLogToES(DsConstants.INTEGER_ONE, orderInfo.getMerCode(), orderNo,
                orderInfo.getOrderState(), orderInfo.getErpState(),
                OrderLogEnum.RECEIVER_DECRYPT.getAction(), "查看成功", sysEmployeeResDTO);
            return BeanUtil.toBean(orderDeliveryAddress, ReceiverInfoRsp.class);
        } catch (Exception e) {
            HydeeEsSyncClientAsync.orderLogToES(DsConstants.INTEGER_ONE, orderInfo.getMerCode(), orderNo,
                orderInfo.getOrderState(), orderInfo.getErpState(),
                OrderLogEnum.RECEIVER_DECRYPT.getAction(), "查看失败", sysEmployeeResDTO);
            throw ExceptionUtil.getWarnException(ErrorType.OPERATOR_ERROR.getCode(), e.getMessage());
        }
    }

    @Override
    public ReceiverInfoRsp getReceiverInfoForRefund(String userId, Long refundNo) {
        RefundOrder refundOrder = refundOrderMapper.selectByRefundNo(refundNo);
        //查询收货信息
        OrderDeliveryAddress orderDeliveryAddress = orderDeliveryAddressMapper.selectByOrderNo(refundOrder.getOrderNo());
        if (Objects.isNull(orderDeliveryAddress)) {
            throw ExceptionUtil.getWarnException(ErrorType.OPERATOR_ERROR.getCode(), "订单收货信息不存在");
        }
        OrderInfo orderInfo = orderInfoMapper.selectOrderInfo(refundOrder.getOrderNo());
        try {
            if (!orderDeliveryAddress.checkReceiveDecryptExist() && PlatformCodeEnum.MEITUAN.getCode()
                .equals(orderInfo.getThirdPlatformCode())) {
                //不存在请求解密接口
                Map<String, String> sessionKeyMap = merchantGroupInfoService.querySessionKeyByMerCodeList(
                    Collections.singletonList(DsConstants.MER_CODE_YXT));
                if (sessionKeyMap.isEmpty()) {
                    throw new OmsException("获取sessionKey失败");
                }
                String sessionKey = sessionKeyMap.get(DsConstants.MER_CODE_YXT);
                List<String> privacyList = Arrays.asList(orderDeliveryAddress.getReceiverNamePrivacy(),
                    orderDeliveryAddress.getReceiverAddressPrivacy(), orderDeliveryAddress.getReceiverPhonePrivacy());
                List<ReceiverDecryptInfoDto> receiverDecryptInfoDtos = netHttpAdapter.decryptReceiverInfo(privacyList,
                    refundOrder.getOrderNo(), OrderChangeSelfDeliveryTypeEnum.MANUAL.name(), DsConstants.MER_CODE_YXT,
                    orderInfo.getThirdPlatformCode(), orderInfo.getClientCode(), sessionKey, orderInfo.getOnlineStoreCode(), orderInfo.getThirdOrderNo());
                orderDeliveryAddress.setDecryptInfo(receiverDecryptInfoDtos);
                //保存入库
                orderDeliveryAddressMapper.updateRecord(orderDeliveryAddress);
                //orderInfo解密状态
                upReceiverDecryptState(CommonStateEnum.SUCCESS.name(), refundOrder.getOrderNo());
                //解密 系统自己的Aes加密  更新操作会将更新值覆盖
                orderDeliveryAddress.setReceiverName(AESUtils.decrypt(orderDeliveryAddress.getReceiverName()));
                orderDeliveryAddress.setReceiverMobile(AESUtils.decrypt(orderDeliveryAddress.getReceiverMobile()));
                orderDeliveryAddress.setReceiverTelephone(
                    AESUtils.decrypt(orderDeliveryAddress.getReceiverTelephone()));
                orderDeliveryAddress.setFullAddress(AESUtils.decrypt(orderDeliveryAddress.getFullAddress()));
                orderDeliveryAddress.setAddress(AESUtils.decrypt(orderDeliveryAddress.getAddress()));

            }
            //获取redis 中改用户查看次数
            Integer viewReceiverNum = orderInfoRedisManager.getViewReceiverNum(userId);
            if (viewReceiverNum > this.viewReceiverInfoNum) {
                throw ExceptionUtil.getWarnException(ErrorType.OPERATOR_ERROR.getCode(),
                    "您已查看过" + this.viewReceiverInfoNum + "次，请明天再查看");
            }
            //记录操作日志
            HydeeEsSyncClientAsync.refundLogToES(DsConstants.INTEGER_ONE,refundOrder,RefundLogEnum.RECEIVER_DECRYPT,null,"查看顾客信息成功",userId);
            return BeanUtil.toBean(orderDeliveryAddress, ReceiverInfoRsp.class);
        } catch (Exception e) {
            HydeeEsSyncClientAsync.refundLogToES(DsConstants.INTEGER_ZERO,refundOrder,RefundLogEnum.RECEIVER_DECRYPT,null,"查看顾客信息失败",userId);
            throw ExceptionUtil.getWarnException(ErrorType.OPERATOR_ERROR.getCode(), e.getMessage());
        }
    }

    /***
     * 更新扩展字段中收货信息解密字段
     * */
    public void upReceiverDecryptState(String state, Long orderNo) {
        OrderInfo orderInfo = orderInfoMapper.selectOrderInfo(orderNo);
        String extendInfo = orderInfo.getExtendInfo();
        if (org.apache.commons.lang3.StringUtils.isNotBlank(extendInfo)) {
            OrderInfoExtendInfo orderInfoExtendInfo = JSON.parseObject(extendInfo, OrderInfoExtendInfo.class);
            //状态不同才更新
            if (orderInfoExtendInfo != null && state.equals(orderInfoExtendInfo.getReceiverDecryptState())) {
                orderInfoExtendInfo.setReceiverDecryptState(state);
                OrderInfo upOrderInfo = new OrderInfo();
                upOrderInfo.setOrderNo(orderNo);
                upOrderInfo.setExtendInfo(JSON.toJSONString(orderInfoExtendInfo));
                orderInfoMapper.updateOrder(upOrderInfo);
            }
        }
    }
    @Override
    public void importStoreOrderToAssigment(String sql) {
        QueryWrapper<OrderInfo> queryWrapper = new QueryWrapper<>();
        queryWrapper.lambda().eq(OrderInfo::getServiceMode, "O2O");
        queryWrapper.last(sql);
        String sqlSelect = queryWrapper.getSqlSelect();
        log.info("经营分析---sqlSelect：{}", sqlSelect);
        int count = orderInfoMapper.selectCount(queryWrapper);
        if (count == 0) {
            return;
        }
        int currentPage = 1;
        int pageSize = 500;
        int totalPageStep = (int) Math.ceil(count * 1.0 / pageSize);
        while (totalPageStep >= currentPage) {
            IPage<OrderInfo> page = new Page<>(currentPage, pageSize);
            List<OrderInfo> records = orderInfoMapper.selectPage(page, queryWrapper).getRecords();
            List<AssignmentEngineAssignment> list = new CopyOnWriteArrayList<>();
            ThreadUtils.doSempColl(records, orderInfo -> {
                String thirdOrderNo = orderInfo.getThirdOrderNo();
                Long orderNo = orderInfo.getOrderNo();
                assignmentReportGateway.deleteAssignmentByBusinessId(AssignmentBizType.OPERATE_ANALYSIS_TO_DORIS, thirdOrderNo);
                Assignment assignment = AssignmentConverter.toDomainObject(AssignmentBizType.OPERATE_ANALYSIS_TO_DORIS, thirdOrderNo, orderNo.toString(), "");
                AssignmentEngineAssignment persistentObject = AssignmentEngineAssignmentConverter.toPersistentObject(assignment);
                list.add(persistentObject);
            }, 16);
            boolean success = assignmentEngine.batchCreateAssignment(list);
            if (!success) {
                throw ExceptionUtil.getErrorException(ErrorType.DEFAULT_TIP_EXCEPTION_MESSAGE);
            }
            currentPage++;
        }

    }

    private void getStoreTimestamp(List<OrderInfoPageRsp> orderInfoPageRspList) {
        if (null == orderInfoPageRspList || orderInfoPageRspList.isEmpty()) {
            return;
        }
        List<String> organizationCodes = orderInfoPageRspList.stream().map(OrderInfoPageRsp::getOrganizationCode).distinct().collect(Collectors.toList());
        Map<String, InnerStoreOpenAutoBillDto> map = new HashMap<>();
        organizationCodes.forEach(e -> map.put(e, innerStoreDictionaryQrtGateway.qryInnerStoreByOrganizationCode(e)));
        for (OrderInfoPageRsp orderInfoPageRsp : orderInfoPageRspList) {
            InnerStoreOpenAutoBillDto dto = map.get(orderInfoPageRsp.getOrganizationCode());
            orderInfoPageRsp.setIsOpenNew("");
            orderInfoPageRsp.setAutoBillTimestamp(null);
            if (null != dto) {
                orderInfoPageRsp.setIsOpenNew(dto.getIsOpenNew());
                orderInfoPageRsp.setAutoBillTimestamp(dto.getAutoBillTimestamp());
            }
        }
    }

    /** 校验下账时机 true 允许下账 false 不允许下账
     * <AUTHOR>
     * @Description
     * @date 2024/3/14 17:19
     */
    private static Boolean checkAutoEnterAccountFlag(StoreBillConfig storeBillConfig, Integer orderState) {
        if (Objects.isNull(storeBillConfig) || storeBillConfig.getAutoEnterAccountFlag() == null) {
            return Boolean.TRUE;
        }
        // 下账时机校验添加
        Integer autoEnterAccountFlag = storeBillConfig.getAutoEnterAccountFlag();

        // 拣货完成后下账 订单状态>待拣货
        // 允许下账
        if(AutoEnterAccountFlagEnum.PICK.getCode().equals(autoEnterAccountFlag) && orderState> OrderStateEnum.UN_PICK.getCode()){
            return Boolean.TRUE;
        }
        // 配送出库后下账 订单状态>待配送
        // 允许下账
        if(AutoEnterAccountFlagEnum.DELIVERY_OUT.getCode().equals(autoEnterAccountFlag) && orderState>OrderStateEnum.UN_DELIVERY.getCode()){
            return Boolean.TRUE;
        }
        return Boolean.FALSE;
    }

    /** 订单直接下发取消消息 生成运费单对应的逆向订单消息
     *  下账状态小于已下账直接修改为已取消
     * <AUTHOR>
     * @Description
     * @date 2024/3/20 15:29
     */
    @Transactional(rollbackFor = Exception.class, propagation = Propagation.NESTED)
    public void undoErpForCancel(OrderInfo update,Long freightOrderNo) {
        // 平台下发的订单状态
        Integer orderState = update.getOrderState();
        OrderStateEnum orderStateEnum = OrderStateEnum.getOrderState(orderState);

        OrderInfo freightOrder = orderInfoMapper.selectOrderInfo(freightOrderNo);
        if(Objects.isNull(freightOrder)){return;}
        update.setOrderState(freightOrder.getOrderState());
        switch (orderStateEnum){
            case CANCEL:
            case CLOSED:
                update.setOrderState(orderStateEnum.getCode());
                update.setErpState(freightOrder.getErpState());
                if(ErpStateEnum.HAS_SALE.getCode().equals(freightOrder.getErpState())){
                    SpringBeanUtils.getBean(RefundBasicService.class).createRefundSuccessful(freightOrder);
                }
                if (freightOrder.getErpState() < ErpStateEnum.HAS_SALE.getCode()) {
                    update.setErpState(ErpStateEnum.CANCELED.getCode());
                }
                break;
            case POSTING:
            case COMPLETED:
                if (freightOrder.getErpState() < ErpStateEnum.HAS_SALE.getCode()) {
                    update.setErpState(ErpStateEnum.WAIT_SALE.getCode());
                }
                break;
        }
        update.setOrderNo(freightOrderNo);
        orderInfoMapper.updateOrder(update);
    }


    private void merchantPartRefund(String merCode, NewOrderPartRefundReqDto orderHandleReqDto, OrderInfo orderInfo) {
        log.info("调用平台商家部分退款 merCode:{} orderHandleReqDto:{} orderNo:{}",merCode,JSONObject.toJSONString(orderHandleReqDto),orderInfo.getOrderNo());
        // 调用三方部分退款接口
        OnlineStoreInfoRspDto storeInfo = baseInfoService.getOnlineStoreInfo(orderInfo.getMerCode(), orderInfo.getThirdPlatformCode(), orderInfo.getClientCode(), orderInfo.getOnlineStoreCode());
        OrderPartRefundReq netPartRefundReqDto = new OrderPartRefundReq();
        netPartRefundReqDto.setOlOrderNo(orderInfo.getThirdOrderNo());
        netPartRefundReqDto.setDeliveryType(orderHandleReqDto.getDelivery_type());
        netPartRefundReqDto.setReason("商品缺货");
        List<OrderPartRefundReq.Refund> refundItemList = new ArrayList<>();
        for (NewOrderPartRefundReqDto.RefundItem e : orderHandleReqDto.getRefundItemList()) {
            OrderPartRefundReq.Refund refundItem = new OrderPartRefundReq.Refund();
            refundItem.setOutSkuId(e.getOuter_iid());
            refundItem.setSkuId(e.getNum_iid());
            refundItem.setCount(e.getPartCount().toString());
            refundItem.setOId(e.getOid());
            refundItemList.add(refundItem);
        }
        netPartRefundReqDto.setRefundList(refundItemList);
        netPartRefundReqDto.setOlShopId(orderInfo.getOnlineStoreCode());
        netPartRefundReqDto.setShopId(storeInfo.getOutShopId());
        netPartRefundReqDto.setReason("商家部分退款");
        HemsBaseData baseData = hemsCommonClient.constructHemsBaseData(merCode, storeInfo.getPlatformCode(), storeInfo.getOnlineClientCode(), storeInfo.getSessionKey());
        try {
            hemsCommonClient.merchantPartRefund(netPartRefundReqDto, baseData);
        }catch (Exception e){
            log.error("部分退款三方接口调用失败 orderNo:{} msg:{}",orderInfo.getOrderNo(),e.getMessage());
            throw e;
        }
    }


    private void partRefundOrderStatusCheck(OrderInfo orderInfo) {
        if(OrderLockFlagEnum.LOCK_REFUND.getCode().equals(orderInfo.getLockFlag())){
            throw ExceptionUtil.getWarnException(DsErrorType.REFUND_LOCK);
        }
        //必须是异常单
        // 缺货
        boolean flag = OrderLockFlagEnum.LOCK_LACK_STOCK.getCode().equals(orderInfo.getLockFlag()) || OrderLockFlagEnum.LOCK_COMMODITY_NOT_EXIST.getCode().equals(orderInfo.getLockFlag())
                //商品不存在异常分类
                || OrderLockFlagEnum.LOCK_ERP_CODE_NULL.getCode().equals(orderInfo.getLockFlag()) || OrderLockFlagEnum.LOCK_THIRD_COMMODITY_UNMATCH.getCode().equals(orderInfo.getLockFlag()) || OrderLockFlagEnum.LOCK_OFFLINE_COMMODITY_NOT_EXSIST.getCode().equals(orderInfo.getLockFlag());
        if (!flag) {
            throw ExceptionUtil.getWarnException(DsErrorType.STATE_NOT_PERMITTED);
        }
        //订单状态必须是发货前
        if(orderInfo.getOrderState().compareTo(OrderStateEnum.POSTING.getCode()) >= 0 && orderInfo.getOrderState().compareTo(OrderStateEnum.CLOSED.getCode())<=0){
            throw ExceptionUtil.getWarnException(DsErrorType.ORDER_DETAIL_STATUS_ERROR);
        }
    }

    private void partRefundOrderGoodsCountCheck(Long orderNo,NewOrderPartRefundReqDto orderHandleReqDto) {
        //获取detail的退款信息,加上现在的退款的得剩下一件
        List<OriThirdOrderDetail> oriThirdOrderDetails = oriThirdOrderDetailService.queryList(orderNo);
        Map<String, List<OriThirdOrderDetail>> oriDetailMap = oriThirdOrderDetails.stream().collect(Collectors.groupingBy(OriThirdOrderDetail::getNumIid));
        if(CollectionUtils.isEmpty(oriThirdOrderDetails)){
            //历史数据不让部分退款，下的新单可以
            throw ExceptionUtil.getWarnException(DsErrorType.THIRD_ORDER_PART_DETAIL_VERSION_ERROR);
        }
        List<NewOrderPartRefundReqDto.RefundItem> refundItemList = orderHandleReqDto.getRefundItemList();
        List<RefundDetail> refundDetailList = refundDetailMapper.queryByOrderNo(orderNo);
        Map<String, List<RefundDetail>> refundMap = refundDetailList.stream().collect(Collectors.groupingBy(RefundDetail::getThirdSkuId));
        for (NewOrderPartRefundReqDto.RefundItem refundItem : refundItemList) {
            List<RefundDetail> detailList = refundMap.get(refundItem.getNum_iid());
            List<OriThirdOrderDetail> oriThirdOrderDetailList = oriDetailMap.get(refundItem.getNum_iid());
            if(CollectionUtil.isEmpty(oriThirdOrderDetailList)){
                throw ExceptionUtil.getWarnException(DsErrorType.THIRD_ORDER_PART_DETAIL_ERROR);
            }
            if(oriThirdOrderDetailList.size()>1){
                //不可能有两个
                throw ExceptionUtil.getWarnException(DsErrorType.THIRD_ORDER_PART_DETAIL_GOODS_ERROR);
            }
            OriThirdOrderDetail oriThirdOrderDetail = oriThirdOrderDetailList.get(0);
            oriThirdOrderDetail.setRefundCount(refundCount(detailList)+refundItem.getPartCount());
        }
        for (OriThirdOrderDetail oriThirdOrderDetail : oriThirdOrderDetails) {
            Integer commodityCount = oriThirdOrderDetail.getNum();
            if(commodityCount.compareTo(oriThirdOrderDetail.getRefundCount())<0){
                //退多了，超出了商品数量
                throw ExceptionUtil.getWarnException(DsErrorType.PART_REFUND_COUNT_ERROR);
            }
        }
        for (OriThirdOrderDetail oriThirdOrderDetail : oriThirdOrderDetails) {
            Integer commodityCount = oriThirdOrderDetail.getNum();
            if(commodityCount.compareTo(oriThirdOrderDetail.getRefundCount())>0){
                //只要有一个有商品就行
                return;
            }
        }
        throw ExceptionUtil.getWarnException(DsErrorType.THIRD_ORDER_PART_DETAIL_COUNT_ERROR);
    }

    @Override
    public ThirdOrderInfoAllRsp thirdDetails(String merCode, String userId, OrderInfNoReq orderInfNoReq) {
        OrderInfo orderInfo = orderInfoMapper.selectOrderInfo(orderInfNoReq.getOrderNo());
        //校验当前订单状态
        partRefundOrderStatusCheck(orderInfo);
        OrderPayInfo orderPayInfo = orderPayInfoMapper.selectByOrderNo(orderInfNoReq.getOrderNo());

        ThirdOrderInfoAllRsp thirdOrderInfoAllRsp = new ThirdOrderInfoAllRsp();
        thirdOrderInfoAllRsp.setOrderPayInfo(orderPayInfo);
        OrderDeliveryAddress orderDeliveryAddress = orderDeliveryAddressMapper.selectByOrderNo(orderInfo.getOrderNo());
        thirdOrderInfoAllRsp.setOrderDeliveryAddress(orderDeliveryAddress);
        thirdOrderInfoAllRsp.setOrderInfo(orderInfo);

        List<OriThirdOrderDetail> oriThirdOrderDetails = oriThirdOrderDetailService.queryList(orderInfo.getOrderNo());

        if(CollectionUtil.isEmpty(oriThirdOrderDetails)){
            return thirdOrderInfoAllRsp;
        }

        OriThirdOrderDetail oriThirdOrderDetail = oriThirdOrderDetails.get(0);
        thirdOrderInfoAllRsp.setDelivery_type(oriThirdOrderDetail.getDeliveryType());
        List<RefundDetail> refundDetailList = refundDetailMapper.queryByOrderNo(orderInfNoReq.getOrderNo());

        List<ThirdOrderDetailRspDto> thirdOrderDetailRspDtoList = getThirdPartRefundOrderDetail(orderInfo,oriThirdOrderDetails,refundDetailList);

        thirdOrderInfoAllRsp.setThirdOrderDetailRspDtoList(thirdOrderDetailRspDtoList);
        return thirdOrderInfoAllRsp;
    }

    @Override
    public void batchPickByStockError(OrderLedgerPageReqDto req,List<String> organizatioinList,List<String> platformList,String userId) {
        req.setAccountFailMessage(AccountFialEnum.STOCK_ERROR.getMsg());
        OrderErpListDtoReq orderErpListDtoReq = new OrderErpListDtoReq();
        orderErpListDtoReq.setErpStateList(Lists.newArrayList(ErpStateEnum.HAS_SALE_FAIL.getCode()));
        req.setBillState(orderErpListDtoReq);
        //缓存处理
        String value = redisTemplate.opsForValue().get(RedisKeyUtil.getRedisKeyBatchPickFailKey(req.getMd5Hash()));
        if (org.apache.commons.lang3.StringUtils.isNotBlank(value)) {
            throw ExceptionUtil.getWarnException(DsErrorType.BATCH_PICK_FAIL_DEALING);
        }
        redisTemplate.opsForValue().set(RedisKeyUtil.getRedisKeyBatchPickFailKey(req.getMd5Hash()), "1", 5, TimeUnit.MINUTES);
        List<Long> orderNos = orderInfoMapper.getOrderNoLedgerList(req, organizatioinList, platformList);
        // 获取所有库存不足且下账失败的订单
        if (CollUtil.isEmpty(orderNos)) {
            throw ExceptionUtil.getWarnException(ErrorType.RECORD_NOT_EXISTS.getCode(),"批量处理失败，无库存不足且下账失败的订单！");
        }
        // 批量处理
        this.batchHandle(orderNos,userId);
        redisTemplate.delete(RedisKeyUtil.getRedisKeyBatchPickFailKey(req.getMd5Hash()));
    }

    /**
     * 批量拣货复核
     * @param orderNos
     */
    private void batchHandle(List<Long> orderNos,String userId) {
        SysEmployeeResDTO sysEmployeeResDTO = baseInfoService.getEmployeeInfo(userId);
        List<OrderInfo> list = orderInfoMapper.selectList(new LambdaQueryWrapper<OrderInfo>().in(OrderInfo::getOrderNo, orderNos).orderByAsc(OrderInfo::getCreated));
        if (CollUtil.isEmpty(list)) {
            throw ExceptionUtil.getWarnException(ErrorType.RECORD_NOT_EXISTS.getCode(),"批量处理失败，未查询到对应订单！");
        }
        Map<String, List<OrderInfo>> pickInfoMap = list.stream().collect(Collectors.groupingBy(OrderInfo::getOrganizationCode));
        List<String> successBatchOrderNos = new ArrayList<>();
        for (Map.Entry<String, List<OrderInfo>> stringListEntry : pickInfoMap.entrySet()) {
            String organizationCode = stringListEntry.getKey();
            // 重要前置处理 商品库存效期查询处理放入redis
            if (comeBefore(stringListEntry)) {
                log.warn("门店{}商品库存效期查询处理放入redis失败！  orderNo:{}", organizationCode,stringListEntry.getValue());
                continue;

            }

            stringListEntry.getValue().forEach(orderInfo -> {
                List<String> orderNoList = new ArrayList<>();
                orderNoList.add(String.valueOf(orderInfo.getOrderNo()));
                List<OrderDetail> orderDetails = orderDetailMapper.selectListByOrderNoV2(orderNoList, OrderDetailStatusEnum.NORMAL.getCode());
                if (CollUtil.isEmpty(orderDetails)) {
                    HydeeEsSyncClientAsync.orderLogToES(DsConstants.INTEGER_ONE,orderInfo.getMerCode(), orderInfo.getOrderNo(), orderInfo.getOrderState(),orderInfo.getErpState(),
                        OrderLogEnum.BATCH_HANDLE.getAction(), "订单无有效的商品明细！", sysEmployeeResDTO);
                    return;
                }
                if (Boolean.TRUE.equals(extracted(orderInfo, orderDetails, organizationCode, sysEmployeeResDTO))) {
                    //更新下账状态
                    LambdaUpdateWrapper<OrderInfo> updateOrderInfoWrapper = new LambdaUpdateWrapper<OrderInfo>()
                        .set(OrderInfo::getErpState, ErpStateEnum.WAIT_SALE.getCode())
                        .set(OrderInfo::getExtendInfo,null)
                        .eq(OrderInfo::getOrderNo, orderInfo.getOrderNo());
                    orderInfoMapper.update(null,updateOrderInfoWrapper);
                    HydeeEsSyncClientAsync.orderLogToES(DsConstants.INTEGER_ONE,orderInfo.getMerCode(), orderInfo.getOrderNo(), orderInfo.getOrderState(),orderInfo.getErpState(),
                        OrderLogEnum.BATCH_HANDLE.getAction(), "下账失败重置拣货批号成功！", sysEmployeeResDTO);
                    successBatchOrderNos.add(String.valueOf(orderInfo.getOrderNo()));

                }
            });
        }
        //延时执行推送下账
        if(CollUtil.isNotEmpty(successBatchOrderNos)){
            batchAccountDelayThreadPool.schedule(()->{
                log.info("批量成功成功,延时推送下账开始,订单号:{}",JSONObject.toJSONString(successBatchOrderNos));
                try {
                    successBatchOrderNos.forEach(SpringUtil.getBean(HdPosGatewayImpl.class)::orderAccounting);
                }catch (Exception e){
                    log.error("批量处理下账失败后,推送海典正单下账失败,请人为介入重新下账,失败原因:{}",JSONObject.toJSONString(e.getMessage()));
                }
            }, batchPickAccountDelayMinute, TimeUnit.MINUTES);
        }
    }

    /**
     * 批量发货前置
     * 前置校验
     * 商品查询
     * 库存校验
     * @param stringListEntry
     * @return
     */
    private boolean comeBefore(Map.Entry<String, List<OrderInfo>> stringListEntry) {
        String organizationCode = stringListEntry.getKey();
        List<OrderInfo> value = stringListEntry.getValue();
        List<String> unDeliveryOmsOrderNos = value.stream()
            .map(OrderInfo::getOrderNo).map(String::valueOf)
            .collect(Collectors.toList());
        CopyOnWriteArrayList<OrderDetail> allOrderDetails = new CopyOnWriteArrayList<>();
        // 打散查询商品信息
        scatter(unDeliveryOmsOrderNos, 100).forEach((x, y) -> {
            List<OrderDetail> orderDetails = orderDetailMapper.selectListByOrderNoV2(y,OrderDetailStatusEnum.NORMAL.getCode());
            if (CollUtil.isNotEmpty(orderDetails)) {
                allOrderDetails.addAll(orderDetails);
            }
        });
        if (CollUtil.isEmpty(allOrderDetails)) {
            return true;
        }
        Set<String> erpSet = allOrderDetails.stream()
            .map(OrderDetail::getErpCode)
            .collect(Collectors.toSet());
        // 打散查询商品中台库存 批号信息
        CopyOnWriteArrayList<CommInfoBatchNumberRespDto> erpList = new CopyOnWriteArrayList<>();
        scatter(new ArrayList<>(erpSet), 200).forEach((x, y) -> {
            List<CommInfoBatchNumberRespDto> dtoList = storeInventoryByCode(y, organizationCode);
            if (CollUtil.isNotEmpty(dtoList)) {
                erpList.addAll(dtoList);
            }
        });
        if (CollUtil.isEmpty(erpList)) {
            return true;
        }
        // 批号过滤 放入redis
        erpList.forEach(a -> {
            List<AllLotStockVO> batchNumStockList = a.getLotList();
            List<AllLotStockVO> collect = batchNumStockList.stream().filter(b -> Objects.nonNull(b.getStock()) && b.getStock() > 0 && Objects.nonNull(b.getDays()) && b.getDays() > days).collect(Collectors.toList());
            if (CollUtil.isNotEmpty(collect)) {
                String commodityCode = a.getErpCode();
                String redisKeyDeliverGoodsKey = RedisKeyUtil.getRedisKeyFailAccountOrderCommodityStock(organizationCode, commodityCode);
                String cacheJson = stringRedisTemplate.opsForValue().get(redisKeyDeliverGoodsKey);
                if (org.apache.commons.lang3.StringUtils.isEmpty(cacheJson)) {
                    int sum = collect.stream().mapToInt(AllLotStockVO::getStock).sum();
                    CommInfoBatchNumberRespDto dto = new CommInfoBatchNumberRespDto();
                    dto.setErpCode(commodityCode);
                    dto.setLotList(collect);
                    dto.setStockAll(sum);
                    // 放入 redis
                    stringRedisTemplate.opsForValue().set(redisKeyDeliverGoodsKey, JSON.toJSONString(dto), Duration.ofMinutes(10));
                }
            }
        });
        erpList.clear();
        allOrderDetails.clear();
        return false;
    }

    /**
     * 打散List集合，批量线程处理
     *
     * @param t        需要打散的集合
     * @param groupNum 按照数目将集合打散
     * @param <T>      传入T进行公共处理
     */
    public static <T> ConcurrentMap<Integer, List<T>> scatter(List<T> t, int groupNum) {
        BlockingQueue<T> queueList = new LinkedBlockingQueue<>(t);
        int merchant = queueList.size() / groupNum;
        int remainder = queueList.size() % groupNum;
        int count = 0;
        if (merchant == 0) {
            count++;
        } else if (remainder == 0) {
            count = merchant;
        } else {
            count = merchant + 1;
        }

        ConcurrentHashMap<Integer, List<T>> result = MapUtil.newConcurrentHashMap();
        for (int j = 0; j < count; j++) {
            List<T> arrayList = new ArrayList<>();
            queueList.drainTo(arrayList, groupNum);
            result.put(j, arrayList);
        }
        return result;
    }

    private List<CommInfoBatchNumberRespDto> storeInventoryByCode(List<String> erpCodeList, String storeCode) {
        try {
            StoreInventoryReqDto dto = new StoreInventoryReqDto();
            dto.setStoreCode(storeCode);
            dto.setCommodityCodeList(erpCodeList);
            ResponseBase<List<CommInfoBatchNumberRespDto>> response = middleMerchandiseClient.batchQueryLotNum(dto);
            log.info("查询商品中台库存,code:{},response:{}", erpCodeList, JSON.toJSONString(response));
            if (response.checkSuccess() && CollUtil.isNotEmpty(response.getData())) {
                return response.getData();
            }
        } catch (Exception e) {
            log.error("查询商品中台库存失败,errorMsg:{}", e.toString());
        }
        return Collections.emptyList();
    }

    private Boolean extracted(OrderInfo orderInfo, List<OrderDetail> orderDetails, String organizationCode, SysEmployeeResDTO sysEmployeeResDTO) {
        // 自动拣货
        // 生成拣货明细
        ConcurrentHashMap<String, Integer> outBoundMap = new ConcurrentHashMap<>();
        orderDetails.forEach(a -> {
                Integer count = outBoundMap.get(a.getErpCode());
                if (count == null || count == 0) {
                    outBoundMap.put(a.getErpCode(), a.getPickCount());
                } else {
                    count = count + a.getPickCount();
                    outBoundMap.put(a.getErpCode(), count);
                }
            }
        );
        ConcurrentHashMap<String, CommInfoBatchNumberRespDto> waitPickCountMap = new ConcurrentHashMap<>();
        for (Map.Entry<String, Integer> stringIntegerEntry : outBoundMap.entrySet()) {
            String key = stringIntegerEntry.getKey();
            Integer value = stringIntegerEntry.getValue();
            String redisKeyDeliverGoodsKey = RedisKeyUtil.getRedisKeyFailAccountOrderCommodityStock(organizationCode, key);
            String cacheJson = stringRedisTemplate.opsForValue().get(redisKeyDeliverGoodsKey);
            log.info("最初redisKeyDeliverGoodsKey:{},cacheJson-:{}", redisKeyDeliverGoodsKey, JSON.toJSONString(cacheJson));
            if (org.apache.commons.lang3.StringUtils.isEmpty(cacheJson)) {
                HydeeEsSyncClientAsync.orderLogToES(DsConstants.INTEGER_ONE,orderInfo.getMerCode(), orderInfo.getOrderNo(), orderInfo.getOrderState(),orderInfo.getErpState(),
                    OrderLogEnum.BATCH_HANDLE.getAction(), "订单没有正常拣货信息,请检查商品是否拥有满足效期批号!", sysEmployeeResDTO);
                return false;
            }
            CommInfoBatchNumberRespDto dto = JSON.parseObject(cacheJson, CommInfoBatchNumberRespDto.class);
            if (dto.getStockAll() < value) {
                HydeeEsSyncClientAsync.orderLogToES(DsConstants.INTEGER_ONE, orderInfo.getMerCode(), orderInfo.getOrderNo(),orderInfo.getOrderState(),orderInfo.getErpState(),
                    OrderLogEnum.BATCH_HANDLE.getAction(), "订单没有正常拣货信息,请检查商品是否拥有满足效期批号!", sysEmployeeResDTO);
                return false;
            }
            waitPickCountMap.put(redisKeyDeliverGoodsKey, dto);
        }
        // 构建自动拣货信息明细
        List<OrderPickInfo> pickInfoList = getOrderPickInfos(organizationCode, orderDetails, waitPickCountMap);
        // 保存拣货信息
        orderPickInfoService.saveIfNotEmptyOrderPickInfo(orderInfo.getOrderNo(), pickInfoList);
        // 更新缓存
        for (Map.Entry<String, CommInfoBatchNumberRespDto> stringIntegerEntry : waitPickCountMap.entrySet()) {
            String key = stringIntegerEntry.getKey();
            CommInfoBatchNumberRespDto value = stringIntegerEntry.getValue();
            stringRedisTemplate.opsForValue().getOperations().delete(key);
            if (Objects.nonNull(value) && CollUtil.isNotEmpty(value.getLotList())) {
                log.info("最新redisKeyDeliverGoodsKey:{},cacheJson-:{}", key, JSON.toJSONString(value));
                stringRedisTemplate.opsForValue().set(key, JSON.toJSONString(value), Duration.ofMinutes(10));
            }
        }
        return true;
    }

    @NotNull
    private static List<OrderPickInfo> getOrderPickInfos(String organizationCode, List<OrderDetail> orderDetails, ConcurrentHashMap<String, CommInfoBatchNumberRespDto> waitPickCountMap) {
        List<OrderPickInfo> pickInfoList = new ArrayList<>();
        for (int i = 0; i < orderDetails.size(); i++) {
            OrderDetail orderDetail = orderDetails.get(i);
            Integer waitPickCount = orderDetail.getPickCount();
            CommInfoBatchNumberRespDto dto = waitPickCountMap.get(RedisKeyUtil.getRedisKeyFailAccountOrderCommodityStock(organizationCode, orderDetail.getErpCode()));
            dto.setStockAll(dto.getStockAll() - waitPickCount);
            List<AllLotStockVO> collect = dto.getLotList().stream().sorted(Comparator.comparing(AllLotStockVO::getDays)).filter(a -> a.getStock() > 0).collect(Collectors.toList());
            Iterator<AllLotStockVO> iterator = collect.iterator();
            while (iterator.hasNext()) {
                AllLotStockVO allLotStockVO = iterator.next();
                // 数量
                Integer stock = allLotStockVO.getStock();

                // 数量足够
                if (waitPickCount.compareTo(stock) < 1) {
                    OrderPickInfo pickInfo = new OrderPickInfo()
                        .setOrderDetailId(orderDetail.getId())
                        .setErpCode(orderDetail.getErpCode())
                        .setPackageId("")
                        .setCommodityBatchNo(allLotStockVO.getLotCode())
                        .setCount(waitPickCount);
                    pickInfoList.add(pickInfo);
                    int newStock = stock - waitPickCount;
                    allLotStockVO.setStock(newStock);
                    break;
                }
                // 不够往下取
                OrderPickInfo pickInfo = new OrderPickInfo()
                    .setOrderDetailId(orderDetail.getId())
                    .setErpCode(orderDetail.getErpCode())
                    .setPackageId("")
                    .setCommodityBatchNo(allLotStockVO.getLotCode())
                    .setCount(stock);
                pickInfoList.add(pickInfo);
                waitPickCount = waitPickCount - stock;
                allLotStockVO.setStock(0);
            }
        }
        return pickInfoList;
    }

    private List<AddOrderInfoReqDto> getAddOrderInfoReqDtoList(OrderInfo orderInfo){
        NetOrderGetDetailReqDto netOrderGetDetailReqDto = new NetOrderGetDetailReqDto();
        netOrderGetDetailReqDto.setOlorderno(orderInfo.getThirdOrderNo());
        netOrderGetDetailReqDto.setOlshop_id(orderInfo.getOnlineStoreCode());
        //根据商户编码查询sessionKey
        DsMerchantGroupInfo merchantGroupInfo = merchantGroupInfoService.querySessionkeyByMerCode(orderInfo.getMerCode());
        ResponseNet<List<AddOrderInfoReqDto>> thirdOrderDetailRes = netHttpAdapter.getOrderDetail(orderInfo.getMerCode(),
                orderInfo.getClientCode(), merchantGroupInfo.getSessionKey(),orderInfo.getThirdPlatformCode(), netOrderGetDetailReqDto);
        List<AddOrderInfoReqDto> addOrderInfoReqDtoList = thirdOrderDetailRes.getData();
        if(CollectionUtils.isEmpty(addOrderInfoReqDtoList)){
            return new ArrayList<>(0);
        }
        return addOrderInfoReqDtoList;
    }

    private List<ThirdOrderDetailRspDto> getThirdPartRefundOrderDetail(OrderInfo orderInfo, List<OriThirdOrderDetail> oriThirdOrderDetails, List<RefundDetail> refundDetailList) {
        List<ThirdOrderDetailRspDto> arr = new ArrayList<>();
        if(CollectionUtils.isEmpty(oriThirdOrderDetails)){
            return arr;
        }
        List<OrderDetail> detailList = orderDetailMapper.selectPartRefundListByOrderNo(orderInfo.getOrderNo());
        Map<String, List<OrderDetail>> collect = detailList.stream().collect(Collectors.groupingBy(OrderDetail::getPlatformSkuId));
        Map<String, List<RefundDetail>> refundMap = refundDetailList.stream().collect(Collectors.groupingBy(RefundDetail::getThirdSkuId));
        for (OriThirdOrderDetail oriThirdOrderDetail : oriThirdOrderDetails) {
            ThirdOrderDetailRspDto thirdOrderDetailRspDto = new ThirdOrderDetailRspDto();
            arr.add(thirdOrderDetailRspDto);
            thirdOrderDetailRspDto.setNum_iid(oriThirdOrderDetail.getNumIid());
            thirdOrderDetailRspDto.setOuter_iid(oriThirdOrderDetail.getOuterIid());
            thirdOrderDetailRspDto.setUpc(oriThirdOrderDetail.getUpc());
            thirdOrderDetailRspDto.setTitle(oriThirdOrderDetail.getTitle());
            List<RefundDetail> refundDetails = refundMap.get(oriThirdOrderDetail.getNumIid());
            thirdOrderDetailRspDto.setNum(String.valueOf(oriThirdOrderDetail.getNum()-refundCount(refundDetails)));
            thirdOrderDetailRspDto.setOid(oriThirdOrderDetail.getOid());
            thirdOrderDetailRspDto.setThirdDetailId(oriThirdOrderDetail.getThirdDetailId());
            List<OrderDetail> orderDetails = collect.get(oriThirdOrderDetail.getNumIid());
            if(CollectionUtil.isNotEmpty(orderDetails)){
                //只取detail中的商品状态和规格，不会因为多条而不一致，所以随便取一条
                OrderDetail orderDetail = orderDetails.get(0);
                thirdOrderDetailRspDto.setStatus(orderDetail.getStatus());
                thirdOrderDetailRspDto.setCommoditySpec(orderDetail.getCommoditySpec());
            }
        }
        return arr;
    }

    private int refundCount(List<RefundDetail> list){
        if(CollectionUtil.isEmpty(list)){
            return 0;
        }
        TreeSet<RefundDetail> set = new TreeSet<>(Comparator.comparing(RefundDetail::getRefundNo));
        set.addAll(list);
        return set.stream().mapToInt(RefundDetail::getPlatformRefundCount).sum();
    }

    @Override
    public ThirdOrderDetailResDto thirdOrderDetail(ThirdOrderDetailReq req) {

        // 运费单的三方单号在后面追加了A,所以不需要特殊处理运费单
        OrderInfo orderInfo = orderInfoMapper.selectOrderByThirdOrderNoAndPlatformCode(
            req.getThirdOrderNo(), req.getThirdPlatformCode());
        if (Objects.isNull(orderInfo)) {
            throw ExceptionUtil.getWarnException(ErrorType.RECORD_NOT_EXISTS.getCode(),
                String.format("订单不存在,%s", JsonUtil.object2Json(req)));
        }

        List<OrderDetail> orderDetailList = orderDetailMapper.selectAllListByOrderNo(
            orderInfo.getOrderNo());
        if (CollectionUtils.isEmpty(orderDetailList)) {
            throw ExceptionUtil.getWarnException(ErrorType.RECORD_NOT_EXISTS.getCode(),
                String.format("订单明细不存在,%s", JsonUtil.object2Json(req)));
        }

        ThirdOrderDetailResDto thirdOrderDetailResDto = new ThirdOrderDetailResDto();
        thirdOrderDetailResDto.setOrderNo(orderInfo.getOrderNo());
        thirdOrderDetailResDto.setThirdOrderNo(orderInfo.getThirdOrderNo());
        thirdOrderDetailResDto.setOrderStatus(orderInfo.getOrderState());
        thirdOrderDetailResDto.setOrderType(orderInfo.getOrderType());
        thirdOrderDetailResDto.setSourceOrganizationCode(orderInfo.getSourceOrganizationCode());
        // 和订单模型的取值逻辑兼容
        if (B2C.equals(orderInfo.getServiceMode())) {
            if (StringUtils.isEmpty(thirdOrderDetailResDto.getSourceOrganizationCode())) {
                thirdOrderDetailResDto.setSourceOrganizationCode(orderInfo.getOrganizationCode());
            }
        }
        thirdOrderDetailResDto.setMemberCard(orderInfo.getMemberNo());
        thirdOrderDetailResDto.setCompleteTime(orderInfo.getCompleteTime());
        thirdOrderDetailResDto.setServiceMode(orderInfo.getServiceMode());

        thirdOrderDetailResDto.setDetailList(orderDetailList.stream().map(detail -> {
            OrderDetailDto detailDto = new OrderDetailDto();
            detailDto.setCommodityCode(detail.getErpCode());
            detailDto.setCommodityName(detail.getCommodityName());
            detailDto.setCommodityCount(detail.getCommodityCount());
            detailDto.setIsGift(detail.getIsGift());
            detailDto.setBillPrice(detail.getBillPrice());
            return detailDto;
        }).collect(Collectors.toList()));

        return thirdOrderDetailResDto;
    }

    @Override
    @Transactional
    public List<String> replaceOnlineStoreCode(List<ReplaceOnlineStoreCodeReq> reqs) {
        List<String> faliList = new ArrayList<>();
        int batchUpdateSize = 1000;
        for (ReplaceOnlineStoreCodeReq req : reqs) {
            try {
                if(StrUtil.isBlank(req.getOldOnlineStoreCode()) || StrUtil.isBlank(req.getNewOnlineStoreCode())){
                    continue;
                }
                List<OrderInfo> orderInfos = orderInfoMapper.selectList(new LambdaQueryWrapper<OrderInfo>()
                    .eq(OrderInfo::getMerCode,DsConstants.MER_CODE_YXT)
                    .eq(OrderInfo::getOnlineStoreCode, req.getOldOnlineStoreCode()));
                if(CollUtil.isEmpty(orderInfos)){
                    continue;
                }
                List<Long> ids = orderInfos.stream().map(OrderInfo::getId).collect(Collectors.toList());
                for (int i = 0; i < ids.size(); i+=batchUpdateSize ) {
                    List<Long> batchIds = ids.subList(i, Math.min(i + batchUpdateSize, ids.size()));
                    String sql = "update order_info set online_store_code = '"+req.getNewOnlineStoreCode()+"' where id in ("+String.join(",", batchIds.stream().map(String::valueOf).toArray(String[]::new))+")";
                    orderInfoMapper.orderUpdateBatch(sql);
                }
                Thread.sleep(3000);
            }catch (Exception e){
                faliList.add(req.getNewOnlineStoreCode());
            }
        }
        return faliList;
    }

    public static void main(String[] args) {
        List<Long> ids = Lists.newArrayList(1L, 2L, 3L, 4L, 5L, 6L, 7L, 8L, 9L, 10L);
        System.out.println(String.join(",", ids.stream().map(String::valueOf).toArray(String[]::new)));
    }
}