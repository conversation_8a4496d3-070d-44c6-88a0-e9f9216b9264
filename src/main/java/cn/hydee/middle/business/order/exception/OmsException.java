package cn.hydee.middle.business.order.exception;

/**
 * oms电商云异常
 * <AUTHOR>
 * @version 3.7.5
 * @date 2020/08/04
 */
public class OmsException extends RuntimeException {
    private static final long serialVersionUID = -8398600407698355389L;

    public OmsException() {
        super();
    }

    public OmsException(String message) {
        super(message);
    }

    public OmsException(String message, Throwable cause) {
        super(message, cause);
    }

    public OmsException(Throwable cause) {
        super(cause);
    }

    protected OmsException(String message, Throwable cause, boolean enableSuppression, boolean writableStackTrace) {
        super(message, cause, enableSuppression, writableStackTrace);
    }
}
