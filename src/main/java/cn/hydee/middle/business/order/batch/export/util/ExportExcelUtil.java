package cn.hydee.middle.business.order.batch.export.util;

import cn.hutool.core.util.BooleanUtil;
import cn.hutool.core.util.StrUtil;
import cn.hydee.middle.business.order.Enums.DsConstants;
import cn.hydee.middle.business.order.batch.export.mergestrategy.MergeStrategy;
import cn.hydee.middle.business.order.batch.export.setting.dto.UserExportColumnResDTO;
import cn.hydee.starter.util.DateUtil;
import com.alibaba.excel.EasyExcelFactory;
import com.alibaba.excel.ExcelWriter;
import com.alibaba.excel.write.builder.ExcelWriterBuilder;
import com.alibaba.excel.write.metadata.WriteSheet;
import com.alibaba.excel.write.style.column.SimpleColumnWidthStyleStrategy;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;

import javax.validation.constraints.NotNull;
import java.io.OutputStream;
import java.util.*;
import java.util.stream.Collectors;

/**
 * 导出工具
 **/
public final class ExportExcelUtil {
    private ExportExcelUtil() {
    }

    /**
     * 获取 excel writer
     */
    public static ExcelWriter getExcelWriter(OutputStream outputStream, List<UserExportColumnResDTO> columns, long maxRow, Boolean needMerge) {
        // 得到需要合并的 列 序号
        List<Integer> mergeColumnIndex = new ArrayList<>();
        // 找到唯一标识记录的下标(默认找订单号没有则取 0 )
        Integer uniqueIndex = 0;
        // 动态添加 表头 headList --> 所有表头行集合
        List<List<String>> headList = new ArrayList<>();
        // 构建表头等信息
        uniqueIndex = buildHeadAndParam(columns, mergeColumnIndex, uniqueIndex, headList);

        ExcelWriterBuilder excelWriterBuilder = EasyExcelFactory.write(outputStream).head(headList);
        if (needMerge) {
            excelWriterBuilder.registerWriteHandler(new MergeStrategy(maxRow, mergeColumnIndex, uniqueIndex));
        }
        return excelWriterBuilder.build();
    }

    /**
     * 获取 sheet
     */
    public static WriteSheet getWriteSheet(@NotNull String name) {
        return EasyExcelFactory
                .writerSheet(name)
                .registerWriteHandler(new SimpleColumnWidthStyleStrategy(20))
                .build();
    }

    /**
     * 导出数据解析
     */
    public static <T> List<List<String>> parseExportData(List<T> dataList, List<UserExportColumnResDTO> columns) {
        // 每一条记录就是一行excel
        return dataList.stream().map(dataItem -> {
            JSONObject jsonObject = JSON.parseObject(JSON.toJSONStringWithDateFormat(dataItem, DateUtil.CN_LONG_FORMAT));
            // 遍历每一个需要写入的行 每个元素一个object 整行是一个list
            return columns.stream().map(column -> {
                if (jsonObject.containsKey(column.getColumnCode()) && StrUtil.isNotBlank(column.getColumnCode())) {
                    return String.valueOf(jsonObject.get(column.getColumnCode()));
                }
                return BooleanUtil.isTrue(column.getDefaultZero()) ? "0" : "";
            }).collect(Collectors.toList());
        }).collect(Collectors.toList());
    }

    /**
     * 构建表头 和需要的参数
     */
    private static Integer buildHeadAndParam(List<UserExportColumnResDTO> columns, List<Integer> mergeColumnIndex, Integer uniqueIndex, List<List<String>> headList) {
        for (int i = 0; i < columns.size(); i++) {
            if (Objects.nonNull(columns.get(i)) && Objects.nonNull(columns.get(i).getNeedMerge()) && DsConstants.INTEGER_ONE.equals(columns.get(i).getNeedMerge())) {
                mergeColumnIndex.add(i);
            }
            // 找到默认唯一识别列的列索引值
            if (Objects.nonNull(columns.get(i)) && Objects.nonNull(columns.get(i).getUniqueColumn()) && DsConstants.INTEGER_ONE.equals(columns.get(i).getUniqueColumn())) {
                uniqueIndex = i;
            }
            // 追加第 i 行 的表头
            List<String> headTitle = new ArrayList<>();
            headTitle.add(columns.get(i).getCategory());
            headTitle.add(columns.get(i).getColumnName());
            headList.add(headTitle);
        }
        return uniqueIndex;
    }
}
