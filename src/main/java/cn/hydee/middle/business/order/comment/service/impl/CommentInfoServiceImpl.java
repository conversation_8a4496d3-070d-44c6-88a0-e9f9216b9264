package cn.hydee.middle.business.order.comment.service.impl;

import cn.hydee.middle.business.order.Enums.DsConstants;
import cn.hydee.middle.business.order.Enums.DsErrorType;
import cn.hydee.middle.business.order.Enums.PlatformCodeEnum;
import cn.hydee.middle.business.order.account.check.batch.list.BatchListHandler;
import cn.hydee.middle.business.order.comment.base.dto.BaseDataDto;
import cn.hydee.middle.business.order.comment.base.dto.CommentByDaysDto;
import cn.hydee.middle.business.order.comment.base.dto.DateDto;
import cn.hydee.middle.business.order.comment.base.dto.req.*;
import cn.hydee.middle.business.order.comment.base.dto.resp.*;
import cn.hydee.middle.business.order.comment.base.enums.ReplyStatusEnum;
import cn.hydee.middle.business.order.comment.context.CommentContext;
import cn.hydee.middle.business.order.comment.handler.PullAndOperateDataHandler;
import cn.hydee.middle.business.order.comment.service.ICommentInfoService;
import cn.hydee.middle.business.order.configuration.ThirdPlatformMigrateConfig;
import cn.hydee.middle.business.order.dto.rsp.baseinfo.The3DsStoreResDTO;
import cn.hydee.middle.business.order.entity.CommentInfo;
import cn.hydee.middle.business.order.mapper.CommentInfoMapper;
import cn.hydee.middle.business.order.mapper.DsOnlineClientRepo;
import cn.hydee.middle.business.order.mapper.DsOnlineStoreRepo;
import cn.hydee.middle.business.order.service.baseinfo.DsMerchantGroupInfoService;
import cn.hydee.middle.business.order.service.thirdplatform.ThirdPlatformService;
import cn.hydee.middle.business.order.util.DateUtil;
import cn.hydee.middle.business.order.util.ExceptionUtil;
import cn.hydee.middle.business.order.v2.feign.hems.HemsCommonClient;
import cn.hydee.middle.business.order.v2.feign.o2o.OTOCommonClient;
import cn.hydee.starter.util.UUIDUtil;
import cn.hydee.thirdo2o.model.BaseEBaiResp;
import cn.hydee.thirdo2o.model.BaseMeiTuanResp;
import cn.hydee.thirdo2o.model.EBaiConfig;
import cn.hydee.thirdo2o.model.MeiTuanConfig;
import cn.hydee.thirdo2o.model.comment.ebai.OrderCommentGetReq;
import cn.hydee.thirdo2o.model.comment.meituan.CommentQueryReq;
import cn.hydee.unified.model.HemsBaseData;
import cn.hydee.unified.model.base.BaseHemsResp;
import com.alibaba.fastjson.JSONObject;
import com.baomidou.dynamic.datasource.annotation.DS;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.yxt.thirdplatform.dto.request.OrderCommentReplyParam;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;
import org.springframework.util.StringUtils;

import java.util.*;
import java.util.stream.Collectors;

/**
 * <p>
 * 三方平台评价表 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2020-12-27
 */
@Service
@Slf4j
public class CommentInfoServiceImpl extends ServiceImpl<CommentInfoMapper, CommentInfo> implements ICommentInfoService {
	
	private final static List<String> PULL_PLATFORM_LIST = new ArrayList<>();
	static {
		PULL_PLATFORM_LIST.add(PlatformCodeEnum.E_BAI.getCode());
		PULL_PLATFORM_LIST.add(PlatformCodeEnum.MEITUAN.getCode());
	}
	
	@Autowired
	private CommentInfoMapper commentInfoMapper;
	@Autowired
	private HemsCommonClient hemsCommonClient;
	@Autowired
	private OTOCommonClient o2oClient;
	@Autowired
	private PullAndOperateDataHandler pullHandler;
	@Autowired
	private DsOnlineStoreRepo onlineStoreMapper;
	@Autowired
	private DsMerchantGroupInfoService dsMerchantGroupInfoService;
	@Autowired
	private DsOnlineClientRepo dsOnlineClientMapper;
	@Autowired
	private ThirdPlatformMigrateConfig thirdPlatformMigrateConfig;
	@Autowired
	private ThirdPlatformService thirdPlatformService;

	@Override
	public void saveUpdateBatch(List<CommentInfo> dataList){
		try {
			List<String> queryList = dataList.stream().map(CommentInfo::getMd5Code).collect(Collectors.toList());
			queryList = queryList.stream().distinct().collect(Collectors.toList());
			QueryWrapper<CommentInfo> queryWrapper = new QueryWrapper<>();
			queryWrapper.lambda().in(CommentInfo::getMd5Code, queryList);
			List<CommentInfo> existDataList = commentInfoMapper.selectList(queryWrapper);
			if (CollectionUtils.isEmpty(existDataList)) {
				existDataList = Collections.emptyList();
			}
			Map<String, CommentInfo> orderNoCommentInfoMap = existDataList.stream().collect(Collectors.toMap(CommentInfo::getMd5Code, a -> a, (v1, v2) -> v1));
			List<CommentInfo> updateDataList = dataList.stream().filter(data -> orderNoCommentInfoMap.containsKey(data.getMd5Code())).collect(Collectors.toList());
			List<CommentInfo> insertDataList = dataList.stream().filter(data -> !orderNoCommentInfoMap.containsKey(data.getMd5Code())).collect(Collectors.toList());
			if (!CollectionUtils.isEmpty(updateDataList)) {
				updateDataList.forEach(data -> {
					data.setId(orderNoCommentInfoMap.get(data.getMd5Code()).getId());
					data.setModifyTime(new Date());
				});
				updateBatchById(updateDataList);
			}
			if (!CollectionUtils.isEmpty(insertDataList)) {
				insertDataList.forEach(data -> {
					data.setCreateTime(new Date());
				});
				saveBatch(insertDataList);
			}
		}catch (Exception e) {
			log.info("CommentInfoSaveUpdateBatch error,dataList:{},cause:{}",dataList,e);
		}
	}
	
	@Override
	@Async
	public void commentByDays(CommentByDaysDto dto) {
		String contextId = UUIDUtil.generateUuid();
		log.info("评价管理[{}]===> start,param:{}",contextId,JSONObject.toJSONString(dto));
		List<String> pullPlatformList = new ArrayList<>();
		if(CollectionUtils.isEmpty(dto.getPlatformCodeList())) {
			pullPlatformList.addAll(PULL_PLATFORM_LIST);
		}else {
			pullPlatformList.addAll(dto.getPlatformCodeList());
		}
		List<OnlineStoreWithAppInfoRespDto> onlineStoreAppInfoDatas = buildOriginData(dto.getMerCodeList(), pullPlatformList, dto.getOnlineStoreCodeList());
		if(CollectionUtils.isEmpty(onlineStoreAppInfoDatas)) {
			log.info("评价管理[{}]===> 门店为空",contextId);
			return;
		}
		log.info("评价管理[{}]===> 获取原始门店数据成功，总计[{}]家门店待拉取",contextId,onlineStoreAppInfoDatas.size());
		onlineStoreAppInfoDatas = onlineStoreAppInfoDatas.stream().filter(storeApp -> null != storeApp.getAppInfo()).collect(Collectors.toList());
		if(CollectionUtils.isEmpty(onlineStoreAppInfoDatas)) {
			log.info("评价管理[{}]===> 门店为空",contextId);
			return;
		}
		CommentContext context = buildContext(contextId,dto,onlineStoreAppInfoDatas);
		
		pullHandler.pullAndOperateData(context);
	}

	@Override
	@DS(DsConstants.DB_ORDER_SLAVE)
	public IPage<CommentRespDto> queryComment(CommentReqDto query, List<String> platFromCodes) {
		Page<CommentRespDto> page = new Page<>(query.getCurrentPage(), query.getPageSize());
		return commentInfoMapper.queryCommentInfo(page, query, platFromCodes);
	}

	@Override
	@DS(DsConstants.DB_ORDER_SLAVE)
	public CommentDetailRespDto queryCommentDetail(CommentDetailReqDto query) {
		return commentInfoMapper.queryCommentDetail(query);
	}

	@Override
	public void replyComment(CommentReplyReqDto req) {
		CommentWithStoreInfoRespDto commentWithStoreInfo = commentInfoMapper.queryCommentWithStoreInfo(
				CommentWithStoreInfoReqDto.builder().merCode(req.getMerCode())
						.thirdPlatformCode(req.getThirdPlatformCode())
						.md5Code(req.getMd5Code())
						.build());
		if(null == commentWithStoreInfo) {
			throw ExceptionUtil.getWarnException(DsErrorType.COMMENT_NOT_EXIST);
		}
		if(ReplyStatusEnum.REPLYED.getCode().equals(commentWithStoreInfo.getReplyStatus())) {
			throw ExceptionUtil.getWarnException(DsErrorType.COMMENT_REPLYED_ERROR);
		}
		boolean replyFlag = false;
		if (thirdPlatformMigrateConfig.isMigrated(req.getThirdPlatformCode(),commentWithStoreInfo.getOnlineStoreCode())) {
			replyFlag = this.replyCommentV2(req, commentWithStoreInfo);
		}else {
			replyFlag = this.replyCommentV1(req, commentWithStoreInfo);
		}
		if(replyFlag) {
			QueryWrapper<CommentInfo> updateWrapper = new QueryWrapper<>();
			updateWrapper.lambda().eq(CommentInfo::getMerCode, commentWithStoreInfo.getMerCode())
					.eq(CommentInfo::getThirdPlatformCode, commentWithStoreInfo.getThirdPlatformCode())
					.eq(CommentInfo::getMd5Code, commentWithStoreInfo.getMd5Code());
			CommentInfo entity = new CommentInfo();
			entity.setReplyStatus(ReplyStatusEnum.REPLYED.getCode());
			entity.setReply(req.getReply());
			entity.setReplyTime(new Date());
			commentInfoMapper.update(entity, updateWrapper);
		}
	}

	private boolean replyCommentV2(CommentReplyReqDto req, CommentWithStoreInfoRespDto commentWithStoreInfo) {
		The3DsStoreResDTO storeAccess = new The3DsStoreResDTO();
		storeAccess.setMerCode(commentWithStoreInfo.getMerCode());
		storeAccess.setPlatformCode(commentWithStoreInfo.getThirdPlatformCode());
		storeAccess.setOnlineClientCode(commentWithStoreInfo.getOnlineClientCode());
		storeAccess.setOnlineStoreCode(commentWithStoreInfo.getOnlineStoreCode());
		storeAccess.setPlatformShopId(commentWithStoreInfo.getOutShopId());
		OrderCommentReplyParam commentReplyParam = new OrderCommentReplyParam();
		commentReplyParam.setCommentId(commentWithStoreInfo.getCommentId());
		commentReplyParam.setReplyContent(req.getReply());
		thirdPlatformService.orderCommentReply(storeAccess, commentReplyParam);
		return true;
	}

	public boolean replyCommentV1(CommentReplyReqDto req, CommentWithStoreInfoRespDto commentWithStoreInfo) {

		// 优化获取appinfo逻辑
		OnlineStoreWithAppInfoRespDto onlineStoreWithAppInfoRespDto = new OnlineStoreWithAppInfoRespDto();
		onlineStoreWithAppInfoRespDto.setMerCode(commentWithStoreInfo.getMerCode());
		onlineStoreWithAppInfoRespDto.setPlatformCode(commentWithStoreInfo.getThirdPlatformCode());
		onlineStoreWithAppInfoRespDto.setOnlineClientCode(commentWithStoreInfo.getOnlineClientCode());
		List<AppInfoRespDto> appInfoRespDtoList = dsOnlineClientMapper.selectClientInfo(Collections.singletonList(onlineStoreWithAppInfoRespDto));
		if(CollectionUtils.isEmpty(appInfoRespDtoList)) {
			throw ExceptionUtil.getWarnException(DsErrorType.COMMENT_CLIENT_STORE_NOT_EXIST);
		}
		commentWithStoreInfo.setAppInfo(appInfoRespDtoList.get(0));

		boolean replyFlag = false;
		if(PlatformCodeEnum.E_BAI.getCode().equals(commentWithStoreInfo.getThirdPlatformCode())) {
			// 获取token
			String accessToken = getAccessToken(commentWithStoreInfo);
			// 回复评论
			EBaiConfig config = o2oClient.buildEBaiConfig(req.getMerCode(), commentWithStoreInfo.getAppInfo().getAppId(),
					commentWithStoreInfo.getAppInfo().getAppSecret(), accessToken);
			BaseEBaiResp<Boolean> replyResult = o2oClient.replay(commentWithStoreInfo.buildReply(req.getReply()),config);
			if( null != replyResult && null != replyResult.getBody()) {
				if(!replyResult.getBody().isSuccess()) {
					log.error("饿百评价回复失败，请求参数：{},具体原因：{}",JSONObject.toJSONString(req),replyResult.getBody().getError());
					throw ExceptionUtil.getWarnException(DsErrorType.COMMENT_REPLY_ERROR.getCode(),DsErrorType.COMMENT_REPLY_ERROR.getMsg()+","+replyResult.getBody().getError());
				}
				replyFlag = true;
			}else {
				throw ExceptionUtil.getWarnException(DsErrorType.COMMENT_REPLY_ERROR);
			}
		}else if(PlatformCodeEnum.MEITUAN.getCode().equals(commentWithStoreInfo.getThirdPlatformCode())) {
			// 回复评论
			MeiTuanConfig config = o2oClient.buildMeiTuanConfig(req.getMerCode(), Integer.valueOf(commentWithStoreInfo.getAppInfo().getAppId()), 
					commentWithStoreInfo.getAppInfo().getAppSecret());
			BaseMeiTuanResp<String> replyResult = o2oClient.commentReply(commentWithStoreInfo.builderCommentReplyReq(req.getReply()), config);
			if( null != replyResult) {
				if(!replyResult.isSuccess()) {
					log.error("美团评价回复失败，请求参数：{},具体原因：{}",JSONObject.toJSONString(req),replyResult.getMsg());
					throw ExceptionUtil.getWarnException(DsErrorType.COMMENT_REPLY_ERROR.getCode(),DsErrorType.COMMENT_REPLY_ERROR.getMsg()+","+replyResult.getMsg());
				}
				replyFlag = true;
			}else {
				throw ExceptionUtil.getWarnException(DsErrorType.COMMENT_REPLY_ERROR);
			}
		}
		return replyFlag;
	}
	
	private String getAccessToken(CommentWithStoreInfoRespDto commentWithStoreInfo) {
		String accessToken = "";
		HemsBaseData baseData = hemsCommonClient.constructHemsBaseData(commentWithStoreInfo.getMerCode(), commentWithStoreInfo.getThirdPlatformCode(),
				commentWithStoreInfo.getAppInfo().getOnlineClientCode(), commentWithStoreInfo.getAppInfo().getSessionKey());
		BaseHemsResp<String> orderTokenResult = null;
		try {
			orderTokenResult = hemsCommonClient.orderToken(baseData);
			if(null != orderTokenResult 
					&& orderTokenResult.isSuccess() 
					&& null != orderTokenResult.getData()) {
				accessToken = orderTokenResult.getData();
			}
		} catch (Exception e) { 
			log.error("getAccessToken error",e);
		}
		return accessToken;
	}

	private String getAccessToken(OnlineStoreWithAppInfoRespDto storeAppInfo) {
		String accessToken = "";
		HemsBaseData baseData = hemsCommonClient.constructHemsBaseData(storeAppInfo.getMerCode(), storeAppInfo.getPlatformCode(),
				storeAppInfo.getAppInfo().getOnlineClientCode(), storeAppInfo.getAppInfo().getSessionKey());
		BaseHemsResp<String> orderTokenResult = null;
		try {
			orderTokenResult = hemsCommonClient.orderToken(baseData);
			if(null != orderTokenResult 
					&& orderTokenResult.isSuccess() 
					&& null != orderTokenResult.getData()) {
				accessToken = orderTokenResult.getData();
			}
		} catch (Exception e) { 
			log.error("getAccessToken error",e);
		}
		return accessToken;
	}
	
	private CommentContext buildContext(String contextId, CommentByDaysDto dto,
			List<OnlineStoreWithAppInfoRespDto> onlineStoreAppInfoDatas) {
		// 计算日期
		String beginDateStr = DateUtil.parseDateToStr(dto.getStartTime(), DateUtil.DATE_FORMAT_YYYY_MM_DD);
		String endDateStr = DateUtil.parseDateToStr(dto.getEndTime(), DateUtil.DATE_FORMAT_YYYY_MM_DD);
		List<String> dayList = DateUtil.getDayListOfDate(beginDateStr, endDateStr);
		List<DateDto> dateDtoList = new ArrayList<>();
		dayList.forEach(dayStr -> {{
			Date day = DateUtil.parseStrToDate(dayStr, DateUtil.DATE_FORMAT_YYYY_MM_DD);
			Date dayStart = DateUtil.getNeedTime(day,0,0,0);
			Date dayEnd = DateUtil.getNeedTime(day,23,59,59);
			String startTime = DateUtil.parseDateToStr(dayStart, DateUtil.DATE_FORMAT_YYYYMMDD);
			String endTime = DateUtil.parseDateToStr(dayEnd, DateUtil.DATE_FORMAT_YYYYMMDD);
			String startTimeStampStr = String.valueOf(dayStart.getTime()/1000);
			String endTimeStampStr = String.valueOf(dayEnd.getTime()/1000);
			dateDtoList.add(DateDto.builder().startTime(startTime).endTime(endTime)
					.startTimeStampStr(startTimeStampStr).endTimeStampStr(endTimeStampStr)
					.build());
		}});
		List<PullCommentDataReqDto> pullDataDtoList = new ArrayList<>();
		List<OnlineStoreWithAppInfoRespDto> storeAppEBList = new ArrayList<>();
		List<OnlineStoreWithAppInfoRespDto> storeAppMTList = new ArrayList<>();
		// 过滤美团
		storeAppMTList.addAll(onlineStoreAppInfoDatas.stream().filter( 
				storeApp -> PlatformCodeEnum.MEITUAN.getCode().equals(storeApp.getPlatformCode())).collect(Collectors.toList()));
		// 过滤饿百
		storeAppEBList.addAll(onlineStoreAppInfoDatas.stream().filter( 
				storeApp -> PlatformCodeEnum.E_BAI.getCode().equals(storeApp.getPlatformCode())).collect(Collectors.toList()));
		pullDataDtoList.addAll(buildListPullCommentDataEB(dateDtoList, storeAppEBList));
		pullDataDtoList.addAll(buildListPullCommentDataMT(dateDtoList, storeAppMTList));
		
		List<PullCommentDataReqDto> pullDataDtoListDistinct = pullDataDtoList.stream().collect(
		           Collectors. collectingAndThen(
		                    Collectors.toCollection(() -> new TreeSet<>(Comparator.comparing(PullCommentDataReqDto::md5Code))), ArrayList::new)
		);
		pullDataDtoListDistinct = pullDataDtoListDistinct.stream().filter(pullDataDto -> pullDataDto.needPull()).collect(Collectors.toList());
		
		return CommentContext.builder().contextId(contextId).pullDataDtoList(pullDataDtoListDistinct).build();
	}
	
	private List<PullCommentDataReqDto> buildListPullCommentDataMT(List<DateDto> dateDtoList,List<OnlineStoreWithAppInfoRespDto> onlineStoreAppInfoDatas){
		if(CollectionUtils.isEmpty(onlineStoreAppInfoDatas)) {
			return Collections.emptyList();
		}
		List<PullCommentDataReqDto> pullDataDtoList = new ArrayList<>();
		// 此接口用于商家查询门店近30天内订单的评价信息
		new BatchListHandler<DateDto>().batchResolve(dateDtoList, 30, (items) ->{
			PullCommentDataReqDto reqDto = null;
			for (OnlineStoreWithAppInfoRespDto storeApp : onlineStoreAppInfoDatas) {
				try {
					reqDto = new PullCommentDataReqDto();
					reqDto.setBaseData(BaseDataDto.builder()
							.merCode(storeApp.getMerCode())
							.thirdPlatformCode(storeApp.getPlatformCode())
							.thirdPlatformName(storeApp.getPlatformName())
							.onlineStoreCode(storeApp.getOnlineStoreCode())
							.onlineStoreName(storeApp.getOnlineStoreName())
							.onlineClientCode(storeApp.getOnlineClientCode())
							.outShopId(storeApp.getOutShopId())
							.platformShopId(storeApp.getPlatformShopId())
							.organizationCode(storeApp.getOrganizationCode())
							.organizationName(storeApp.getOrganizationName())
							.build());
					
					MeiTuanConfig configMT = new MeiTuanConfig();
					configMT.setAppId(Integer.valueOf(storeApp.getAppInfo().getAppId()));
					configMT.setMerCode(storeApp.getMerCode());
					configMT.setAppSecret(storeApp.getAppInfo().getAppSecret());
					reqDto.setConfigMT(configMT);
					
					CommentQueryReq reqMT = new CommentQueryReq();
					reqMT.setAppPoiCode(storeApp.getOnlineStoreCode());
					reqMT.setReplyStatus(-1);
					reqMT.setStartTime(items.get(0).getStartTime());
					reqMT.setEndTime(items.get(items.size()-1).getEndTime());
					reqMT.setPageOffSet(0);
					reqMT.setPageSize(20);
					// 为保护用户隐私，当日评价次日才可查询，上传的查询结束日期不能大于等于当前日期。
					Date endDay = DateUtil.parseStrToDate(reqMT.getEndTime(), DateUtil.DATE_FORMAT_YYYYMMDD);
					Date yesterDay = DateUtil.getDayBefore(DateUtil.getNeedTime(new Date(),0,0,0), 1);
					if(endDay.getTime() > yesterDay.getTime()) {
						reqMT.setEndTime(DateUtil.parseDateToStr(yesterDay, DateUtil.DATE_FORMAT_YYYYMMDD));
						Long daysBetween = DateUtil.getDistanceTimestamp(yesterDay, endDay);
						Date startDay = DateUtil.parseStrToDate(reqMT.getStartTime(), DateUtil.DATE_FORMAT_YYYYMMDD);
						reqMT.setStartTime(DateUtil.parseDateToStr(DateUtil.getDayBefore(startDay, daysBetween.intValue()), 
								DateUtil.DATE_FORMAT_YYYYMMDD));
					};
					reqDto.setReqMT(reqMT);
					pullDataDtoList.add(reqDto);
				} catch (NumberFormatException e) {
					// do nothing
					log.error("buildListPullCommentDataMT error",e);
				}
			}
		});
		return pullDataDtoList;
	}
	
	private List<PullCommentDataReqDto> buildListPullCommentDataEB(List<DateDto> dateDtoList,List<OnlineStoreWithAppInfoRespDto> onlineStoreAppInfoDatas){
		if(CollectionUtils.isEmpty(onlineStoreAppInfoDatas)) {
			return Collections.emptyList();
		}
		List<PullCommentDataReqDto> pullDataDtoList = new ArrayList<>();
//		Map<String,String> merchantTokenMap = new HashMap<>();
//		onlineStoreAppInfoDatas.forEach(storeAppInfo -> {{
//			String key = Md5Utils.md5Encode(storeAppInfo.getMerCode()+storeAppInfo.getPlatformCode()+
//					storeAppInfo.getAppInfo().getOnlineClientCode()+storeAppInfo.getAppInfo().getSessionKey(),
//					Charsets.UTF_8.name());
//			if(!merchantTokenMap.containsKey(key)) {
//				String accessToken = getAccessToken(storeAppInfo);
//				merchantTokenMap.put(key, accessToken);
//			}
//		}});
		new BatchListHandler<DateDto>().batchResolve(dateDtoList, 7, (items) ->{
			PullCommentDataReqDto reqDto = null;
			for (OnlineStoreWithAppInfoRespDto storeApp : onlineStoreAppInfoDatas) {
				try {
					reqDto = new PullCommentDataReqDto();
					reqDto.setBaseData(BaseDataDto.builder()
							.merCode(storeApp.getMerCode())
							.thirdPlatformCode(storeApp.getPlatformCode())
							.thirdPlatformName(storeApp.getPlatformName())
							.onlineStoreCode(storeApp.getOnlineStoreCode())
							.onlineStoreName(storeApp.getOnlineStoreName())
							.onlineClientCode(storeApp.getOnlineClientCode())
							.outShopId(storeApp.getOutShopId())
							.platformShopId(storeApp.getPlatformShopId())
							.organizationCode(storeApp.getOrganizationCode())
							.organizationName(storeApp.getOrganizationName())
							.build());
					
					EBaiConfig configEB = new EBaiConfig();
					configEB.setAppId(storeApp.getAppInfo().getAppId());
					configEB.setMerCode(storeApp.getMerCode());
					configEB.setAppSecret(storeApp.getAppInfo().getAppSecret());
//					String key = Md5Utils.md5Encode(storeApp.getMerCode()+storeApp.getPlatformCode()+
//							storeApp.getAppInfo().getOnlineClientCode()+storeApp.getAppInfo().getSessionKey(),
//							Charsets.UTF_8.name());
//					configEB.setAccessToken(merchantTokenMap.get(key));
					reqDto.setConfigEB(configEB);
					
					OrderCommentGetReq reqEB = new OrderCommentGetReq();
					reqEB.setPage("1");
					reqEB.setBaiduShopId(storeApp.getOutShopId());
					reqEB.setShopId(storeApp.getOnlineStoreCode());
					reqEB.setStartTime(items.get(0).getStartTimeStampStr());
					reqEB.setEndTime(items.get(items.size()-1).getEndTimeStampStr());
					
					reqDto.setReqEB(reqEB);
					pullDataDtoList.add(reqDto);
				} catch (Exception e) {
					// do nothing
					log.error("buildListPullCommentDataEB error",e);
				}
			}
		});
		return pullDataDtoList;
	}

	private List<OnlineStoreWithAppInfoRespDto> buildOriginData(List<String> merCodeList,List<String> platformCodeList, List<String> onlineStoreCodeList){
		List<OnlineStoreWithAppInfoRespDto> onlineStoreAppInfoData = new ArrayList<>();
		// 分页获取店铺信息
		Page<OnlineStoreWithAppInfoRespDto> page = new Page<>(1, 1000);
		Page<OnlineStoreWithAppInfoRespDto> pageData = onlineStoreMapper.getOnlineStoreWithAppInfo(page,merCodeList, platformCodeList,onlineStoreCodeList);
		if(null == pageData || CollectionUtils.isEmpty(pageData.getRecords())) {
			return Collections.emptyList();
		}
		onlineStoreAppInfoData.addAll(pageData.getRecords());
		long pages = pageData.getPages();
		long pageNum = pageData.getCurrent();
		while(pageNum < pages) {
			pageNum++;
			page.setCurrent(pageNum);
			pageData = onlineStoreMapper.getOnlineStoreWithAppInfo(page,merCodeList, platformCodeList,onlineStoreCodeList);
			if(null == pageData || CollectionUtils.isEmpty(pageData.getRecords())) {
				continue;
			}
			onlineStoreAppInfoData.addAll(pageData.getRecords());
		}
		// 获取全部商户sessionKey数据
		Map<String,String> sessionKeyMap = dsMerchantGroupInfoService.querySessionKeyByMerCodeList(Collections.emptyList());
		List<AppInfoRespDto> allAppInfoRespDtoList = new ArrayList<>();
		// 分页补充app数据
		List<OnlineStoreWithAppInfoRespDto> distinctClientDataList = onlineStoreAppInfoData.stream().distinct()
				//美团服务商业务区别，暂无法通过此方式处理，先过滤掉减少错误
				.filter(item -> !DsConstants.INTEGER_TWO.equals(item.getAccessType())).collect(Collectors.toList());
		new BatchListHandler<OnlineStoreWithAppInfoRespDto>().batchResolve(distinctClientDataList, 100, (items) ->{
			List<AppInfoRespDto> appInfoRespDtoList = dsOnlineClientMapper.selectClientInfo(items);
			if(!CollectionUtils.isEmpty(appInfoRespDtoList)){
				allAppInfoRespDtoList.addAll(appInfoRespDtoList);
			}
		});
		// 重组原始数据
		Map<String,AppInfoRespDto> uniqueKeyAppMap = allAppInfoRespDtoList.stream().collect(Collectors.toMap(AppInfoRespDto::uniqueKey,a->a,(a,b)->a));
		List<OnlineStoreWithAppInfoRespDto> originStoreDataList = new ArrayList<>();
		onlineStoreAppInfoData.forEach(data -> {
			String sessionKey = sessionKeyMap.get(data.getMerCode());
			AppInfoRespDto appInfoRespDto = uniqueKeyAppMap.get(data.uniqueKey());
			if(StringUtils.isEmpty(sessionKey) || null == appInfoRespDto){
				return;
			}
			appInfoRespDto.setSessionKey(sessionKey);
			data.setAppInfo(appInfoRespDto);
			originStoreDataList.add(data);
		});
		return originStoreDataList;
	}
	
}
