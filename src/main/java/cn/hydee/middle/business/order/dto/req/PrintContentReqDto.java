package cn.hydee.middle.business.order.dto.req;

import cn.hydee.middle.business.order.domain.SimplePage;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotNull;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2020/1/17 14:21
 */
@Data
public class PrintContentReqDto extends SimplePage {
    @ApiModelProperty(value = "商家编码",hidden = true)
    private String merCode;
    @ApiModelProperty(value = "线下门店编码",required = true)
    @NotNull(message = "线下门店编码不能为空")
    private String storeCode;
    @ApiModelProperty(value = "打印状态", hidden = true)
    private Integer status;
}
