package cn.hydee.middle.business.order.dto.rsp;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * cn.hydee.middle.business.order.dto.rsp
 *
 * <AUTHOR>
 * @version 1.0
 * @date 2020/6/18 10:52
 **/
@Data
public class OrderRealTimeCountRsp {
    @ApiModelProperty("待审方")
    Integer unCheckNum=0;
    @ApiModelProperty("待接单")
    Integer unTakeNum=0;
    @ApiModelProperty("待拣货")
    Integer unPickNum=0;
    @ApiModelProperty("待配送")
    Integer unDeliveryNum=0;
    @ApiModelProperty("配送中")
    Integer unReceiveNum=0;
}
