
package cn.hydee.middle.business.order.point.dto.rsp;

import java.io.Serializable;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

@Data
public class OnlineStorePlatformOrderRspDto implements Serializable {

	private static final long serialVersionUID = -615737236821845549L;

    @ApiModelProperty(value = "平台编码")
	private String platformCode;
    
    @ApiModelProperty(value = "平台名称")
	private String platformCodeName;
    
    @ApiModelProperty(value = "订单量")
	private int orderCount;
}
  
