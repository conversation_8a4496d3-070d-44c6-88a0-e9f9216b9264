package cn.hydee.middle.business.order.dto;

import cn.hydee.middle.business.order.Enums.OrderLockFlagEnum;
import cn.hydee.middle.business.order.Enums.OrderStateEnum;
import cn.hydee.middle.business.order.dto.rsp.CommodityRspDto;
import lombok.Builder;
import lombok.Data;

import java.util.List;

@Data
@Builder
public class CommodityCheckDto {

    private boolean sendReviewMessage;

    private OrderStateEnum orderStateEnum;

    private OrderLockFlagEnum lockFlagEnum;

    private List<CommodityRspDto> commodityRspDtoList;

}
