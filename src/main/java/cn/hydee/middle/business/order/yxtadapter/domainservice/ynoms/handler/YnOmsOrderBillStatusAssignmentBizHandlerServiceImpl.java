package cn.hydee.middle.business.order.yxtadapter.domainservice.ynoms.handler;

import cn.hydee.middle.business.order.yxtadapter.constant.AssignmentBizType;
import cn.hydee.middle.business.order.yxtadapter.domain.assignment.Assignment;
import cn.hydee.middle.business.order.yxtadapter.domain.oms.transform.OrderStd;
import cn.hydee.middle.business.order.yxtadapter.domainservice.assignment.AssignmentBizHandleService;
import cn.hydee.middle.business.order.yxtadapter.domainservice.assignment.AssignmentEngine;
import cn.hydee.middle.business.order.yxtadapter.domainservice.channel.ChannelGateway;
import cn.hydee.middle.business.order.yxtadapter.infrastructure.db.mysql.mapper.YnOmsOrderInforMapper;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

@Component
public class YnOmsOrderBillStatusAssignmentBizHandlerServiceImpl implements AssignmentBizHandleService {


    @Autowired
    private AssignmentEngine assignmentEngine;

    @Autowired
    private ChannelGateway channelGateway;

    @Autowired
    private YnOmsOrderInforMapper ynOmsOrderInforMapper;

    @Override
    public AssignmentBizType getAssignmentBizType() {
        return AssignmentBizType.OMS_ORDER_BILL_STATUS_WB;
    }

    @Override
    public boolean executeAssignment(Assignment assignment) {
        String reqContent = assignment.getReqContent();
        LambdaQueryWrapper<OrderStd> driver = new LambdaQueryWrapper<>();
        driver.eq(OrderStd::getOrdercode,reqContent);
        OrderStd orderStd = ynOmsOrderInforMapper.selectOne(driver);
        assignmentEngine.executeAssignment(assignment, jobStatus -> {
            boolean b = channelGateway.orderBillStatusWriteBackStep2(reqContent,orderStd);
            if (!b) {
                jobStatus.markFailure(AssignmentBizType.OMS_ORDER_BILL_STATUS_WB.getCnName() + " 失败");
            }
            return assignment;
        });

        return true;
    }
}
