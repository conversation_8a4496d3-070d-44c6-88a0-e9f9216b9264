package cn.hydee.middle.business.order.service;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.json.JSONUtil;
import cn.hydee.middle.business.order.Enums.AccountFialEnum;
import cn.hydee.middle.business.order.Enums.DsConstants;
import cn.hydee.middle.business.order.Enums.ErpStateEnum;
import cn.hydee.middle.business.order.batch.export.dto.OrderQueryExportReqDTO;
import cn.hydee.middle.business.order.batch.export.dto.OverallOrderExportDTO;
import cn.hydee.middle.business.order.configuration.ClusterOrderConfiguration;
import cn.hydee.middle.business.order.configuration.MerCodesConfig;
import cn.hydee.middle.business.order.dto.*;
import cn.hydee.middle.business.order.dto.common.OrderCommonDTO;
import cn.hydee.middle.business.order.dto.gy.GyInfoDTO;
import cn.hydee.middle.business.order.dto.req.*;
import cn.hydee.middle.business.order.dto.rsp.OrderInfoPageRsp;
import cn.hydee.middle.business.order.dto.rsp.OrderNoStoreExceptionRsp;
import cn.hydee.middle.business.order.dto.rsp.SearchDeliveryRspDto;
import cn.hydee.middle.business.order.dto.rsp.SearchOrderRspDto;
import cn.hydee.middle.business.order.elasticsearch.model.OrderInfoEsDto;
import cn.hydee.middle.business.order.elasticsearch.requestbuild.*;
import cn.hydee.middle.business.order.elasticsearch.service.ElasticService;
import cn.hydee.middle.business.order.elasticsearch.util.ESUtils;
import cn.hydee.middle.business.order.entity.OrderInfo;
import cn.hydee.middle.business.order.entity.OrderPrescription;
import cn.hydee.middle.business.order.entity.b2c.OmsOrderInfo;
import cn.hydee.middle.business.order.mapper.OrderDeliveryRecordMapper;
import cn.hydee.middle.business.order.mapper.OrderInfoExportMapper;
import cn.hydee.middle.business.order.mapper.OrderInfoMapper;
import cn.hydee.middle.business.order.mapper.OrderPrescriptionMapper;
import cn.hydee.middle.business.order.mapper.b2c.OmsOrderInfoMapper;
import cn.hydee.middle.business.order.point.dto.ClusterBaseDto;
import cn.hydee.middle.business.order.point.dto.req.PointClusterBaseReqDto;
import cn.hydee.middle.business.order.point.dto.req.PointClusterReqDto;
import cn.hydee.middle.business.order.point.dto.rsp.OnlineStorePlatformOrderRspDto;
import cn.hydee.middle.business.order.point.dto.rsp.StorePlatformOrderCountEsDto;
import cn.hydee.middle.business.order.util.RegularUtil;
import cn.hydee.starter.dto.PageDTO;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import javafx.util.Pair;
import lombok.extern.slf4j.Slf4j;
import org.elasticsearch.action.search.SearchRequest;
import org.elasticsearch.action.search.SearchScrollRequest;
import org.elasticsearch.client.core.CountRequest;
import org.elasticsearch.core.TimeValue;
import org.elasticsearch.search.Scroll;
import org.jetbrains.annotations.Nullable;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;
import org.springframework.util.StringUtils;

import java.util.*;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;

/**
 * es增强类  用于处理查库转到查es
 *
 * @Author: chufeng(2910)
 * @Date: 2021/10/27 11:24
 */
@Service
@Slf4j
public class EsEnhanceServiceImpl implements EsEnhanceService {

    @Autowired
    private MerCodesConfig merCodesConfig;
    @Autowired
    private OrderInfoMapper orderInfoMapper;
    @Autowired
    private OmsOrderInfoMapper omsOrderInfoMapper;
    @Autowired
    private ElasticService elasticService;
    @Autowired
    private OrderInfoExportMapper orderInfoExportMapper;
    @Autowired
    private OrderDeliveryRecordMapper orderDeliveryRecordMapper;
    @Value("${queryes.indexname}")
    private String baseIndexName;
    @Autowired
    private ClusterOrderConfiguration clusterOrderConfiguration;
    @Autowired
    private OrderPrescriptionMapper orderPrescriptionMapper;


    @Override
    public IPage<SearchOrderRspDto> searchOrderAllPage(Page<SearchOrderRspDto> page, OrderQuerySearchReqDto pageBase, List<String> organizationList, List<String> platformCodeList) {
        if (merCodesConfig.isQueryEs()) {
            //带订单号精准查询的走数据库查询
            if (!StringUtils.isEmpty(pageBase.getThirdOrderNo()) || null != pageBase.getOrderNo()) {
                return orderInfoMapper.searchOrderAllPage(page, pageBase, organizationList, platformCodeList);
            }
            SearchRequest searchRequest = null;
            try {
                String index = ESUtils.getOrderInfoIndexByStr(baseIndexName);
                searchRequest = OrderAllQueryBuild.assemblyRequest(index, pageBase, organizationList, platformCodeList);
                PageDTO<OrderInfoEsDto> pageResult = elasticService.queryModelFromEs(searchRequest, OrderInfoEsDto.class);
                IPage<SearchOrderRspDto> result = new Page();
                result.setTotal(pageResult.getTotalCount());
                result.setSize(pageBase.getPageSize());
                result.setCurrent(pageBase.getCurrentPage());
                List<SearchOrderRspDto> records = trans2PageResult(pageResult.getData());
                result.setRecords(records);
                return result;
            } catch (Exception e) {
                log.error("searchOrderAllPage from es error,request source:{},", null == searchRequest ? "" : JSON.toJSONString(searchRequest), e);
            }
        }
        return orderInfoMapper.searchOrderAllPage(page, pageBase, organizationList, platformCodeList);
    }

    @Override
    public IPage<OrderInfoPageRsp> selectOrderAllLedgerPage(Page<OrderInfoPageRsp> page, OrderLedgerPageReqDto req, List<String> organizationList, List<String> platformCodeList) {
        if (Objects.nonNull(req.getBillState()) && CollUtil.isNotEmpty(req.getBillState().getErpStateList()) && req.getBillState().getErpStateList().contains(ErpStateEnum.HAS_SALE_FAIL.getCode()) && Objects.nonNull(req.getAccountFailStatus())) {
            req.setAccountFailMessage(AccountFialEnum.getMsgByCode(req.getAccountFailStatus()));
        }
        if (merCodesConfig.isQueryEs()) {
            //带订单号精准查询的走数据库查询
            if (!StringUtils.isEmpty(req.getThirdOrderNo()) || null != req.getOrderNo()) {
                return orderInfoMapper.selectOrderAllLedgerPage(page, req, organizationList, platformCodeList);
            }
            SearchRequest searchRequest = null;
            try {
                String index = ESUtils.getOrderInfoIndexByStr(baseIndexName);
                searchRequest = OrderLedgerQueryBuild.assemblyRequest(index, req, organizationList, platformCodeList);
                PageDTO<OrderInfoEsDto> pageResult = elasticService.queryModelFromEs(searchRequest, OrderInfoEsDto.class);
                IPage<OrderInfoPageRsp> result = new Page();
                result.setTotal(pageResult.getTotalCount());
                result.setSize(req.getPageSize());
                result.setCurrent(req.getCurrentPage());
                List<OrderInfoPageRsp> records = trans2PageLedgerResult(pageResult.getData(), req.getBillState());
                result.setRecords(records);
                return result;
            } catch (Exception e) {
                log.error("selectOrderAllLedgerPage from es error,request source {},", null == searchRequest ? "" : JSONObject.toJSONString(searchRequest), e);
            }
        }
        return orderInfoMapper.selectOrderAllLedgerPage(page, req, organizationList, platformCodeList);
    }

    @Override
    public int selectOrderAllLedgerCount(OrderLedgerPageReqDto req, List<String> organizationList, List<String> platformCodeList) {
        if (Objects.nonNull(req.getBillState()) && CollUtil.isNotEmpty(req.getBillState().getErpStateList()) && req.getBillState().getErpStateList().contains(ErpStateEnum.HAS_SALE_FAIL.getCode()) && Objects.nonNull(req.getAccountFailStatus())) {
            req.setAccountFailMessage(AccountFialEnum.getMsgByCode(req.getAccountFailStatus()));
        }
        if (merCodesConfig.isQueryEs()) {
            //带订单号精准查询的走数据库查询
            if (!StringUtils.isEmpty(req.getThirdOrderNo()) || null != req.getOrderNo()) {
                return orderInfoMapper.selectOrderAllLedgerCount(req, organizationList, platformCodeList);
            }
            CountRequest searchRequest = null;
            try {
                String index = ESUtils.getOrderInfoIndexByStr(baseIndexName);
                searchRequest = OrderLedgerCountBuild.assemblyCountRequest(index, req, organizationList, platformCodeList);
                long count = elasticService.countSearch(searchRequest);
                return (int) count;
            } catch (Exception e) {
                log.error("selectOrderAllLedgerPage from es error,request source {},", null == searchRequest ? "" : JSONObject.toJSONString(searchRequest), e);
            }
        }
        return orderInfoMapper.selectOrderAllLedgerCount(req, organizationList, platformCodeList);
    }

    @Override
    public IPage<OrderNoStoreExceptionRsp> selectOrderNoStoreExceptionPage(Page<OrderNoStoreExceptionRsp> page, OrderNoStoreExceptionReqDto pageBase, Integer minExceptionCode, Integer maxExceptionCode, List<String> platformCodeList) {
        if (merCodesConfig.isQueryEs()) {
            //带订单号精准查询的走数据库查询
            if (!StringUtils.isEmpty(pageBase.getThirdOrderNo()) || null != pageBase.getOrderNo()) {
                return orderInfoMapper.selectOrderNoStoreExceptionPage(page, pageBase, minExceptionCode, maxExceptionCode, platformCodeList);
            }
            SearchRequest searchRequest = null;
            try {
                String index = ESUtils.getOrderInfoIndexByStr(baseIndexName);
                searchRequest = OrderNoStoreQueryBuild.assemblyRequest(index, pageBase, minExceptionCode, maxExceptionCode, platformCodeList);
                PageDTO<OrderInfoEsDto> pageResult = elasticService.queryModelFromEs(searchRequest, OrderInfoEsDto.class);
                IPage<OrderNoStoreExceptionRsp> result = new Page();
                result.setTotal(pageResult.getTotalCount());
                result.setSize(pageBase.getPageSize());
                result.setCurrent(pageBase.getCurrentPage());
                List<OrderNoStoreExceptionRsp> records = trans2NoStoreResult(pageResult.getData());
                result.setRecords(records);
                return result;
            } catch (Exception e) {
                log.error("selectOrderNoStoreExceptionPage from es error,request source:{},", null == searchRequest ? "" : JSONObject.toJSONString(searchRequest), e);
            }
        }
        return orderInfoMapper.selectOrderNoStoreExceptionPage(page, pageBase, minExceptionCode, maxExceptionCode, platformCodeList);
    }

    @Override
    public IPage<OrderInfoPageRsp> selectOrderPage(Page<OrderInfoPageRsp> page, OrderPageReqDto req, List<String> organizationList, List<String> platformCodeList) {
        if (merCodesConfig.isQueryEs()) {
            //带订单号精准查询的走数据库查询
            if (!StringUtils.isEmpty(req.getThirdOrderNo()) || null != req.getOrderNo()) {
                return orderInfoMapper.selectOrderPage(page, req, organizationList, platformCodeList);
            }
            SearchRequest searchRequest = null;
            try {
                String index = ESUtils.getOrderInfoIndexByStr(baseIndexName);
                if (req.getCurrentPage() <= 0) {
                    log.info("selectOrderPage currentPage is 0 ,req:{}", JSONObject.toJSONString(req));
                    req.setCurrentPage(1);
                }
                searchRequest = OrderNormalQueryBuild.assemblyRequest(index, req, organizationList, platformCodeList);
                PageDTO<OrderInfoEsDto> pageResult = elasticService.queryModelFromEs(searchRequest, OrderInfoEsDto.class);
                IPage<OrderInfoPageRsp> result = new Page();
                result.setTotal(pageResult.getTotalCount());
                result.setSize(req.getPageSize());
                result.setCurrent(req.getCurrentPage());
                List<OrderInfoPageRsp> records = trans2NormalResult(pageResult.getData(), req.getOrderBy(), req.getOrderState());
                result.setRecords(records);
                return result;
            } catch (Exception e) {
                log.error("selectOrderAllLedgerPage from es error,request source {},", null == searchRequest ? "" : JSONObject.toJSONString(searchRequest), e);
            }
        }

        log.info("req:{},organizationList:{},platformCodeList：{}", JSONUtil.toJsonStr(req), JSONUtil.toJsonStr(organizationList), JSONUtil.toJsonStr(platformCodeList));
        return orderInfoMapper.selectOrderPage(page, req, organizationList, platformCodeList);
    }

    @Override
    public IPage<OrderInfoPageRsp> selectOrderPageForSourceOrg(Page<OrderInfoPageRsp> page, OrderPageReqDto req, List<String> sourceOrganizationList, List<String> platformCodeList) {
        if (merCodesConfig.isQueryEs()) {
            //带订单号精准查询的走数据库查询
            if (!StringUtils.isEmpty(req.getThirdOrderNo()) || null != req.getOrderNo()) {
                return orderInfoMapper.selectOrderPageForSourceOrg(page, req, sourceOrganizationList, platformCodeList);
            }
            SearchRequest searchRequest = null;
            try {
                String index = ESUtils.getOrderInfoIndexByStr(baseIndexName);
                if (req.getCurrentPage() <= 0) {
                    log.info("selectOrderPage currentPage is 0 ,req:{}", JSONObject.toJSONString(req));
                    req.setCurrentPage(1);
                }
                searchRequest = OrderNormalQueryBuild.assemblyRequestForSourceOrgCode(index, req, sourceOrganizationList, platformCodeList);
                PageDTO<OrderInfoEsDto> pageResult = elasticService.queryModelFromEs(searchRequest, OrderInfoEsDto.class);
                IPage<OrderInfoPageRsp> result = new Page();
                result.setTotal(pageResult.getTotalCount());
                result.setSize(req.getPageSize());
                result.setCurrent(req.getCurrentPage());
                List<OrderInfoPageRsp> records = trans2NormalResult(pageResult.getData(), req.getOrderBy(), req.getOrderState());
                result.setRecords(records);
                return result;
            } catch (Exception e) {
                log.error("selectOrderAllLedgerPage from es error,request source {},", null == searchRequest ? "" : JSONObject.toJSONString(searchRequest), e);
            }
        }

        log.info("req:{},organizationList:{},platformCodeList：{}", JSONUtil.toJsonStr(req), JSONUtil.toJsonStr(sourceOrganizationList), JSONUtil.toJsonStr(platformCodeList));
        return orderInfoMapper.selectOrderPageForSourceOrg(page, req, sourceOrganizationList, platformCodeList);
    }

    @Override
    public Integer countWithOrganizationList(OrderQueryExportReqDTO reqDTO, List<String> platFromCodes, List<String> organizationList) {
        if (merCodesConfig.isQueryEs()) {
            if (!StringUtils.isEmpty(reqDTO.getThirdOrderNo()) || reqDTO.getOrderNo() != null) {
                return orderInfoExportMapper.countWithOrganizationList(reqDTO, platFromCodes, organizationList);
            }
            CountRequest countRequest = null;
            try {
                String index = ESUtils.getOrderInfoIndexByStr(baseIndexName);
                countRequest = OrderExportQueryBuild.assemblyCount(index, reqDTO, platFromCodes, organizationList);
                Long count = elasticService.countSearch(countRequest);
                return count.intValue();
            } catch (Exception e) {
                log.error("countWithOrganizationList from es error,request source {},", null == countRequest ? "" : JSONObject.toJSONString(countRequest), e);
            }
        }
        return orderInfoExportMapper.countWithOrganizationList(reqDTO, platFromCodes, organizationList);
    }

    @Override
    public List<OverallOrderExportDTO> listOverall(OrderQueryExportReqDTO req, List<String> platformCodeList, List<String> organizationList) {
        if (merCodesConfig.isQueryEs()) {
            //带订单号精准查询的走数据库查询
            if (!StringUtils.isEmpty(req.getThirdOrderNo()) || null != req.getOrderNo()) {
                return orderInfoExportMapper.listOverall(req, platformCodeList, organizationList);
            }
            SearchRequest searchRequest = null;
            try {
                String index = ESUtils.getOrderInfoIndexByStr(baseIndexName);
                searchRequest = OrderExportQueryBuild.assemblyRequest(index, req, organizationList, platformCodeList);
                PageDTO<OrderInfoEsDto> pageResult = elasticService.queryModelFromEs(searchRequest, OrderInfoEsDto.class);
                List<OverallOrderExportDTO> records = trans2ExportResult(pageResult.getData());
                return records;
            } catch (Exception e) {
                log.error("selectOrderAllLedgerPage from es error,request source {},OrderQueryExportReqDTO {},platformCodeList {},organizationList{},", null == searchRequest ? "" : JSON.toJSONString(searchRequest),
                        JSON.toJSONString(req), JSON.toJSONString(platformCodeList), JSON.toJSONString(organizationList),
                        e);
            }
        }
        return orderInfoExportMapper.listOverall(req, platformCodeList, organizationList);
    }

    @Override
    public Pair<String, List<OverallOrderExportDTO>> listOverallByScroll(OrderQueryExportReqDTO req, List<String> platformCodeList, List<String> organizationList) {
        if (!merCodesConfig.isQueryEs()) {
            List<OverallOrderExportDTO> orderExportFromDB = orderInfoExportMapper.listOverall(req, platformCodeList, organizationList);
            return new Pair<>(null, orderExportFromDB);
        }
        //带订单号精准查询的走数据库查询
        if (!StringUtils.isEmpty(req.getThirdOrderNo()) || null != req.getOrderNo()) {
            List<OverallOrderExportDTO> orderExportFromDB = orderInfoExportMapper.listOverall(req, platformCodeList, organizationList);
            return new Pair<>(null, orderExportFromDB);
        }
        Pair<String, List<OrderInfoEsDto>> resultPair = null;
        //scroll为空，表示第一次查询
        Scroll scroll = new Scroll(new TimeValue(5L, TimeUnit.MINUTES));
        if (StrUtil.isBlank(req.getScrollId())) {
            SearchRequest searchRequest = null;
            String index = ESUtils.getOrderInfoIndexByStr(baseIndexName);
            searchRequest = OrderExportQueryBuild.assemblyScrollRequest(index, req, organizationList, platformCodeList);
            searchRequest.scroll(scroll);
            resultPair = elasticService.queryModelFromEsV2(searchRequest, OrderInfoEsDto.class);
        } else {
            SearchScrollRequest searchScrollRequest = new SearchScrollRequest(req.getScrollId());
            searchScrollRequest.scroll(scroll);
            resultPair = elasticService.queryModelFromEsByScroll(searchScrollRequest, OrderInfoEsDto.class);
        }
        List<OverallOrderExportDTO> records = trans2ExportResult(resultPair.getValue());
        log.info("listOverallByScroll查询参数:{},查询结果:{}",JSONObject.toJSONString(req),JSONObject.toJSONString(records));
        return new Pair<>(resultPair.getKey(), records);
    }

    @Override
    public List<String> queryPrescription(OrderQueryExportReqDTO req, List<String> platformCodeList, List<String> organizationList) {
        if (merCodesConfig.isQueryEs()) {
            //带订单号精准查询的走数据库查询
            if (!StringUtils.isEmpty(req.getThirdOrderNo()) || null != req.getOrderNo()) {
                return orderInfoExportMapper.listPrescriptionUrl(req, platformCodeList, organizationList);
            }
            SearchRequest searchRequest = null;
            try {
                String index = ESUtils.getOrderInfoIndexByStr(baseIndexName);
                searchRequest = OrderExportQueryBuild.assemblyRequest(index, req, organizationList, platformCodeList);
                PageDTO<OrderInfoEsDto> pageResult = elasticService.queryModelFromEs(searchRequest, OrderInfoEsDto.class);
                List<String> records = trans2ExportUrlResult(pageResult.getData());
                return records;
            } catch (Exception e) {
                log.error("selectOrderAllLedgerPage from es error,request source {},", null == searchRequest ? "" : JSONObject.toJSONString(searchRequest), e);
            }
        }
        return orderInfoExportMapper.listPrescriptionUrl(req, platformCodeList, organizationList);
    }

    @Override
    public List<Long> queryMemberConsumerData(MemberNoQueryDto reqDto) {
        String index = ESUtils.getOrderInfoIndexByStr(baseIndexName);
        reqDto.setCurrentPage(1);
        SearchRequest searchRequest = OrderExportQueryBuild.assemblyRequestOnlyOrderNo(index, reqDto);
        try {
            PageDTO<OrderInfoEsDto> pageResult = elasticService.queryModelFromEs(searchRequest, OrderInfoEsDto.class);
            // 2000/次 查全部
            int pageSize = 2000;
            int pageNo = 1;
            int exeCount = pageResult.getTotalCount() / pageSize + 1;
            reqDto.setPageSize(pageSize);
            List<Long> orderNoList = new ArrayList<>();
            while (exeCount >= pageNo) {
                reqDto.setCurrentPage(pageNo);
                searchRequest = OrderExportQueryBuild.assemblyRequestOnlyOrderNo(index, reqDto);
                pageResult = elasticService.queryModelFromEs(searchRequest, OrderInfoEsDto.class);
                if (null != pageResult && !CollectionUtils.isEmpty(pageResult.getData())) {
                    orderNoList.addAll(pageResult.getData().stream().map(OrderInfoEsDto::getOrderNo).collect(Collectors.toList()));
                }
                pageNo++;
            }
            return orderNoList;
        } catch (Exception e) {
            log.error("queryMemberConsumerData from es error,request source {},", null == searchRequest ? "" : JSONObject.toJSONString(searchRequest), e);
        }
        return Collections.emptyList();
    }

    @Override
    public List<GyInfoDTO> queryOrderBySaleNos(String merCode, List<String> saleNos) {
        if (merCodesConfig.isQueryEs()) {
            SearchRequest searchRequest = null;
            try {
                String index = ESUtils.getOrderInfoIndexByStr(baseIndexName);
                searchRequest = OrderSaleNoQueryBuild.assemblyRequest(index, merCode, saleNos);
                PageDTO<OrderInfoEsDto> pageResult = elasticService.queryModelFromEs(searchRequest, OrderInfoEsDto.class);
                List<GyInfoDTO> records = trans2GyInfoResult(pageResult.getData());
                return records;
            } catch (Exception e) {
                log.error("queryOrderBySaleNos from es error,request source {},", null == searchRequest ? "" : JSONObject.toJSONString(searchRequest), e);
            }
        }

        QueryWrapper<OrderInfo> queryWrapper = new QueryWrapper<>();
        queryWrapper.lambda().eq(OrderInfo::getMerCode, merCode).in(OrderInfo::getErpSaleNo, saleNos).select(OrderInfo::getOrderNo, OrderInfo::getThirdOrderNo, OrderInfo::getErpSaleNo, OrderInfo::getThirdPlatformCode);
        List<OrderInfo> orderInfoList = orderInfoMapper.selectList(queryWrapper);
        if (CollectionUtils.isEmpty(orderInfoList)) {
            return Collections.emptyList();
        }
        return orderInfoList.stream().map(orderInfo -> {
            GyInfoDTO gyInfoDTO = new GyInfoDTO();
            gyInfoDTO.setOrderId(orderInfo.getOrderNo());
            gyInfoDTO.setSaleNo(orderInfo.getErpSaleNo());
            gyInfoDTO.setThirdOrderNo(orderInfo.getThirdOrderNo());
            gyInfoDTO.setPlatformCode(orderInfo.getThirdPlatformCode());
            return gyInfoDTO;
        }).collect(Collectors.toList());
    }

    @Override
    public OrderCommonDTO queryByOrderNoOrThirdNo(String merCode, String orderNoOrThirdNo, List<String> platformCodeList, List<String> organizationCodeList) {
        if (merCodesConfig.isQueryEs()) {
            SearchRequest searchRequest = null;
            try {
                String index = ESUtils.getOrderInfoIndexByStr(baseIndexName);
                searchRequest = OrderNoOrThirdNoQueryBuild.assemblyRequest(index, merCode, orderNoOrThirdNo, platformCodeList, organizationCodeList);
                PageDTO<OrderInfoEsDto> pageResult = elasticService.queryModelFromEs(searchRequest, OrderInfoEsDto.class);
                if (Objects.nonNull(pageResult) && !CollectionUtils.isEmpty(pageResult.getData())) {
                    return OrderCommonDTO.convertBean(pageResult.getData().get(0), orderNoOrThirdNo);
                }
            } catch (Exception e) {
                log.error("queryByOrderNoOrThirdNo from es error,request source {},", null == searchRequest ? "" : JSONObject.toJSONString(searchRequest), e);
            }
        }

        // 非纯数字，只查平台单号
        if (!RegularUtil.checkIsNumber(orderNoOrThirdNo)) {
            return queryInfoByThirdNo(merCode, orderNoOrThirdNo);
        }
        // 纯数字，先查公共表的系统订单号
        QueryWrapper<OrderInfo> queryWrapper = new QueryWrapper<>();
        queryWrapper.lambda().eq(OrderInfo::getMerCode, merCode).eq(OrderInfo::getOrderNo, orderNoOrThirdNo).select(OrderInfo::getOrderNo, OrderInfo::getThirdOrderNo, OrderInfo::getServiceMode);
        List<OrderInfo> orderInfoList = orderInfoMapper.selectList(queryWrapper);
        if (!CollectionUtils.isEmpty(orderInfoList)) {
            return OrderCommonDTO.convertBean(orderInfoList.get(0), orderNoOrThirdNo);
        } else {
            // 再查公共表的平台单号
            return queryInfoByThirdNo(merCode, orderNoOrThirdNo);
        }
    }

    @Nullable
    private OrderCommonDTO queryInfoByThirdNo(String merCode, String orderNoOrThirdNo) {
        QueryWrapper<OrderInfo> queryWrapper = new QueryWrapper<>();
        queryWrapper.lambda().eq(OrderInfo::getMerCode, merCode).eq(OrderInfo::getThirdOrderNo, orderNoOrThirdNo).select(OrderInfo::getOrderNo, OrderInfo::getThirdOrderNo, OrderInfo::getServiceMode);
        List<OrderInfo> orderInfoList = orderInfoMapper.selectList(queryWrapper);
        if (!CollectionUtils.isEmpty(orderInfoList)) {
            return OrderCommonDTO.convertBean(orderInfoList.get(0), orderNoOrThirdNo);
        }
        // 再查B2C独立表
        QueryWrapper<OmsOrderInfo> b2CQueryWrapper = new QueryWrapper<>();
        b2CQueryWrapper.lambda().eq(OmsOrderInfo::getMerCode, merCode).eq(OmsOrderInfo::getOmsOrderNo, orderNoOrThirdNo).select(OmsOrderInfo::getOmsOrderNo, OmsOrderInfo::getThirdOrderNo);
        List<OmsOrderInfo> omsOrderInfoList = omsOrderInfoMapper.selectList(b2CQueryWrapper);
        if (!CollectionUtils.isEmpty(omsOrderInfoList)) {
            return OrderCommonDTO.convertBean(omsOrderInfoList.get(0), orderNoOrThirdNo);
        }

        return null;
    }

    private List<GyInfoDTO> trans2GyInfoResult(List<OrderInfoEsDto> datas) {
        if (CollectionUtils.isEmpty(datas)) {
            return Collections.emptyList();
        }
        return datas.stream().map(orderInfo -> {
            GyInfoDTO gyInfoDTO = new GyInfoDTO();
            gyInfoDTO.setOrderId(orderInfo.getOrderNo());
            gyInfoDTO.setSaleNo(orderInfo.getErpSaleNo());
            gyInfoDTO.setThirdOrderNo(orderInfo.getThirdOrderNo());
            gyInfoDTO.setPlatformCode(orderInfo.getThirdPlatformCode());
            return gyInfoDTO;
        }).collect(Collectors.toList());
    }

    @Override
    public IPage<SearchDeliveryRspDto> searchDeliveryAllPage(Page<SearchDeliveryRspDto> page, DeliveryQuerySearchReqDto pageBase, List<String> organizationList, List<String> platformCodeList) {
        // orderDeliveryRecordMapper.searchDeliveryAllPage
        if (merCodesConfig.isQueryEs()) {
            //带订单号精准查询的走数据库查询
            if (!StringUtils.isEmpty(pageBase.getThirdOrderNo()) || null != pageBase.getOrderNo()) {
                return orderDeliveryRecordMapper.searchDeliveryAllPage(page, pageBase, organizationList, platformCodeList);
            }
            SearchRequest searchRequest = null;
            try {
                String index = ESUtils.getOrderInfoIndexByStr(baseIndexName);
                if (pageBase.getCurrentPage() <= 0) {
                    log.info("searchDeliveryAllPage currentPage is 0 ,req:{}", JSONObject.toJSONString(pageBase));
                    pageBase.setCurrentPage(1);
                }
                searchRequest = OrderDeliveryQueryBuild.assemblyRequest(index, pageBase, organizationList, platformCodeList);
                PageDTO<OrderInfoEsDto> pageResult = elasticService.queryModelFromEs(searchRequest, OrderInfoEsDto.class);
                IPage<SearchDeliveryRspDto> result = new Page();
                result.setTotal(pageResult.getTotalCount());
                result.setSize(pageBase.getPageSize());
                result.setCurrent(pageBase.getCurrentPage());
                List<SearchDeliveryRspDto> records = trans2DeliveryResult(pageResult.getData());
                result.setRecords(records);
                return result;
            } catch (Exception e) {
                log.error("searchDeliveryAllPage from es error,request source {},", null == searchRequest ? "" : JSONObject.toJSONString(searchRequest), e);
            }
        }
        return orderDeliveryRecordMapper.searchDeliveryAllPage(page, pageBase, organizationList, platformCodeList);
    }

    @Override
    public int countSearchDeliveryAllPage(DeliveryExportReqDto req, List<String> organizationList, List<String> platformCodeList) {
        //orderDeliveryRecordMapper.countSearchDeliveryAllPage
        if (merCodesConfig.isQueryEs()) {
            //带订单号精准查询的走数据库查询
            if (!StringUtils.isEmpty(req.getThirdOrderNo()) || null != req.getOrderNo()) {
                return orderDeliveryRecordMapper.countSearchDeliveryAllPage(req, organizationList, platformCodeList);
            }
            CountRequest searchRequest = null;
            try {
                String index = ESUtils.getOrderInfoIndexByStr(baseIndexName);
                searchRequest = OrderDeliveryCountBuild.assemblyCountRequest(index, req, organizationList, platformCodeList);
                long count = elasticService.countSearch(searchRequest);
                return (int) count;
            } catch (Exception e) {
                log.error("countSearchDeliveryAllPage from es error,request source {},", null == searchRequest ? "" : JSONObject.toJSONString(searchRequest), e);
            }
        }
        return orderDeliveryRecordMapper.countSearchDeliveryAllPage(req, organizationList, platformCodeList);
    }

    @Override
    public List<SearchDeliveryRspDto> searchDelivery(DeliveryExportReqDto reqDTO, List<String> organizationList, List<String> platformCodeList) {
        //orderDeliveryRecordMapper.searchDelivery
        if (merCodesConfig.isQueryEs()) {
            //带订单号精准查询的走数据库查询
            if (!StringUtils.isEmpty(reqDTO.getThirdOrderNo()) || null != reqDTO.getOrderNo()) {
                return orderDeliveryRecordMapper.searchDelivery(reqDTO, organizationList, platformCodeList);
            }
            SearchRequest searchRequest = null;
            try {
                String index = ESUtils.getOrderInfoIndexByStr(baseIndexName);
                if (reqDTO.getCurrentPage() <= 0) {
                    log.info("searchDeliveryAllPage currentPage is 0 ,req:{}", JSONObject.toJSONString(reqDTO));
                    reqDTO.setCurrentPage(1);
                }
                searchRequest = OrderDeliveryQueryBuild.assemblyRequest(index, reqDTO, organizationList, platformCodeList);
                PageDTO<OrderInfoEsDto> pageResult = elasticService.queryModelFromEs(searchRequest, OrderInfoEsDto.class);
                List<SearchDeliveryRspDto> records = trans2DeliveryResult(pageResult.getData());
                return records;
            } catch (Exception e) {
                log.error("searchDelivery from es error,request source {},", null == searchRequest ? "" : JSONObject.toJSONString(searchRequest), e);
            }
        }
        return orderDeliveryRecordMapper.searchDelivery(reqDTO, organizationList, platformCodeList);
    }

    private List<SearchDeliveryRspDto> trans2DeliveryResult(List<OrderInfoEsDto> datas) {
        if (CollectionUtils.isEmpty(datas)) {
            return new ArrayList<>();
        }
        List<Long> orderNoList = new ArrayList<>();
        datas.forEach(item -> {
            orderNoList.add(item.getOrderNo());
        });
        return orderDeliveryRecordMapper.searchDeliveryAllPageByOrderNos(orderNoList);
    }

    private List<OverallOrderExportDTO> trans2ExportResult(List<OrderInfoEsDto> datas) {
        if (CollectionUtils.isEmpty(datas)) {
            return new ArrayList<>();
        }
        List<Long> orderNoList = new ArrayList<>();
        datas.forEach(item -> {
            orderNoList.add(item.getOrderNo());
        });
        return orderInfoExportMapper.listOverallByOrderNos(orderNoList);
    }

    private List<String> trans2ExportUrlResult(List<OrderInfoEsDto> datas) {
        if (CollectionUtils.isEmpty(datas)) {
            return new ArrayList<>();
        }
        List<Long> orderNoList = new ArrayList<>();
        datas.forEach(item -> {
            orderNoList.add(item.getOrderNo());
        });
        List<OrderPrescription> orderPrescriptions = orderPrescriptionMapper.selectListByOrderNoList(orderNoList);
        return orderPrescriptions.stream().map(OrderPrescription::getCfpicurl).filter(o -> !StringUtils.isEmpty(o)).distinct().collect(Collectors.toList());
    }

    private List<OrderInfoPageRsp> trans2NormalResult(List<OrderInfoEsDto> datas, Integer orderBy, Integer orderState) {
        if (CollectionUtils.isEmpty(datas)) {
            return new ArrayList<>();
        }
        List<Long> orderNoList = new ArrayList<>();
        datas.forEach(item -> {
            orderNoList.add(item.getOrderNo());
        });
        return orderInfoMapper.selectOrderPageByOrderNos(orderNoList, orderBy, orderState);
    }

    private List<OrderNoStoreExceptionRsp> trans2NoStoreResult(List<OrderInfoEsDto> datas) {
        if (CollectionUtils.isEmpty(datas)) {
            return new ArrayList<>();
        }
        List<Long> orderNoList = new ArrayList<>();
        datas.forEach(item -> {
            orderNoList.add(item.getOrderNo());
        });
        return orderInfoMapper.selectOrderNoStoreExceptionPageByOrderNos(orderNoList);
    }


    //es内数据转换至返回数据   orderInfoMapper.selectOrderAllLedgerPage
    private List<OrderInfoPageRsp> trans2PageLedgerResult(List<OrderInfoEsDto> datas, OrderErpListDtoReq billState) {
        if (CollectionUtils.isEmpty(datas)) {
            return new ArrayList<>();
        }
        List<Long> orderNoList = new ArrayList<>();
        datas.forEach(item -> {
            orderNoList.add(item.getOrderNo());
        });
        return orderInfoMapper.selectOrderAllLedgerPageByOrderNos(orderNoList, billState);
    }

    //es内数据转换至返回数据   orderInfoMapper.searchOrderAllPage
    private List<SearchOrderRspDto> trans2PageResult(List<OrderInfoEsDto> datas) {
        if (CollectionUtils.isEmpty(datas)) {
            return new ArrayList<>();
        }
        List<Long> orderNoList = new ArrayList<>();
        datas.forEach(item -> {
            orderNoList.add(item.getOrderNo());
        });
        return orderInfoMapper.searchOrderAllPageByOrderNos(orderNoList);
    }

    @Override
    public Integer countOrderFromErpPrescription(ErpPrescriptionQryReqDto reqDto, List<String> platformCodes) {
        if (merCodesConfig.isQueryEs()) {
            CountRequest countRequest = null;
            try {
                String index = ESUtils.getOrderInfoIndexByStr(baseIndexName);
                countRequest = OrderFromErpPrescriptionQueryBuild.assemblyCountRequest(index, reqDto, platformCodes);
                Long count = elasticService.countSearch(countRequest);
                return count.intValue();
            } catch (Exception e) {
                log.error("countOrderFromErpPrescription from es error, request source {},", countRequest == null ? "" : JSON.toJSONString(countRequest), e);
            }
        }
        return orderInfoMapper.countOrderFromErpPrescription(reqDto, platformCodes);
    }

    @Override
    public List<ErpQryPrescriptionTempDto> queryOrderFromErpPrescription(ErpPrescriptionQryReqDto reqDto, List<String> platformCodes) {
        if (merCodesConfig.isQueryEs()) {
            //外部已执行传订单号直接查询数据库
            SearchRequest searchRequest = null;
            try {
                String index = ESUtils.getOrderInfoIndexByStr(baseIndexName);
                if (reqDto.getCurrentPage() <= 0) {
                    log.info("queryOrderFromErpPrescription currentPage is 0 ,req:{}", JSONObject.toJSONString(reqDto));
                    reqDto.setCurrentPage(1);
                }
                searchRequest = OrderFromErpPrescriptionQueryBuild.assemblyRequest(index, reqDto, platformCodes);
                PageDTO<OrderInfoEsDto> pageResult = elasticService.queryModelFromEs(searchRequest, OrderInfoEsDto.class);
                List<ErpQryPrescriptionTempDto> records = trans2ErpPrescription(pageResult.getData());
                return records;
            } catch (Exception e) {
                log.error("queryOrderFromErpPrescription from es error,request source {},", null == searchRequest ? "" : JSONObject.toJSONString(searchRequest), e);
            }
        }
        return orderInfoMapper.queryOrderFromErpPrescription(reqDto, platformCodes, DsConstants.INTEGER_ZERO, reqDto.getCurrentPage() * reqDto.getPageSize() + DsConstants.ONE_HUNDRED);
    }

    private List<ErpQryPrescriptionTempDto> trans2ErpPrescription(List<OrderInfoEsDto> records) {
        return records.stream().map(item -> {
            ErpQryPrescriptionTempDto tempDto = new ErpQryPrescriptionTempDto();
            tempDto.setOrderNo(item.getOrderNo());
            tempDto.setBillTime(new Date(item.getBillTime()));
            tempDto.setServiceMode(DsConstants.INTEGER_ONE);
            return tempDto;
        }).collect(Collectors.toList());
    }

    @Override
    public List<ClusterBaseDto> searchClusterOrder(PointClusterReqDto reqDto) {
        if (merCodesConfig.isQueryEs() && clusterOrderConfiguration.clusterFromEs(reqDto.getMerCode())) {
            SearchRequest searchRequest = null;
            try {
                String index = ESUtils.getOrderInfoIndexByStr(baseIndexName);
                searchRequest = ClusterOrderCountBuild.assymblyRequest(index, reqDto);
                PageDTO<OrderInfoEsDto> pageResult = elasticService.queryModelFromEs(searchRequest, OrderInfoEsDto.class);
                List<ClusterBaseDto> records = trans2ClusterBase(pageResult);
                return records;
            } catch (Exception e) {
                log.error("searchClusterOrder from es error, request source: {}", null == searchRequest ? "" : JSON.toJSONString(searchRequest), e);
            }
        }
        return orderInfoMapper.queryOrderLngLatByGeom(reqDto);
    }

    private List<ClusterBaseDto> trans2ClusterBase(PageDTO<OrderInfoEsDto> pageResult) {
        if (CollectionUtils.isEmpty(pageResult.getData())) {
            return Collections.emptyList();
        }
        return pageResult.getData().stream().map(item -> {
            ClusterBaseDto clusterBaseDto = new ClusterBaseDto();
            clusterBaseDto.setLatitude(Double.parseDouble(item.getReceiverLat()));
            clusterBaseDto.setLongitude(Double.parseDouble(item.getReceiverLng()));
            return clusterBaseDto;
        }).collect(Collectors.toList());
    }

    @Override
    public List<OnlineStorePlatformOrderRspDto> queryOrderCountGroupByPlatForm(String stCode, PointClusterBaseReqDto req) {
        if (merCodesConfig.isQueryEs() && clusterOrderConfiguration.clusterFromEs(req.getMerCode())) {
            SearchRequest searchRequest = null;
            try {
                String index = ESUtils.getOrderInfoIndexByStr(baseIndexName);
                searchRequest = PlatformOrderCountBuild.asymblyRequest(index, stCode, req);
                PageDTO<StorePlatformOrderCountEsDto> pageResult = elasticService.queryFieldFromEs(searchRequest, StorePlatformOrderCountEsDto.class);
                List<OnlineStorePlatformOrderRspDto> records = trans2StorePlatformOrderCount(pageResult);
                return records;
            } catch (Exception e) {
                log.error("queryOrderCountGroupByPlatForm from es error, request source: {}", null == searchRequest ? "" : JSON.toJSONString(searchRequest), e);
            }
        }

        return orderInfoMapper.queryOrderCountGroupByPlatForm(stCode, req);
    }

    private List<OnlineStorePlatformOrderRspDto> trans2StorePlatformOrderCount(PageDTO<StorePlatformOrderCountEsDto> pageResult) {
        if (CollectionUtils.isEmpty(pageResult.getData())) {
            return Collections.emptyList();
        }
        Map<String, Long> countMap = pageResult.getData().stream().collect(Collectors.groupingBy(StorePlatformOrderCountEsDto::getThirdPlatformCode, Collectors.counting()));
        List<OnlineStorePlatformOrderRspDto> data = new ArrayList<>();
        countMap.forEach((platformCode, count) -> {
            OnlineStorePlatformOrderRspDto rspDto = new OnlineStorePlatformOrderRspDto();
            rspDto.setPlatformCode(platformCode);
            rspDto.setOrderCount(Math.toIntExact(count));
            data.add(rspDto);
        });
        return data;
    }

    /**
     * 查询所有机构订单数（累加）
     *
     * @param merCode
     * @param platformCodes
     * @param stCodeList
     * @param startTime
     * @param endTime
     * @return
     */
    @Override
    public Integer queryAllStoreOrderCount(String merCode, List<String> platformCodes, List<String> stCodeList, Date startTime, Date endTime) {
        if (merCodesConfig.isQueryEs() && clusterOrderConfiguration.clusterFromEs(merCode)) {
            CountRequest countRequest = null;
            try {
                String index = ESUtils.getOrderInfoIndexByStr(baseIndexName);
                PointClusterBaseReqDto reqDto = new PointClusterBaseReqDto();
                reqDto.setMerCode(merCode);
                reqDto.setStartTime(startTime);
                reqDto.setEndTime(endTime);
                reqDto.setPlatformCodeList(platformCodes);
                reqDto.setStCodeList(stCodeList);
                countRequest = AllStoreOrderCountBuild.asymblyRequest(index, reqDto);
                Long count = elasticService.countSearch(countRequest);
                return count.intValue();
            } catch (Exception e) {
                log.error("queryClusterOrderCount from es error, request source: {}", countRequest == null ? "" : JSON.toJSONString(countRequest), e);
            }
        }

        QueryWrapper<OrderInfo> orderQueryWrapper = new QueryWrapper<>();
        orderQueryWrapper.lambda().eq(OrderInfo::getMerCode, merCode).isNotNull(OrderInfo::getReceiverLng).isNotNull(OrderInfo::getReceiverLat).ge(OrderInfo::getCreated, startTime).le(OrderInfo::getCreated, endTime);
        if (!CollectionUtils.isEmpty(platformCodes)) {
            orderQueryWrapper.lambda().in(OrderInfo::getThirdPlatformCode, platformCodes);
        }
        // 订单数
        orderQueryWrapper.lambda().in(OrderInfo::getOrganizationCode, stCodeList);

        return orderInfoMapper.selectCount(orderQueryWrapper);
    }

//    public Page scrollPage(Page page, String key, String state, String code, Date begin, Date end) {
//        int pageSize = page.getPageSize();
//        int currentPageNo = page.getCurrentPageNo();
//        TransportClient client = this.elasticsearchTemplate.getTransportClient();
//        // 组织查询条件
//        BoolQueryBuilder query = setBoolQueryBuilder(state, begin, end, key, code);
//        // Scroll查询请求
//        // 首次搜索 包含数据
//        SearchResponse searchResponse = client.prepareSearch("my_index")
//                .setTypes("my_type")
//                // 执行检索的类别
//                .setSearchType(SearchType.DEFAULT)
//                // 指定超时时间
//                .setScroll(new TimeValue(5000))
//                // 排序
//                .addSort("createDate", SortOrder.DESC)
//                // 查询条件
//                .setQuery(query)
//                // 大小
//                .setSize(page.getPageSize())
//                .execute()
//                .actionGet();
//        // 获取查询总数量
//        long totalCount = searchResponse.getHits().getTotalHits();
//        // 设置分页数据总数
//        page.setTotalCount(totalCount);
//        List<EntityEsDto> result = new ArrayList<>(pageSize);
//        // 查询总数量为0，直接返回
//        if (totalCount == 0) {
//            page.setResult(result);
//            return page;
//        }
//        // 计算总页数
//        long pageCount = totalCount % (long) pageSize == 0L ? totalCount / (long) pageSize : totalCount / (long) pageSize + 1L;
//        // 重新设置当前页数
//        if (pageCount == 1 || pageCount <= currentPageNo) {
//            currentPageNo = (int) pageCount;
//        }
//        // 获取currentPageNo的数据，从Scroll快照拿数据，首页数据已经拿到，
//        for (int i = 2; i <= currentPageNo; i++) {
//            // 从第二页开始，使用上次搜索结果的ScrollId，从Scroll快照拿数据
//            searchResponse = client
//                    .prepareSearchScroll(searchResponse.getScrollId())
//                    .setScroll(new TimeValue(5000))
//                    .execute()
//                    .actionGet();
//        }
//        // 获取分页结果
//        SearchHits hits = searchResponse.getHits();
//        EntityEsDto entityEsDto= null;
//        // 遍历转换结果对象
//        for (SearchHit searchHit : hits) {
//            entityEsDto= JSONObject.parseObject(searchHit.getSourceAsString(), EntityEsDto.class);
//            if (Objects.nonNull(entityEsDto)) {
//                // 设置主键
//                entityEsDto.setId(searchHit.getId());
//                result.add(entityEsDto);
//            }
//        }
//        page.setCurrentPageNo(currentPageNo);
//        page.setResult(result);
//        return page;
//    }

}
