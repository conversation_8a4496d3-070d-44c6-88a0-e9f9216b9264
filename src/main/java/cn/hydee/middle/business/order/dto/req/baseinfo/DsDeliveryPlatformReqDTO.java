package cn.hydee.middle.business.order.dto.req.baseinfo;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotNull;

@Data
public class DsDeliveryPlatformReqDTO {

    @NotNull(message = "线上门店编码不能为空")
    @ApiModelProperty(value = "线上门店编码",required = true)
    private String onlineStoreCode;

    @NotNull(message = "平台编码不能为空")
    @ApiModelProperty(value = "平台编码不能为空",required = true)
    private String platformCode;

    @ApiModelProperty(value = "1 拣货复核过滤")
    private Integer filterFlag;

    @ApiModelProperty(value = "系统订单号")
    private Long orderNo;

}
