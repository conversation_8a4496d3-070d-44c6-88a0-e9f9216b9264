package cn.hydee.middle.business.order.service;

import cn.hydee.middle.business.order.domain.OrderInfoAllDomain;
import cn.hydee.middle.business.order.dto.PrintByDeviceClientAndTemplateDTO;
import cn.hydee.middle.business.order.dto.req.PrintContentReqDto;
import cn.hydee.middle.business.order.dto.req.PrintContentTestReqDto;
import cn.hydee.middle.business.order.dto.req.PrintUpdateReqDto;
import cn.hydee.middle.business.order.dto.rsp.OnlineStoreInfoRspDto;
import cn.hydee.middle.business.order.dto.rsp.TemplateInfoDTO;
import cn.hydee.middle.business.order.entity.CloudPrintContent;

import java.util.Date;
import java.util.List;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2020/1/17 14:20
 */
public interface PrintService {
    /** 获取列表 */
    List<CloudPrintContent> getPrintContentList(PrintContentReqDto printContentReqDto);
    /** 更新状态 */
    void updatePrinted(PrintUpdateReqDto printUpdateReqDto);

    /** web print  拣货前打印 */
    void printBeforePick(OrderInfoAllDomain orderInfoAllDomain, OnlineStoreInfoRspDto onlineStoreInfoRspDto,Integer templateType);

    /** web print  拣货后打印 */
    void printAfterPick(OrderInfoAllDomain orderInfoAllDomain, OnlineStoreInfoRspDto onlineStoreInfoRspDto,Integer templateType);

    /** web print */
    String webPrint(Long orderNo, Integer templateType, String userId);

    /** 保存测试打印数据*/
    void saveTestPrintContent(String merCode, PrintContentTestReqDto reqDto,Integer templateType);

    int dropDataByTime(Date endTime);

    List<TemplateInfoDTO> getPrintTemplate(Long orderNo);

    void testPrintByDeviceClientAndTemplate(PrintByDeviceClientAndTemplateDTO dto);
}
