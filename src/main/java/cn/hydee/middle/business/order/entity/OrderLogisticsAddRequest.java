package cn.hydee.middle.business.order.entity;

import lombok.Data;

import javax.validation.constraints.NotBlank;

@Data
public class OrderLogisticsAddRequest {


    /**
     * 订单编号
     */
    private String thirdOrderNo;

    /**
     * 物流公司编号不能为空
     */
    @NotBlank(message = "物流公司编号不能为空")
    private String logisticsCompanyCode;

    /**
     * 运单号
     */
    @NotBlank(message = "运单号不能为空")
    private String logisticsNo;

    /**
     * 配送方式 1 平台配送，2 平台合作方配送，3 自配送 4 自提 5 物流配送
     */
    private String deliveryType;

    /**
     * 送货人姓名
     */
    private String riderName;

    /**
     * 送货人电话
     */
    private String riderPhone;

}
