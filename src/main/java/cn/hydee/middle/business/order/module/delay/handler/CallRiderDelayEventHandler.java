package cn.hydee.middle.business.order.module.delay.handler;

import cn.hydee.middle.business.order.module.delay.dto.CallRiderParamDto;
import cn.hydee.middle.business.order.util.JsonUtil;
import cn.hydee.middle.business.order.util.SpringBeanUtils;
import cn.hydee.middle.business.order.v2.manager.DeliveryOutManager;
import org.springframework.stereotype.Service;
import org.springframework.util.StringUtils;

/**
 * <AUTHOR>
 * @date 2021/07/14
 */
@Service("callRiderDelayEventHandler")
public class CallRiderDelayEventHandler implements DelayEventHandler{

    @Override
    public void handle(String data) {
        if(StringUtils.isEmpty(data)){
            return;
        }
        DeliveryOutManager deliveryOutManager = SpringBeanUtils.getBean(DeliveryOutManager.class);
        CallRiderParamDto paramDto = JsonUtil.json2Object(data,CallRiderParamDto.class);
        if(null == paramDto){
            return;
        }
        // 呼叫运力
        deliveryOutManager.deliveryOutNotifyForDelayTask(paramDto);
    }
}
