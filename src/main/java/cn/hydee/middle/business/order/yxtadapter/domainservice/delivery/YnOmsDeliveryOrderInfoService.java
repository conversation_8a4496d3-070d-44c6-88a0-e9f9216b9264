package cn.hydee.middle.business.order.yxtadapter.domainservice.delivery;

import cn.hydee.middle.business.order.yxtadapter.domain.oms.transform.OrderDetail;
import cn.hydee.middle.business.order.yxtadapter.domain.oms.transform.OrderInvoice;
import cn.hydee.middle.business.order.yxtadapter.domain.oms.transform.OrderRxReview;
import cn.hydee.middle.business.order.yxtadapter.domain.oms.transform.OrderStd;

import java.util.List;

/**
 * <AUTHOR>
 * Date: 2023/9/22
 */
public interface YnOmsDeliveryOrderInfoService {

    List<OrderStd> queryOrderInfoByStoreCode(String storeCode);

    List<OrderDetail> queryOrderDetail(String orderCode);

    List<OrderDetail> queryOrderGift(String orderCode);

    List<OrderInvoice> queryOrderInvoice(String orderCode);

    OrderRxReview queryOrderRxReview(String orderCode,String createtime);

}
