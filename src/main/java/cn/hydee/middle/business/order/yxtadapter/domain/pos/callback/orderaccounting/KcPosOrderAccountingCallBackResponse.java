package cn.hydee.middle.business.order.yxtadapter.domain.pos.callback.orderaccounting;

import cn.hydee.middle.business.order.yxtadapter.domain.pos.callback.getorderinfo.KcPosOrderReturnValue;
import lombok.Data;

/**
 * Created by Intellij IDEA.
 *
 * <AUTHOR>
 * Date:  2023/9/6
 */
@Data
public class KcPosOrderAccountingCallBackResponse implements java.io.Serializable {

    /**
     * 返回码
     */
    private String Code;


    /**
     * 异常信息
     */
    private String Msg;



    /**
     * 返回值，如果正常则为空，异常的话，不存在ReturnValue
     */
    private String ReturnValue;

    public static KcPosOrderAccountingCallBackResponse fail(String failureMessage) {
        KcPosOrderAccountingCallBackResponse response = new KcPosOrderAccountingCallBackResponse();
        response.setCode("1");
        response.setMsg(failureMessage);
        response.setReturnValue(null);
        return response;
    }

    public static KcPosOrderAccountingCallBackResponse success() {
        KcPosOrderAccountingCallBackResponse response = new KcPosOrderAccountingCallBackResponse();
        response.setCode("0");
        response.setMsg("成功");
        response.setReturnValue(null);
        return response;
    }
    public static KcPosOrderAccountingCallBackResponse success(String Msg) {
        KcPosOrderAccountingCallBackResponse response = new KcPosOrderAccountingCallBackResponse();
        response.setCode("0");
        response.setMsg(Msg);
        response.setReturnValue(null);
        return response;
    }

    public boolean isSuccess() {
        return "0".equals(this.Code);
    }


}
