package cn.hydee.middle.business.order.dto.req;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotNull;
import java.util.Date;

/**
 * <AUTHOR>
 * @date 2020/8/24
 */
@Data
public class OrderCureReq {
    @NotNull(message = "系统订单号不可为空")
    @ApiModelProperty("系统订单号")
    private Long orderNo;
    @ApiModelProperty("订单修复的期望时间，不传默认当前处理时间")
    private Date handleTime;
}
