package cn.hydee.middle.business.order.dto.req;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import java.math.BigDecimal;
import javax.validation.constraints.NotNull;
import lombok.Data;

@Data
@ApiModel(description = "预警设置创建信息")
public class ForewarnSetCreateReqDto {

  @NotNull(message = "毛利率不可为空")
  @ApiModelProperty(value = "毛利率")
  private BigDecimal profit;

  @NotNull(message = "毛利额不可为空")
  @ApiModelProperty(value = "毛利额")
  private BigDecimal profitQuota;

  @NotNull(message = "服务类型不可为空")
  @ApiModelProperty(value = "服务类型：O2O、B2C")
  private String serviceType;

}
