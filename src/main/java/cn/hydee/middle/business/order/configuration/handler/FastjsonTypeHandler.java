package cn.hydee.middle.business.order.configuration.handler;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.serializer.SerializerFeature;
import org.apache.ibatis.type.JdbcType;
import org.apache.ibatis.type.MappedJdbcTypes;
import org.apache.ibatis.type.MappedTypes;

@MappedTypes({Object.class})
@MappedJdbcTypes({JdbcType.VARCHAR})
public class FastjsonTypeHandler extends AbstractJsonTypeHandler<Object> {
    private final Class<?> type;

    public FastjsonTypeHandler(Class<?> type) {
        this.type = type;
    }

    protected Object parse(String json) {
        return JSON.parseObject(json, this.type);
    }

    protected String toJson(Object obj) {
        return JSON.toJSONString(obj, SerializerFeature.WriteMapNullValue, SerializerFeature.WriteNullListAsEmpty, SerializerFeature.WriteNullStringAsEmpty);
    }
}
