package cn.hydee.middle.business.order.storeautosync.infrastructure.mysql.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

import java.time.LocalDateTime;

@TableName(value = "ds_online_store_auth_record")
@Data
public class DsOnlineStoreAuthRecord {

    @TableId(type = IdType.AUTO)
    private Long id;

    @TableField(value = "platform_code")
    private String platformCode;

    @TableField(value = "service_mode")
    private String serviceMode;

    @TableField(value = "platform_store_code")
    private String platformStoreCode;

    @TableField(value = "online_store_code")
    private String onlineStoreCode;

    @TableField(value = "online_store_name")
    private String onlineStoreName;

    @TableField(value = "auth_status")
    private Integer authStatus;

    @TableField(value = "relieve_status")
    private Integer relieveStatus;

    @TableField(value = "change_status")
    private Integer changeStatus;

    @TableField(value = "error_msg")
    private String errorMsg;

    @TableField(value = "create_time")
    private LocalDateTime createTime;

    @TableField(value = "modify_time")
    private LocalDateTime modifyTime;

    @TableField(value = "creater")
    private String creater;

    @TableField(value = "retry_times")
    private Integer retryTimes;

    @TableField(value = "`lock`")
    private Boolean lock;

    @TableField(value = "auth_time")
    private LocalDateTime authTime;
}
