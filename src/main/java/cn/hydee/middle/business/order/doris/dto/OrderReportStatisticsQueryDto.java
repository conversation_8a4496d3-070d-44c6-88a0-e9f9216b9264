package cn.hydee.middle.business.order.doris.dto;

import cn.hydee.middle.business.order.canal.config.CanalConfig;
import cn.hydee.middle.business.order.doris.dto.req.DorisTableDto;
import cn.hydee.middle.business.order.doris.dto.req.OrderReportStatisticsPageQueryReqDto;
import cn.hydee.middle.business.order.doris.dto.req.OrderReportStatisticsQueryReqDto;
import cn.hydee.middle.business.order.util.SpecialPropertyUtils;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import org.springframework.beans.BeanUtils;

import java.util.Date;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2021/06/17
 */
@Data
@EqualsAndHashCode(callSuper=true)
public class OrderReportStatisticsQueryDto extends DorisTableDto {

    @ApiModelProperty(value = "商户编码")
    private String merCode;

    @ApiModelProperty(value = "平台编码集合")
    private List<String> platformCodeList;

    @ApiModelProperty(value = "组织结构集合")
    private List<String> organizationCodeList;
    @ApiModelProperty(value = "organizationCodeList 长度超过10000使用")
    private List<String> organizationCodeListPart;

    @ApiModelProperty(value = "开始时间")
    private Date timeStart;

    @ApiModelProperty(value = "结束时间")
    private Date timeEnd;

    @ApiModelProperty(value = "排序类型，0-数据库排序，1-内存排序")
    private Integer sortType = 0;

    @ApiModelProperty(value = "排序字段，默认商家实收金额;字段参考返回对象名")
    private String sortField;

    @ApiModelProperty(value = "排序顺序：升序 asc 降序 desc，默认降序")
    private String sortSequence;

    public static OrderReportStatisticsQueryDto convert(OrderReportStatisticsQueryReqDto orderReportStatisticsQueryReqDto, CanalConfig canalConfig){
        OrderReportStatisticsQueryDto queryDto = new OrderReportStatisticsQueryDto();
        BeanUtils.copyProperties(orderReportStatisticsQueryReqDto,queryDto);
        queryDto.buildTable(canalConfig);
        return queryDto;
    }

    public static OrderReportStatisticsQueryDto convert(OrderReportStatisticsPageQueryReqDto reqDto,Class<?> cls, CanalConfig canalConfig){
        OrderReportStatisticsQueryDto queryDto = new OrderReportStatisticsQueryDto();
        BeanUtils.copyProperties(reqDto,queryDto);
        queryDto.setSortType(SpecialPropertyUtils.needMemorySortField(reqDto.getSortField(),cls));
        queryDto.buildTable(canalConfig);
        return queryDto;
    }

}
