/*  
 * Project Name:hydee-business-order  
 * File Name:RequestDeliverGoodsDto.java  
 * Package Name:cn.hydee.middle.business.order.dto.req.ob.cloud.shelf  
 * Date:2020年10月22日上午10:30:39  
 * Copyright (c) 2020, Shanghai Hydee Software Corp., Ltd. All Rights Reserved.  
 *  
*/  
  
package cn.hydee.middle.business.order.dto.req.ob.cloud.shelf;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotNull;
import java.util.List;

@Data
public class RequestDeliverGoodsDto {
	
    @ApiModelProperty(value = "OMS系统订单号", required = true)
    @NotNull(message = "OMS系统订单号不能为空")
	private Long orderNo;
    
    @ApiModelProperty(value = "OMS平台订单号", required = true)
    @NotNull(message = "OMS平台订单号不能为空")
	private Long thirdOrderNo;
    
    @ApiModelProperty(value = "商户编码", required = true)
    @NotNull(message = "商户编码不能为空")
	private String merCode;
    
    @ApiModelProperty(value = "供应商编码", required = true)
    @NotNull(message = "供应商编码不能为空")
	private String supplierCode;
    
    @ApiModelProperty(value = "收件人姓名", required = true)
    @NotNull(message = "收件人姓名不能为空")
	private String recipientName;
    
    @ApiModelProperty(value = "收件人电话", required = true)
    @NotNull(message = "收件人电话不能为空")
	private String recipientPhone;
    
    @ApiModelProperty(value = "收件人地址", required = true)
    @NotNull(message = "收件人地址不能为空")
	private String recipientAddress;
    
    @ApiModelProperty(value = "收件人备注")
	private String recipientNotes;
    
    @ApiModelProperty(value = "商品list", required = true)
    @NotNull(message = "商品list不能为空")
	private List<DeliverGoodsDto> deliveryOrderGoodsDTOList;

    @ApiModelProperty(value = "创建人名字")
    private String createName;

    @ApiModelProperty(value = "支付请求单号")
    private String businessPayOrderCode;

    @ApiModelProperty(value = "服务商平台商户号")
    private String facilitatorPlatformBusinessNo;

    @ApiModelProperty(value = "商户平台商户号")
    private String merchantPlatformBusinessNo;

    @ApiModelProperty(value = "支付来源")
    private String channelSource;

    @ApiModelProperty(value = "订单状态: 1-待接单 2-配送中 3-已完成 4-已取消 5-已关闭")
    private Integer orderStatus;
}
  
