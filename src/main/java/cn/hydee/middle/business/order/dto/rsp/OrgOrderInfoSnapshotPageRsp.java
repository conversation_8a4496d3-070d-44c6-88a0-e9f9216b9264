package cn.hydee.middle.business.order.dto.rsp;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

@Data
public class OrgOrderInfoSnapshotPageRsp {

    private Long id;

    @ApiModelProperty(value ="机构编码")
    private String organizationCode;
    @ApiModelProperty(value = "机构名称")
    private String organizationName;
    @ApiModelProperty(value = "销售单待下账")
    private Long waitSale = 0L;
    @ApiModelProperty(value = "退款单单待下账")
    private Long refundWaitSale = 0L;
    @ApiModelProperty(value = "待拣货")
    private Long waitPick = 0L;
    @ApiModelProperty(value = "待配送")
    private Long waitPost = 0L;
    @ApiModelProperty(value = "配送中")
    private Long posting = 0L;
    @ApiModelProperty(value = "异常")
    private Long exception = 0L;
    @ApiModelProperty(value = "退款中")
    private Long refunding = 0L;

    @ApiModelProperty(value = "是否开启全自动下账，否/是")
    private String autoEnterAccountOpenFlag;
}
