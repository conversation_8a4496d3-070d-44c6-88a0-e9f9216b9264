/*  
 * Project Name:hydee-business-order  
 * File Name:BatchExpressDeliveryQueryDto.java  
 * Package Name:cn.hydee.middle.business.order.batch.imports.dto  
 * Date:2020年10月19日上午10:16:55  
 * Copyright (c) 2020, Shanghai Hydee Software Corp., Ltd. All Rights Reserved.  
 *  
*/  
  
package cn.hydee.middle.business.order.batch.imports.dto;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;

@Data
public class BaseImportQueryDto implements Serializable{

    private static final long serialVersionUID = -1274404098653338996L;

    @ApiModelProperty("任务id")
    private String taskId;
    
    @ApiModelProperty("任务名称")
    private String taskName;
    
    @ApiModelProperty("1-待处理 2-处理中 3-已完成 4-异常终止")
    private Integer status;

    @ApiModelProperty("处理结果:全部成功;部分失败;全部失败")
    private String taskResultDesc;

    @ApiModelProperty("操作人")
    private String operatorName;
    
    @ApiModelProperty("创建时间")
    private String createTime;

    @ApiModelProperty("下载链接")
    private String resultPath;

}