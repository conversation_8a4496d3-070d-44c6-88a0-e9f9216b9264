package cn.hydee.middle.business.order.v2.controller;

import cn.hutool.json.JSONUtil;
import cn.hydee.middle.business.order.Enums.DsConstants;
import cn.hydee.middle.business.order.Enums.DsErrorType;
import cn.hydee.middle.business.order.Enums.FactoryTagEnum;
import cn.hydee.middle.business.order.domain.ResponseHEMS;
import cn.hydee.middle.business.order.dto.req.*;
import cn.hydee.middle.business.order.dto.rsp.OrderBillRspDto;
import cn.hydee.middle.business.order.entity.OmsHemsMap;
import cn.hydee.middle.business.order.feign.UnifiedClient;
import cn.hydee.middle.business.order.mapper.OmsHemsMapMapper;
import cn.hydee.middle.business.order.module.orderdiff.handler.OrderDiffManager;
import cn.hydee.middle.business.order.service.OrderCancelBillService;
import cn.hydee.middle.business.order.service.rocket.ordermessage.ForwardMessageComponent;
import cn.hydee.middle.business.order.util.RedisKeyUtil;
import cn.hydee.middle.business.order.util.redis.RedisLockUtil;
import cn.hydee.middle.business.order.v2.aop.RedisLockHandler;
import cn.hydee.middle.business.order.v2.factory.OrderForwardOperateFactory;
import cn.hydee.middle.business.order.v2.manager.base.OrderInfoRedisManager;
import cn.hydee.middle.business.order.v2.service.EnterAccountService;
import cn.hydee.middle.business.order.v2.service.OrderForwardOperateService;
import cn.hydee.middle.business.order.v2.service.OrderSyncService;
import cn.hydee.middle.business.order.v2.service.PickConfirmService;
import cn.hydee.oms.utils.MD5Utils;
import cn.hydee.starter.controller.AbstractController;
import cn.hydee.starter.dto.ResponseBase;
import com.alibaba.fastjson.JSON;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import java.nio.charset.StandardCharsets;
import javax.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.util.StringUtils;
import org.springframework.validation.BindingResult;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.validation.Valid;
import java.time.LocalDateTime;
import java.time.ZoneId;
import java.time.format.DateTimeFormatter;

/**
 * 订单正向里路程操作2.0
 *
 * <AUTHOR>
 * @since 2020-8-18
 */
@RestController
@RequestMapping("/${api.version2}/ds/order")
@Validated
@Slf4j
@Api(tags = "V2订单操作(写)接口")
public class OrderInfoHandleController extends AbstractController {

    @Autowired
    private RedisLockHandler redisLockHandler;
    @Autowired
    private UnifiedClient unifiedClient;
    @Autowired
    private OmsHemsMapMapper omsHemsMapMapper;
    @Autowired
    private OrderInfoRedisManager orderInfoRedisManager;
    @Autowired
    private OrderCancelBillService orderCancelBillService;
    @Autowired
    private OrderSyncService orderSyncService;
    @Autowired
    private OrderDiffManager orderDiffManager;
    @Resource
    private ForwardMessageComponent forwardMessageComponent;

    @ApiOperation(value = "创建订单接口", notes = "创建订单接口")
    @PostMapping("/create")
    public ResponseBase<Void> createOrder(
            @Valid @RequestBody AddOrderInfoReqDto addOrderInfoReqDto, BindingResult result) {
        log.info("OrderInfoHandleController入口创建订单:" + JSONUtil.toJsonStr(addOrderInfoReqDto));
        checkValid(result);

        // 转发到rocketMq
        String message = JSON.toJSONString(addOrderInfoReqDto);
        if (forwardMessageComponent.forwardMessageToOrderSync(message)) {
            log.info("创建订单-转发到order-sync:" + message);
            return generateSuccess(null);
        }

        log.info("创建订单:" + JSONUtil.toJsonStr(addOrderInfoReqDto));
        OrderForwardOperateService forwardOperateService =
                OrderForwardOperateFactory.getForwardOperateServiceDefault();
        if (redisLockHandler.redisDelayLock(addOrderInfoReqDto.getEctype(), addOrderInfoReqDto.getOlorderno(), addOrderInfoReqDto, 1)) {
            forwardOperateService.saveOrder(addOrderInfoReqDto);
            //释放锁
            redisLockHandler.redisUnLock(addOrderInfoReqDto.getEctype(), addOrderInfoReqDto.getOlorderno());
        } else {
            log.info("获取锁失败，进入延迟队列");
        }
        return generateSuccess(null);
    }

    @ApiOperation(value = "订单接单", notes = "订单接单")
    @PostMapping("/take/confirm")
    public ResponseBase<Void> takeOrderConfirm(@RequestHeader("userId") String userId,
                                               @RequestHeader("merCode") String merCode, @Valid @RequestBody OrderHandleReqDto orderPageReqDto,
                                               BindingResult result) {
        checkValid(result);
        OrderForwardOperateService forwardOperateService = OrderForwardOperateFactory.getForwardOperateServiceDefault();
        forwardOperateService.takeOrderConfirm(merCode, userId, orderPageReqDto,
                orderPageReqDto.getOrderNo().toString());
        return generateSuccess(null);
    }

    @ApiOperation(value = "订单拒单", notes = "订单拒单")
    @PostMapping("/take/refuse")
    public ResponseBase<Void> takeOrderRefuse(@RequestHeader("userId") String userId,
                                              @RequestHeader("merCode") String merCode, @Valid @RequestBody OrderHandleReqDto orderPageReqDto,
                                              BindingResult result) {
        checkValid(result);
        OrderForwardOperateService forwardOperateService = OrderForwardOperateFactory.getForwardOperateServiceDefault();
        forwardOperateService.takeOrderRefuse(merCode, userId, orderPageReqDto,
                orderPageReqDto.getOrderNo().toString());
        return generateSuccess(null);
    }

    @ApiOperation(value = "确认拣货", notes = "确认拣货")
    @PostMapping("/pick/confirm")
    public ResponseBase<String> pickConfirm(@RequestHeader("userId") String userId,
                                            @RequestHeader("merCode") String merCode, @Valid @RequestBody OrderHandlePickConfirmReqDto orderHandleReqDto,
                                            BindingResult result) {
        checkValid(result);
        PickConfirmService pickConfirmService = OrderForwardOperateFactory.getPickConfirmService("default");
        String billMessage = pickConfirmService.pickConfirm(merCode, userId, orderHandleReqDto, DsConstants.INTEGER_ONE, orderHandleReqDto.getOrderNo().toString());
        if (StringUtils.isEmpty(billMessage)) {
            return generateSuccess(null);
        }
        ResponseBase<String> base = new ResponseBase<>();
        base.setCode(DsErrorType.AUTO_ENTER_ACCOUNT_FAIL.getCode());
        base.setMsg(DsErrorType.AUTO_ENTER_ACCOUNT_FAIL.getMsg());
        return base;
    }

    @ApiOperation(value = "商家回复催单", notes = "商家回复催单")
    @PostMapping("/merchant/urge/reply")
    public ResponseBase<Void> urgeReply(
            @RequestHeader("userId") String userId,
            @RequestHeader("merCode") String merCode,
            @Valid @RequestBody OrderUrgeReplyReqDto orderHandleReqDto) {
        OrderForwardOperateService forwardOperateService =
                OrderForwardOperateFactory.getForwardOperateServiceDefault();
        forwardOperateService.merchantUrgeReply(merCode, userId, orderHandleReqDto
                , orderHandleReqDto.getOrderNo().toString());
        return generateSuccess(null);
    }

    @ApiOperation(value = "不对接ERP确认拣货", notes = "不对接ERP确认拣货")
    @PostMapping("/noErp/pick/confirm")
    public ResponseBase<Void> noErpPickConfirm(@RequestHeader("userId") String userId,
                                               @RequestHeader("merCode") String merCode, @Valid @RequestBody NoErpPickConfirmReqDto orderHandleReqDto,
                                               BindingResult result) {
        checkValid(result);
        PickConfirmService pickConfirmService = OrderForwardOperateFactory.getPickConfirmService("noErp");
        pickConfirmService.pickConfirm(merCode, userId, orderHandleReqDto, DsConstants.INTEGER_ONE, orderHandleReqDto.getOrderNo().toString());
        return generateSuccess(null);
    }

    @ApiOperation(value = "销售单下账")
    @PostMapping("/" +
            "sale/bill")
    public ResponseBase<OrderBillRspDto> saleBill(@RequestHeader("userId") String userId,
                                                  @RequestHeader("merCode") String merCode,
                                                  @Valid @RequestBody OrderHandlePickConfirmReqDto req) {
        EnterAccountService enterAccountService = OrderForwardOperateFactory.getEnterAccountService(
                FactoryTagEnum.ENTERACCOUNT.getTag());
        enterAccountService.enterAccountSaleBill(userId, merCode, req, req.getOrderNo().toString());
        return generateSuccess(null);
    }

    @ApiOperation(value = "下账按钮，无adjustno，接单锁库存")
    @PostMapping("/lock/inventory")
    public ResponseBase<LockInventoryReqDto> receiveLockInventory(@RequestHeader("userId") String userId,
                                                                  @RequestHeader("merCode") String merCode,
                                                                  @Valid @RequestBody OrderHandleReqDto req) {
        EnterAccountService enterAccountService = OrderForwardOperateFactory.getEnterAccountService(
                FactoryTagEnum.ENTERACCOUNT.getTag());
        return generateSuccess(enterAccountService.receiveLockInventory(req, req.getOrderNo().toString(), userId));
    }

    @ApiOperation(value = "手动pushToHEMS")
    @PostMapping("/pushToHEMS")
    public ResponseBase<Void> pushToHEMS(@RequestBody BaseHemsReqDto req) {
        ResponseHEMS<Void> response = null;
        resetSign(req);
        try {
            response = unifiedClient.pushOrder(req);
        } catch (Exception e) {
            ResponseBase responseBase = new ResponseBase();
            responseBase.setCode(DsConstants.FAILED);
            responseBase.setMsg(e.getMessage());
            return responseBase;
        }
        if (response == null || !response.checkSuccess()) {
            ResponseBase responseBase = new ResponseBase();
            responseBase.setCode(DsConstants.FAILED);
            responseBase.setMsg(JSON.toJSONString(response));
            return responseBase;
        }
        return generateSuccess(null);
    }

    private void resetSign(BaseHemsReqDto baseHemsReqDto) {
        OmsHemsMap omsHemsMap = omsHemsMapMapper.selectOneByUnique(baseHemsReqDto.getGroupid(), baseHemsReqDto.getEccode(), baseHemsReqDto.getOlshopid());
        String appsecret = omsHemsMap.getAppsecret();
        StringBuilder sb = new StringBuilder();
        sb.append(appsecret);
        sb.append("body");
        sb.append(baseHemsReqDto.getBody());
        sb.append("eccode");
        sb.append(baseHemsReqDto.getEccode());
        sb.append("groupid");
        sb.append(baseHemsReqDto.getGroupid());
        sb.append("olshopid");
        sb.append(baseHemsReqDto.getOlshopid());
        sb.append("timestamp");
        String timeStamp = DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss").format(LocalDateTime.now(ZoneId.systemDefault()));
        sb.append(timeStamp);
        if (!org.apache.commons.lang3.StringUtils.isEmpty(baseHemsReqDto.getV())) {
            sb.append("v");
            sb.append(baseHemsReqDto.getV());
        }
        sb.append(appsecret);
        String md5 = MD5Utils.MD5Encode(sb.toString(), "UTF-8");
        baseHemsReqDto.setTimestamp(timeStamp);
        baseHemsReqDto.setSign(md5);
    }

    @ApiOperation(value = "直接解锁库存")
    @PostMapping("/unlock/inventory")
    public ResponseBase<Void> unLockInventory(@RequestHeader("userId") String userId,
                                              @RequestHeader("merCode") String merCode,
                                              @Valid @RequestBody OrderHandleReqDto req) {
        EnterAccountService enterAccountService = OrderForwardOperateFactory.getEnterAccountService(
                FactoryTagEnum.ENTERACCOUNT.getTag());
        return generateSuccess(enterAccountService.unlockInventory(req, req.getOrderNo().toString()));
    }

    @ApiOperation(value = "全量跑系统所有订单", notes = "全量跑系统所有订单")
    @PostMapping("/countSysOrder")
    public ResponseBase<Void> countSysOrder() {
        orderInfoRedisManager.syncOrderInfo();
        return generateSuccess(null);
    }

    @ApiOperation(value = "已下账订单取消下账")
    @PostMapping("/cancel/bill")
    public ResponseBase<Object> cancelBill(@RequestHeader("merCode") String merCode,
                                           @RequestHeader("userId") String userId,
                                           @RequestParam("orderNo") Long orderNo) {
        orderCancelBillService.cancelBill(merCode, userId, String.valueOf(orderNo));
        return generateSuccess(null);
    }

    @ApiOperation(value = "订单数据同步")
    @PostMapping("/sync")
    public ResponseBase<Void> sync(@RequestHeader("merCode") String merCode,
                                   @RequestHeader("userId") String userId,
                                   @RequestBody OrderSyncReqDto orderSyncReqDto) {
        //如果传所有机构标识，直接返回
        if (DsConstants.ORGANIZATION_CODE_ALL.equals(orderSyncReqDto.getOrganizationCode())) {
            return generateSuccess(null);
        }
        String key = RedisKeyUtil.getOrderSyncKey(merCode, orderSyncReqDto.getOrganizationCode(), orderSyncReqDto.getIndex());
        boolean flag = RedisLockUtil.getLockKey(key, DsConstants.STRING_ONE, 3 * 60L);
        if (!flag) {
            return generateSuccess(null);
        }
        orderSyncReqDto.setMerCode(merCode);
        orderSyncReqDto.setUserId(userId);
        orderSyncService.orderSync(orderSyncReqDto);
        return generateSuccess(null);
    }

    @ApiOperation(value = "重算订单金额", notes = "重算订单金额")
    @PostMapping("/recalculate")
    public ResponseBase<Void> recalculateAmount(@RequestParam Long orderNo) {
        orderDiffManager.amountCompareWithLock(orderNo);
        return generateSuccess(null);
    }

}
