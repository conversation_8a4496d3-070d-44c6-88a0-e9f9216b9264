package cn.hydee.middle.business.order.service.baseinfo;

import cn.hydee.middle.business.order.entity.DsMerchantGroupInfo;

import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 */
public interface DsMerchantGroupInfoService {

    /**
     * 查询sessionKey
     * @param merCode 商户编码
     * @return
     */
    DsMerchantGroupInfo querySessionkeyByMerCode(String merCode);

    /**
     * 保存商户分组信息
     * @param merCode
     * @param merName
     * @param sessionKey
     * @return
     */
    int saveMerchantGroupInfo(String merCode, String merName, String sessionKey);

    Map<String,String> querySessionKeyByMerCodeList(List<String> merCodeList);
}
