package cn.hydee.middle.business.order.dto;

import io.swagger.annotations.ApiModelProperty;
import lombok.Builder;
import lombok.Data;

@Data
@Builder
public class B2cSourceStoreDto {

    @ApiModelProperty(value = "商户编码")
    private String merCode;
    @ApiModelProperty(value = "B2C门店的网店编码")
    private String outB2cClientId;
    @ApiModelProperty(value = "原O2O门店的网店编码")
    private String onlineClientCode;
    @ApiModelProperty(value = "原O2O门店的线上门店编码")
    private String onlineStoreCode;

}
