package cn.hydee.middle.business.order.doris.service;

import cn.hydee.middle.business.order.doris.dto.req.PushMessage2KafkaReqDto;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2021/06/25
 */
public interface OrderDorisService {
    /**
    * @Description: 异步推送
    * @Param: [reqDto]
    * @return: void
    * @Author: syuson
    * @Date: 2021-7-8
    */
    void pushMessage2KafkaSync(PushMessage2KafkaReqDto reqDto);

    /**
    * @Description: 推送数据到kafka
    * @Param: [reqDto]
    * @return: void
    * @Author: syuson
    * @Date: 2021-6-25
    */
    void pushMessage2Kafka(PushMessage2KafkaReqDto reqDto);

    /**
     * @Description: 推送数据到kafka
     * @Param: [reqDto]
     * @return: void
     * @Author: syuson
     * @Date: 2021-6-25
     */
    boolean pushMessage2KafkaOMSByOrderNo(Long orderNo);

    /**
    * @Description: 推送数据到kafka（取消下账专用）
    * @Param: [reqDto]
    * @return: void
    * @Author: syuson
    * @Date: 2021-10-22
    */
    void pushMessage2KafkaForCancelBill(PushMessage2KafkaReqDto reqDto);

    /**
    * @Description: 删除数据
    * @Param: [orderNoList]
    * @return: void
    * @Author: syuson
    * @Date: 2021-7-8
    */
    void deleteByOrderNos(List<Long> orderNoList);

}
