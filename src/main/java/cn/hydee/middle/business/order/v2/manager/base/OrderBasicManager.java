package cn.hydee.middle.business.order.v2.manager.base;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.date.LocalDateTimeUtil;
import cn.hutool.core.map.MapUtil;
import cn.hutool.core.util.NumberUtil;
import cn.hutool.core.util.StrUtil;
import cn.hydee.middle.business.order.Enums.*;
import cn.hydee.middle.business.order.account.check.base.enums.TCGDeliveryStatusEnum;
import cn.hydee.middle.business.order.domain.OrderInfoAllDomain;
import cn.hydee.middle.business.order.domain.ResponseNet;
import cn.hydee.middle.business.order.dto.CommodityExceptionAnalyseMessageDto;
import cn.hydee.middle.business.order.dto.OrderPrescriptionDataQryRespDto;
import cn.hydee.middle.business.order.dto.gy.GyInfoDTO;
import cn.hydee.middle.business.order.dto.req.AddOrderInfoReqDto;
import cn.hydee.middle.business.order.dto.req.OrderHandleReqDto;
import cn.hydee.middle.business.order.dto.rsp.OnlineStoreInfoRspDto;
import cn.hydee.middle.business.order.dto.rsp.baseinfo.DsStoreDeliveryInfoResDTO;
import cn.hydee.middle.business.order.entity.*;
import cn.hydee.middle.business.order.entity.b2c.OmsOrderInfo;
import cn.hydee.middle.business.order.interceptor.GlobalInterceptor;
import cn.hydee.middle.business.order.mapper.*;
import cn.hydee.middle.business.order.mapper.b2c.OmsOrderInfoMapper;
import cn.hydee.middle.business.order.point.dto.rsp.QueryJobCancelRiderRspDto;
import cn.hydee.middle.business.order.retry.YxtRetry;
import cn.hydee.middle.business.order.route.db.mysql.model.DeliveryFeeRule;
import cn.hydee.middle.business.order.route.db.mysql.model.DeliveryFeeRuleAddprice;
import cn.hydee.middle.business.order.route.domain.enums.AddPriceTypeEnum;
import cn.hydee.middle.business.order.route.domain.external.*;
import cn.hydee.middle.business.order.route.domain.model.OrderInfoAllDomainCheckValueObject;
import cn.hydee.middle.business.order.route.domain.repository.DeliveryFeeRuleRepository;
import cn.hydee.middle.business.order.service.CommodityExceptionOrderService;
import cn.hydee.middle.business.order.service.DeliveryFeeEconomizeRecordService;
import cn.hydee.middle.business.order.service.EsEnhanceService;
import cn.hydee.middle.business.order.service.OrderDeliveryIdRelationService;
import cn.hydee.middle.business.order.service.async.HydeeEsSyncClientAsync;
import cn.hydee.middle.business.order.service.baseinfo.DsOnlineStoreConfigService;
import cn.hydee.middle.business.order.service.rocket.MessageProducerService;
import cn.hydee.middle.business.order.service.suport.BaseInfoService;
import cn.hydee.middle.business.order.util.ExceptionUtil;
import cn.hydee.middle.business.order.util.JsonUtil;
import cn.hydee.middle.business.order.v2.feign.hems.HemsCommonClient;
import cn.hydee.middle.business.order.v2.manager.DeliveryManager;
import cn.hydee.middle.business.order.v2.manager.OrderEnterAccountManager;
import cn.hydee.starter.dto.ErrorType;
import cn.hydee.starter.dto.ResponseBase;
import cn.hydee.unified.model.HemsBaseData;
import cn.hydee.unified.model.base.BaseHemsResp;
import cn.hydee.unified.model.order.*;
import cn.hydee.unified.model.rider.RiderOrderCancelReq;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.alibaba.fastjson.serializer.SerializerFeature;
import com.baomidou.mybatisplus.core.conditions.Wrapper;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.core.conditions.update.UpdateWrapper;
import com.baomidou.mybatisplus.core.toolkit.CollectionUtils;
import com.baomidou.mybatisplus.core.toolkit.StringUtils;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.google.common.cache.Cache;
import com.google.common.cache.CacheBuilder;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.yxt.middle.baseinfo.res.store.StoreInfoDataResDTO;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.context.annotation.Lazy;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;

import java.math.BigDecimal;
import java.sql.Time;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.util.*;
import java.util.Map.Entry;
import java.util.concurrent.Executor;
import java.util.concurrent.Future;
import java.util.concurrent.ThreadPoolExecutor;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;

import static cn.hydee.middle.business.order.configuration.ThreadPoolConfig.ORDER_BUSINESS_ASYNC_THREAD_POOL;

/**
 * 订单基础manager
 *
 * <AUTHOR>
 * @since 2020/8/20 15:09
 */
@Slf4j
@Component
public class OrderBasicManager {

    @Autowired
    private OrderInfoMapper orderInfoMapper;
    @Autowired
    private OrderPayInfoMapper orderPayInfoMapper;
    @Autowired
    private OrderDetailMapper orderDetailMapper;
    @Autowired
    private OrderDeliveryRecordMapper orderDeliveryRecordMapper;
    @Autowired
    private OrderLogMapper orderLogMapper;
    @Autowired
    private DsDeliveryStoreManager dsDeliveryStoreManager;
    @Autowired
    private OrderDeliveryLogManager orderDeliveryLogManager;
    @Autowired
    private OrderDeliveryIdRelationService orderDeliveryIdRelationService;
    @Autowired
    private CommodityExceptionOrderService commodityExceptionOrderService;
    @Autowired
    private DeliveryManager deliveryManager;
    @Autowired
    private SelfGetDeliveryInfoMapper selfGetDeliveryInfoMapper;
    @Autowired
    private cn.hydee.middle.business.order.v2.feign.hems.HemsCommonClientB2C hemsCommonClientB2C;
    @Autowired
    private DsMerchantGroupInfoRepo dsMerchantGroupInfoRepo;
    @Lazy
    @Autowired
    private OrderEnterAccountManager orderEnterAccountManager;
    @Autowired
    private BaseInfoService baseInfoService;
    @Autowired
    private HemsCommonClient hemsCommonClient;
    @Autowired
    private EsEnhanceService esEnhanceService;
    @Autowired
    @Lazy
    private OmsOrderInfoMapper omsOrderInfoMapper;

    @Autowired
    private MessageProducerService messageProducerService;

    @Qualifier(ORDER_BUSINESS_ASYNC_THREAD_POOL)
    @Autowired
    private Executor asyncThreadPool;

    @Autowired
    private HemsClientService hemsClientService;

    @Autowired
    private TxMapClientService txMapClientService;

    @Autowired
    private DeliveryFeeRuleRepository deliveryFeeRuleRepository;

    @Autowired
    @Qualifier("bestDeliveryFeeThreadPool")
    private ThreadPoolExecutor threadPoolExecutor;

    @Autowired
    private DsOnlineStoreRepo dsOnlineStoreRepo;

    @Autowired
    private DsOnlineStoreDeliveryRepo dsOnlineStoreDeliveryRepo;

    @Autowired
    private DsDeliveryStoreRepo dsDeliveryStoreRepo;

    @Autowired
    private DsMerchantGroupInfoRepo merchantGroupInfoRepo;

    @Autowired
    private DeliveryFeeEconomizeRecordService deliveryFeeEconomizeRecordService;

    @Autowired
    private RouteClientService routeClientService;

    @Autowired
    private DsOnlineStoreConfigService dsOnlineStoreConfigService;

    private Cache<String, String> sessionKeyCache;

    {
        sessionKeyCache = CacheBuilder.newBuilder().expireAfterWrite(30L, TimeUnit.MINUTES).maximumSize(100L).build();
    }

    /**
     * `status` COMMENT '订单状态 3：待发货 4：已发货 5：已完成 6：已取消 7：已关闭 0 其他',
     * O2O 根据status 转换成  omsstatus 1 待接单 2待拣货 3待配送 4配送中 5已完成 6订单取消 7订单退款 8待退货 9待退款 0其他
     */
    private HashMap<String, String> statusMap = new HashMap() {{
        put("3", "3");
        put("4", "4");
        put("5", "5");
        put("6", "6");
        put("7", "6");
        put("0", "0");
    }};

    /**
     * 通过三方平台与三方订单号查询订单基本信息
     *
     * @Param: * @param null
     * @Return: {@link null}
     * @Author: chufeng(2910)
     * @Date: 2020/8/21 11:03
     */
    public OrderInfo getOrderBaseByThirdNo(String thirdPlatformCode, String thirdOrderNo) {
        return orderInfoMapper.selectBaseByUnique(thirdOrderNo, thirdPlatformCode);
    }

    /**
     * 通过订单号查询订单支付信息
     *
     * @Param: * @param null
     * @Return: {@link null}
     * @Author: chufeng(2910)
     * @Date: 2020/8/21 11:50
     */
    public OrderPayInfo getOrderPayInfoWithCheck(Long orderNo) {
        OrderPayInfo orderPayInfo = orderPayInfoMapper.selectByOrderNo(orderNo);
        if (orderPayInfo == null) {
            throw ExceptionUtil.getWarnException(DsErrorType.ORDER_NOT_EXIST);
        }
        return orderPayInfo;
    }

    /**
     * 获取订单商品列表信息
     *
     * @Param: * @param null
     * @Return: {@link null}
     * @Author: chufeng(2910)
     * @Date: 2020/8/21 11:51
     */
    public List<OrderDetail> getOrderDetailListWithCheck(Long orderNo) {
        List<OrderDetail> detailList = orderDetailMapper.selectListByOrderNo(orderNo);
        if (detailList == null) {
            throw ExceptionUtil.getWarnException(DsErrorType.ORDER_DATA_ERROR.getCode(), "订单商品信息不存在");
        }
        return detailList;
    }

    /**
     * 订单取消或关闭时，更新配送记录信息
     *
     * @Param: * @param null
     * @Return: {@link null}
     * @Author: chufeng(2910)
     * @Date: 2020/8/21 11:53
     */
    public void orderCancelUpdateDeliveryRecord(OrderInfo orderInfo) {
        try {
            YxtRetry.retry(6, () -> orderCancelWithEx(orderInfo, CancelRiderEnum.OrderMsgCancel));
        } catch (Exception e) {
            log.error("cancel rider exception, orderNo:{}", orderInfo.getOrderNo(), e);
            return;
        }
    }

    public void orderCancelWithEx(OrderInfo orderInfo, CancelRiderEnum cancelRiderEnum) {
        if (null == orderInfo) {
            return;
        }
        OrderDeliveryRecord orderDeliveryRecord = orderDeliveryRecordMapper.selectByOrderNo(orderInfo.getOrderNo());
        if (orderDeliveryRecord == null) {
            return;
        }

        if (!DeliveryTypeEnum.SELLER_SELF.getCode().equals(orderDeliveryRecord.getDeliveryType())) {
            return;
        }
        if (!DeliveryPlatformEnum.checkRider(orderDeliveryRecord.getDeliveryPlatName())) {
            return;
        }

        Long deliveryId = orderDeliveryIdRelationService.queryDeliveryIdByOrderNo(orderInfo.getOrderNo());


        if (orderDeliveryRecord.getState() <= DeliveryStateEnum.UN_CALL.getCode()
                || orderDeliveryRecord.getState() > DeliveryStateEnum.DELIVERY_ON.getCode()) {
            return;
        }

        DsStoreDeliveryInfoResDTO deliveryStore = dsDeliveryStoreManager.getDeliveryStoreByOnlineStoreId(
                orderInfo.getMerCode(), orderInfo.getOnlineStoreCode(), orderInfo.getThirdPlatformCode(),
                orderDeliveryRecord.getDeliveryPlatName());
        if (deliveryStore == null) {
            log.error("OrderDelivery:modifyDeliveryPlatform, deliveryStore null, orderNo:{}, platName:{} 获取不到配送门店信息,之前就未呼叫骑手成功",
                    orderInfo.getOrderNo(), orderDeliveryRecord.getDeliveryPlatName());
            return;
        }

        RiderOrderCancelReq cancelReq = new RiderOrderCancelReq();
        this.setCancelRiderNewReason(orderDeliveryRecord, cancelReq, cancelRiderEnum);
        cancelReq.setDeliveryId(deliveryId);
        cancelReq.setRiderOrderId(orderDeliveryRecord.getRiderOrderNo());
        GlobalInterceptor.tObject.set(orderInfo);

        ResponseBase<Object> result = deliveryManager.cancelRider(orderInfo, cancelReq, deliveryStore, orderDeliveryRecord, deliveryId);
        if (!DsConstants.ALL_SUCCESS.equals(result.getCode())) {
            orderDeliveryRecord.setCancelFrom(cancelRiderEnum.getCancelFrom());
            orderDeliveryRecord.setCancelReason(cancelRiderEnum.getCancelReason().toString());
            orderDeliveryRecord.setExceptionReason(result.getMsg());
            orderDeliveryRecordMapper.updateDeliveryRecord(orderDeliveryRecord);
            throw ExceptionUtil.getWarnException(DsErrorType.CANCEL_RIDER_ERROR.getCode(), result.getMsg());
        }

        orderDeliveryRecord.setCancelFrom(cancelRiderEnum.getCancelFrom());
        orderDeliveryRecord.setCancelReason(cancelRiderEnum.getCancelReason().toString());
        orderDeliveryRecord.setCancelFlag(1);
        orderDeliveryRecord.setCancelDetail(cancelReq.getCancelDetail());
        orderDeliveryRecord.setState(DeliveryStateEnum.CANCELED.getCode());
        orderDeliveryRecord.setRiderOrderNo("");
        log.info("根据订单编号修改订单配送信息 orderDeliveryRecord：{}", orderDeliveryRecord);
        int num = orderDeliveryRecordMapper.updateDeliveryRecord(orderDeliveryRecord);
        if (num > 0) {
            // 记录订单配送日志
            orderDeliveryLogManager.saveOrderDeliveryLog(orderDeliveryRecord.getOrderNo(),
                    orderDeliveryRecord.getState(), orderDeliveryRecord.getDeliveryPlatName(),
                    orderDeliveryRecord.getRiderName(), orderDeliveryRecord.getRiderPhone(), "订单取消");
        }
    }

    /**
     * 设置配送取消原因
     *
     * @Param: * @param null
     * @Return: {@link null}
     * @Author: chufeng(2910)
     * @Date: 2020/8/21 11:56
     */
    public void setCancelRiderNewReason(OrderDeliveryRecord orderDeliveryRecord, RiderOrderCancelReq cancelReq, CancelRiderEnum cancelRiderEnum) {
        if (cancelRiderEnum == CancelRiderEnum.OrderMsgCancel) {
            Integer cancelReason = 36;
            String cancelDetail = "我不需要配送了";
            // 蜂鸟
            if (DeliveryPlatformEnum.HUMMINGBIRD.getName().equals(orderDeliveryRecord.getDeliveryPlatName())) {
                cancelReason = 9;
                cancelDetail = "顾客下错单/临时不想要了";
            }
            //蜂鸟即配
            if (DeliveryPlatformEnum.checkCancelSource(orderDeliveryRecord.getDeliveryPlatName())) {
                cancelReason = 9;
                cancelDetail = "顾客下错单/临时不想要了";
                //蜂鸟即配需要注明取消来源
                cancelReq.setCancelFrom(DsConstants.STRING_TWO);
            }
            // 美团
            if (DeliveryPlatformEnum.MEITUAN_RIDER.getName().equals(orderDeliveryRecord.getDeliveryPlatName())) {
                cancelReason = 101;
                cancelDetail = "顾客主动取消";
            }
            // 达达
            if (DeliveryPlatformEnum.DADA.getName().equals(orderDeliveryRecord.getDeliveryPlatName())) {
                cancelReason = 4;
                cancelDetail = "顾客取消订单";
            }
            // 顺丰同城
            if (DeliveryPlatformEnum.SFTC.getName().equals(orderDeliveryRecord.getDeliveryPlatName())) {
                cancelReason = 0;
                cancelDetail = "顾客取消订单";
            }

            cancelReq.setCancelReason(cancelReason);
            cancelReq.setCancelDetail(cancelDetail);

        } else {
            cancelReq.setCancelReason(cancelRiderEnum.getCancelReason(orderDeliveryRecord.getDeliveryPlatName()));
            cancelReq.setCancelDetail(cancelRiderEnum.getcancelDetail());
            if (DeliveryPlatformEnum.checkCancelSource(orderDeliveryRecord.getDeliveryPlatName())) {
                cancelReq.setCancelFrom(cancelRiderEnum.getCancelFrom(cancelRiderEnum));
            }
        }

    }

    /**
     * 订单取消或关闭时，更新配送记录信息
     *
     * @Param: * @param null
     * @Return: {@link null}
     * @Author: chufeng(2910)
     * @Date: 2020/8/21 11:57
     */
    public void saveOrderInfoLog(OrderInfo orderInfo, Integer stateNew, String operatorId, String operateDesc,
                                 String extraInfo, Integer erpState, String operatorName) {
        asyncThreadPool.execute(() -> {
            OrderLog orderLog = new OrderLog();
            orderLog.setOrderNo(orderInfo.getOrderNo());
            orderLog.setOperatorId(operatorId);
            orderLog.setOperateDesc(operateDesc);
            orderLog.setStateBefore(orderInfo.getOrderState());
            orderLog.setExtraInfo(extraInfo);
            orderLog.setState(stateNew);
            orderLog.setErpState(erpState);
            orderLog.setOperatorName(operatorName);
            log.info("保持订单日志:{}", JsonUtil.object2Json(orderInfo));
            orderLogMapper.insert(orderLog);
        });

    }

    @Transactional(rollbackFor = Exception.class)
    public void setOrderDetailOutOfStock(List<String> erpCodeList, OrderInfo orderInfo,
                                         List<OrderDetail> orderDetailList) {
        //DsConstants.INTEGER_TWO  默认是erp
        this.setOrderDetailOutOfStockDo(erpCodeList, orderInfo, DsConstants.INTEGER_TWO, null, orderDetailList);
    }

    @Transactional(rollbackFor = Exception.class)
    public void setOrderDetailOutOfStock(List<String> erpCodeList, OrderInfo orderInfo, Integer bizType, Map<String, Integer> outOfStockInfoMap,
                                         List<OrderDetail> orderDetailList) {
        this.setOrderDetailOutOfStockDo(erpCodeList, orderInfo, bizType, outOfStockInfoMap, orderDetailList);
        if (!DsConstants.INTEGER_ONE.equals(bizType)) {
            return;
        }
        if (MapUtil.isEmpty(outOfStockInfoMap)) {
            return;
        }
        StringBuilder stringBuilder = new StringBuilder("商品中台库存不足商品：");
        for (Map.Entry<String, Integer> entry : outOfStockInfoMap.entrySet()) {
            //商品编码
            String key = entry.getKey();
            //上一次同步库存数量
            Integer value = entry.getValue();
            String format = StrUtil.format("商品编码:{},最近一次同步给平台库存为:{}", key, value);
            stringBuilder.append(format).append(",");
        }
        String lockMsg = StrUtil.maxLength(stringBuilder.toString(), 900);
        OrderInfo updateEx = new OrderInfo();
        updateEx.setOrderNo(orderInfo.getOrderNo());
        updateEx.setLockMsg(lockMsg);
        updateEx.setLockFlag(OrderLockFlagEnum.LOCK_LACK_STOCK.getCode());
        orderInfo.setLockFlag(OrderLockFlagEnum.LOCK_LACK_STOCK.getCode());
        orderInfoMapper.updateOrder(updateEx);
        // 增加操作日志
        HydeeEsSyncClientAsync.orderLogToES(DsConstants.INTEGER_ONE, orderInfo.getMerCode(), orderInfo.getOrderNo(), orderInfo.getOrderState(),
                orderInfo.getErpState(), OrderLogEnum.COMMODITY_STOCK_DEDUCT.getAction(), OrderLogEnum.getCommodityStockDeduct(lockMsg), null);
    }


    /**
     * 设置订单商品缺库存
     *
     * @Param: * @param null
     * @Return: {@link null}
     * @Author: chufeng(2910)
     * @Date: 2020/8/21 15:03
     */
    @Transactional(rollbackFor = Exception.class)
    public void setOrderDetailOutOfStockDo(List<String> erpCodeList, OrderInfo orderInfo, Integer bizType, Map<String, Integer> outOfStockInfoMap, List<OrderDetail> orderDetailList) {
        List<CommodityExceptionOrder> commodityExceptionOrders = new ArrayList<>();
        if (Objects.isNull(outOfStockInfoMap)) {
            outOfStockInfoMap = MapUtil.newHashMap();
        }
        Map<String, String> commodityCodeNameMap = new HashMap<>();
        if (!CollectionUtils.isEmpty(orderDetailList)) {
            commodityCodeNameMap = orderDetailList.stream().collect(Collectors.toMap(OrderDetail::getErpCode, OrderDetail::getCommodityName, (o1, o2) -> o1));
        }
        String lockMsg = "商品中台库存不足商品：";
        for (String erpStr : erpCodeList) {
            OrderDetail detailUpdate = new OrderDetail();
            detailUpdate.setOrderNo(orderInfo.getOrderNo());
            detailUpdate.setErpCode(erpStr);
            lockMsg = lockMsg + erpStr + ",";
            detailUpdate.setStatus(OrderDetailStatusEnum.OUT_OF_STOCK.getCode());
            orderDetailMapper.updateByOrderNoErpCode(detailUpdate);

            //异常商品订单
            CommodityExceptionOrder commodityExceptionOrder = new CommodityExceptionOrder();
            commodityExceptionOrder.setMerCode(orderInfo.getMerCode());
            commodityExceptionOrder.setOrderNo(orderInfo.getOrderNo());
            commodityExceptionOrder.setErpCode(erpStr);
            commodityExceptionOrder.setStatus(OrderDetailStatusEnum.OUT_OF_STOCK.getCode());
            Date now = new Date();
            commodityExceptionOrder.setCreateTime(now);
            commodityExceptionOrder.setModifyTime(now);
            commodityExceptionOrder.setBizType(bizType);
            // 增加商品中台侧库存快照
            Optional.ofNullable(outOfStockInfoMap.get(erpStr)).ifPresent(stockTemp -> {
                commodityExceptionOrder.setStockTemp(stockTemp);
            });
            // 增加商品名称快照
            Optional.ofNullable(commodityCodeNameMap.get(erpStr)).ifPresent(commodityName -> {
                commodityExceptionOrder.setCommodityName(commodityName);
            });
            commodityExceptionOrderService.saveOrUpdate(Collections.singletonList(commodityExceptionOrder));
            commodityExceptionOrders.add(commodityExceptionOrder);
        }
        OrderInfo updateEx = new OrderInfo();
        updateEx.setLockFlag(OrderLockFlagEnum.LOCK_LACK_STOCK.getCode());
        updateEx.setOrderNo(orderInfo.getOrderNo());
        updateEx.setLockMsg(lockMsg);
        orderInfo.setLockFlag(OrderLockFlagEnum.LOCK_LACK_STOCK.getCode());
        orderInfoMapper.updateOrder(updateEx);
        for (CommodityExceptionOrder commodityExceptionOrder : commodityExceptionOrders) {
            CommodityExceptionAnalyseMessageDto dto = new CommodityExceptionAnalyseMessageDto();
            dto.setCommodityExceptionOrderId(commodityExceptionOrder.getId());
            dto.setErpCode(commodityExceptionOrder.getErpCode());
            dto.setMerCode(orderInfo.getMerCode());
            dto.setOrderNo(orderInfo.getOrderNo());
            dto.setOnlineStoreCode(orderInfo.getOnlineStoreCode());
            dto.setOrgCode(orderInfo.getOrganizationCode());
            messageProducerService.sendCommodityAnalyseMessage(dto);
        }
    }


    /**
     * 设置订单商品缺库存
     *
     * @Param: * @param null
     * @Return: {@link null}
     * @Author: chufeng(2910)
     * @Date: 2020/8/21 15:03
     */
    @Transactional(rollbackFor = Exception.class)
    public void setOrderDetailOutOfStockDoV2(List<String> erpCodeList, OrderInfo orderInfo, Integer bizType, Map<String, Integer> outOfStockInfoMap, List<OrderDetail> orderDetailList) {
        List<CommodityExceptionOrder> commodityExceptionOrders = new ArrayList<>();
        if (Objects.isNull(outOfStockInfoMap)) {
            outOfStockInfoMap = MapUtil.newHashMap();
        }
        Map<String, String> commodityCodeNameMap = new HashMap<>();
        if (!CollectionUtils.isEmpty(orderDetailList)) {
            commodityCodeNameMap = orderDetailList.stream().collect(Collectors.toMap(OrderDetail::getErpCode, OrderDetail::getCommodityName, (o1, o2) -> o1));
        }
        String lockMsg = "商品中台库存不足商品：";
        for (String erpStr : erpCodeList) {
            OrderDetail detailUpdate = new OrderDetail();
            detailUpdate.setOrderNo(orderInfo.getOrderNo());
            detailUpdate.setErpCode(erpStr);
            lockMsg = lockMsg + erpStr + ",";
            detailUpdate.setStatus(OrderDetailStatusEnum.OUT_OF_STOCK.getCode());
            orderDetailMapper.updateByOrderNoErpCode(detailUpdate);

            //异常商品订单
            CommodityExceptionOrder commodityExceptionOrder = new CommodityExceptionOrder();
            commodityExceptionOrder.setMerCode(orderInfo.getMerCode());
            commodityExceptionOrder.setOrderNo(orderInfo.getOrderNo());
            commodityExceptionOrder.setErpCode(erpStr);
            commodityExceptionOrder.setStatus(OrderDetailStatusEnum.OUT_OF_STOCK.getCode());
            Date now = new Date();
            commodityExceptionOrder.setCreateTime(now);
            commodityExceptionOrder.setModifyTime(now);
            commodityExceptionOrder.setBizType(bizType);
            // 增加商品中台侧库存快照
            Optional.ofNullable(outOfStockInfoMap.get(erpStr)).ifPresent(stockTemp -> {
                commodityExceptionOrder.setStockTemp(stockTemp);
            });
            // 增加商品名称快照
            Optional.ofNullable(commodityCodeNameMap.get(erpStr)).ifPresent(commodityName -> {
                commodityExceptionOrder.setCommodityName(commodityName);
            });
            commodityExceptionOrderService.insert(commodityExceptionOrder);
            commodityExceptionOrders.add(commodityExceptionOrder);
        }
        OrderInfo updateEx = new OrderInfo();
        updateEx.setLockFlag(OrderLockFlagEnum.LOCK_LACK_STOCK.getCode());
        updateEx.setOrderNo(orderInfo.getOrderNo());
        updateEx.setLockMsg(lockMsg);
        orderInfo.setLockFlag(OrderLockFlagEnum.LOCK_LACK_STOCK.getCode());
        orderInfoMapper.updateOrder(updateEx);
        for (CommodityExceptionOrder commodityExceptionOrder : commodityExceptionOrders) {
            CommodityExceptionAnalyseMessageDto dto = new CommodityExceptionAnalyseMessageDto();
            dto.setCommodityExceptionOrderId(commodityExceptionOrder.getId());
            dto.setErpCode(commodityExceptionOrder.getErpCode());
            dto.setMerCode(orderInfo.getMerCode());
            dto.setOrderNo(orderInfo.getOrderNo());
            dto.setOnlineStoreCode(orderInfo.getOnlineStoreCode());
            dto.setOrgCode(orderInfo.getOrganizationCode());
            messageProducerService.sendCommodityAnalyseMessage(dto);
        }
    }

    /**
     * 设置订单商品状态 商品不存在
     *
     * @Param: * @param null
     * @Return: {@link null}
     * @Author: chufeng(2910)
     * @Date: 2020/8/21 15:02
     */
    @Transactional(rollbackFor = Exception.class)
    public void setOrderDetailNotExist(List<String> erpCodeList, OrderInfo orderInfo,
                                       List<OrderDetail> orderDetailList) {
        Map<String, String> commodityCodeNameMap = new HashMap<>();
        if (!CollectionUtils.isEmpty(orderDetailList)) {
            commodityCodeNameMap = orderDetailList.stream().collect(Collectors.toMap(OrderDetail::getErpCode, OrderDetail::getCommodityName, (o1, o2) -> o1));
        }
        String lockMsg = "商品中台不存在商品：";
        for (String erpStr : erpCodeList) {
            OrderDetail detailUpdate = new OrderDetail();
            detailUpdate.setOrderNo(orderInfo.getOrderNo());
            detailUpdate.setErpCode(erpStr);
            lockMsg = lockMsg + erpStr + ",";
            detailUpdate.setStatus(OrderDetailStatusEnum.NOT_EXIST.getCode());
            orderDetailMapper.updateByOrderNoErpCode(detailUpdate);

            //异常商品订单
            CommodityExceptionOrder commodityExceptionOrder = new CommodityExceptionOrder();
            commodityExceptionOrder.setMerCode(orderInfo.getMerCode());
            commodityExceptionOrder.setOrderNo(orderInfo.getOrderNo());
            commodityExceptionOrder.setErpCode(erpStr);
            commodityExceptionOrder.setStatus(OrderDetailStatusEnum.NOT_EXIST.getCode());
            Date now = new Date();
            commodityExceptionOrder.setCreateTime(now);
            commodityExceptionOrder.setModifyTime(now);
            commodityExceptionOrder.setBizType(DsConstants.INTEGER_ONE);
            commodityExceptionOrder.setReasonType(OrderOverSoldEnum.COMM_NOT_EXSIT.getCode());
            commodityExceptionOrder.setReason(OrderOverSoldEnum.COMM_NOT_EXSIT.getMsg());
            // 增加商品名称快照
            Optional.ofNullable(commodityCodeNameMap.get(erpStr)).ifPresent(commodityName -> {
                commodityExceptionOrder.setCommodityName(commodityName);
            });
            commodityExceptionOrderService.saveOrUpdate(Collections.singletonList(commodityExceptionOrder));
        }
        OrderInfo updateEx = new OrderInfo();
        //商品不存在异常分类改造
        updateEx.setLockFlag(OrderLockFlagEnum.LOCK_COMMODITY_NOT_EXIST.getCode());
        updateEx.setOrderNo(orderInfo.getOrderNo());
        updateEx.setLockMsg(lockMsg);
        orderInfo.setLockFlag(OrderLockFlagEnum.LOCK_COMMODITY_NOT_EXIST.getCode());
        orderInfoMapper.updateOrder(updateEx);
        // 增加操作日志
        HydeeEsSyncClientAsync.orderLogToES(DsConstants.INTEGER_ONE, orderInfo.getMerCode(), orderInfo.getOrderNo(), orderInfo.getOrderState(),
                orderInfo.getErpState(), OrderLogEnum.COMMODITY_STOCK_DEDUCT.getAction(), OrderLogEnum.getCommodityStockDeduct(lockMsg), null);
    }



    /**
     * getOrderBaseInfo:根据订单号获取订单信息. <br/>
     *
     * @param orderNo 订单号
     * @return {@link OrderInfo}
     * <AUTHOR>
     * @date 2020年8月20日 下午8:37:31
     * @modifier zhucun
     * @modifyDate 2020年8月20日 下午8:37:31
     * @since JDK 1.8
     */
    public OrderInfo getOrderBaseInfo(Long orderNo) {
        Wrapper<OrderInfo> queryWrapper = Wrappers.<OrderInfo>lambdaQuery().eq(OrderInfo::getOrderNo, orderNo);
        return orderInfoMapper.selectOne(queryWrapper);
    }


    /**
     * getOrderBaseWithCheck:根据订单号获取订单信息（检测锁）. <br/>
     *
     * @param orderNo 订单号
     * @return {@link OrderInfo}
     * <AUTHOR>
     * @date 2020年8月20日 下午8:38:02
     * @modifier zhucun
     * @modifyDate 2020年8月20日 下午8:38:02
     * @since JDK 1.8
     */
    public OrderInfo getOrderBaseWithCheck(Long orderNo) {
        OrderInfo orderInfo = this.getOrderBaseInfo(orderNo);
        if (orderInfo == null) {
            throw ExceptionUtil.getWarnException(DsErrorType.ORDER_NOT_EXIST);
        }
        return orderInfo;
    }


    /**
     * checkOrderLock:获取订单信息（可配置检查订单是否锁定） <br/>
     *
     * @param userId            用户id
     * @param orderHandleReqDto {@link OrderHandleReqDto}
     * @param verifyLock        校验锁
     * @return {@link OrderInfo}
     * <AUTHOR>
     * @date 2020年8月20日 下午8:10:24
     * @modifier zhucun
     * @modifyDate 2020年8月20日 下午8:10:24
     * @since JDK 1.8
     */
    public OrderInfo checkOrderLock(String userId, OrderHandleReqDto orderHandleReqDto, boolean verifyLock) {
        OrderInfo orderInfo = this.getOrderBaseWithCheck(orderHandleReqDto.getOrderNo());
        if (!verifyLock) {
            return orderInfo;
        }
        if (!OrderLockFlagEnum.NOT_LOCK.getCode().equals(orderInfo.getLockFlag())) {
            throw ExceptionUtil.getWarnException(DsErrorType.ORDER_LOCKED);
        }
        return orderInfo;
    }


    /**
     * getOrderDeliveryRecordWithCheck:获取订单delivery_record. <br/>
     *
     * @param orderNo 订单号
     * @return {@link OrderDeliveryRecord}
     * <AUTHOR>
     * @date 2020年8月20日 下午8:13:12
     * @modifier zhucun
     * @modifyDate 2020年8月20日 下午8:13:12
     * @since JDK 1.8
     */
    public OrderDeliveryRecord getOrderDeliveryRecordWithCheck(Long orderNo) {

        OrderDeliveryRecord orderDeliveryRecord = orderDeliveryRecordMapper.selectByOrderNo(orderNo);
        if (orderDeliveryRecord == null) {
            throw ExceptionUtil.getWarnException(DsErrorType.ORDER_DATA_ERROR.getCode(), "订单配送信息不存在");
        }
        return orderDeliveryRecord;
    }

    /**
     * 检查订单是否锁定
     *
     * @Param: * @param null
     * @Return: {@link null}
     * @Author: chufeng(2910)
     * @Date: 2020/8/22 11:18
     */
    public OrderInfoAllDomain checkOrderAllLock(Long orderNo, boolean verifyLock) {
        OrderInfoAllDomain orderInfo = this.getOrderAllWithCheck(orderNo);
        if (verifyLock == false) {
            return orderInfo;
        }
        if(OrderLockFlagEnum.CFCHECK_NOT_PASSS.getCode().equals(orderInfo.getLockFlag())){
            throw ExceptionUtil.getWarnException(DsErrorType.PRESCRIPTION_APPROVE_UN_PASS);
        }
        if (!OrderLockFlagEnum.NOT_LOCK.getCode().equals(orderInfo.getLockFlag())) {
            throw ExceptionUtil.getWarnException(ErrorType.STATUS_ERROR);
        }
        return orderInfo;
    }

    public int updateOrder(OrderInfo orderInfo) {
        return orderInfoMapper.updateOrder(orderInfo);
    }

    /**
     * 查询订单总信息
     *
     * @Param: * @param null
     * @Return: {@link null}
     * @Author: chufeng(2910)
     * @Date: 2020/8/24 10:22
     */
    public OrderInfoAllDomain getOrderAllWithCheck(Long orderNo) {
        OrderInfoAllDomain orderInfo = orderInfoMapper.selectOrderInfoDetail(orderNo);
        if (orderInfo == null) {
            throw ExceptionUtil.getWarnException(DsErrorType.ORDER_NOT_EXIST);
        }
        return orderInfo;
    }

    /**
     * 获取订单配送类型名称
     *
     * @Param: * @param null
     * @Return: {@link null}
     * @Author: chufeng(2910)
     * @Date: 2020/8/24 18:07
     */
    public String getDeliveryPlatName(String thirdPlatformCode, String deliveryType) {
        String platformName = "";
        if (DeliveryTypeEnum.PLAT.getCode().equals(deliveryType) ||
                DeliveryTypeEnum.PLAT_THIRD.getCode().equals(deliveryType)) {
            platformName = DeliveryPlatformEnum.PLAT_DELIVERY.getName();
        }

        if (DeliveryTypeEnum.BUYER_SELF.getCode().equals(deliveryType)) {
            platformName = DeliveryPlatformEnum.BUYER.getName();
        } else if (DeliveryTypeEnum.EXPRESS.getCode().equals(deliveryType)) {
            platformName = DeliveryPlatformEnum.EXPRESS.getName();
        }
        return platformName;
    }

    public void CancelRiderWhenNeed() {
        // orderNo MerCode OnlineStoreCode ThirdPlatformCode ; DeliveryPlatName RiderOrderNo id
        List<QueryJobCancelRiderRspDto> queryJobCancelRiderRspDtos = orderInfoMapper.selectOrderNeedCancelRider();
        if (!CollectionUtils.isEmpty(queryJobCancelRiderRspDtos)) {
            for (int i = 0; i < queryJobCancelRiderRspDtos.size(); i++) {
                QueryJobCancelRiderRspDto queryJobCancelRiderRspDto = queryJobCancelRiderRspDtos.get(i);
                Long deliveryId = orderDeliveryIdRelationService.queryDeliveryIdByOrderNo(queryJobCancelRiderRspDto.getOrderNo());
                DsStoreDeliveryInfoResDTO deliveryStore = dsDeliveryStoreManager.getDeliveryStoreByOnlineStoreId(
                        queryJobCancelRiderRspDto.getMerCode(), queryJobCancelRiderRspDto.getOnlineStoreCode()
                        , queryJobCancelRiderRspDto.getThirdPlatformCode(), queryJobCancelRiderRspDto.getDeliveryPlatName());
                if (deliveryStore == null) {
                    log.error("OrderDelivery:modifyDeliveryPlatform, deliveryStore null, orderNo:{}, platName:{} 获取不到配送门店信息,之前就未呼叫骑手成功",
                            queryJobCancelRiderRspDto.getOrderNo(), queryJobCancelRiderRspDto.getDeliveryPlatName());
                    return;
                }

                OrderDeliveryRecord orderDeliveryRecord = new OrderDeliveryRecord();
                BeanUtils.copyProperties(queryJobCancelRiderRspDto, orderDeliveryRecord);
                OrderInfo orderInfo = new OrderInfo();
                BeanUtils.copyProperties(queryJobCancelRiderRspDto, orderInfo);

                RiderOrderCancelReq cancelReq = new RiderOrderCancelReq();
                this.setCancelRiderNewReason(orderDeliveryRecord, cancelReq, CancelRiderEnum.JobCancel);
                cancelReq.setDeliveryId(deliveryId);
                cancelReq.setRiderOrderId(orderDeliveryRecord.getRiderOrderNo());
                GlobalInterceptor.tObject.set(orderInfo);

                ResponseBase<Object> result = deliveryManager.cancelRider(orderInfo, cancelReq, deliveryStore, orderDeliveryRecord, deliveryId);
                if (!DsConstants.ALL_SUCCESS.equals(result.getCode())) {
                    orderDeliveryRecord.setCancelFrom(CancelRiderEnum.JobCancel.getCancelFrom());
                    orderDeliveryRecord.setCancelReason(CancelRiderEnum.JobCancel.getCancelReason().toString());
                    orderDeliveryRecord.setExceptionReason(result.getMsg());
                    orderDeliveryRecordMapper.updateDeliveryRecord(orderDeliveryRecord);
                    continue;
                }

                orderDeliveryRecord.setCancelFrom(CancelRiderEnum.JobCancel.getCancelFrom());
                orderDeliveryRecord.setCancelReason(CancelRiderEnum.JobCancel.getCancelReason().toString());
                orderDeliveryRecord.setCancelFlag(1);
                orderDeliveryRecord.setCancelDetail(cancelReq.getCancelDetail());
                orderDeliveryRecord.setState(DeliveryStateEnum.CANCELED.getCode());
                orderDeliveryRecord.setRiderOrderNo("");
                int num = orderDeliveryRecordMapper.updateDeliveryRecord(orderDeliveryRecord);
                if (num > 0) {
                    // 记录订单配送日志
                    orderDeliveryLogManager.saveOrderDeliveryLog(orderDeliveryRecord.getOrderNo(),
                            orderDeliveryRecord.getState(), orderDeliveryRecord.getDeliveryPlatName(),
                            orderDeliveryRecord.getRiderName(), orderDeliveryRecord.getRiderPhone(), "订单取消");
                }
            }
        }
    }

    public void B2COrderQueryLogistics(Long orderNo, String forceStatus) {
        List<SelfGetDeliveryInfo> records = selfGetDeliveryInfoMapper.selectHandleRecords();
        if (!org.springframework.util.CollectionUtils.isEmpty(records)) {
            ArrayList<Long> updates = new ArrayList<>();
            for (SelfGetDeliveryInfo record : records) {
                OrderInfo orderInfo = orderInfoMapper.selectOrderInfo(record.getOrderNo());
                if (orderInfo.getOrderState().intValue() <= OrderStateEnum.UN_PICK.getCode()) {
                    continue;
                }
                if (orderInfo.getOrderState().intValue() > OrderStateEnum.COMPLETED.getCode()) {
                    selfGetDeliveryInfoMapper.setCanceled(record.getOrderNo());
                    continue;
                }

                HemsBaseData baseData = hemsCommonClientB2C.constructHemsBaseData(record.getMerCode(), record.getPlatformCode(),
                        record.getOnlineClientCode(), getSessionKey(record.getMerCode()));
                QueryLogisticsReq queryLogisticsReq = new QueryLogisticsReq();
                queryLogisticsReq.setMainOrderId(record.getThirdOrderNo());
                BaseHemsResp<String> rs = hemsCommonClientB2C.orderQueryLogistics(queryLogisticsReq, baseData);
                if (rs == null || rs.getData() == null) {
                    log.info("orderQueryLogistics data: {}, orderNo {}", JSON.toJSONString(rs), orderInfo.getOrderNo());
                    HydeeEsSyncClientAsync.orderLogToES(DsConstants.INTEGER_ZERO, baseData.getGroupId(), orderInfo.getOrderNo(), orderInfo.getOrderState(), orderInfo.getErpState(),
                            OrderLogEnum.NOTIFY_TCG_GET_DELIVERY_INFO.getAction(), "接口调用失败", null);
                    continue;
                }

                if (!rs.isSuccess()) {
                    log.info("orderQueryLogistics data failed: {}, orderNo {}", JSON.toJSONString(rs), orderInfo.getOrderNo());
                    HydeeEsSyncClientAsync.orderLogToES(DsConstants.INTEGER_ZERO, baseData.getGroupId(), orderInfo.getOrderNo(), orderInfo.getOrderState(), orderInfo.getErpState(),
                            OrderLogEnum.NOTIFY_TCG_GET_DELIVERY_INFO.getAction(), "返回失败:" + JSON.toJSONString(rs), null);
                    continue;
                }

                QueryLogisticsResp queryLogisticsResp = null;
                try {
                    queryLogisticsResp = JSON.parseObject(rs.getData(), QueryLogisticsResp.class);
                } catch (Exception e) {
                    SelfGetDeliveryInfo selfGetDeliveryInfo = new SelfGetDeliveryInfo();
                    selfGetDeliveryInfo.setId(record.getId());
                    selfGetDeliveryInfo.setState(DsConstants.INTEGER_FOUR);
                    selfGetDeliveryInfo.setMark(rs.getData());
                    selfGetDeliveryInfoMapper.updateEx(selfGetDeliveryInfo);
                    HydeeEsSyncClientAsync.orderLogToES(DsConstants.INTEGER_ZERO, baseData.getGroupId(), orderInfo.getOrderNo(), orderInfo.getOrderState(), orderInfo.getErpState(),
                            OrderLogEnum.NOTIFY_TCG_GET_DELIVERY_INFO.getAction(), "返回失败:" + JSON.toJSONString(rs.getData()), null);
                    continue;
                }
                if (queryLogisticsResp == null || !queryLogisticsResp.isSuccess()) {
                    log.info("QueryLogisticsResp data failed: {}, orderNo {}", JSON.toJSONString(queryLogisticsResp), orderInfo.getOrderNo());
                    HydeeEsSyncClientAsync.orderLogToES(DsConstants.INTEGER_ZERO, baseData.getGroupId(), orderInfo.getOrderNo(), orderInfo.getOrderState(), orderInfo.getErpState(),
                            OrderLogEnum.NOTIFY_TCG_GET_DELIVERY_INFO.getAction(), "未得到响应数据 " + JSON.toJSONString(queryLogisticsResp), null);
                    continue;
                }

                String logisticStatus = TCGDeliveryStatusEnum.INIT.name();
                try {
                    logisticStatus = queryLogisticsResp.getLogisticStatus();
                    if (StringUtils.isNotEmpty(forceStatus) && record.getOrderNo().equals(orderNo)) {
                        logisticStatus = forceStatus;
                    }

                    if (ErpStateEnum.WAIT_SALE.getCode().equals(orderInfo.getErpState())
                            && (TCGDeliveryStatusEnum.SHIP.name().equals(logisticStatus) || TCGDeliveryStatusEnum.SIGN_SUCCESS.name().equals(logisticStatus))) {
                        OrderInfo oi = new OrderInfo();
                        oi.setOrderNo(record.getOrderNo());
                        oi.setOrderState(TCGDeliveryStatusEnum.getOrderStateByName(logisticStatus));
                        if (TCGDeliveryStatusEnum.SIGN_SUCCESS.name().equals(logisticStatus)) {
                            oi.setCompleteTime(queryLogisticsResp.getLogisticTime());
                            updates.add(record.getOrderNo());
                        }
                        orderInfoMapper.updateOrder(oi);
                        orderEnterAccountManager.saleBillForB2COnlineOrder(record.getOrderNo());
                    } else if (TCGDeliveryStatusEnum.SIGN_SUCCESS.name().equals(logisticStatus) && OrderStateEnum.COMPLETED.getCode() > orderInfo.getOrderState()) {
                        OrderInfo oi = new OrderInfo();
                        oi.setOrderNo(record.getOrderNo());
                        oi.setOrderState(OrderStateEnum.COMPLETED.getCode());
                        oi.setCompleteTime(queryLogisticsResp.getLogisticTime());
                        orderInfoMapper.updateOrder(oi);
                        updates.add(record.getOrderNo());
                    } else if (TCGDeliveryStatusEnum.SHIP.name().equals(logisticStatus) && OrderStateEnum.COMPLETED.getCode() > orderInfo.getOrderState()) {
                        OrderInfo oi = new OrderInfo();
                        oi.setOrderNo(record.getOrderNo());
                        oi.setOrderState(OrderStateEnum.POSTING.getCode());
                        orderInfoMapper.updateOrder(oi);
                    }
                } catch (Exception e) {
                    log.info("B2C onlineStoreOrder bill error {}, orderNo {}", e.getMessage(), orderInfo.getOrderNo());
                } finally {
                    Integer deliveryStatus = TCGDeliveryStatusEnum.getDeliveryStatusByName(logisticStatus);
                    if (!DsConstants.INTEGER_ZERO.equals(deliveryStatus)) {
                        OrderDeliveryRecord orderDeliveryRecord = new OrderDeliveryRecord();
                        orderDeliveryRecord.setState(deliveryStatus);
                        if (DeliveryStateEnum.UN_TAKE.getCode().equals(deliveryStatus)) {
                            orderDeliveryRecord.setCallTime(queryLogisticsResp.getLogisticTime());
                        }
                        if (DeliveryStateEnum.UN_PICK_UP.getCode().equals(deliveryStatus)) {
                            orderDeliveryRecord.setAcceptTime(queryLogisticsResp.getLogisticTime());
                        }
                        if (DeliveryStateEnum.DELIVERY_ON.getCode().equals(deliveryStatus)) {
                            orderDeliveryRecord.setPickTime(queryLogisticsResp.getLogisticTime());
                        }
                        if (DeliveryStateEnum.CANCELED.getCode().equals(deliveryStatus)) {
                            orderDeliveryRecord.setCancelDetail("同城购显示已取消");
                        }
                        int update = orderDeliveryRecordMapper.update(orderDeliveryRecord, new UpdateWrapper<OrderDeliveryRecord>().lambda()
                                .eq(OrderDeliveryRecord::getOrderNo, orderInfo.getOrderNo()).ne(OrderDeliveryRecord::getState, deliveryStatus));
                        if (update > 0) {
                            HydeeEsSyncClientAsync.orderLogToES(DsConstants.INTEGER_ONE, baseData.getGroupId(), orderInfo.getOrderNo(), orderInfo.getOrderState(), orderInfo.getErpState(),
                                    OrderLogEnum.NOTIFY_TCG_GET_DELIVERY_INFO.getAction(), "得到新的配送状态:" + TCGDeliveryStatusEnum.getDescByName(logisticStatus), null);
                        }
                    }
                }
            }
            if (CollectionUtils.isNotEmpty(updates)) {
                selfGetDeliveryInfoMapper.setRecordHandle(updates);
            }
        }
    }

    private String getSessionKey(String merCode) {
        if (sessionKeyCache.getIfPresent(merCode) == null) {
            DsMerchantGroupInfo dsMerchantGroupInfo = dsMerchantGroupInfoRepo.querySessionKeyByMerCode(merCode);
            sessionKeyCache.put(merCode, dsMerchantGroupInfo.getSessionKey());
        }
        return sessionKeyCache.getIfPresent(merCode);
    }

    public void transStatusForB2COnlineOrder(AddOrderInfoReqDto addOrderInfoReqDto) {
        if (org.apache.commons.lang.StringUtils.isEmpty(addOrderInfoReqDto.getOmsstatus()) && org.apache.commons.lang.StringUtils.isNotEmpty(addOrderInfoReqDto.getStatus())) {
            addOrderInfoReqDto.setOmsstatus(statusMap.get(addOrderInfoReqDto.getStatus()));
        }
    }

    /**
     * @Description: 退款后从接口中台获取最新的订单数据
     * @Param: [orderInfo]
     * @return: cn.hydee.unified.model.order.OrderInformation
     * @Author: syuson
     * @Date: 2021-12-9
     */
    public OrderInformation getOrderInformation(OrderInfo orderInfo) {
        OrderDetailReq req = new OrderDetailReq();
        req.setOlOrderNo(orderInfo.getThirdOrderNo());
        req.setEcType(orderInfo.getThirdPlatformCode());
        req.setOlShopId(orderInfo.getOnlineStoreCode());
        OnlineStoreInfoRspDto storeInfo = baseInfoService.getOnlineStoreInfo(orderInfo.getMerCode(), orderInfo.getThirdPlatformCode(),
                orderInfo.getClientCode(), orderInfo.getOnlineStoreCode());
        req.setShopId(storeInfo.getOutShopId());
        HemsBaseData baseData = hemsCommonClient.constructHemsBaseData(orderInfo.getMerCode(), DsConstants.CLOUD,
                storeInfo.getOnlineClientCode(), storeInfo.getSessionKey());
        BaseHemsResp<List<OrderInformation>> result = hemsCommonClient.orderInformation(req, baseData);
        if (org.springframework.util.CollectionUtils.isEmpty(result.getData())) {
            return null;
        }
        return result.getData().get(0);
    }

    public List<GyInfoDTO> queryInfoBySaleNos(String merCode, List<String> saleNos) {
        // 【【依赖需求】/1.0/ds/baseinfo/queryInfoBySaleNos接口兼容B2C订单查询】https://www.tapd.cn/61969829/prong/stories/view/1161969829001050994
        saleNos = saleNos.stream().distinct().collect(Collectors.toList());
        // 先查 O2O
        List<GyInfoDTO> dataList = new ArrayList<>();
        dataList.addAll(esEnhanceService.queryOrderBySaleNos(merCode, saleNos));
        List<String> o2oSaleNos = dataList.stream().map(GyInfoDTO::getSaleNo).collect(Collectors.toList());
        if (saleNos.size() == o2oSaleNos.size()) {
            return dataList;
        }
        // 再补充B2C（已合罗队沟通，先用索引过渡，后面还是要调整ES）
        List<String> needSearchB2CSaleNos = saleNos.stream().filter(saleNo -> !o2oSaleNos.contains(saleNo)).collect(Collectors.toList());
        QueryWrapper<OmsOrderInfo> queryWrapper = new QueryWrapper<>();
        queryWrapper.lambda().eq(OmsOrderInfo::getMerCode, merCode).in(OmsOrderInfo::getErpSaleNo, needSearchB2CSaleNos).select(OmsOrderInfo::getOrderNo
                , OmsOrderInfo::getThirdOrderNo, OmsOrderInfo::getErpSaleNo, OmsOrderInfo::getThirdPlatformCode);
        List<OmsOrderInfo> orderInfoList = omsOrderInfoMapper.selectList(queryWrapper);
        if (CollectionUtils.isEmpty(orderInfoList)) {
            return dataList;
        }
        dataList.addAll(orderInfoList.stream().map(orderInfo -> {
            GyInfoDTO gyInfoDTO = new GyInfoDTO();
            gyInfoDTO.setOrderId(orderInfo.getOrderNo());
            gyInfoDTO.setSaleNo(orderInfo.getErpSaleNo());
            gyInfoDTO.setThirdOrderNo(orderInfo.getThirdOrderNo());
            gyInfoDTO.setPlatformCode(orderInfo.getThirdPlatformCode());
            return gyInfoDTO;
        }).collect(Collectors.toList()));
        return dataList;
    }

    public List<GyInfoDTO> queryInfoByThirdOrderNos(String merCode, List<String> thirdOrderNos) {
        thirdOrderNos = thirdOrderNos.stream().distinct().collect(Collectors.toList());
        // 先查 O2O
        List<GyInfoDTO> dataList = new ArrayList<>();
        dataList.addAll(queryOrderByThirdOrderNos(merCode, thirdOrderNos));
        List<String> o2oThirdOrderNos = dataList.stream().map(GyInfoDTO::getThirdOrderNo).collect(Collectors.toList());
        if (thirdOrderNos.size() == o2oThirdOrderNos.size()) {
            return dataList;
        }
        // 再补充B2C
        List<String> needSearchB2CNos = thirdOrderNos.stream().filter(saleNo -> !o2oThirdOrderNos.contains(saleNo)).collect(Collectors.toList());
        QueryWrapper<OmsOrderInfo> queryWrapper = new QueryWrapper<>();
        queryWrapper.lambda().eq(OmsOrderInfo::getMerCode, merCode).eq(OmsOrderInfo::getThirdPlatformCode, PlatformCodeEnum.YD_JIA.getCode())
                .in(OmsOrderInfo::getThirdOrderNo, needSearchB2CNos).select(OmsOrderInfo::getOrderNo
                        , OmsOrderInfo::getThirdOrderNo, OmsOrderInfo::getErpSaleNo);
        List<OmsOrderInfo> orderInfoList = omsOrderInfoMapper.selectList(queryWrapper);
        if (CollectionUtils.isEmpty(orderInfoList)) {
            return dataList;
        }
        dataList.addAll(orderInfoList.stream().filter(orderInfo -> StringUtils.isNotEmpty(orderInfo.getErpSaleNo())).map(orderInfo -> {
            GyInfoDTO gyInfoDTO = new GyInfoDTO();
            gyInfoDTO.setOrderId(orderInfo.getOrderNo());
            gyInfoDTO.setSaleNo(orderInfo.getErpSaleNo());
            gyInfoDTO.setThirdOrderNo(orderInfo.getThirdOrderNo());
            return gyInfoDTO;
        }).collect(Collectors.toList()));
        return dataList;
    }


    private List<GyInfoDTO> queryOrderByThirdOrderNos(String merCode, List<String> thirdOrderNos) {
        QueryWrapper<OrderInfo> queryWrapper = new QueryWrapper<>();
        queryWrapper.lambda().eq(OrderInfo::getMerCode, merCode).eq(OrderInfo::getThirdPlatformCode, PlatformCodeEnum.YD_JIA.getCode())
                .in(OrderInfo::getThirdOrderNo, thirdOrderNos).select(OrderInfo::getOrderNo
                        , OrderInfo::getThirdOrderNo, OrderInfo::getErpSaleNo);
        List<OrderInfo> orderInfoList = orderInfoMapper.selectList(queryWrapper);
        if (CollectionUtils.isEmpty(orderInfoList)) {
            return Collections.emptyList();
        }
        return orderInfoList.stream().filter(orderInfo -> StringUtils.isNotEmpty(orderInfo.getErpSaleNo())).map(orderInfo -> {
            GyInfoDTO gyInfoDTO = new GyInfoDTO();
            gyInfoDTO.setOrderId(orderInfo.getOrderNo());
            gyInfoDTO.setSaleNo(orderInfo.getErpSaleNo());
            gyInfoDTO.setThirdOrderNo(orderInfo.getThirdOrderNo());
            return gyInfoDTO;
        }).collect(Collectors.toList());
    }

    public List<OrderPrescriptionResp> getPlatPrescription(OrderPrescriptionDataQryRespDto dto) {
        OrderPrescriptionReq req = new OrderPrescriptionReq();
        req.setOlOrderNo(dto.getThirdOrderNo());
        req.setOlShopId(dto.getOnlineStoreCode());
        HemsBaseData baseData = hemsCommonClient.constructHemsBaseData(dto.getMerCode(), dto.getThirdPlatformCode(), dto.getClientCode(), dto.getSessionKey());
        BaseHemsResp<List<OrderPrescriptionResp>> resp = hemsCommonClient.getOrderPrescription(req, baseData);
        log.info("调用获取处方单信息接口返回结果：{}", JSON.toJSONString(resp));
        if (!resp.isSuccess() || resp.getData() == null || CollectionUtils.isEmpty(resp.getData())) {
            return null;
        }
        return resp.getData();
    }

    @Transactional(rollbackFor = Exception.class)
    public void updateOrderOutError(OrderInfo orderInfo){
        if (OrderLockFlagEnum.LOCK_LACK_STOCK.getCode().equals(orderInfo.getLockFlag()) || OrderLockFlagEnum.LOCK_COMMODITY_NOT_EXIST.getCode().equals(orderInfo.getLockFlag())
                //商品不存在异常分类
                || OrderLockFlagEnum.LOCK_ERP_CODE_NULL.getCode().equals(orderInfo.getLockFlag()) || OrderLockFlagEnum.LOCK_THIRD_COMMODITY_UNMATCH.getCode().equals(orderInfo.getLockFlag()) || OrderLockFlagEnum.LOCK_OFFLINE_COMMODITY_NOT_EXSIST.getCode().equals(orderInfo.getLockFlag())) {
            //修改订单异常标识（库存不足异常、部分商品不存在）
            OrderInfo orderUp = new OrderInfo();
            orderUp.setOrderNo(orderInfo.getOrderNo());
            orderUp.setLockFlag(OrderLockFlagEnum.NOT_LOCK.getCode());
            orderInfoMapper.updateOrder(orderUp);
        }
        //兼容查询
        List<OrderDetail> orderDetails = orderDetailMapper.selectListByOrderNo(orderInfo.getOrderNo());
        //修改订单明细异常（库存不足异常、商品不存在）
        List<String> erpCodeList = new ArrayList<>();
        orderDetails.forEach(orderDetail -> {
            if (OrderDetailStatusEnum.NOT_EXIST.getCode().equals(orderDetail.getStatus()) || OrderDetailStatusEnum.OUT_OF_STOCK.getCode().equals(orderDetail.getStatus())) {
                erpCodeList.add(orderDetail.getErpCode());
            }
        });
        if (!org.springframework.util.CollectionUtils.isEmpty(erpCodeList)) {
            orderDetailMapper.batchUpdateStatus(orderInfo.getOrderNo(), erpCodeList, OrderDetailStatusEnum.NORMAL.getCode());
        }
    }

    /**
     * 获取最优配送方式
     * @param deliveryType
     * @param orderInfoDomain
     * @return
     */
    public String getDeliveryPlatName(String deliveryType,OrderInfoAllDomainCheckValueObject orderInfoDomain){
        if (DeliveryTypeEnum.PLAT.getCode().equals(deliveryType) || DeliveryTypeEnum.PLAT_THIRD.getCode().equals(deliveryType)) {
            return DeliveryPlatformEnum.PLAT_DELIVERY.getName();
        }
        if (DeliveryTypeEnum.BUYER_SELF.getCode().equals(deliveryType)) {
            return DeliveryPlatformEnum.BUYER.getName();
        }
        if (DeliveryTypeEnum.EXPRESS.getCode().equals(deliveryType)) {
            return DeliveryPlatformEnum.EXPRESS.getName();
        }if(DeliveryTypeEnum.SELLER_SELF.getCode().equals(deliveryType)){
            try {
                //如果有配送费节约记录 不需要再走后面逻辑 针对接单即呼叫骑手
                DeliveryFeeEconomizeRecord deliveryFeeEconomizeRecord = deliveryFeeEconomizeRecordService.getDeliveryFeeEconomizeRecord(orderInfoDomain.getOrderNo());
                if(Objects.nonNull(deliveryFeeEconomizeRecord)){
                    return StrUtil.isNotBlank(deliveryFeeEconomizeRecord.getRealDeliveryPlatName())?deliveryFeeEconomizeRecord.getRealDeliveryPlatName():deliveryFeeEconomizeRecord.getPredictDeliveryPlatName();
                }
                //商家自配 需要通过比较获取配送费最少的配送方式
                Map<String, BigDecimal> deliveryFeeMap = mapDeliveryFee(orderInfoDomain);
                if(CollUtil.isEmpty(deliveryFeeMap)){
                    return null;
                }
                //获取配送费失败的方式集合
                Map<String, BigDecimal> failDeliveryFeeMap = new HashMap<>();
                //获取配送费成功的方式集合
                Map<String, BigDecimal> successDeliveryFeeMap = new HashMap<>();
                for (Entry<String, BigDecimal> entry : deliveryFeeMap.entrySet()) {
                    if(Objects.isNull(entry.getValue())){
                        failDeliveryFeeMap.put(entry.getKey(), null);
                    }else {
                        successDeliveryFeeMap.put(entry.getKey(), entry.getValue());
                    }
                }

                if(deliveryFeeMap.size() == failDeliveryFeeMap.size()){
                    //说明所有配送方式的配送费获取都失败了
                    return null;
                }
                //获取配送费成功的
                successDeliveryFeeMap = successDeliveryFeeMap.entrySet().stream()
                    .sorted(Map.Entry.comparingByValue())
                    .collect(Collectors.toMap(Map.Entry::getKey, Map.Entry::getValue, (oldValue, newValue) -> oldValue, LinkedHashMap::new));
                List<Entry<String, BigDecimal>> successDeliveryFeeMapList = new ArrayList<>(successDeliveryFeeMap.entrySet());
                //最小运费
                Entry<String, BigDecimal> minDeliveryFeeEntry = successDeliveryFeeMapList.get(0);
                //如果相同最小配送费存在多个 包含店铺默认自配送时候取默认 否则按美团骑手、达达骑手、顺丰同城来取
                String deliveryPlatformName = DsDeliveryType.getPlatformName(minDeliveryFeeEntry.getKey());
                BigDecimal minDeliveryFee = minDeliveryFeeEntry.getValue();
                List<String> sameMinDeliveryFeeKeys = successDeliveryFeeMap.entrySet().stream()
                    .filter(entry -> entry.getValue().equals(minDeliveryFee)).map(entry -> DsDeliveryType.getPlatformName(entry.getKey()))
                    .collect(Collectors.toList());
                if(sameMinDeliveryFeeKeys.size() > 1){
                    DsOnlineStoreConfig dsOnlineStoreConfig = dsOnlineStoreConfigService.getOne(new LambdaQueryWrapper<DsOnlineStoreConfig>().eq(DsOnlineStoreConfig::getOnlineStoreId,orderInfoDomain.getDsOnlineStoreId()));
                    if(Objects.nonNull(dsOnlineStoreConfig)){
                        //店铺默认自配送方式
                        String selfDeliveryType = dsOnlineStoreConfig.getSelfDeliveryType();
                        if(sameMinDeliveryFeeKeys.contains(selfDeliveryType)){
                            deliveryPlatformName = selfDeliveryType;
                        }else if(sameMinDeliveryFeeKeys.contains(DsDeliveryType.MT.getPlatformName())){
                            deliveryPlatformName = DsDeliveryType.MT.getPlatformName();
                        }else if(sameMinDeliveryFeeKeys.contains(DsDeliveryType.DD.getPlatformName())){
                            deliveryPlatformName = DsDeliveryType.DD.getPlatformName();
                        }else if(sameMinDeliveryFeeKeys.contains(DsDeliveryType.SFTC.getPlatformName())){
                            deliveryPlatformName = DsDeliveryType.SFTC.getPlatformName();
                        }
                    }
                }
                //最大运费
                Entry<String, BigDecimal> maxDeliveryFeeEntry = successDeliveryFeeMapList.get(successDeliveryFeeMapList.size() - 1);
                //预估节约配送费
                BigDecimal predictEconomizeDeliveryFee = maxDeliveryFeeEntry.getValue().subtract(minDeliveryFee);
                //-1转为null
                if(CollUtil.isNotEmpty(failDeliveryFeeMap)){
                    for (Entry<String, BigDecimal> entry : failDeliveryFeeMap.entrySet()) {
                        successDeliveryFeeMap.put(entry.getKey(),null);
                    }
                }
                //记录
                deliveryFeeEconomizeRecordService.saveDeliveryFeeEconomizeRecord(buildEconomizeDeliveryFeeRecord(orderInfoDomain,predictEconomizeDeliveryFee,successDeliveryFeeMap,deliveryPlatformName));
                return deliveryPlatformName;
            }catch (Exception e){
                log.info("最优配送方式获取异常,三方平台订单号:{},异常信息:{}",orderInfoDomain.getThirdOrderNo(),e.getMessage());
                return null;
            }
        }
        return null;
    }


    /**
     * 构建节约配送费记录
     * @param orderInfoDomain
     * @param predictEconomizeDeliveryFee
     * @param deliveryFeeMap
     * @return
     */
    private DeliveryFeeEconomizeRecord buildEconomizeDeliveryFeeRecord(OrderInfoAllDomainCheckValueObject orderInfoDomain,BigDecimal predictEconomizeDeliveryFee,Map<String, BigDecimal> deliveryFeeMap,String deliveryPlatformName){
        DeliveryFeeEconomizeRecord deliveryFeeEconomizeRecord = new DeliveryFeeEconomizeRecord();
        if(CollUtil.isNotEmpty(orderInfoDomain.getBizUnitOrgResDTOS()) && StrUtil.isNotBlank(orderInfoDomain.getBizUnitOrgResDTOS().get(0).getOrCode())){
            deliveryFeeEconomizeRecord.setSubCompanyCode(orderInfoDomain.getBizUnitOrgResDTOS().get(0).getOrCode());
        }
        deliveryFeeEconomizeRecord.setStoreCode(orderInfoDomain.getSourceOnlineStoreCode());
        deliveryFeeEconomizeRecord.setStoreName(orderInfoDomain.getSourceOnlineStoreName());
        deliveryFeeEconomizeRecord.setThirdPlatformCode(orderInfoDomain.getThirdPlatformCode());
        deliveryFeeEconomizeRecord.setThirdOrderNo(orderInfoDomain.getThirdOrderNo());
        deliveryFeeEconomizeRecord.setOrderNo(orderInfoDomain.getOrderNo());
        deliveryFeeEconomizeRecord.setOrderCreated(orderInfoDomain.getCreated());
        deliveryFeeEconomizeRecord.setDeliveryType(DeliveryTypeEnum.SELLER_SELF.getCode());
        Map<String, BigDecimal> deliveryNameFeeMap = new HashMap<>();
        deliveryFeeMap.forEach((key,value)->{
            deliveryNameFeeMap.put(DeliveryPlatformEnum.getByCode(key),value);
        });
        deliveryFeeEconomizeRecord.setDeliveryFeeJson(JSONObject.toJSONString(deliveryNameFeeMap, SerializerFeature.WriteMapNullValue));
        deliveryFeeEconomizeRecord.setPredictDeliveryPlatName(deliveryPlatformName);
        deliveryFeeEconomizeRecord.setPredictEconomizeDeliveryFee(predictEconomizeDeliveryFee);
        return deliveryFeeEconomizeRecord;
    }

    /**
     * 获取配送方式和配送费的map集合
     * @param orderInfoDomain
     * @return
     */
    private Map<String, BigDecimal> mapDeliveryFee(OrderInfoAllDomainCheckValueObject orderInfoDomain){
        List<DsDeliveryStore> deliveryStores;
        if(CollUtil.isNotEmpty(orderInfoDomain.getDeliveryStores())){
            //转商家自配送时 如果没有配送费记录才需要查询
            deliveryStores = orderInfoDomain.getDeliveryStores();
        }else {
            deliveryStores = getDeliveryStores(orderInfoDomain);
        }
        if(CollUtil.isEmpty(deliveryStores)){
            return Maps.newHashMap();
        }
        orderInfoDomain.setDeliveryStores(deliveryStores);
        DsMerchantGroupInfo merchantGroupInfo = merchantGroupInfoRepo.selectOne(new LambdaUpdateWrapper<DsMerchantGroupInfo>().eq(DsMerchantGroupInfo::getMerCode,"500001"));
        //key:配送方式  value:配送费
        Map<String,Future<BigDecimal>> deliveryFeeFutureMap = new HashMap<>();
        deliveryStores.parallelStream().forEach(deliveryStore -> {
            deliveryFeeFutureMap.put(deliveryStore.getPlatformCode(), getDeliveryFee(orderInfoDomain, deliveryStore, merchantGroupInfo.getSessionKey()));
        });
        //转成Map<String,BigDecimal>
        Map<String,BigDecimal> deliveryFeeMap = new HashMap<>();
        deliveryFeeFutureMap.forEach((key, value)->{
            try {
                deliveryFeeMap.put(key, Objects.isNull(value)?null:value.get());
            }catch (Exception e){
                deliveryFeeMap.put(key, null);
            }
        });
        log.info("获取门店配送获取最优的配送方式,三方平台订单号:{},结果:{}",orderInfoDomain.getThirdOrderNo(),JSONObject.toJSONString(deliveryFeeMap));
        return deliveryFeeMap;
    }

    /**
     * 获取门店配送信息
     * @return
     */
    public List<DsDeliveryStore> getDeliveryStores(OrderInfoAllDomainCheckValueObject orderInfoDomain){
        DsOnlineStore dsOnlineStore = dsOnlineStoreRepo.selectById(orderInfoDomain.getDsOnlineStoreId());
        if(Objects.isNull(dsOnlineStore)){
            return Lists.newArrayList();
        }
        List<DsOnlineStoreDelivery> dsOnlineStoreDeliveries = dsOnlineStoreDeliveryRepo.selectList(
            new LambdaQueryWrapper<DsOnlineStoreDelivery>()
                .eq(DsOnlineStoreDelivery::getOnlineStoreId, dsOnlineStore.getId())
                .eq(DsOnlineStoreDelivery::getStatus, DsConstants.INTEGER_ONE)
                .notIn(DsOnlineStoreDelivery::getDeliveryType,Lists.newArrayList(DsDeliveryType.YGZS.getPlatformName(),DsDeliveryType.DDZT.getPlatformName(),DsDeliveryType.KDPS.getPlatformName(),DsDeliveryType.SJKDPS.getPlatformName())));
        if(CollUtil.isEmpty(dsOnlineStoreDeliveries)){
            return Lists.newArrayList();
        }
        List<Long> deliveryStoreIds = dsOnlineStoreDeliveries.stream().map(DsOnlineStoreDelivery::getDeliveryStoreId).filter(Objects::nonNull).collect(Collectors.toList());
        List<DsDeliveryStore> dsDeliveryStores = dsDeliveryStoreRepo.listByIdsAndPlatformCode("500001", deliveryStoreIds, null);
        if(CollUtil.isEmpty(dsDeliveryStores)){
            return Lists.newArrayList();
        }
        StoreInfoDataResDTO storeInfo = routeClientService.getStoreInfo(dsOnlineStore.getOrganizationCode());
        if(Objects.nonNull(storeInfo)){
            orderInfoDomain.setBizUnitOrgResDTOS(storeInfo.getBizUnitOrgList().stream().filter(bizUnitOrg -> bizUnitOrg.getLayer().equals(DsConstants.STRING_ONE)).collect(Collectors.toList()));
            dsDeliveryStores.forEach(e->{
                e.setLongitude(storeInfo.getLongitude());
                e.setLatitude(storeInfo.getLatitude());
                e.setCity(storeInfo.getCity());
            });
        }
        return dsDeliveryStores;
    }

    /**
     * 异步获取配送费
     * @param orderInfoDomain
     * @param dsDeliveryStore
     * @param sessionKey
     * @return
     */
    private Future<BigDecimal> getDeliveryFee(OrderInfoAllDomainCheckValueObject orderInfoDomain,DsDeliveryStore dsDeliveryStore,String sessionKey) {
        return threadPoolExecutor.submit(() -> {
            try {
            //先调用美团、达达等配送平台
            ResponseNet<CreateRiderOrderResp> responseNet = hemsClientService.riderPreCompare(buildReverseOrderAddReq(orderInfoDomain, dsDeliveryStore), dsDeliveryStore.getMerCode(), sessionKey, dsDeliveryStore.getPlatformCode(), dsDeliveryStore.getDeliveryClientCode());
            log.info("调用三方平台获取配送费,三方平台订单号:{},返回结果：{}",orderInfoDomain.getThirdOrderNo(),JSONObject.toJSONString(responseNet));
            if (responseNet.getCode() == 0) {
                return NumberUtil.toBigDecimal(responseNet.getData().getTotalPrice());
            }
            //请求失败 调用腾讯计算距离
            return getDeliveryFeeByRuleSet(orderInfoDomain,dsDeliveryStore);
        }catch (Exception e){
                log.warn("调用三方平台获取配送费异常,返回默认值,三方平台订单号:{}",
                    orderInfoDomain.getThirdOrderNo(), e);
            return getDeliveryFeeByRuleSet(orderInfoDomain,dsDeliveryStore);
        }});

    }

    /**
     * 请求参数组装
     * @param orderInfoDomain
     * @param dsDeliveryStore
     * @return
     */
    private ReqCreateRiderOrderDto buildReverseOrderAddReq(OrderInfoAllDomainCheckValueObject orderInfoDomain,DsDeliveryStore dsDeliveryStore) {
        ReqCreateRiderOrderDto riderOrderAddReq = new ReqCreateRiderOrderDto();
        ReqCreateRiderOrderDto.RiderOrderInfoDto riderOrder = new ReqCreateRiderOrderDto.RiderOrderInfoDto();
        riderOrder.setOrderid(orderInfoDomain.getOrderNo().toString());
        riderOrder.setDeliveryid(orderInfoDomain.getOrderNo());
        riderOrder.setShopid(dsDeliveryStore.getDeliveryStoreCode());
        riderOrder.setDeliveryServiceCode(dsDeliveryStore.getDefaultServiceCode());
        riderOrder.setOrderSource("101");
        riderOrder.setOrderWeight("0.1");
        riderOrder.setIsInsured(0);
        riderOrder.setOrderType(1);
        riderOrder.setIsPersonDirect(0);
        riderOrder.setOrderTotalAmount(orderInfoDomain.getOrderPayInfo().getTotalAmount().toString());
        riderOrderAddReq.setOrder(riderOrder);

        ReqCreateRiderOrderDto.RiderReceiverInfoDto receiver = new ReqCreateRiderOrderDto.RiderReceiverInfoDto();
        OrderDeliveryAddress orderDeliveryAddress = orderInfoDomain.getOrderDeliveryAddress();
        receiver.setAddress(orderDeliveryAddress.getAddress());
        receiver.setName(orderDeliveryAddress.getReceiverName());
        receiver.setLatitude(orderInfoDomain.getReceiverLat());
        receiver.setLongitude(orderInfoDomain.getReceiverLng());
        receiver.setPhone(orderDeliveryAddress.getReceiverTelephone());
        receiver.setPositionSource("3");
        riderOrderAddReq.setReceiver(receiver);

        ReqCreateRiderOrderDto.RiderTransportInfoDto transport = new ReqCreateRiderOrderDto.RiderTransportInfoDto();
        transport.setAddress(dsDeliveryStore.getAddress());
        transport.setLatitude(dsDeliveryStore.getLatitude());
        transport.setLongitude(dsDeliveryStore.getLongitude());
        transport.setName(dsDeliveryStore.getDeliveryStoreName());
        transport.setPositionSource("3");
        transport.setPhone(dsDeliveryStore.getContactPhone());
        riderOrderAddReq.setTransport(transport);

        List<ReqCreateRiderOrderDto.RiderItemInfoDto> itemList = new ArrayList<>();
        for (OrderDetail orderDetail : orderInfoDomain.getOrderDetailList()) {
            ReqCreateRiderOrderDto.RiderItemInfoDto item = new ReqCreateRiderOrderDto.RiderItemInfoDto();
            item.setItemName(orderDetail.getCommodityName());
            item.setItemId(orderDetail.getErpCode());
            item.setItemPrice(orderDetail.getPrice().toString());
            item.setItemQuantity(orderDetail.getCommodityCount());
            item.setItemSize(1);
            item.setItemRemark("");
            item.setItemActualPrice(orderDetail.getPrice().toString());
            itemList.add(item);
        }
        riderOrderAddReq.setItems(itemList);
        return riderOrderAddReq;
    }

    /**
     * 通过配置运费规则计算配送费
     * @param orderInfoDomain
     * @param dsDeliveryStore
     * @return
     */
    private BigDecimal getDeliveryFeeByRuleSet(OrderInfoAllDomainCheckValueObject orderInfoDomain,DsDeliveryStore dsDeliveryStore){
        List<DeliveryFeeRuleAddprice> deliveryFeeRuleAddprices = deliveryFeeRuleRepository.listFeeRuleAddpriceByCityAndType(dsDeliveryStore.getCity(), AddPriceTypeEnum.DISTANCE_ADD_PRICE.name());
        if (CollUtil.isEmpty(deliveryFeeRuleAddprices)) {
            return null;
        }
        List<Long> ids = deliveryFeeRuleAddprices.stream().map(DeliveryFeeRuleAddprice::getDeliveryRuleId).collect(Collectors.toList());
        List<Long> ruleIds = deliveryFeeRuleRepository.listFeeRuleByIdsAndPlatFormCode(ids, dsDeliveryStore.getPlatformCode())
            .stream().map(DeliveryFeeRule::getId).collect(Collectors.toList());
        if (CollUtil.isEmpty(ruleIds)) {
            return null;
        }
        deliveryFeeRuleAddprices = deliveryFeeRuleAddprices.stream().filter(x-> ruleIds.contains(x.getDeliveryRuleId())).sorted(Comparator.comparingLong(DeliveryFeeRuleAddprice::getId).reversed()).collect(Collectors.toList());
        //调用腾讯地图获取距离
        Double distance = txMapClientService.getDistance(dsDeliveryStore.getLatitude(), dsDeliveryStore.getLongitude(), orderInfoDomain.getReceiverLat(), orderInfoDomain.getReceiverLng());
        if (Objects.isNull(distance)) {
            return null;
        }
        DeliveryFeeRule deliveryFeeRule = deliveryFeeRuleRepository.getDeliveryFeeRuleById(deliveryFeeRuleAddprices.get(0).getDeliveryRuleId());
        //配送费 = 起步价+距离加价+时间加价
        BigDecimal deliveryFee = deliveryFeeRule.getStartPrice();
        if(distance > Double.parseDouble(deliveryFeeRuleAddprices.get(0).getEndRange())){
            //如果配送距离大于最大区间 直接取最大区间对应的加价
            deliveryFee = deliveryFee.add(deliveryFeeRuleAddprices.get(0).getAddPrice());
        }else {
            //配送距离在区间内 取区间对应的加价
            Optional<DeliveryFeeRuleAddprice> fitlerRuleOptional = deliveryFeeRuleAddprices.stream().filter(rule -> Double.parseDouble(rule.getStartRange()) <= distance && distance < Double.parseDouble(rule.getEndRange())).findFirst();
            if (fitlerRuleOptional.isPresent()) {
                deliveryFee = deliveryFee.add(fitlerRuleOptional.get().getAddPrice());
            }
        }
        List<DeliveryFeeRuleAddprice> deliveryFeeRuleTimes = deliveryFeeRuleRepository.listFeeRuleAddpriceByCityAndType(dsDeliveryStore.getCity(), AddPriceTypeEnum.TIME_ADD_PRICE.name());
        deliveryFeeRuleTimes = deliveryFeeRuleTimes.stream().filter(x-> ruleIds.contains(x.getDeliveryRuleId())).collect(Collectors.toList());
        Optional<DeliveryFeeRuleAddprice> anyTime = deliveryFeeRuleTimes.stream()
            .filter(time -> judgeTime(orderInfoDomain.getCreated(), Time.valueOf(time.getStartRange()), Time.valueOf(time.getEndRange())))
            .findAny();
        if (anyTime.isPresent()) {
            deliveryFee = deliveryFee.add(anyTime.get().getAddPrice());
        }
        log.info("通过心云配送费规则获取配送费,三方平台订单号:{},返回结果：{}",orderInfoDomain.getThirdOrderNo(),deliveryFee);
        //匹配导返回起步价加阶梯价
        return NumberUtil.toBigDecimal(deliveryFee.doubleValue());
    }

    private boolean judgeTime(Date nowDate, Time startTime, Time endTime) {
        LocalDateTime localDateTime = Objects.isNull(nowDate)?LocalDateTime.now():LocalDateTimeUtil.of(nowDate);
        LocalDateTime startDate = startTime.toLocalTime().atDate(LocalDate.now());
        LocalDateTime endDate = endTime.toLocalTime().atDate(LocalDate.now());
        return localDateTime.isAfter(startDate) && localDateTime.isBefore(endDate);
    }
}
