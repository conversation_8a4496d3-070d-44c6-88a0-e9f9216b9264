package cn.hydee.middle.business.order.yxtadapter.domainservice.mdm;

import cn.hutool.core.bean.BeanUtil;
import cn.hydee.middle.business.order.dto.rsp.AccountEmpResDTO;
import cn.hydee.middle.business.order.dto.rsp.AccountResDTO;
import cn.hydee.middle.business.order.dto.rsp.OrgInfoCodesResDTO;
import cn.hydee.middle.business.order.dto.rsp.UserInfoDTO;
import cn.hydee.middle.business.order.dto.rsp.baseinfo.EmployeeResDTO;
import cn.hydee.middle.business.order.dto.rsp.baseinfo.SysEmployeeResDTO;
import cn.hydee.middle.business.order.dto.rsp.obOrder.OrganizationRspDto;
import cn.hydee.middle.business.order.yxtadapter.domain.baseinfo.SysOrganizationDto;
import cn.hydee.starter.dto.PageDTO;
import com.google.common.collect.Lists;
import com.yxt.login.opensdk.account.old.dto.response.AccountDetailResDTO;
import com.yxt.login.opensdk.token.dto.response.LoginOpenResDTO;
import com.yxt.org.read.opensdk.emp.dto.response.old.EmployeeInfoResDTO;
import com.yxt.org.read.opensdk.org.dto.response.old.OrganizationResDTO;
import com.yxt.org.read.opensdk.org.dto.response.old.QueryStoreInfoResDTO;
import com.yxt.org.read.opensdk.org.dto.response.old.SysOrganization;
import org.springframework.util.CollectionUtils;

import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @date 2024/12/25
 * @since 1.0
 */
public class MiddleBaseInfoAdapterDtoConvert {

    public static SysEmployeeResDTO convertGetEmployeeByUserIdRes(EmployeeInfoResDTO newResDto) {
        if(newResDto == null) return null;
        SysEmployeeResDTO oldResDto = new SysEmployeeResDTO();
        //oldResDto.setAccount();
        oldResDto.setEmpId(newResDto.getId());
        oldResDto.setEmpCode(newResDto.getEmpCode());
        oldResDto.setEmpName(newResDto.getEmpName());
        oldResDto.setMobile(newResDto.getMobile());
        //oldResDto.setEmpId();
        oldResDto.setSubOrgCode(newResDto.getSubOrgCode());
        //oldResDto.setBusNo();
        oldResDto.setMerCode(newResDto.getMerCode());
        return oldResDto;
    }

    public static UserInfoDTO convertLoginRes(LoginOpenResDTO newResDto){
        if(newResDto == null) return null;
        UserInfoDTO oldResDto = new UserInfoDTO();
        oldResDto.setToken(newResDto.getToken());
        //oldResDto.setPassword();
        oldResDto.setUserId(newResDto.getUserId());
        oldResDto.setUserName(newResDto.getUserName());
        oldResDto.setMerCode(newResDto.getMerCode());
        oldResDto.setMobile(newResDto.getMobile());
        //oldResDto.setUserStatus();
        //oldResDto.setMerStatus();
        return oldResDto;
    }

    public static PageDTO<EmployeeResDTO> convertQueryEmpByConditionRes(com.yxt.lang.dto.api.PageDTO<EmployeeInfoResDTO> newResDto){
        PageDTO<EmployeeResDTO> oldResDto = new PageDTO<>();
        BeanUtil.copyProperties(newResDto, oldResDto);
        List<EmployeeInfoResDTO> newData = newResDto.getData();
        if(!CollectionUtils.isEmpty(newData)){
            oldResDto.setData(newData.stream().map(MiddleBaseInfoAdapterDtoConvert::convertQueryEmpByConditionData).collect(Collectors.toList()));
        }
        return oldResDto;
    }

    private static EmployeeResDTO convertQueryEmpByConditionData(EmployeeInfoResDTO newResDto){
        if(newResDto == null) return null;
        EmployeeResDTO oldResDto = new EmployeeResDTO();
        oldResDto.setEmpCode(newResDto.getEmpCode());
        oldResDto.setEmpName(newResDto.getEmpName());
        oldResDto.setSex(newResDto.getSex());
        oldResDto.setMobile(newResDto.getMobile());
        oldResDto.setAddress(newResDto.getAddress());
        oldResDto.setEmpType(newResDto.getEmpType());
        oldResDto.setEmpStatus(newResDto.getEmpStatus());
        //oldResDto.setWorks();
        oldResDto.setSubOrgCode(newResDto.getSubOrgCode());
        oldResDto.setPost(newResDto.getPost());
        oldResDto.setSubOrgName(newResDto.getSubOrgName());
        oldResDto.setRemark(newResDto.getRemark());
        //oldResDto.setRoleName();
        oldResDto.setId(newResDto.getId());
        oldResDto.setMerCode(newResDto.getMerCode());
        oldResDto.setCreateName(newResDto.getCreateName());
        oldResDto.setCreateTime(new Date(newResDto.getCreateTime()));
        oldResDto.setModifyName(newResDto.getModifyName());
        oldResDto.setModifyTime(new Date(newResDto.getModifyTime()));
        return oldResDto;
    }

    public static AccountResDTO convertGetAccountByIdRes(AccountDetailResDTO newResDto){
        if(newResDto == null) return null;
        AccountResDTO oldResDto = new AccountResDTO();
        oldResDto.setIsvalid(newResDto.getIsvalid());
        oldResDto.setCreateName(newResDto.getCreateName());
        oldResDto.setCreateTime(new Date(newResDto.getCreateTime()));
        oldResDto.setModifyName(newResDto.getModifyName());
        oldResDto.setModifyTime(new Date(newResDto.getModifyTime()));
        oldResDto.setUserName(newResDto.getUserName());

        oldResDto.setId(newResDto.getId());
        oldResDto.setMerCode(newResDto.getMerCode());
        oldResDto.setUserCode(newResDto.getUserCode());
        oldResDto.setAccount(newResDto.getAccount());
        oldResDto.setPassword(newResDto.getPassword());
        oldResDto.setAvatarPath(newResDto.getAvatarPath());
        oldResDto.setMobile(newResDto.getMobile());
        oldResDto.setMail(newResDto.getMail());
        oldResDto.setUserType(newResDto.getUserType());
        oldResDto.setAdmin(newResDto.getAdmin());
        oldResDto.setUserStatus(newResDto.getUserStatus());
        return oldResDto;
    }

    public static AccountEmpResDTO convertAccountByAccountRes(com.yxt.login.opensdk.account.old.dto.response.AccountEmpResDTO newResDto){
        if(newResDto == null) return null;
        AccountEmpResDTO oldResDto = new AccountEmpResDTO();
        oldResDto.setId(newResDto.getId());
        oldResDto.setMobile(newResDto.getMobile());
        oldResDto.setEmpCode(newResDto.getEmpCode());
        oldResDto.setEmpName(newResDto.getEmpName());
        oldResDto.setEmpStatus(newResDto.getEmpStatus());
        oldResDto.setSubOrgCode(newResDto.getSubOrgCode());
        oldResDto.setPost(newResDto.getPost());
        oldResDto.setMerCode(newResDto.getMerCode());
        return oldResDto;
    }

    public static AccountEmpResDTO convertQueryEmployeeByIdRes(EmployeeInfoResDTO newResDto){
        if(newResDto == null) return null;
        AccountEmpResDTO oldResDto = new AccountEmpResDTO();
        oldResDto.setId(newResDto.getId());
        oldResDto.setMobile(newResDto.getMobile());
        oldResDto.setEmpCode(newResDto.getEmpCode());
        oldResDto.setEmpName(newResDto.getEmpName());
        oldResDto.setEmpStatus(newResDto.getEmpStatus().toString());
        oldResDto.setSubOrgCode(newResDto.getSubOrgCode());
        oldResDto.setPost(newResDto.getPost());
        oldResDto.setMerCode(newResDto.getMerCode());
        oldResDto.setAccountType(newResDto.getEmpType());
        return oldResDto;
    }

    public static EmployeeResDTO convertQueryEmpInfoByCodeRes(com.yxt.org.read.opensdk.emp.dto.response.old.SysEmployeeResDTO newResDto){
        if(newResDto == null) return null;
        EmployeeResDTO oldResDto = new EmployeeResDTO();
        oldResDto.setEmpCode(newResDto.getEmpCode());
        oldResDto.setEmpName(newResDto.getEmpName());
        oldResDto.setSex(newResDto.getSex());
        //oldResDto.setMobile();
        oldResDto.setAddress(newResDto.getAddress());
        oldResDto.setEmpType(newResDto.getEmpType());
        oldResDto.setEmpStatus(newResDto.getEmpStatus());
        //oldResDto.setWorks();
        oldResDto.setSubOrgCode(newResDto.getSubOrgCode());
        oldResDto.setPost(newResDto.getPost());
        //oldResDto.setSubOrgName();
        oldResDto.setRemark(newResDto.getRemark());
        //oldResDto.setRoleName();
        //oldResDto.setId();
        oldResDto.setMerCode(newResDto.getMerCode());
        oldResDto.setCreateName(newResDto.getCreateName());
        oldResDto.setCreateTime(new Date(newResDto.getCreateTime()));
        oldResDto.setModifyName(newResDto.getModifyName());
        oldResDto.setModifyTime(new Date(newResDto.getModifyTime()));
        return oldResDto;
    }

    public static OrganizationRspDto convertQueryOrgByCodeRes(OrganizationResDTO newResDto){
        if(newResDto == null) return null;
        OrganizationRspDto oldResDto = new OrganizationRspDto();
        oldResDto.setId(newResDto.getId());
        oldResDto.setMerCode(newResDto.getMerCode());
        oldResDto.setCreateName(newResDto.getCreateName());
        oldResDto.setCreateTime(new Date(newResDto.getCreateTime()).toString());
        oldResDto.setModifyName(newResDto.getModifyName());
        oldResDto.setModifyTime(new Date(newResDto.getModifyTime()).toString());
        oldResDto.setOrParent(newResDto.getOrParent());
        oldResDto.setOrCode(newResDto.getOrCode());
        oldResDto.setOrName(newResDto.getOrName());
        oldResDto.setOrClass(newResDto.getOrClass().toString());
        oldResDto.setOrType(newResDto.getOrType().toString());
        oldResDto.setStatus(newResDto.getStatus().toString());
        oldResDto.setRemark(newResDto.getRemark());
        oldResDto.setHeadPerson(newResDto.getHeadPerson());
        oldResDto.setPhone(newResDto.getPhone());
        oldResDto.setSortNumber(newResDto.getSortNumber().toString());
        oldResDto.setOrParentName(newResDto.getOrParentName());
        oldResDto.setOrParentCode(newResDto.getOrParentCode());
        oldResDto.setIsvalid(newResDto.getIsvalid().toString());
        return oldResDto;
    }

    public static List<SysOrganizationDto> convertBatchGetOrgByCodesRes(List<SysOrganization> newResDto){
        if(CollectionUtils.isEmpty(newResDto)) return Lists.newArrayList();
        List<SysOrganizationDto> oldResDto = new ArrayList<>();
        for (SysOrganization newSysOrganization:newResDto) {
            SysOrganizationDto oldSysOrganizationDto = new SysOrganizationDto();
            oldSysOrganizationDto.setId(newSysOrganization.getId());
            oldSysOrganizationDto.setOrParent(newSysOrganization.getOrParent());
            oldSysOrganizationDto.setMerCode(newSysOrganization.getMerCode());
            oldSysOrganizationDto.setOrCode(newSysOrganization.getOrCode());
            oldSysOrganizationDto.setOrName(newSysOrganization.getOrName());
            oldSysOrganizationDto.setOrType(newSysOrganization.getOrType());
            oldSysOrganizationDto.setOrClass(newSysOrganization.getOrClass());
            oldSysOrganizationDto.setStatus(newSysOrganization.getStatus());
            oldSysOrganizationDto.setRemark(newSysOrganization.getRemark());
            oldSysOrganizationDto.setHeadPerson(newSysOrganization.getHeadPerson());
            oldSysOrganizationDto.setPhone(newSysOrganization.getPhone());
            oldSysOrganizationDto.setSortNumber(newSysOrganization.getSortNumber());
            oldSysOrganizationDto.setShortName(newSysOrganization.getShortName());
            oldSysOrganizationDto.setOrTag(newSysOrganization.getOrTag());
            oldSysOrganizationDto.setCrmCode(newSysOrganization.getCrmCode());
            oldSysOrganizationDto.setFnumber(newSysOrganization.getFnumber());
            oldSysOrganizationDto.setLayer(newSysOrganization.getLayer());
            oldSysOrganizationDto.setUnitCode(newSysOrganization.getUnitCode());
            oldSysOrganizationDto.setUnitName(newSysOrganization.getUnitName());
            oldSysOrganizationDto.setIsvalid(newSysOrganization.getIsvalid());
            oldSysOrganizationDto.setCreateName(newSysOrganization.getCreateName());
            oldSysOrganizationDto.setCreateTime(new Date(newSysOrganization.getCreateTime()));
            oldSysOrganizationDto.setModifyName(newSysOrganization.getModifyName());
            oldSysOrganizationDto.setModifyTime(new Date(newSysOrganization.getModifyTime()));
            oldSysOrganizationDto.setParentPath(newSysOrganization.getParentPath());
            oldResDto.add(oldSysOrganizationDto);
        }
        return oldResDto;
    }

    public static OrgInfoCodesResDTO convertQueryOrgByCodesRes(QueryStoreInfoResDTO newResDto){
        if(null == newResDto) return null;
        OrgInfoCodesResDTO oldResDto = new OrgInfoCodesResDTO();
        oldResDto.setScrollId(newResDto.getScrollId());
        oldResDto.setStoreInfoList(newResDto.getStoreInfoList().stream().map(newStoreInfoDTO -> {
            OrgInfoCodesResDTO.StoreInfo oldStoreInfo = new OrgInfoCodesResDTO.StoreInfo();
            oldStoreInfo.setStoreCode(newStoreInfoDTO.getStoreCode());
            oldStoreInfo.setStoreName(newStoreInfoDTO.getStoreName());
            oldStoreInfo.setOrgCodePath(newStoreInfoDTO.getOrgCodePath());
            oldStoreInfo.setOrgNamePath(newStoreInfoDTO.getOrgNamePath());
            oldStoreInfo.setId(newStoreInfoDTO.getId());
            oldStoreInfo.setOrgCode(newStoreInfoDTO.getOrgCode());
            oldStoreInfo.setOrgName(newStoreInfoDTO.getOrgName());
            oldStoreInfo.setOrgClass(newStoreInfoDTO.getOrgClass().toString());
            oldStoreInfo.setOrgType(newStoreInfoDTO.getOrgType().toString());
            oldStoreInfo.setOrgStatus(newStoreInfoDTO.getOrgStatus().toString());
            oldStoreInfo.setParentPath(newStoreInfoDTO.getParentPath());
            //oldStoreInfo.setAddress();
            return oldStoreInfo;
        }).collect(Collectors.toList()));
        return oldResDto;
    }

    public static List<EmployeeResDTO> convertGetListRes(List<EmployeeInfoResDTO> newResDto){
        if(CollectionUtils.isEmpty(newResDto)) return Lists.newArrayList();
        List<EmployeeResDTO> oldResDto = new ArrayList<>();
        for (EmployeeInfoResDTO newEmployeeInfoResDTO:newResDto) {
            EmployeeResDTO oldResDate = new EmployeeResDTO();
            oldResDate.setEmpCode(newEmployeeInfoResDTO.getEmpCode());
            oldResDate.setEmpName(newEmployeeInfoResDTO.getEmpName());
            oldResDate.setSex(newEmployeeInfoResDTO.getSex());
            oldResDate.setMobile(newEmployeeInfoResDTO.getMobile());
            oldResDate.setAddress(newEmployeeInfoResDTO.getAddress());
            oldResDate.setEmpType(newEmployeeInfoResDTO.getEmpType());
            oldResDate.setEmpStatus(newEmployeeInfoResDTO.getEmpStatus());
            //oldResDto.setWorks();
            oldResDate.setSubOrgCode(newEmployeeInfoResDTO.getSubOrgCode());
            oldResDate.setPost(newEmployeeInfoResDTO.getPost());
            oldResDate.setSubOrgName(newEmployeeInfoResDTO.getSubOrgName());
            oldResDate.setRemark(newEmployeeInfoResDTO.getRemark());
            //oldResDto.setRoleName();
            oldResDate.setId(newEmployeeInfoResDTO.getId());
            oldResDate.setMerCode(newEmployeeInfoResDTO.getMerCode());
            oldResDate.setCreateName(newEmployeeInfoResDTO.getCreateName());
            oldResDate.setCreateTime(new Date(newEmployeeInfoResDTO.getCreateTime()));
            oldResDate.setModifyName(newEmployeeInfoResDTO.getModifyName());
            oldResDate.setModifyTime(new Date(newEmployeeInfoResDTO.getModifyTime()));
            oldResDto.add(oldResDate);
        }
        return oldResDto;
    }
}
