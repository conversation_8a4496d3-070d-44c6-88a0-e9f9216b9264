package cn.hydee.middle.business.order.yxtadapter.domain.oms.getorderstatus;

import cn.hydee.middle.business.order.yxtadapter.domain.oms.createorder.YnOmsFeeDetail;
import cn.hydee.middle.business.order.yxtadapter.domain.oms.createorder.YnOmsGoods;
import cn.hydee.middle.business.order.yxtadapter.domain.oms.createorder.YnOmsOrderInfo;
import lombok.Data;

import java.util.List;

/**
 * Created by Intellij IDEA.
 *
 * <AUTHOR>
 * Date:  2023/9/2
 */
@Data
public class YnOmsGetOrderStatusContext implements java.io.Serializable {

    private List<YnOmsFeeDetail> FEE;
    private List<YnOmsOrderInfo> ORDER;
    private List<YnOmsGoods> GOODS;
}
