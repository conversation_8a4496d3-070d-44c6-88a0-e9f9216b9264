package cn.hydee.middle.business.order.storeautosync.domain.adapter;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.date.LocalDateTimeUtil;
import cn.hydee.middle.business.order.Enums.PlatformCodeEnum;
import cn.hydee.middle.business.order.storeautosync.constants.StoreSyncStatus;
import cn.hydee.middle.business.order.storeautosync.domain.storeauth.entity.StoreAuthExcelEntity;
import cn.hydee.middle.business.order.storeautosync.facade.storeauth.entity.StoreAuthRecordResponse;
import cn.hydee.middle.business.order.storeautosync.infrastructure.mysql.entity.DsOnlineStoreAuthRecord;

import java.util.ArrayList;
import java.util.List;
import java.util.stream.Collectors;

public class StoreAutoAuthAdapter {

    public static List<DsOnlineStoreAuthRecord> storeExcel2DbEntity(List<StoreAuthExcelEntity> excelStoreList, String platformCode, String serviceMode, String userName) {

        return excelStoreList.stream().map(store -> {
            DsOnlineStoreAuthRecord storeAuthRecord = new DsOnlineStoreAuthRecord();
            storeAuthRecord.setPlatformCode(platformCode);
            storeAuthRecord.setServiceMode(serviceMode);
            storeAuthRecord.setCreater(userName);
            storeAuthRecord.setPlatformStoreCode(store.getPlatformStoreId());
            storeAuthRecord.setOnlineStoreCode(store.getStoreCOde());
            storeAuthRecord.setOnlineStoreName(store.getStoreName());
            storeAuthRecord.setAuthStatus(StoreSyncStatus.WAITING.getCode());
            storeAuthRecord.setRelieveStatus(StoreSyncStatus.WAITING.getCode());
            storeAuthRecord.setChangeStatus(StoreSyncStatus.WAITING.getCode());
            return storeAuthRecord;
        }).collect(Collectors.toList());

    }

    public static List<StoreAuthRecordResponse> dbEntity2StoreAuthRecordResponse(List<DsOnlineStoreAuthRecord> records) {
        if (CollUtil.isEmpty(records)) {
            return new ArrayList<>(0);
        }
        return records.stream().map(record -> {
            StoreAuthRecordResponse response = new StoreAuthRecordResponse();
            response.setId(record.getId());
            response.setPlatformCode(record.getPlatformCode());
            PlatformCodeEnum platformCodeEnum = PlatformCodeEnum.getByCode(record.getPlatformCode());
            if (platformCodeEnum != null) {
                response.setPlatformName(platformCodeEnum.getType());
            }
            response.setServiceMode(record.getServiceMode());
            response.setOnlineStoreCode(record.getOnlineStoreCode());
            response.setOnlineStoreName(record.getOnlineStoreName());
            response.setPlatformStoreCode(record.getPlatformStoreCode());
            response.setRelieveStatus(record.getRelieveStatus());
            response.setCreater(record.getCreater());
            response.setCreateTime(LocalDateTimeUtil.formatNormal(record.getCreateTime()));
            response.setAuthTime(LocalDateTimeUtil.formatNormal(record.getAuthTime()));
            response.setAuthStatus(record.getAuthStatus());
            response.setRelieveStatus(record.getRelieveStatus());
            response.setChangeStatus(record.getChangeStatus());
            return response;
        }).collect(Collectors.toList());
    }
}
