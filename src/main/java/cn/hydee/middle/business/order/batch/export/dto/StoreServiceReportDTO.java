package cn.hydee.middle.business.order.batch.export.dto;

import com.alibaba.excel.annotation.ExcelProperty;
import com.alibaba.excel.annotation.write.style.ColumnWidth;
import com.baomidou.mybatisplus.annotation.TableField;
import lombok.Data;

import java.io.Serializable;

/**
 * <AUTHOR>
 */
@Data
public class StoreServiceReportDTO implements Serializable {

    private static final long serialVersionUID = 6843130586877251932L;

    @ColumnWidth(30)
    @ExcelProperty(value = "上级机构", index = 0)
    private String parentOrganizationName;
    @ColumnWidth(30)
    @ExcelProperty(value = "机构名称", index = 1)
    private String organizationName;
    @ColumnWidth(10)
    @ExcelProperty(value = "机构编码", index = 2)
    private String organizationCode;
    @ColumnWidth(30)
    @ExcelProperty(value = "门店名称", index = 3)
    private String onlineStoreName;
    @ColumnWidth(10)
    @ExcelProperty(value = "门店编码", index = 4)
    private String onlineStoreCode;
    @ColumnWidth(10)
    @ExcelProperty(value = "平台名称", index = 5)
    private String thirdPlatformName;
    @ColumnWidth(10)
    @ExcelProperty(value = "有效订单数", index = 6)
    private Integer valideOrders;
    @ColumnWidth(10)
    @ExcelProperty(value = "平均拣货时长", index = 7)
    private String averagePickMins;
    @ColumnWidth(10)
    @ExcelProperty(value = "拣货超时订单数", index = 8)
    private Integer pickOvertimeOrders;
    @TableField(exist = false)
    @ColumnWidth(10)
    @ExcelProperty(value = "拣货超时率", index = 9)
    private String pickOvertimePercent;
    @ColumnWidth(10)
    @ExcelProperty(value = "平均配送时长", index = 10)
    private String averageDeliveryMins;
    @ColumnWidth(10)
    @ExcelProperty(value = "配送超时订单数", index = 11)
    private Integer deliveryOvertimeOrders;
    @TableField(exist = false)
    @ColumnWidth(10)
    @ExcelProperty(value = "配送超时率", index = 12)
    private String deliveryOvertimePercent;
    @ColumnWidth(10)
    @ExcelProperty(value = "无效订单数", index = 13)
    private Integer invalideOrders;
    @TableField(exist = false)
    @ColumnWidth(10)
    @ExcelProperty(value = "无效订单率", index = 14)
    private String invalidOrdePercent;
    @ColumnWidth(10)
    @ExcelProperty(value = "缺货订单数", index = 15)
    private Integer lackCommidityOrders;
    @ColumnWidth(10)
    @ExcelProperty(value = "缺货订单率", index = 16)
    private String lackCommidityPercent;
}
