package cn.hydee.middle.business.order.elasticsearch.config;

import org.apache.http.client.config.RequestConfig;
import org.elasticsearch.action.search.SearchRequest;
import org.elasticsearch.client.RequestOptions;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;

/**
 * <AUTHOR> (moatkon)
 * @date 2024年03月25日 18:31
 * @email: <EMAIL>
 */
@Component
public class RequestOptionsConfig {

  @Value("${esCustom.connectTimeout:2000}")
  private Integer connectTimeout;

  @Value("${esCustom.socketTimeout:3000}")
  private Integer socketTimeout;

  @Value("${esCustom.connectionRequestTimeout:2000}")
  private Integer connectionRequestTimeout;

  @Value("${esCustom.maxConcurrentShardRequests:20}")
  private Integer maxConcurrentShardRequests;


  public RequestOptions customOption() {
    RequestConfig requestConfig = RequestConfig.custom().setConnectTimeout(connectTimeout)
        .setSocketTimeout(socketTimeout).setConnectionRequestTimeout(connectionRequestTimeout)
        .build();
    return RequestOptions.DEFAULT.toBuilder().setRequestConfig(requestConfig).build();
  }

  public SearchRequest searchIndex(String index) {
    SearchRequest request = new SearchRequest(index);
    request.setMaxConcurrentShardRequests(maxConcurrentShardRequests);
    return request;
  }

  public SearchRequest searchIndex() {
    SearchRequest request = new SearchRequest();
    request.setMaxConcurrentShardRequests(20);
    return request;
  }


}
