package cn.hydee.middle.business.order.dto.template;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;

/**
 *  处理模板数据
 */
@Data
public class PrintClientTemplateBaseDto {
 @JsonProperty("printtype")
    private int printType;

    @JsonProperty("drawingsprinting")
    private int drawingSprinting;

    @JsonProperty("pagewidth")
    private int pageWidth;

    @JsonProperty("pageheight")
    private int pageHeight;

    @JsonProperty("iscolor")
    private int isColor;

    private int left;
    private int right;
    private int top;
    private int bottom;

    @JsonProperty("printtofile")
    private int printToFile;
    @JsonProperty("fileextension")
    private String fileExtension;
}
