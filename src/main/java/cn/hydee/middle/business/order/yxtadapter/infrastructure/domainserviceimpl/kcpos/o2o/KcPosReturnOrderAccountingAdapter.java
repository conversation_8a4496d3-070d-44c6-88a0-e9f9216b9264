package cn.hydee.middle.business.order.yxtadapter.infrastructure.domainserviceimpl.kcpos.o2o;

import cn.hutool.extra.spring.SpringUtil;
import cn.hydee.erp.model.order.OrderRefundReq;
import cn.hydee.middle.business.order.Enums.*;
import cn.hydee.middle.business.order.domain.OrderDetailDomain;
import cn.hydee.middle.business.order.domain.RefundOrderInfoAllDomain;
import cn.hydee.middle.business.order.dto.req.RefundBillReqDto;
import cn.hydee.middle.business.order.entity.*;
import cn.hydee.middle.business.order.entity.b2c.AfterSaleOrder;
import cn.hydee.middle.business.order.http.construct.ErpHttpReqStructUtil;
import cn.hydee.middle.business.order.interceptor.GlobalInterceptor;
import cn.hydee.middle.business.order.mapper.*;
import cn.hydee.middle.business.order.mapper.b2c.AfterSaleOrderMapper;
import cn.hydee.middle.business.order.service.*;
import cn.hydee.middle.business.order.service.async.HydeeEsSyncClientAsync;
import cn.hydee.middle.business.order.service.mongo.BillLogService;
import cn.hydee.middle.business.order.service.rocket.MessageProducerService;
import cn.hydee.middle.business.order.service.suport.OrderBasicService;
import cn.hydee.middle.business.order.service.suport.RefundBasicService;
import cn.hydee.middle.business.order.v2.manager.base.BaseInfoManager;
import cn.hydee.middle.business.order.yxtadapter.domain.pos.callback.getreturnorderinfo.KcPosReturnOrderDetail;
import cn.hydee.middle.business.order.yxtadapter.domain.pos.callback.returnorderaccounting.KcPosReturnOrderAccountingCallBackResponse;
import com.alibaba.fastjson.JSON;
import com.alibaba.google.common.collect.Lists;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;
import org.springframework.util.StringUtils;

import java.util.*;
import java.util.concurrent.Executor;
import java.util.stream.Collectors;

import static cn.hydee.middle.business.order.configuration.ThreadPoolConfig.ORDER_BUSINESS_ASYNC_THREAD_POOL;

/**
 * Created by Intellij IDEA.
 *
 * <AUTHOR>
 * Date:  2023/9/12
 */
@Slf4j
@Component
public class KcPosReturnOrderAccountingAdapter {

    private static final String YXT_POS_RETUNR_PRE1 = "YXT_POS_RETURN_PRE2_";


    @Autowired
    private BillLogService billLogService;

    @Autowired
    private RefundOrderMapper refundOrderMapper;


    @Autowired
    private OrderRefundStatisticsService orderRefundStatisticsService;
    @Autowired
    private RefundBasicService refundBasicService;

    @Autowired
    private MessageProducerService messageProducerService;

    @Autowired
    private BaseInfoManager baseInfoManager;

    @Autowired
    private OrderBasicService orderBasicService;

    @Autowired
    private OrderCommodityCostPriceService orderCommodityCostPriceService;

    @Autowired
    private OrderDetailMapper orderDetailMapper;
    @Autowired
    private ErpHttpReqStructUtil erpHttpReqStructUtil;


    @Autowired
    private RefundDetailMapper refundDetailMapper;


    @Autowired
    private ErpRefundInfoMapper erpRefundInfoMapper;


    @Autowired
    private ErpPayModeService erpPayModeService;

    @Autowired
    private StoreBillConfigService storeBillConfigService;

    @Autowired
    private CommodityStockService commodityStockService;

    @Autowired
    private AfterSaleOrderMapper afterSaleOrderMapper;

    @Value("${business.order.yxt.merCode:'1'}")
    private String yxtMerCode = "1";


    @Qualifier(ORDER_BUSINESS_ASYNC_THREAD_POOL)
    @Autowired
    private Executor asyncThreadPool;

    @Transactional(rollbackFor = Exception.class)
    public KcPosReturnOrderAccountingCallBackResponse afterReturnOrderAccountingSuccess(RefundOrderInfoAllDomain refundOrder, String refundPosNo) {


        OrderInfo orderInfo = orderBasicService.getOrderBaseWithCheck(refundOrder.getOrderNo());
        /*//兼容B2C
        if (DsConstants.B2C.equals(orderInfo.getServiceMode())) {
            return afterB2CReturnOrderAccountingSuccess(refundOrder,refundPosNo,orderInfo);
        }*/

        if (refundOrder.getErpState() < ErpStateEnum.HAS_SALE.getCode()) {
            //兼容同步下账逻辑

            RefundBillReqDto refundBillReqDto = new RefundBillReqDto();
            refundBillReqDto.setRefundBusinessFlag(1);
            refundBillReqDto.setRemark("");
            refundBillReqDto.setZeroEbaiCommission(1);
            refundBillReqDto.setRefundNo(refundOrder.getRefundNo());
            int businessFlag = refundBillReqDto.getRefundBusinessFlag();

            OrderPayInfo orderPayInfo = orderBasicService.getOrderPayInfoWithCheck(refundOrder.getOrderNo());

            List<OrderDetailDomain> orderDetailDomainList = orderDetailMapper.selectDetailDomainListAllByOrderNo(orderInfo.getOrderNo());
            validCommoditySwap(orderInfo, orderDetailDomainList);

            List<RefundDetail> refundDetailList = refundDetailMapper.selectListByRefundNo(refundOrder.getRefundNo());


            ErpRefundInfo erpRefundInfo = erpRefundInfoMapper.selectOne(
                    new QueryWrapper<ErpRefundInfo>().lambda().eq(ErpRefundInfo::getRefundNo, refundOrder.getRefundNo())
            );

            //查询ERP支付方式
            List<Long> ids = new ArrayList<>();
            ids.add(orderInfo.getClientConfId());
            List<ErpPayMode> erpPayModeList = erpPayModeService.getErpPayCodeByClientConfId(ids);

            //查询网店下账配置
            StoreBillConfig storeBillConfig = storeBillConfigService.getBillConfigById(orderInfo.getClientConfId());


            OrderRefundReq refundReq = erpHttpReqStructUtil.structuringOrderRefundReq(businessFlag, orderInfo, orderPayInfo, orderDetailDomainList, refundOrder, refundDetailList, erpRefundInfo, storeBillConfig, erpPayModeList);
            // 【ID1020096】仅退款场景：如果线上门店设置的为【商品报损处理】，则退款下账时，下传类型【3. 退款退货报损】
            resetRefundBuisiessFlag(businessFlag, storeBillConfig, refundReq);

            //解锁库存
            commodityStockService.unLockStockNew(orderInfo, null,"O2O退款单下账成功释放库存",UnLockStockTypeEnum.ALL);

//            billLogService.saveRefundBillLog(refundReq);
            HydeeEsSyncClientAsync.refundBillLogToES(DsConstants.INTEGER_ONE, refundReq, null, GlobalInterceptor.refundUserId.get());

            // 设置退款流水
            RefundOrder reUpdate = new RefundOrder().setBillTime(new Date())
                    .setRefundNo(refundOrder.getRefundNo())
                    .setErpRefundNo(refundPosNo)
                    .setErpState(RefundErpStateEnum.HAS_REFUND.getCode())
                    .setBillType(refundOrder.getBillType());

            refundOrderMapper.updateByRefundNo(reUpdate);
            // 退款单下账预统计处理
            orderRefundStatisticsService.calculateHasSaleRefundOrderRefundAmountByOrderNo(refundOrder.getOrderNo());
            refundOrder.setErpState(RefundErpStateEnum.HAS_REFUND.getCode());
            refundOrder.setErpRefundNo(refundPosNo);

            /**  退款单下账日志记录 **/
            refundBasicService.saveRefundLog(refundOrder, "系统", "退款单下账", refundOrder.getState(), refundOrder.getErpState());

            asyncThreadPool.execute(() -> {
                // 订单更新异步消费
                messageProducerService.produceUpdateOrderMessage(Collections.singletonList(refundOrder.getOrderNo()), OrderUpdateCodeEnum.REFUND_BILL.getCode());
                // 发送日志到Es
                HydeeEsSyncClientAsync.orderLogToES(DsConstants.INTEGER_ONE, yxtMerCode, orderInfo.getOrderNo(), orderInfo.getOrderState(), orderInfo.getErpState(), OrderLogEnum.REFUND_ENTER_ACCOUNT.getAction(), OrderLogEnum.getRefundEntelAccountInfo(refundOrder.getErpState()), null);
            });

//            orderCommodityCostPriceService.saveAndMove(orderCommodityCostPriceList, orderInfo.getOrderNo());
        }
        return KcPosReturnOrderAccountingCallBackResponse.success();
    }

    /**
     * B2C退款单下账成功处理
     * */
   /* public KcPosReturnOrderAccountingCallBackResponse  afterB2CReturnOrderAccountingSuccess(RefundOrderInfoAllDomain refundOrder, String refundPosNo, OrderInfo orderInfo){
        if (refundOrder.getErpState() < ErpStateEnum.HAS_SALE.getCode()) {
            //兼容同步下账逻辑

            RefundBillReqDto refundBillReqDto = new RefundBillReqDto();
            refundBillReqDto.setRefundBusinessFlag(1);
            refundBillReqDto.setRemark("");
            refundBillReqDto.setZeroEbaiCommission(1);
            refundBillReqDto.setRefundNo(refundOrder.getRefundNo());
            int businessFlag = refundBillReqDto.getRefundBusinessFlag();
            OrderPayInfoMapper orderPayInfoMapper = SpringUtil.getBean(OrderPayInfoMapper.class);
            OrderPayInfo orderPayInfo = orderPayInfoMapper.getOrderPayInfoForB2C(refundOrder.getOrderNo());

            List<OrderDetailDomain> orderDetailDomainList = orderDetailMapper.selectDetailDomainListAllByOrderNo(orderInfo.getOrderNo());
            validCommoditySwap(orderInfo, orderDetailDomainList);

            List<RefundDetail> refundDetailList = refundDetailMapper.selectListByRefundNo(refundOrder.getRefundNo());


            ErpRefundInfo erpRefundInfo = erpRefundInfoMapper.selectOne(
                    new QueryWrapper<ErpRefundInfo>().lambda().eq(ErpRefundInfo::getRefundNo, refundOrder.getRefundNo())
            );

            //查询ERP支付方式
            List<Long> ids = new ArrayList<>();
            ids.add(orderInfo.getClientConfId());
            List<ErpPayMode> erpPayModeList = erpPayModeService.getErpPayCodeByClientConfId(ids);

            //查询网店下账配置
            StoreBillConfig storeBillConfig = storeBillConfigService.getBillConfigById(orderInfo.getClientConfId());


            refundOrder.setType(RefundTypeEnum.PART.getCode());//兼容B2C最后一笔退款后类型改为全部退款问题

            OrderRefundReq refundReq = erpHttpReqStructUtil.structuringOrderRefundReq(businessFlag, orderInfo, orderPayInfo, orderDetailDomainList, refundOrder, refundDetailList, erpRefundInfo, storeBillConfig, erpPayModeList);
            // 【ID1020096】仅退款场景：如果线上门店设置的为【商品报损处理】，则退款下账时，下传类型【3. 退款退货报损】
            resetRefundBuisiessFlag(businessFlag, storeBillConfig, refundReq);

            //解锁库存
//            commodityStockService.unlockStock(orderInfo, null);
            commodityStockService.unLockStockNew(orderInfo, null,"B2C退款单下账成功释放库存",UnLockStockTypeEnum.ALL);

            billLogService.saveRefundBillLog(refundReq);
            HydeeEsSyncClientAsync.refundBillLogToES(DsConstants.INTEGER_ONE, refundReq, null, GlobalInterceptor.refundUserId.get());
            //查询B2C所有退款单
            *//*List<RefundOrder> refundOrders = refundOrderMapper.selectList(new LambdaQueryWrapper<RefundOrder>().eq(RefundOrder::getOrderNo, orderInfo.getOrderNo()));
            // 设置退款流水
            for (RefundOrder refundOrderUp : refundOrders) {
                if (!StringUtils.isEmpty(refundOrderUp.getErpRefundNo())) {
                    continue;
                }
                refundOrderUp.setErpState(RefundErpStateEnum.HAS_REFUND.getCode());
                refundOrderUp.setErpRefundNo(refundPosNo);
                refundOrderUp.setBillType(refundOrder.getBillType());
                refundOrderMapper.updateById(refundOrderUp);
            }*//*

            RefundOrder refundOrderUp = new RefundOrder();
            refundOrderUp.setErpState(RefundErpStateEnum.HAS_REFUND.getCode());
            refundOrderUp.setErpRefundNo(refundPosNo);
            refundOrderUp.setRefundNo(refundOrder.getRefundNo());
            refundOrderMapper.updateByRefundNo(refundOrderUp);

            //查询当前平台退款号下所有退款单 用于更新全部退款后的邮费单
            List<RefundOrder> refundOrders = refundOrderMapper.selectList(new LambdaQueryWrapper<RefundOrder>().eq(RefundOrder::getThirdRefundNo, refundOrder.getThirdRefundNo()));
            //B2C更新售后单
            AfterSaleOrder afterSaleOrder = new AfterSaleOrder();
            afterSaleOrder.setErpState(RefundErpStateEnum.HAS_REFUND.getCode());
            afterSaleOrder.setErpRefundNo(refundPosNo);
            afterSaleOrder.setBillTime(new Date());

            afterSaleOrderMapper.update(afterSaleOrder,
                    new LambdaQueryWrapper<AfterSaleOrder>().in(AfterSaleOrder::getRefundNo,refundOrders.stream().map(RefundOrder::getRefundNo).collect(Collectors.toList())));

            // 退款单下账预统计处理
            orderRefundStatisticsService.calculateHasSaleRefundOrderRefundAmountByOrderNo(refundOrder.getOrderNo());
            refundOrder.setErpState(RefundErpStateEnum.HAS_REFUND.getCode());
            refundOrder.setErpRefundNo(refundPosNo);

            *//**  退款单下账日志记录 **//*
            refundBasicService.saveRefundLog(refundOrder, "系统", "退款单下账", refundOrder.getState(), refundOrder.getErpState());
            //订单更新异步消费
            messageProducerService.produceUpdateOrderMessage(Collections.singletonList(refundOrder.getOrderNo()), OrderUpdateCodeEnum.REFUND_BILL.getCode());
            HydeeEsSyncClientAsync.orderLogToB2CES(DsConstants.INTEGER_ONE, yxtMerCode, afterSaleOrder.getAfterSaleNo(), orderInfo.getOrderState(), orderInfo.getErpState(), OrderLogEnum.REFUND_ENTER_ACCOUNT.getAction(), OrderLogEnum.getRefundEntelAccountInfo(refundOrder.getErpState()), null);

//            orderCommodityCostPriceService.saveAndMove(orderCommodityCostPriceList, orderInfo.getOrderNo());
        }
        return KcPosReturnOrderAccountingCallBackResponse.success();
    }
*/

    /**
     * 校验换货商品退款时拣货信息
     *
     * @param orderInfo
     * @param orderDetailDomainList
     */
    private void validCommoditySwap(OrderInfo orderInfo, List<OrderDetailDomain> orderDetailDomainList) {
        //复杂换货不匹配
        if (DsConstants.INTEGER_ONE.equals(orderInfo.getComplexModifyFlag())) {
            return;
        }
        //不存在换货并且非同商品替换时直接返回
        List<OrderDetailDomain> swapDetailist = orderDetailDomainList.stream()
                .filter(item -> item.getSwapId() != null && !item.getSwapId().equals(item.getId())).collect(Collectors.toList());
        if (CollectionUtils.isEmpty(swapDetailist)) {
            return;
        }
        Map<Long, OrderDetailDomain> swapDetailMap = swapDetailist.stream().collect(Collectors.toMap(OrderDetail::getSwapId, i -> i, (a, b) -> a));

        for (OrderDetailDomain orderDetailDomain : orderDetailDomainList) {
            //过滤非换货不同商品的明细
            if (!swapDetailMap.containsKey(orderDetailDomain.getId())) {
                continue;
            }
            List<OrderPickInfo> orderPickInfoList = orderDetailDomain.getOrderPickInfoList();
            if (!CollectionUtils.isEmpty(orderPickInfoList)) {
                orderDetailDomain.setOrderPickInfoList(orderPickInfoList);
            } else {
                OrderPickInfo orderPickInfo = new OrderPickInfo();
                orderPickInfo.setErpCode(orderDetailDomain.getErpCode());
                orderPickInfo.setOrderDetailId(orderDetailDomain.getId());
                orderPickInfo.setCommodityBatchNo(DsConstants.SYSTEM_DEFAULT_BATCH_NO);
                orderPickInfo.setCount(orderDetailDomain.getPickCount());
                orderPickInfo.setPurchasePrice(orderDetailDomain.getPrice());
                orderDetailDomain.setOrderPickInfoList(Collections.singletonList(orderPickInfo));
            }
            log.info("换货全自动下账后退款补充拣货明细数据,orderDetailDomain: {}", JSON.toJSONString(orderDetailDomain));
        }
    }


    private void resetRefundBuisiessFlag(Integer businessFlag, StoreBillConfig storeBillConfig, OrderRefundReq refundReq) {
        // 【ID1020096】仅退款场景：如果线上门店设置的为【商品报损处理】，则退款下账时，下传类型【3. 退款退货报损】
        if (!DsConstants.INTEGER_ONE.equals(businessFlag) ||
                !RefundBusinessFlagEnum.COMMODITY_FAULTY.getCode().equals(storeBillConfig.getRefundBusinessFlag())) {
            return;
        }
        businessFlag = DsConstants.INTEGER_THREE;
        refundReq.setBusinessFlag(businessFlag);
    }

    public List<OrderCommodityCostPrice> getOrderCommodityCostPriceList(RefundOrderInfoAllDomain refundOrder, List<KcPosReturnOrderDetail> OrderDetail) {
        List<OrderCommodityCostPrice> resultList = Lists.newArrayList();
//        Date modifyTime = new Date();
//        for (KcPosReturnOrderDetail kcPosOrderDetail : OrderDetail) {
//            for (KcPosBatchInfo batch : kcPosOrderDetail.getBATCH()) {
//                OrderCommodityCostPrice orderCommodityCostPrice = new OrderCommodityCostPrice();
//                orderCommodityCostPrice.setMerCode(yxtMerCode);
//                orderCommodityCostPrice.setThirdPlatformCode(refundOrder.getThirdPlatformCode());
//                orderCommodityCostPrice.setOrderNo(refundOrder.getRefundNo());
//                orderCommodityCostPrice.setOnlineStoreCode(refundOrder.getOnlineStoreCode());
//                orderCommodityCostPrice.setOnlineStoreName(refundOrder.getOnlineStoreName());
//                orderCommodityCostPrice.setOrganizationCode(refundOrder.getOrganizationCode());
//                orderCommodityCostPrice.setOrganizationName(refundOrder.getOrganizationName());
//                orderCommodityCostPrice.setErpSaleNo(refundOrder.getOrderNo().toString());//todo
//                orderCommodityCostPrice.setBillTime(refundOrder.getBillTime());
//                orderCommodityCostPrice.setErpCode(kcPosOrderDetail.getGOODSCODE());
//                orderCommodityCostPrice.setCommodityMakeNo(batch.getMAKENO());
//                orderCommodityCostPrice.setCommodityBatch(batch.getBATCHNO());
//                orderCommodityCostPrice.setCommodityCount(batch.getCOUNT().doubleValue());
//                orderCommodityCostPrice.setCommodityCostPrice(batch.getCOSTPRICE());
//                orderCommodityCostPrice.setCommodityCostTotal(batch.getCOSTPRICE().multiply(batch.getCOUNT()));
//                orderCommodityCostPrice.setCommodityBillTotal(batch.getCOSTPRICE().multiply(batch.getCOUNT()));
//                orderCommodityCostPrice.setCreateTime(modifyTime);
//                orderCommodityCostPrice.setModifyTime(modifyTime);
//                orderCommodityCostPrice.setChaiLingNum(0);
//
//                resultList.add(orderCommodityCostPrice);
//            }
//
//        }
//

        return resultList;
    }

}
