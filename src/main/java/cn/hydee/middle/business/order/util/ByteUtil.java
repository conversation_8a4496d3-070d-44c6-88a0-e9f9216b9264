package cn.hydee.middle.business.order.util;

import java.nio.ByteBuffer;

/**
 * <AUTHOR>
 * @since 2021/6/17 11:37
 */
public class ByteUtil {

    public static void main(String[] args){
        byte[] bytes = intToByte(67108872);
        for (byte b : bytes) {
            System.out.println(Integer.toBinaryString((b & 0xFF) + 0x100).substring(1));
        }
    }

    public static String getValueStr(int value){
        StringBuilder res = new StringBuilder();
        byte[] bytes = ByteBuffer.allocate(4).putInt(value).array();
        for (byte b : bytes) {
            res.append(Integer.toBinaryString((b & 0xFF) + 0x100).substring(1)).append("-");
        }
        return res.toString();
    }

    public static byte[] intToByte(int value){
        byte[] bytes = ByteBuffer.allocate(4).putInt(value).array();
        return bytes;
    }

    public static int byteToInt(byte[] bytes){
        int value = 0;
        for (int i = 0; i < 4; i++) {
            int shift = (4 - 1 - i) * 8;
            value += (bytes[i] & 0x000000FF) << shift;// 往高位游
        }
        return value;
    }

    public static void setBit(byte[] bytes, int offset, boolean flag) {
        int idx = bytes.length - 1 - offset / 8;
        byte seg = bytes[idx];
        int pos = offset % 8;
        if (flag) {
            bytes[idx] = (byte) ((1 << pos) | seg);
        } else {
            bytes[idx] = (byte) (~(1 << pos) & seg);
        }
    }

    public static boolean getBit(byte[] bytes, int offset) {
        int idx = bytes.length - 1 - offset / 8;
        byte seg = bytes[idx];
        int pos = offset % 8;
        if (pos == 0) {
            return (seg & 1) == 1;
        }
        return ((seg >>> pos) & 1) == 1;
    }
}
