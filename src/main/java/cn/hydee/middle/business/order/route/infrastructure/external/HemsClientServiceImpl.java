package cn.hydee.middle.business.order.route.infrastructure.external;

import cn.hutool.json.JSONUtil;
import cn.hydee.middle.business.order.configuration.ThirdPlatformMigrateConfig;
import cn.hydee.middle.business.order.domain.ResponseNet;
import cn.hydee.middle.business.order.route.domain.external.CreateRiderOrderResp;
import cn.hydee.middle.business.order.route.domain.external.HemsClientService;
import cn.hydee.middle.business.order.route.domain.external.ReqCreateRiderOrderDto;
import cn.hydee.middle.business.order.service.thirdplatform.ThirdPlatformService;
import cn.hydee.middle.business.order.service.thirdplatform.impl.ThirdPlatformServiceImpl;
import cn.hydee.middle.business.order.util.DateUtil;
import cn.hydee.middle.business.order.util.HttpClientManager;
import cn.hydee.middle.business.order.util.SignUtil;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.TypeReference;
import com.yxt.lang.dto.api.ResponseBase;
import com.yxt.thirdplatform.api.O2ORiderApi;
import com.yxt.thirdplatform.dto.response.PreAddRiderOrderResult;
import java.util.Date;
import java.util.HashMap;
import java.util.Map;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

/**
 * <AUTHOR>
 * @date 2024/03/29 15:30
 **/
@Slf4j
@Service
public class HemsClientServiceImpl implements HemsClientService {

    @Autowired
    private HttpClientManager httpClientManager;

    @Value("${hydee-platform.url.net}")
    private String url;
    @Autowired
    private ThirdPlatformMigrateConfig thirdPlatformMigrateConfig;
    @Autowired
    private ThirdPlatformService thirdPlatformService;
    @Override
    public ResponseNet<CreateRiderOrderResp> riderPreCompare(ReqCreateRiderOrderDto reqCreateRiderOrderDto, String merCode, String sessionKey, String platformCode, String clientCode) {
        if(thirdPlatformMigrateConfig.isMigrated(platformCode,reqCreateRiderOrderDto.order.getShopid())) {
            ResponseBase<PreAddRiderOrderResult> responseBase= thirdPlatformService.preOrder(merCode, platformCode, clientCode, reqCreateRiderOrderDto);
            if(responseBase.checkSuccess()) {
                ResponseNet<CreateRiderOrderResp> responseNet = new ResponseNet<>();
                responseNet.setCode(0);
                CreateRiderOrderResp createRiderOrderResp = new CreateRiderOrderResp();
                createRiderOrderResp.setDistance(responseBase.getData().getDeliveryDistance());
                createRiderOrderResp.setTotalPrice(responseBase.getData().getDeliveryFee());
                responseNet.setData(createRiderOrderResp);
                return responseNet;
            }else {
                return new ResponseNet<>( -1, responseBase.getMsg());
            }
        }
        String method = "hems.rider.order.add.pre.compare";
        Map<String, Object> map = this.constructSign(method, merCode, sessionKey, platformCode, clientCode, JSON.toJSONString(reqCreateRiderOrderDto));
        log.info("配送平台预发单,url:{},请求参数:{}", url,JSON.toJSONString(map));
        ResponseNet<CreateRiderOrderResp> createRiderOrderRespResponseNet = httpClientManager.requestNew(url, map,
            null, new TypeReference<ResponseNet<CreateRiderOrderResp>>() {
            });
        log.info("配送平台预发单,url返回结果:{}", JSON.toJSONString(createRiderOrderRespResponseNet));
        return createRiderOrderRespResponseNet;
    }

    private Map<String, Object> constructSign(String method, String merCode, String sessionKey, String platformCode, String clientCode, String body) {
        Date date = new Date();
        Map<String, Object> paramMap = new HashMap<>();
        paramMap.put("method", method);
        paramMap.put("groupid", merCode);
        paramMap.put("clientid", clientCode);
        paramMap.put("eccode", platformCode);
        paramMap.put("body", body);
        paramMap.put("timestamp", DateUtil.parseDateToStr(date, DateUtil.DATE_TIME_FORMAT_YYYY_MM_DD_HH_MI_SS));
        paramMap.put("v", "1.0");
        String sign = SignUtil.netSign(sessionKey, paramMap);
        paramMap.put("sign", sign);

        return paramMap;
    }
}
