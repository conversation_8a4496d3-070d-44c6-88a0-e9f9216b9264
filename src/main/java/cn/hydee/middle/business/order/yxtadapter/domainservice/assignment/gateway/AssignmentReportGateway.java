package cn.hydee.middle.business.order.yxtadapter.domainservice.assignment.gateway;

import cn.hydee.middle.business.order.yxtadapter.constant.AssignmentBizType;
import cn.hydee.middle.business.order.yxtadapter.domain.assignment.Assignment;
import cn.hydee.middle.business.order.yxtadapter.infrastructure.db.mysql.model.AssignmentEngineAssignment;

import java.util.List;

/**
 * Created by Intellij IDEA.
 *
 * <AUTHOR>
 * Date:  2023/8/31
 */
public interface AssignmentReportGateway {

    int save(Assignment assignment);


    void saveBatch(List<Assignment> assignmentList);
    int batchCreateAssignment(List<AssignmentEngineAssignment> list);


    AssignmentEngineAssignment selectAssignment(AssignmentBizType assignmentBizType, String businessId);

}
