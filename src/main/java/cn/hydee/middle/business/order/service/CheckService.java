package cn.hydee.middle.business.order.service;

import cn.hydee.middle.business.order.dto.req.*;

import java.util.List;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2020/4/13 17:13
 */
public interface CheckService {
    /** check ex flag*/
    void removeExFlag(OrderHandleReqDto orderHandleReqDto);

    /** 移除终态订单异常状态 */
    void removeExLock();

    /** 标记订单为门店异常 */
    void signOrgEx(OrderHandleReqDto orderHandleReqDto);

    /** 标记订单已完成 */
    void signOrderComplete(OrderStateSignReqDto orderHandleReqDto);

    /** 标记退款单已完成 */
    void signRefundComplete(RefundStateSignReqDto refundStateSignReqDto);

    /** 标记订单erp状态 */
    void signErpState(OrderErpStateSignReqDto orderHandleReqDto);

    /** 标记订单erp状态 */
    void signRefundErpState(RefundErpStateSignReqDto refundHandleReqDto);

    /** 员工关系变更 */
    void staffRelationChanged(List<String> userIds);
}
