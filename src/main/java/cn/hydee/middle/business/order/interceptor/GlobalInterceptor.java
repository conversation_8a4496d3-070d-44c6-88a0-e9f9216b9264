package cn.hydee.middle.business.order.interceptor;

import lombok.extern.slf4j.Slf4j;
import org.springframework.web.servlet.HandlerInterceptor;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.util.Objects;

@Slf4j
public  class  GlobalInterceptor implements HandlerInterceptor {

    public static ThreadLocal<Object> tObject = new ThreadLocal<>();
    public static ThreadLocal<String> refundUserId = new ThreadLocal<>();

    @Override
    public void afterCompletion(HttpServletRequest request, HttpServletResponse response, Object handler, Exception ex) throws Exception {
        if(Objects.nonNull(tObject.get())) {
            tObject.remove();
//            log.info("Interceptor tObject remove");
        }
        if (Objects.nonNull(refundUserId.get())) {
            refundUserId.remove();
        }
    }
}
