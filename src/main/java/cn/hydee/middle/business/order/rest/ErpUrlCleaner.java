package cn.hydee.middle.business.order.rest;

/***
 * sentinel资源名清洗url
 */
public class ErpUrlCleaner {

    public static  String clean(String originUrl) {
        if (originUrl == null || originUrl.isEmpty()) {
            return originUrl;
        }

        //不是域名情况下，不清理url
        if( !originUrl.contains("http")) {
            return  originUrl;
        }

        //查找出现次数，截取域名
        int count =0, start =0;
        while( ( start=originUrl.indexOf("/",start ) ) >=0 ){
            start += 1;
            count ++;

            if (count >= 3) break;
        }

        //为域名路径情况下，直接返回作为资源key
        if (count < 3) {
            return originUrl;
        }

        //把请求地址都归于域名下
        originUrl = originUrl.substring(0, start -1 );
        //截取掉http method
        originUrl = originUrl.substring(originUrl.indexOf(":") + 1);

        return originUrl;
    }

}


