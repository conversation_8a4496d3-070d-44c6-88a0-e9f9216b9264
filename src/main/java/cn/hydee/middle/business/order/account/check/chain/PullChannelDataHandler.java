
package cn.hydee.middle.business.order.account.check.chain;

import java.util.ArrayList;
import java.util.List;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import cn.hydee.middle.business.order.account.check.base.dto.req.PullDataReqDto;
import cn.hydee.middle.business.order.account.check.base.enums.PullDataPlatformTypeEnum;
import cn.hydee.middle.business.order.account.check.batch.list.BatchListHandler;
import cn.hydee.middle.business.order.account.check.context.AccountCheckContext;
import cn.hydee.middle.business.order.account.check.factory.ChannelDataPullFactory;
import cn.hydee.middle.business.order.entity.AccountCheckChannel;
import cn.hydee.unified.model.store.ShopBalanceBillEBReq;
import cn.hydee.unified.model.store.ShopBalanceBillJDReq;
import cn.hydee.unified.model.store.ShopBalanceBillMTReq;
import io.swagger.annotations.Scope;
import lombok.extern.slf4j.Slf4j;

/**  
 * 
 * Date:     2020年11月5日 下午4:32:05 <br/>  
 * <AUTHOR>  
 */
@Component
@Scope(name = "prototype", description = "prototype")
@Slf4j
public class PullChannelDataHandler extends AbstractAccountCheckChain {
	
	@Autowired
	private ChannelDataPullFactory pullFactory;
	
	@Override
	public void handleChain(AccountCheckContext context) {
		log.info("自动对账[{}]===> PullChannelDataHandler start",context.getContextId());
		List<AccountCheckChannel> pullData = new ArrayList<>();
		new BatchListHandler<PullDataReqDto>().batchResolve(context.getPullDataDtoList(), 100, (items) ->{
			items.forEach(pullDataDto -> {{
				ShopBalanceBillEBReq reqEB = pullDataDto.getReqEB();
				ShopBalanceBillMTReq reqMT = pullDataDto.getReqMT();
				ShopBalanceBillJDReq reqJD = pullDataDto.getReqJD();
				// 拉取饿百数据
				if(null != reqEB) {
					pullData.addAll(pullFactory.pullData(PullDataPlatformTypeEnum.EB, pullDataDto,context.getContextId()));
				}
				// 拉取美团数据
				if(null != reqMT) {
					pullData.addAll(pullFactory.pullData(PullDataPlatformTypeEnum.MT, pullDataDto,context.getContextId()));
				}
				// 拉取京东数据
				if(null != reqJD) {
					pullData.addAll(pullFactory.pullData(PullDataPlatformTypeEnum.JD, pullDataDto,context.getContextId()));
				}
			}});
		});
		log.info("自动对账[{}]===> PullChannelDataHandler processing,total pull size: [{}]",context.getContextId(),pullData.size());
		pullFactory.saveData(pullData); 
		log.info("自动对账[{}]===> PullChannelDataHandler end",context.getContextId());
		
		super.handleNext(context);
	}

}
  
