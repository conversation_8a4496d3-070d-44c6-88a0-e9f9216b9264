package cn.hydee.middle.business.order.entity.dto;

import io.swagger.annotations.ApiModelProperty;
import java.math.BigDecimal;
import lombok.Data;

/**
 * <AUTHOR> (moatkon)
 * @date 2024年07月25日 17:14
 * @email: <EMAIL>
 */
@Data
public class PaySaleInfo {

  @ApiModelProperty(value = "券名称")
  private String cName;
  @ApiModelProperty(value = "优惠券核销金额")
  private BigDecimal saleAmount;
  @ApiModelProperty("成本中心(code)")
  private String costCenterCode;
  @ApiModelProperty("成本中心(名称)")
  private String costCenterName;
}

