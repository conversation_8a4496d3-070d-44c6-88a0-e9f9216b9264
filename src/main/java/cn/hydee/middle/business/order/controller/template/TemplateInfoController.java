package cn.hydee.middle.business.order.controller.template;


import cn.hydee.middle.business.order.Enums.template.TemplateTypeEnum;
import cn.hydee.middle.business.order.client.ws.dto.MerchantPrintTemplateDTO;
import cn.hydee.middle.business.order.dto.req.template.*;
import cn.hydee.middle.business.order.dto.rsp.template.QueryTemplateInfoRspDto;
import cn.hydee.middle.business.order.dto.rsp.template.QueryTemplateRspDto;
import cn.hydee.middle.business.order.dto.template.PrintPropertyDto;
import cn.hydee.middle.business.order.entity.TemplateInfo;
import cn.hydee.middle.business.order.service.TemplateInfoService;
import cn.hydee.starter.controller.AbstractController;
import cn.hydee.starter.dto.ResponseBase;
import com.baomidou.mybatisplus.core.metadata.IPage;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.BindingResult;
import org.springframework.web.bind.annotation.*;

import javax.validation.Valid;
import java.util.Arrays;
import java.util.List;

/**
 * <p>
 * 模板数据表 前端控制器
 * </p>
 *
 * <AUTHOR>
 * @since 2021-01-13
 */
@RestController
@RequestMapping("/${api.version}/ds/templateInfo")
@Api(tags = "模板数据相关接口")
public class TemplateInfoController extends AbstractController {
	
	@Autowired
	private TemplateInfoService templateInfoService;
	
	@ApiOperation(value = "分页获取模板数据(快捷回复模板)", notes = "分页获取模板数据(快捷回复模板)")
    @PostMapping("/page/queryTemplate")
    public ResponseBase<IPage<QueryTemplateRspDto>> queryTemplate(
    		@RequestHeader("userId") String userId,
            @RequestHeader("merCode") String merCode,
            @Valid @RequestBody QueryTemplateReqDto query,
            BindingResult result){
		checkValid(result);
		query.setMerCode(merCode);
		if(TemplateTypeEnum.ALL.getCode().equals(query.getTemplateType())){
			query.setTemplateTypeList(Arrays.asList(TemplateTypeEnum.REPLY_COMMENT.getCode(),TemplateTypeEnum.REPLY_REFUND.getCode()));
		}else{
			query.setTemplateTypeList(Arrays.asList(query.getTemplateType()));
		}
        return generateSuccess(templateInfoService.queryTemplate(query));
    }

	@ApiOperation(value = "分页获取模板数据(打印模板)", notes = "分页获取模板数据(打印模板)")
	@PostMapping("/page/queryTemplatePrint")
	public ResponseBase<IPage<QueryTemplateRspDto>> queryTemplatePrint(
			@RequestHeader("userId") String userId,
			@RequestHeader("merCode") String merCode,
			@Valid @RequestBody QueryTemplateReqDto query,
			BindingResult result){
		checkValid(result);
		query.setMerCode(merCode);
		query.setTemplateTypeList(Arrays.asList(TemplateTypeEnum.PRINT.getCode()));
		return generateSuccess(templateInfoService.queryTemplate(query));
	}
	
	@ApiOperation(value = "增加模板", notes = "注意：不过滤空格")
    @PostMapping("/addTemplateInfo")
    public ResponseBase<String> addTemplateInfo(
    		@RequestHeader("userId") String userId,
            @RequestHeader("merCode") String merCode,
            @Valid @RequestBody AddTemplateReqDto addInfo,
            BindingResult result){
		checkValid(result);
		addInfo.setMerCode(merCode);
		templateInfoService.addTemplateInfo(addInfo,userId);
        return generateSuccess(null);
    }
	
	@ApiOperation(value = "更新模板", notes = "注意：不过滤空格")
    @PostMapping("/updateTemplateInfo")
    public ResponseBase<String> updateTemplateInfo(
    		@RequestHeader("userId") String userId,
            @RequestHeader("merCode") String merCode,
            @Valid @RequestBody UpdateTemplateReqDto updateInfo,
            BindingResult result){
		checkValid(result);
		updateInfo.setMerCode(merCode);
		templateInfoService.updateTemplateInfo(updateInfo,userId);
        return generateSuccess(null);
    }
	
	@ApiOperation(value = "查询模板详情数据", notes = "查询模板详情数据")
    @PostMapping("/queryDetail")
    public ResponseBase<TemplateInfo> queryDetail(
    		@RequestHeader("userId") String userId,
            @RequestHeader("merCode") String merCode,
            @Valid @RequestBody QueryTemplateDetailReqDto query,
            BindingResult result){
		checkValid(result);
		query.setMerCode(merCode);
        return generateSuccess(templateInfoService.queryDetail(query));
    }
	
	@ApiOperation(value = "删除模板数据", notes = "删除模板数据")
    @PostMapping("/deleteTemplateInfo")
    public ResponseBase<String> deleteTemplateInfo(
    		@RequestHeader("userId") String userId,
            @RequestHeader("merCode") String merCode,
            @Valid @RequestBody DeleteTemplateReqDto deleteInfo,
            BindingResult result){
		checkValid(result);
		deleteInfo.setMerCode(merCode);
		templateInfoService.deleteTemplateInfo(deleteInfo);
        return generateSuccess(null);
    }
	
	@ApiOperation(value = "根据模板类型查询所有模板数据", notes = "查不到时返回空集合")
    @PostMapping("/selectByType")
    public ResponseBase<List<QueryTemplateInfoRspDto>> selectByType(
    		@RequestHeader("userId") String userId,
            @RequestHeader("merCode") String merCode,
            @Valid @RequestBody QueryTemplateByTypeReqDto query,
            BindingResult result){
		checkValid(result);
		query.setMerCode(merCode);
        return generateSuccess(templateInfoService.selectByType(query));
    }

	@ApiOperation(value = "获取打印系统默认模板", notes = "获取打印系统默认模板")
	@PostMapping("/getDefaultPrintProperty")
	public ResponseBase<PrintPropertyDto> getDefaultPrintProperty(
			@RequestHeader("userId") String userId,
			@RequestHeader("merCode") String merCode){
		return generateSuccess(templateInfoService.getDefaultPrintProperty());
	}

	@ApiOperation(value = "更新系统模板", notes = "更新系统模板")
	@PostMapping("/defaultTemplateUpdate")
	public ResponseBase<String> defaultTemplateUpdate(
			@RequestHeader("userId") String userId,
			@RequestHeader("merCode") String merCode){
		templateInfoService.defaultTemplateUpdate();
		return generateSuccess(null);
	}

	@ApiOperation(value = "获取某商户旗下所有打印模板（用作下拉框）", notes = "获取某商户旗下所有打印模板（用作下拉框）")
	@PostMapping("/merchantAllPrintTemplate")
	public ResponseBase<List<MerchantPrintTemplateDTO>> merchantAllPrintTemplate(
			@RequestHeader("userId") String userId,
			@RequestHeader("merCode") String merCode,
			@RequestParam("organizationCode") String organizationCode){
		return generateSuccess(templateInfoService.merchantAllPrintTemplate(merCode,organizationCode));
	}
}

