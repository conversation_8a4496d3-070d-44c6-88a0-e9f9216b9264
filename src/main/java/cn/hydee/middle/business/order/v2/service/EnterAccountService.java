/*  
 * Project Name:hydee-business-order  
 * File Name:EnterAccountService.java  
 * Package Name:cn.hydee.middle.business.order.v2.service  
 * Date:2020年8月24日下午3:44:25  
 * Copyright (c) 2020, Shanghai Hydee Software Corp., Ltd. All Rights Reserved.  
 *  
*/  
  
package cn.hydee.middle.business.order.v2.service;

import cn.hydee.middle.business.order.dto.req.LockInventoryReqDto;
import cn.hydee.middle.business.order.dto.req.OrderHandlePickConfirmReqDto;
import cn.hydee.middle.business.order.dto.req.OrderHandleReqDto;

/**  
 * ClassName:EnterAccountService <br/>  
 * Function: 下账相关业务处理服务. <br/> 
 * Date:     2020年8月24日 下午3:44:25 <br/>  
 * <AUTHOR>  
 */
public interface EnterAccountService {

    /**
     * 
     * enterAccountSaleBill:销售单下账（不带拣货）. <br/>  
     *  
     * @param userId 用户id
     * @param merCode 商户编码
     * @param req {@link OrderHandlePickConfirmReqDto}
     * @param orderNo 订单号（redis锁）  
     * <AUTHOR>  
     * @date 2020年8月24日 上午11:43:36
     */
    void enterAccountSaleBill(String userId, String merCode, OrderHandlePickConfirmReqDto req,String orderNo);
    
	/**
	 * 
	 * receiveLockInventory:下账锁库存，无adjustno,则调用接单接口锁库存. <br/>  
	 * 
	 * @param req {@link OrderHandleReqDto}
	 * @param orderNo 订单号（redis锁）
	 * @return  {@link LockInventoryReqDto}
	 * <AUTHOR>  
	 * @date 2020年8月24日 下午2:59:58
	 */
	LockInventoryReqDto receiveLockInventory(OrderHandleReqDto req,String orderNo,String userId);

	/**
	 * unlockInventory: 直接解锁库存
	 * @Author: chufeng(2910)
	 * @Date: 2021/4/27 16:05
	 */
	Void unlockInventory(OrderHandleReqDto req, String orderNo);
}
  
