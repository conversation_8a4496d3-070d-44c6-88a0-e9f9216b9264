
package cn.hydee.middle.business.order.point.dto.rsp;

import java.util.List;

import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**  
 * <AUTHOR> 
 * @date: 2020年12月25日     
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class QueryOnlineStoreAndOrderRspDto {

	@ApiModelProperty(value="线上门店数量（总和）")
	private int onlineStoreCount;
	
	@ApiModelProperty(value="订单数量（总和）")
	private int orderCount;
	
	@ApiModelProperty(value="各门店明细统计")
	private List<OnlineStoreOrderCountDto> dataList;
}
  
