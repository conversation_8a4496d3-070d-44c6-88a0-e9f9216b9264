package cn.hydee.middle.business.order.refund.creation.strategy;

import cn.hydee.middle.business.order.Enums.PlatformCodeEnum;
import cn.hydee.middle.business.order.dto.message.NetRefundNotifyMessage;
import cn.hydee.middle.business.order.refund.context.RefundOrderContext;

/**
 * 饿百创建退款单工厂策略
 *
 * <AUTHOR>
 * @version 2.9.2.2
 * @date 2020/09/15
 */
public class EbaiRefundCreateFactoryStrategy extends AbstractPlatRefundCreateFactoryStrategy {

    public EbaiRefundCreateFactoryStrategy() {
        super(PlatformCodeEnum.E_BAI.getCode());
    }

    @Override
    public RefundOrderContext createPartRefund(NetRefundNotifyMessage message) {
        return super.createPartRefund(message);
    }

    @Override
    public RefundOrderContext createAllRefund(NetRefundNotifyMessage message) {
        return super.createAllRefund(message);
    }

}
