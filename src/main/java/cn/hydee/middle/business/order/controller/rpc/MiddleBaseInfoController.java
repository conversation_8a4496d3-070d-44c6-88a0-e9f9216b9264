package cn.hydee.middle.business.order.controller.rpc;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hydee.middle.business.order.Enums.DeliveryPlatformEnum;
import cn.hydee.middle.business.order.Enums.DeliveryTypeEnum;
import cn.hydee.middle.business.order.Enums.DsConstants;
import cn.hydee.middle.business.order.Enums.DsErrorType;
import cn.hydee.middle.business.order.Enums.PlatformCodeEnum;
import cn.hydee.middle.business.order.dto.StoreQueryBase;
import cn.hydee.middle.business.order.dto.gy.GyInfoDTO;
import cn.hydee.middle.business.order.dto.req.AccountLoginDTO;
import cn.hydee.middle.business.order.dto.req.DsOnlineClientReqDTO;
import cn.hydee.middle.business.order.dto.req.OnlineStoreByOrDto;
import cn.hydee.middle.business.order.dto.req.StoreConfigReqDTO;
import cn.hydee.middle.business.order.dto.req.baseinfo.DefaultServiceCodeReqDTO;
import cn.hydee.middle.business.order.dto.req.baseinfo.DsBindDeliveryStoreQueryDTO;
import cn.hydee.middle.business.order.dto.req.baseinfo.DsDeliveryClientCreateUpdateDTO;
import cn.hydee.middle.business.order.dto.req.baseinfo.DsDeliveryClientQueryDTO;
import cn.hydee.middle.business.order.dto.req.baseinfo.DsDeliveryPlatformReqDTO;
import cn.hydee.middle.business.order.dto.req.baseinfo.DsOnlineClientCreateUpdateDTO;
import cn.hydee.middle.business.order.dto.req.baseinfo.DsOnlineClientQueryDTO;
import cn.hydee.middle.business.order.dto.req.baseinfo.DsOnlineStoreConfCreateUpdateDTO;
import cn.hydee.middle.business.order.dto.req.baseinfo.DsOnlineStoreConfReqDTO;
import cn.hydee.middle.business.order.dto.req.baseinfo.DsOnlineStorePageReqDto;
import cn.hydee.middle.business.order.dto.req.baseinfo.DsOnlineStoreQueryDTO;
import cn.hydee.middle.business.order.dto.req.baseinfo.DsOnlineStoreSetStatusDTO;
import cn.hydee.middle.business.order.dto.req.baseinfo.DsOnlineStoreTreeReqDto;
import cn.hydee.middle.business.order.dto.req.baseinfo.DsSelfDeliveryQueryDTO;
import cn.hydee.middle.business.order.dto.req.baseinfo.DsStoreDeliveryCreateUpdateDTO;
import cn.hydee.middle.business.order.dto.req.baseinfo.DsStoreOpenB2CReqDTO;
import cn.hydee.middle.business.order.dto.req.baseinfo.DsStoreOrderConfCreateUpdateDTO;
import cn.hydee.middle.business.order.dto.req.baseinfo.DsStoreOrderConfReqDTO;
import cn.hydee.middle.business.order.dto.req.baseinfo.DsStoreSoundConfCreateUpdateDTO;
import cn.hydee.middle.business.order.dto.req.baseinfo.DsStoreSoundConfQueryDTO;
import cn.hydee.middle.business.order.dto.req.baseinfo.DsSynDeliveryStoreQueryDTO;
import cn.hydee.middle.business.order.dto.req.baseinfo.DsSynOnlineStoreQueryDTO;
import cn.hydee.middle.business.order.dto.req.baseinfo.GetOnlineStoreIdReqDto;
import cn.hydee.middle.business.order.dto.req.baseinfo.GetOnlineStoreListByPlatReqDto;
import cn.hydee.middle.business.order.dto.req.baseinfo.MyStoreDTO;
import cn.hydee.middle.business.order.dto.req.baseinfo.OnlineStoreCodeDto;
import cn.hydee.middle.business.order.dto.req.baseinfo.QueryEmpDTO;
import cn.hydee.middle.business.order.dto.req.baseinfo.QueryStoreDTO;
import cn.hydee.middle.business.order.dto.req.baseinfo.StoreCreateUpdateDTO;
import cn.hydee.middle.business.order.dto.rsp.BaseInfoOrgTreeResDto;
import cn.hydee.middle.business.order.dto.rsp.DeliveryTypeRspDto;
import cn.hydee.middle.business.order.dto.rsp.HuditConfigRestDto;
import cn.hydee.middle.business.order.dto.rsp.UserInfoDTO;
import cn.hydee.middle.business.order.dto.rsp.baseinfo.DsDeliveryAccountResDTO;
import cn.hydee.middle.business.order.dto.rsp.baseinfo.DsDeliveryStoreResDTO;
import cn.hydee.middle.business.order.dto.rsp.baseinfo.DsGetPlatformClientStoreResDTO;
import cn.hydee.middle.business.order.dto.rsp.baseinfo.DsOnlineAndOfflineStoreResDTO;
import cn.hydee.middle.business.order.dto.rsp.baseinfo.DsOnlineClientResDTO;
import cn.hydee.middle.business.order.dto.rsp.baseinfo.DsOnlineStoreConfResDTO;
import cn.hydee.middle.business.order.dto.rsp.baseinfo.DsOnlineStoreResDTO;
import cn.hydee.middle.business.order.dto.rsp.baseinfo.DsOnlineStoreTreeResDto;
import cn.hydee.middle.business.order.dto.rsp.baseinfo.DsStoreConfInfoResDTO;
import cn.hydee.middle.business.order.dto.rsp.baseinfo.DsStoreDeliveryResDTO;
import cn.hydee.middle.business.order.dto.rsp.baseinfo.DsStoreOrderConfResDTO;
import cn.hydee.middle.business.order.dto.rsp.baseinfo.DsStoreSoundConfResDTO;
import cn.hydee.middle.business.order.dto.rsp.baseinfo.EmployeeResDTO;
import cn.hydee.middle.business.order.dto.rsp.baseinfo.GetOnlineStoreAllResDto;
import cn.hydee.middle.business.order.dto.rsp.baseinfo.GetOnlineStoreByPlatResDto;
import cn.hydee.middle.business.order.dto.rsp.baseinfo.GetOnlineStoreIdResDto;
import cn.hydee.middle.business.order.dto.rsp.baseinfo.GetStoreByUserIdResDto;
import cn.hydee.middle.business.order.dto.rsp.baseinfo.NewStoreResDTO;
import cn.hydee.middle.business.order.dto.rsp.baseinfo.PlatformResDTO;
import cn.hydee.middle.business.order.dto.rsp.baseinfo.StoreDeliveryResDTO;
import cn.hydee.middle.business.order.dto.rsp.baseinfo.StoreResDTO;
import cn.hydee.middle.business.order.dto.rsp.baseinfo.SysEmployeeResDTO;
import cn.hydee.middle.business.order.dto.rsp.baseinfo.The3DsOnlineClientResDTO;
import cn.hydee.middle.business.order.entity.DsMerchantGroupInfo;
import cn.hydee.middle.business.order.entity.DsOnlineClient;
import cn.hydee.middle.business.order.entity.DsOnlineStore;
import cn.hydee.middle.business.order.entity.OrderDeliveryRecord;
import cn.hydee.middle.business.order.entity.OrderInfo;
import cn.hydee.middle.business.order.eo.DsSyncDeliveryStoreEO;
import cn.hydee.middle.business.order.eo.DsSyncSafeCenterStoreEO;
import cn.hydee.middle.business.order.eo.SynSFDeliveryStoreEO;
import cn.hydee.middle.business.order.exception.OmsException;
import cn.hydee.middle.business.order.feign.MiddleBaseInfoClient;
import cn.hydee.middle.business.order.service.O2OB2CCommonService;
import cn.hydee.middle.business.order.service.OrderDeliveryRecordService;
import cn.hydee.middle.business.order.service.auto.DsOnlineStoreServiceAuto;
import cn.hydee.middle.business.order.service.baseinfo.DsDeliveryClientService;
import cn.hydee.middle.business.order.service.baseinfo.DsDeliveryStoreService;
import cn.hydee.middle.business.order.service.baseinfo.DsMerchantGroupInfoService;
import cn.hydee.middle.business.order.service.baseinfo.DsOnlineClientService;
import cn.hydee.middle.business.order.service.baseinfo.DsOnlineStoreConfigService;
import cn.hydee.middle.business.order.service.baseinfo.DsOnlineStoreDeliveryService;
import cn.hydee.middle.business.order.service.baseinfo.DsOnlineStoreService;
import cn.hydee.middle.business.order.service.baseinfo.DsOnlineStoreTreeService;
import cn.hydee.middle.business.order.service.baseinfo.DsStoreOrderConfigService;
import cn.hydee.middle.business.order.service.baseinfo.DsStoreSoundConfigService;
import cn.hydee.middle.business.order.service.cache.BaseInfoCache;
import cn.hydee.middle.business.order.util.ExcelUtil;
import cn.hydee.middle.business.order.util.ExceptionUtil;
import cn.hydee.middle.business.order.v2.manager.OrderUpdateStoreDeliveryMatchManager;
import cn.hydee.middle.business.order.v2.manager.base.OrderBasicManager;
import cn.hydee.middle.business.order.yxtadapter.domainservice.mdm.MiddleBaseInfoClientAdapter;
import cn.hydee.starter.controller.AbstractController;
import cn.hydee.starter.dto.PageDTO;
import cn.hydee.starter.dto.ResponseBase;
import com.alibaba.fastjson.JSONObject;
import com.yxt.lang.util.JsonUtils;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import java.io.IOException;
import java.util.ArrayList;
import java.util.Collections;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;
import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import javax.validation.Valid;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Lazy;
import org.springframework.util.CollectionUtils;
import org.springframework.util.StringUtils;
import org.springframework.validation.BindingResult;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestHeader;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.ResponseBody;
import org.springframework.web.bind.annotation.RestController;
import org.springframework.web.multipart.MultipartFile;

@RestController
@RequestMapping(value = "/${api.version}/ds/baseinfo")
@Api(tags = "商户中台接口")
@Slf4j
public class
MiddleBaseInfoController extends AbstractController {

    @Value("${queryDsOnlineStoreOnOff:true}")
    private Boolean queryDsOnlineStoreOnOff;

    @Resource
	private MiddleBaseInfoClient middleBaseInfoClient;
    @Resource
    private MiddleBaseInfoClientAdapter middleBaseInfoClientAdapter;
	@Resource
    private BaseInfoCache baseInfoCache;
    @Autowired
    private DsOnlineClientService dsOnlineShopService;

    @Autowired
    private DsOnlineStoreService dsOnlineStoreService;

    @Autowired
    private DsMerchantGroupInfoService dsMerchantGroupInfoService;

    @Autowired
    private DsOnlineStoreConfigService dsOnlineStoreConfigService;

    @Autowired
    private DsStoreOrderConfigService dsStoreOrderConfigService;

    @Autowired
    private DsStoreSoundConfigService dsStoreSoundConfigService;

    @Autowired
    private DsOnlineStoreDeliveryService dsOnlineStoreDeliveryService;

    @Autowired
    private DsDeliveryClientService dsDeliveryClientService;

    @Autowired
    private DsDeliveryStoreService deliveryStoreService;

    @Autowired
    private DsOnlineClientService onlineClientService;

    @Autowired
    private DsOnlineStoreServiceAuto dsOnlineStoreServiceAuto;

    @Autowired
    private O2OB2CCommonService o2OB2CCommonService;
    @Autowired
    private DsOnlineStoreTreeService storeTreeService;
    @Autowired
    @Lazy
    private OrderBasicManager orderBasicManager;
    @Autowired
    private OrderUpdateStoreDeliveryMatchManager updateStoreDeliveryMatchManager;

    @Autowired
    private OrderDeliveryRecordService orderDeliveryRecordService;

	@ApiOperation(value = "获取erp统一接口Url", notes = "获取erp统一接口Url")
    @GetMapping("/{merCode}/{type}")
    public ResponseBase<HuditConfigRestDto> getHuditConfigByMerCode(@PathVariable String merCode,
                                                                    @PathVariable String type) {
        return middleBaseInfoClient.getHuditConfigByMerCode(merCode,type);
    }

	@ApiOperation(value = "根据用户ID查询员工数据", notes = "根据用户ID查询员工数据")
    @GetMapping("/getEmployeeByUserId/{userId}")
    public ResponseBase<SysEmployeeResDTO> getEmployeeByUserId(@PathVariable String userId) {
        return middleBaseInfoClientAdapter.getEmployeeByUserId(userId);
    }


	@ApiOperation(value = "获取所有线上门店及其配置", notes = "获取所有线上门店及其配置")
    @GetMapping("/getOnlineStoreAll")
    public ResponseBase<List<GetOnlineStoreAllResDto>> getOnlineStoreAll(){
        return generateSuccess(dsOnlineStoreService.getOnlineStoreAllInfo());
    }

	@ApiOperation(value = "根据线下门店id获取线上门店的id", notes = "根据线下门店id获取线上门店的id")
    @PostMapping("/getOnlineStoreId")
    public ResponseBase<List<GetOnlineStoreIdResDto>> getOnlineStoreId(@Valid @RequestBody GetOnlineStoreIdReqDto dto, BindingResult result) {
        this.checkValid(result);
        return generateSuccess(dsOnlineStoreService.queryOnlineStoreBySysId(dto));
    }

	@ApiOperation(value = "用户登录", notes = "用户登录")
    @PostMapping("/acc/_login")
    public ResponseBase<SysEmployeeResDTO> login(@RequestBody AccountLoginDTO accountLoginDTO) {
        List<Integer> userType = new ArrayList<>();
        userType.add(3);
        userType.add(5);
        accountLoginDTO.setUserType(userType);
        ResponseBase<UserInfoDTO> baseUser = middleBaseInfoClientAdapter.login(accountLoginDTO);
        if (!baseUser.checkSuccess() || baseUser.getData() == null){
            throw ExceptionUtil.getWarnException(baseUser.getCode(), baseUser.getMsg());
        }
        UserInfoDTO userInfoDTO = baseUser.getData();
        return middleBaseInfoClientAdapter.getEmployeeByUserId(userInfoDTO.getUserId());
    }

	// 已废弃
	@ApiOperation(value = "根据用户ID获取默认的线下门店数据以及所有的线上门店配置", notes = "根据用户ID获取默认的线下门店数据以及所有的线上门店配置")
    @GetMapping("/getOnlineStoreConfByUserId/{userId}")
    public ResponseBase<DsOnlineAndOfflineStoreResDTO> getOnlineStoreConfByUserId(@PathVariable String userId){
        return generateSuccess(dsOnlineStoreService.getOnlineStoreConfByUserId(userId));
    }

	@ApiOperation(value = "用于切换线下门店获取线上门店配置", notes = "用于切换线下门店获取线上门店配置")
    @GetMapping("/getOnlineStoreInfoByStCode/{stCode}")
    public ResponseBase<DsOnlineAndOfflineStoreResDTO> getOnlineStoreInfoByStCode(@PathVariable String stCode, @RequestParam(value = "platformCode", required = false) String platformCode, @RequestHeader("merCode") String merCode) throws InstantiationException, IllegalAccessException {
        return generateSuccess(dsOnlineStoreService.getOnlineStoreInfoByStCode(stCode,merCode,platformCode));
    }


    @ApiOperation(value = "用于切换线下门店获取线上门店配置", notes = "用于切换线下门店获取线上门店配置")
    @GetMapping("/getOnlineStoreInfo2ByStCode/{stCode}")
    public ResponseBase<DsOnlineAndOfflineStoreResDTO> getOnlineStoreInfo2ByStCode(@PathVariable String stCode, @RequestParam(value = "platformCode", required = false) String platformCode, @RequestHeader("merCode") String merCode) throws InstantiationException, IllegalAccessException {
        return generateSuccess(dsOnlineStoreService.getOnlineStoreInfo2ByStCode(stCode,merCode,platformCode));
    }

	@ApiOperation(value = "根据商户编码以及平台编码查询平台对应的网店以及所有的线上门店", notes = "根据商户编码以及平台编码查询平台对应的网店以及所有的线上门店")
    @GetMapping("/getPlatformClientStore/{merCode}/{platformCode}")
    public ResponseBase<List<DsGetPlatformClientStoreResDTO>> getPlatformClientStore(@PathVariable String merCode, @PathVariable String platformCode){
        return generateSuccess(onlineClientService.getPlatformAndClientAndStore(merCode,platformCode));
    }

	@ApiOperation(value = "根据商户编码以及平台编码查询平台对应的网店以及有权限的线上门店", notes = "根据商户编码以及平台编码查询平台对应的网店以及有权限的线上门店")
    @GetMapping("/getPlatformClientAuthorityStore/{merCode}/{platformCode}")
    public ResponseBase<List<DsGetPlatformClientStoreResDTO>> getPlatformClientAuthorityStore(@RequestHeader("userId") String userId,
                                                                                              @PathVariable String merCode,
                                                                                              @PathVariable String platformCode,
                                                                                              @RequestParam(required = false,value = "serviceMode") String serviceMode,
                                                                                              @RequestParam(required = false) boolean config){
        return generateSuccess(onlineClientService.getPlatformAndClientAndAuthorityStore(userId,merCode,platformCode,config,serviceMode));
    }

    @ApiOperation(value = "根据商户编码以及平台编码查询平台对应的网店以及有权限的线上门店", notes = "根据商户编码以及平台编码查询平台对应的网店以及有权限的线上门店")
    @PostMapping("/getPlatformClientAuthorityStoreWithOrg/{merCode}/{platformCode}")
    public ResponseBase<List<DsGetPlatformClientStoreResDTO>> getPlatformClientAuthorityStoreByStore(@RequestHeader("userId") String userId,
                                                                                              @PathVariable String merCode,
                                                                                              @PathVariable String platformCode,
                                                                                              @RequestParam(required = false) boolean config,
                                                                                                     @RequestBody OnlineStoreByOrDto reqDto){
        reqDto.setMerCode(merCode);
        reqDto.setPlatformCode(platformCode);
        return generateSuccess(onlineClientService.getPlatformClientAuthorityStoreByOrg(userId,reqDto));
    }


    @ApiOperation(value = "根据商户编码以及平台编码查询平台对应的网店以及有权限的线上门店", notes = "根据商户编码以及平台编码查询平台对应的网店以及有权限的线上门店")
    @PostMapping("/getPlatformClientAuthorityStoreWithOrgV2")
    public ResponseBase<List<DsGetPlatformClientStoreResDTO>> getPlatformClientAuthorityStoreByStoreV2(@RequestHeader("userId") String userId,
                                                                                                     @RequestBody OnlineStoreByOrDto reqDto){
        if(ObjectUtil.isEmpty(reqDto) || ObjectUtil.isEmpty(reqDto.getMerCode())  || ObjectUtil.isEmpty(reqDto.getPlatformCode()) || CollectionUtils.isEmpty(reqDto.getOrgIdList())){
            return generateSuccess(new ArrayList<>());
        }

        return generateSuccess(onlineClientService.getPlatformClientAuthorityStoreByOrg(userId,reqDto));
    }



    @ApiOperation(value = "根据机构id懒加载当前用户可查看的机构树",notes = "根据机构id懒加载当前用户可查看的机构树")
    @PostMapping("/getOrgTreeWithOrg/{merCode}")
    public ResponseBase<List<BaseInfoOrgTreeResDto>> getOrgTreeWithOrg(@RequestHeader("userId") String userId, @PathVariable String merCode, @RequestParam String orgId){
        return generateSuccess(storeTreeService.getOrgTreeWithOrg(userId,merCode,orgId));
    }

    @ApiOperation(value = "根据商户编码和机构筛选平台对应的网店及有权限的线上门店",notes = "根据商户编码和机构筛选平台对应的网店及有权限的线上门店")
    @PostMapping("/getStoreTreeByOrg/{merCode}")
    public ResponseBase<List<DsOnlineStoreTreeResDto>> getStoreTreeByOrg(@RequestHeader("userId") String userId, @PathVariable String merCode,
                                                                                    @RequestBody DsOnlineStoreTreeReqDto reqDTO){
        //查平台层，平台编码为空；查单个平台下网店层级，平台编码不能为空；查单个平台单个网店下门店层级，平台编码、网店编码不能为空
        if((DsConstants.INTEGER_TWO.equals(reqDTO.getQryType()) && StringUtils.isEmpty(reqDTO.getPlatformCode()))
                || (DsConstants.INTEGER_THREE.equals(reqDTO.getQryType()) && StringUtils.isEmpty(reqDTO.getPlatformCode())
                    && StringUtils.isEmpty(reqDTO.getClientCode()))
                || (DsConstants.INTEGER_FOUR.equals(reqDTO.getQryType()) && StringUtils.isEmpty(reqDTO.getStoreName()))){
            throw ExceptionUtil.getWarnException(DsErrorType.PARAM_VALID_ERROR);
        }
        reqDTO.setMerCode(merCode);
        return generateSuccess(storeTreeService.getStoreTreeByOrg(userId,reqDTO));
    }

	@ApiOperation(value = "根据用户ID查询线下门店信息以及已经有权限的线上门店信息", notes = "根据用户ID查询线下门店信息以及已经有权限的线上门店信息")
    @PostMapping("/getStoreInfoByUserId")
    public ResponseBase<GetStoreByUserIdResDto> getStoreInfoByUserId(@RequestHeader("merCode") String merCode, @RequestParam("userId") String userId){
        return generateSuccess(dsOnlineStoreService.getStoreInfoByUserId(merCode,userId));
    }

	@ApiOperation(value = "商户平台员工多条件查询", notes = "商户平台员工多条件查询")
    @PostMapping("/queryEmpByCondition")
    public  ResponseBase<PageDTO<EmployeeResDTO>> queryEmpByCondition(@Valid  @RequestBody QueryEmpDTO queryEmpDTO, BindingResult result){
        this.checkValid(result);
        return middleBaseInfoClientAdapter.queryEmpByCondition(queryEmpDTO);
    }

	@ApiOperation(value = "根据用户id，查询用户可查看的门店", notes = "查询用户可查看的门店")
    @PostMapping("/my")
    public ResponseBase<PageDTO<StoreResDTO>> queryStoreByUser(@Valid @RequestBody MyStoreDTO dto,BindingResult result){
        this.checkValid(result);
        //【*********】【优化】OMS机构取值范围优化
        PageDTO<StoreResDTO> page = new PageDTO<>();
        page.setCurrentPage(dto.getCurrentPage());
        page.setPageSize(dto.getPageSize());
        ResponseBase<PageDTO<StoreResDTO>> storePage = baseInfoCache.queryStoreByUser(dto);
        List<StoreResDTO> allDataList = baseInfoCache.storeDataAppendStoreHouseData(dto,storePage);
        page.setTotalCount(allDataList.size());
        page.setData(allDataList);
		return generateSuccess(page);
    }

    @ApiOperation(value = "新写的根据用户id，查询用户可查看的门店", notes = "查询用户可查看的门店")
    @PostMapping("/newMy")
    public ResponseBase<PageDTO<NewStoreResDTO>> newQueryStoreByUser(@Valid @RequestBody MyStoreDTO dto,BindingResult result){
        this.checkValid(result);
        //【*********】【优化】OMS机构取值范围优化
        PageDTO<NewStoreResDTO> page = new PageDTO<>();
        page.setCurrentPage(dto.getCurrentPage());
        page.setPageSize(dto.getPageSize());
        ResponseBase<PageDTO<StoreResDTO>> storePage = baseInfoCache.queryStoreByUser(dto);
        List<StoreResDTO> allDataList = baseInfoCache.storeDataAppendStoreHouseData(dto,storePage);
        page.setTotalCount(allDataList.size());
        List<NewStoreResDTO> newList = allDataList.stream().map(e -> {
            NewStoreResDTO d = new NewStoreResDTO();
            BeanUtils.copyProperties(e, d);
            return d;
        }).collect(Collectors.toList());
        page.setData(newList);
        return generateSuccess(page);
    }

	@ApiOperation(value = "商户平台门店管理接口_门店多条件查询", notes = "商户平台门店管理接口_多条件查询")
    @PostMapping("/queryStoreByCondition")
    public ResponseBase<PageDTO<StoreResDTO>> queryStoreByCondition(@Valid @RequestBody QueryStoreDTO queryStoreDTO, BindingResult result){
        this.checkValid(result);
        return middleBaseInfoClient.queryStoreByCondition(queryStoreDTO);
    }

    @ApiOperation(value = "新商户平台门店管理接口_门店多条件查询", notes = "新商户平台门店管理接口_多条件查询")
    @PostMapping("/newQueryStoreByCondition")
    public ResponseBase<PageDTO<NewStoreResDTO>> newQueryStoreByCondition(@Valid @RequestBody QueryStoreDTO queryStoreDTO, BindingResult result){
        this.checkValid(result);
        return middleBaseInfoClient.newQueryStoreByCondition(queryStoreDTO);
    }

    @ApiOperation(
value = "商户平台门店管理接口_修改电商云是否有线上门店以及线上库存同步比例", notes = "商户平台门店管理接口_修改电商云是否有线上门店以及线上库存同步比例",
            response = String.class,
            responseContainer = "Object")
    @PutMapping(value = "/updateDsyOnlineInfo")
    public ResponseBase<Boolean> updateDsyOnlineInfo(@Valid  @RequestBody StoreCreateUpdateDTO updateDto, BindingResult result){
        this.checkValid(result);
        return middleBaseInfoClient.updateDsyOnlineInfo(updateDto);
    }


    @ApiOperation(value = "根据MerCode、userId查询已经开通的Platform", notes = "根据MerCode、userId查询已经开通的Platform")
    @GetMapping("/getPlatformByMerCode/{merCode}")
    public ResponseBase<PlatformResDTO> getPlatformByMerCode(@PathVariable String merCode ,@RequestParam(value="userId",required = false) String userId,
                                                             @RequestParam(value = "serviceModel", required = false) String serviceModel){
        // TODO:jiangjian 第三方商品 列取平台列表 https://dev-merchants.hxyxt.com/commodity/store/goods
        return generateSuccess(dsOnlineShopService.getPlatformByMerCode(merCode,userId,serviceModel));
    }

	@ApiOperation(value = "O2O网店管理_分页查询o2o网店信息", notes = "O2O网店管理_分页查询o2o网店信息")
    @PostMapping("/searchOnlineClient")
    public ResponseBase<PageDTO<DsOnlineClientResDTO>> queryDsOnlineClient(@Valid @RequestBody DsOnlineClientQueryDTO dto,
                                                     @RequestHeader("merCode") String merCode,
                                                     BindingResult result) throws InstantiationException, IllegalAccessException {
        checkValid(result);
        return generateSuccess(dsOnlineShopService.queryOnlineShop(merCode,dto));
    }

    /**
	 * yanggl-2478 2020-08-03 调整下账
	 * 
	 * @param dto
	 *            网店创建类
	 * @param result
	 *            参数校验
	 * @param merCode
	 *            商家编码
	 * @return Integer
	 */
	@ApiOperation(value = "O2O网店管理_添加网店", notes = "O2O网店管理_添加网店")
    @PostMapping("/createClient")
    public ResponseBase<Integer> createClient(@Valid @RequestBody DsOnlineClientCreateUpdateDTO dto,
                                              BindingResult result,
                                              @RequestHeader("merCode") String merCode){
        this.checkValid(result);
        if(DsConstants.O2O.equals(dto.getServiceMode())){
            dto.setServiceMode(DsConstants.O2O);
            o2OB2CCommonService.createOnlineClientO2O(merCode,dto);
        }else if (DsConstants.B2C.equals(dto.getServiceMode())){
            dto.setServiceMode(DsConstants.B2C);
            o2OB2CCommonService.createOnlineClientB2C(merCode,dto);
        }else{
            throw new OmsException(DsErrorType.SERVICE_MODE_NOT_EXIST.getMsg());
        }
        return generateSuccess(null);
    }

	@ApiOperation(value = "O2O网店管理_修改网店信息", notes = "O2O网店管理_修改网店信息")
    @PutMapping("/updateClient")
    public ResponseBase<Integer> updateShop(@Valid @RequestBody DsOnlineClientCreateUpdateDTO dto,
                                            BindingResult result,
                                            @RequestHeader("merCode") String merCode){
        this.checkValid(result);
        if(DsConstants.O2O.equals(dto.getServiceMode())){
            o2OB2CCommonService.updateOnlineClientO2O(merCode,dto);
        }else if (DsConstants.B2C.equals(dto.getServiceMode())){
            o2OB2CCommonService.updateOnlineClientB2C(merCode,dto);
        }else{
            throw new OmsException(DsErrorType.SERVICE_MODE_NOT_EXIST.getMsg());
        }
        return generateSuccess(null);
    }

    @ApiOperation(value = "O2O网店管理_删除网店信息", notes = "O2O网店管理_删除网店信息")
    @DeleteMapping("/deleteClientById/{id}")
    public ResponseBase<Void> deleteClient(@RequestHeader("merCode") String merCode, @RequestHeader("userId") String userId,
                                           @PathVariable("id") Long id){
        o2OB2CCommonService.deleteOnlineClient(merCode,userId,id);
        return generateSuccess(null);
    }

	@ApiOperation(value = "O2O网店管理_同步门店", notes = "O2O网店管理_同步门店信息")
    @PostMapping("/synStore")
    public ResponseBase<Integer> synStore(
            @Valid @RequestBody DsOnlineClientCreateUpdateDTO dto,
            @RequestHeader("merCode") String merCode
    ) {
        String sessionKey = null;
        DsMerchantGroupInfo dsMerchantGroupInfo = dsMerchantGroupInfoService.querySessionkeyByMerCode(merCode);
        if (dsMerchantGroupInfo != null) {
            sessionKey = dsMerchantGroupInfo.getSessionKey();
        }
        dto.setMerCode(merCode);
        int ret = dsOnlineStoreService.synStore(dto, dto.getOnlineClientCode(), sessionKey);
        return generateSuccess(ret);
    }

	@ApiOperation(value = "提供商品中台_根据mercode查询sessionkey", notes = "提供商品中台_根据mercode查询sessionkey")
    @PostMapping("/sessionKey")
    public ResponseBase<DsMerchantGroupInfo> getSessionKey(@RequestParam("merCode") String merCode){
        DsMerchantGroupInfo dsMerchantGroupInfo = dsMerchantGroupInfoService.querySessionkeyByMerCode(merCode);
        return generateObjectSuccess(dsMerchantGroupInfo);
    }


	@ApiOperation(value = "提供商品中台_分页查询线上门店", notes = "提供商品中台_分页查询线上门店")
    @PostMapping("/queryOnlineStorePage")
    public ResponseBase<PageDTO> queryOnlineStorePage(@Valid @RequestBody DsOnlineStorePageReqDto dto,
                                                    @RequestHeader("merCode") String merCode,
                                                    @RequestHeader(value = "userId",required = false) String userId,
                                                    BindingResult result) throws IllegalAccessException, InstantiationException {
        this.checkValid(result);
        dto.setMerCode(merCode);
        dto.setUserId(userId);
        return generateSuccess(dsOnlineStoreService.queryOnlineStorePage(merCode,dto));
    }

    @ApiOperation(value = "平台-门店筛选分页查询",notes = "平台-门店筛选分页查询")
    @PostMapping("/queryDsOnlineStorePage")
    public ResponseBase<PageDTO> queryDsOnlineStorePage(@Valid @RequestBody DsOnlineStorePageReqDto dto,
                                                      @RequestHeader("merCode") String merCode,
                                                      @RequestHeader(value = "userId",required = false) String userId,
                                                      BindingResult result) throws IllegalAccessException, InstantiationException {
        this.checkValid(result);
        dto.setMerCode(merCode);
        dto.setUserId(userId);
        return generateSuccess(dsOnlineStoreService.queryDsOnlineStorePage(merCode,dto));
    }

	@ApiOperation(value = "O2O门店管理_分页查询机构下拉数据", notes = "O2O门店管理_分页查询机构下拉信息")
    @PostMapping("/queryOrganization")
    public ResponseBase<PageDTO<StoreResDTO>> queryOrganization(@Valid @RequestBody DsOnlineStoreQueryDTO dto,
                                                                 @RequestHeader("merCode") String merCode,
                                                                 BindingResult result){
        this.checkValid(result);
        ResponseBase<PageDTO<StoreResDTO>> responseBase = middleBaseInfoClient.queryOrganization(dto, merCode);
        return responseBase;
    }

    @ApiOperation(value = "修改门店_查询机构下拉数据", notes = "修改门店_查询机构下拉数据")
    @PostMapping("/queryOrganizationForModify")
    public ResponseBase<PageDTO<StoreResDTO>> queryOrganizationForModify(@RequestHeader("merCode") String merCode) {
        QueryStoreDTO queryStoreDTO = new QueryStoreDTO();
        queryStoreDTO.setMerCode(merCode);
        queryStoreDTO.setStatus(DsConstants.INTEGER_ONE);
        queryStoreDTO.setPageSize(Integer.MAX_VALUE);
        queryStoreDTO.setCurrentPage(DsConstants.INTEGER_ONE);
        ResponseBase<PageDTO<StoreResDTO>> responseBase = middleBaseInfoClient.queryStoreByCondition(queryStoreDTO);
        if (responseBase != null && responseBase.getData() != null) {
            List<StoreResDTO> storeResDTOS = responseBase.getData().getData();
            if (!CollectionUtils.isEmpty(storeResDTOS)) {
                List<StoreResDTO> newResult = storeResDTOS.stream().filter(s -> ((!DsConstants.INTEGER_THREE.equals(s.getStClass()) && DsConstants.INTEGER_ONE.equals(s.getOnlineStoreStatus())) || DsConstants.INTEGER_THREE.equals(s.getStClass()))).collect(Collectors.toList());
                newResult.sort((a, b) -> DsConstants.INTEGER_THREE.equals(b.getStClass()) ? 1 : -1);
                responseBase.getData().setData(newResult);
                responseBase.getData().setTotalCount(newResult.size());
            }
        }
        return responseBase;
    }

	@ApiOperation(value = "O2O门店管理_门店设置", notes = "O2O门店管理_门店设置")
    @PostMapping("/storeConf")
    public ResponseBase<DsOnlineStoreConfResDTO> toStoreConf(@Valid @RequestBody DsOnlineStoreConfReqDTO dto,
                                                           @RequestHeader("merCode") String merCode,
                                                           BindingResult result) throws IllegalAccessException, InstantiationException {
        this.checkValid(result);
        return generateSuccess(dsOnlineStoreConfigService.queryStoreConf(merCode,dto));
    }


	@ApiOperation(value = "O2O门店管理_保存门店设置", notes = "O2O门店管理_保存门店设置信息")
    @PostMapping("/saveStoreConf")
    public ResponseBase<Void> saveStoreConf(@Valid @RequestBody DsOnlineStoreConfCreateUpdateDTO dto){
        dsOnlineStoreConfigService.saveStoreConfig(dto);
        return generateSuccess(null);
    }


	@ApiOperation(value = "O2O门店管理_根据线上门店id查询线上门店数据", notes = "O2O门店管理_根据线上门店id查询线上门店数据")
    @GetMapping("/queryOnlineStoreById/{onlineStoreId}")
    public ResponseBase<DsOnlineStoreResDTO> queryOnlineStoreById(@PathVariable Long onlineStoreId) {
        return generateSuccess(dsOnlineStoreService.queryOnlineStoreById(onlineStoreId));
    }

	@ApiOperation(value = "O2O门店管理_根据线上门店编码查询线上门店数据", notes = "O2O门店管理_根据线上门店编码查询线上门店数据")
    @PostMapping("/queryByCode")
    public ResponseBase<DsOnlineStore> queryOnlineStoreByCode(@Valid @RequestBody StoreQueryBase base,
                                                              BindingResult result) {
        checkValid(result);
        return generateSuccess(dsOnlineStoreService.queryDsStoreStore(base));
    }

    @ApiOperation(value = "O2O门店管理_获取client id 下的所有门店编码map", notes = "O2O门店管理_获取client id 下的所有门店编码map")
    @PostMapping("/queryShopMap")
    public ResponseBase<Map<String,String>> queryShopMap(@Valid @RequestBody StoreQueryBase base,
                                          BindingResult result) {
        checkValid(result);
        return generateSuccess(dsOnlineStoreService.queryDsStoreStoreMap(base));
    }

	// 更新门店同步配置
	@ApiOperation(value = "O2O门店管理_修改门店设置信息", notes = "O2O门店管理_修改门店设置信息")
    @PutMapping("/updateStoreConf")
    public ResponseBase<Void> updateStoreConf(@Valid @RequestBody DsOnlineStoreConfCreateUpdateDTO dto,
                                              @RequestHeader("userId") String userId,
                                              @RequestHeader("userName") String userName){
        dto.setUserId(userId);
	    dsOnlineStoreConfigService.updateStoreConfig(dto, userId, userName);
        return generateSuccess(null);
    }

	@ApiOperation(value = "O2O门店管理_查询线上门店订单配置信息", notes = "O2O门店管理_查询线上门店订单配置信息")
    @PostMapping("/toOrderConf")
    public ResponseBase<DsStoreOrderConfResDTO> toOrderConf(@Valid @RequestBody DsStoreOrderConfReqDTO dto,
                                                          BindingResult result){
        this.checkValid(result);
        return generateSuccess(dsStoreOrderConfigService.queryStoreOrderConf(dto));
    }

    @ApiOperation(value = "开启b2c门店配置", notes = "开启b2c门店配置")
    @PostMapping("/openB2c")
    public ResponseBase<DsStoreOrderConfResDTO> openB2c(@Valid @RequestBody DsStoreOpenB2CReqDTO dto,
                                                            BindingResult result){
        this.checkValid(result);
        dsStoreOrderConfigService.openB2c(dto);
        return generateSuccess(null);
    }

	@ApiOperation(value = "O2O门店管理_保存订单配置信息", notes = "O2O门店管理_保存订单配置信息")
    @PostMapping("/saveOrderConf")
    public ResponseBase<Void> saveOrderConf(@Valid @RequestBody DsStoreOrderConfCreateUpdateDTO dto){
        dsStoreOrderConfigService.saveStoreOrderConf(dto);
        return generateSuccess(null);
    }

	@ApiOperation(value = "O2O门店管理_修改订单配置信息", notes = "O2O门店管理_修改订单配置信息")
    @PutMapping("/updateOrderConf")
    public ResponseBase<Void> updateOrderConf(@Valid @RequestBody DsStoreOrderConfCreateUpdateDTO dto,
                                              @RequestHeader("userId") String userId,
                                              @RequestHeader("userName") String userName){
        dsStoreOrderConfigService.updateStoreOrderConf(dto, userId, userName);
        return generateSuccess(null);
    }

	@ApiOperation(value = "O2O门店管理_查询提示音设置信息", notes = "O2O门店管理_线上门店提示音设置信息")
    @PostMapping("/toSoundConf")
    public ResponseBase<DsStoreSoundConfResDTO> toSoundConf(@Valid @RequestBody DsStoreSoundConfQueryDTO dto,
                                                          BindingResult result){
        this.checkValid(result);
        return generateSuccess(dsStoreSoundConfigService.querySoundConf(dto));
    }

	@ApiOperation(value = "O2O门店管理_保存提示音设置", notes = "O2O门店管理_保存门店提示音设置信息")
    @PostMapping("/saveSoundConf")
    public ResponseBase<Void> saveSoundConf(@Valid @RequestBody DsStoreSoundConfCreateUpdateDTO dto){
        dsStoreSoundConfigService.saveSoundConf(dto);
        return generateSuccess(null);
    }

	@ApiOperation(value = "O2O门店管理_修改提示音设置", notes = "O2O门店管理_修改门店提示音设置信息")
    @PutMapping("/updateSoundConf")
    public ResponseBase<Void> updateSoundConf(@Valid @RequestBody DsStoreSoundConfCreateUpdateDTO dto,
                                              @RequestHeader("userId") String userId,
                                              @RequestHeader("userName") String userName){
        dsStoreSoundConfigService.updateSoundConf(dto, userId, userName);
        return generateSuccess(null);
    }

	@ApiOperation(value = "O2O门店管理_查询自配送设置列表", notes = "O2O门店管理_查询自配送设置列表")
    @PostMapping("/toDeliveryConf")
    public ResponseBase<List<DsStoreDeliveryResDTO>> toDeliveryConf(@Valid @RequestBody DsSelfDeliveryQueryDTO dto,
                                                                  @RequestHeader("merCode") String merCode,
                                                                  BindingResult result) throws IllegalAccessException, InstantiationException {
        checkValid(result);
        return generateSuccess(dsOnlineStoreDeliveryService.queryDeliveryConf(merCode,dto));
    }

	@ApiOperation(value = "O2O门店管理_绑定配送门店_查询配送门店数据", notes = "O2O门店管理_绑定配送门店_查询配送门店数据")
    @PostMapping("/searchDeliveryStore")
    public ResponseBase<PageDTO> searchDeliveryStore(@Valid @RequestBody DsBindDeliveryStoreQueryDTO dto,
                                                    @RequestHeader("merCode") String merCode,
                                                    BindingResult result) throws IllegalAccessException, InstantiationException {
        checkValid(result);
        return generateSuccess(dsOnlineStoreDeliveryService.queryDeliveryStoreConf(merCode,dto));
    }

	@ApiOperation(value = "O2O门店管理_绑定配送门店_保存配送门店数据", notes = "O2O门店管理_绑定配送门店_保存配送门店数据")
    @PostMapping("/saveDeliveryConf")
    public ResponseBase<Void> saveDeliveryConf(@Valid @RequestBody DsStoreDeliveryCreateUpdateDTO dto,
                                               @RequestHeader("merCode") String merCode,
                                               @RequestHeader("userId") String userId,
                                               @RequestHeader("userName") String userName){
        dsOnlineStoreDeliveryService.saveDeliveryConf(merCode,dto, userId, userName);
        return generateSuccess(null);
    }

    @ApiOperation(value = "O2O门店管理_设置启用禁用状态", notes = "O2O门店管理_设置启用禁用状态")
    @PostMapping("/setStatus")
    public ResponseBase<Integer> setStatus(@Valid @RequestBody DsStoreDeliveryCreateUpdateDTO dto,
                                           @RequestHeader("merCode") String merCode,
                                           @RequestHeader("userId") String userId,
                                           @RequestHeader("userName") String userName,
                                           BindingResult result) {
        return generateSuccess(dsOnlineStoreDeliveryService.settingStatus(merCode, dto, userId, userName));
    }

	@ApiOperation(value = "O2O第三方配送_分页查询O2O第三方配送", notes = "O2O第三方配送_分页查询O2O第三方配送")
    @PostMapping("/searchDeliveryClient")
    public ResponseBase<PageDTO> searchDeliveryClient(@Valid @RequestBody DsDeliveryClientQueryDTO dto,
                                                   @RequestHeader("merCode") String merCode,
                                                   BindingResult result) throws InstantiationException, IllegalAccessException {
        checkValid(result);
        return generateSuccess(dsDeliveryClientService.queryDeliveryClient(merCode,dto));
    }

    @ApiOperation(value = "O2O 查询商户授权了的配送方式", notes = "O2O 查询商户授权了的配送方式")
    @GetMapping("/getAuthedDeliveryName/{merCode}")
    public ResponseBase<List<String>> getAuthedDeliveryName(@PathVariable String merCode){
        return generateSuccess(dsDeliveryClientService.getAuthedDeliveryName(merCode));
    }


	@ApiOperation(value = "O2O第三方配送_配送账号管理", notes = "O2O第三方配送_查询配送账号管理数据")
    @PostMapping("/queryDeliveryAccount")
    public ResponseBase<List<DsDeliveryAccountResDTO>> queryDeliveryAccount(@RequestHeader("merCode") String merCode) throws IllegalAccessException, InstantiationException {
        return generateSuccess(dsDeliveryClientService.queryDeliveryAccount(merCode));
    }

	@ApiOperation(value = "O2O第三方配送_配送账号授权", notes = "O2O第三方配送_配送账号授权")
    @PostMapping("/saveDeliveryClient")
    public ResponseBase<Void> saveDeliveryClient(@Valid @RequestBody DsDeliveryClientCreateUpdateDTO dto,
                                                BindingResult result,
                                                @RequestHeader("merCode") String merCode){
        this.checkValid(result);
        dsDeliveryClientService.createDeliveryClient(merCode,dto);
        return generateSuccess(null);
    }

	@ApiOperation(value = "O2O第三方配送_修改配送账号授权", notes = "O2O第三方配送_修改配送账号授权")
    @PutMapping("/updateDeliveryClient")
    public ResponseBase<Void> updateDeliveryClient(@Valid @RequestBody DsDeliveryClientCreateUpdateDTO dto,
                                                   BindingResult result,
                                                   @RequestHeader("merCode") String merCode){
        this.checkValid(result);
        dsDeliveryClientService.updateDeliveryClient(merCode,dto);
        return generateSuccess(null);
    }

    /**
     * 初始化配送平台的配送服务范围经纬度坐标信息
     * @param merCode 500001
     * @param platformCode  配送平台编码  1. dada: 2003  蜂鸟：2001 ， 美团： 2002 ，顺丰：2004
     */
    @ApiOperation(value = "初始化配送平台的配送服务范围经纬度坐标信息", notes = "初始化配送电子围栏")
    @GetMapping("/initDeliveryStoreScope")
    public ResponseBase<Void> initDeliveryStoreScope(@RequestHeader("merCode") String merCode, @RequestParam("platformCode") String platformCode){
        deliveryStoreService.initDeliveryStoreScope(merCode,platformCode);
        return  ResponseBase.success();
    }

	@ApiOperation(value = "O2O第三方配送_下载Excel", notes = "O2O第三方配送_下载指定目录下的Excel文件")
    @PostMapping("/downloadExcel")
    @ResponseBody
    public void downloadExcel(@RequestParam("response") HttpServletResponse response,@RequestParam("request") HttpServletRequest request){
        middleBaseInfoClient.downloadExcel(response,request);
    }

	@ApiOperation(value = "O2O第三方配送_上传excel文件批量同步配送门店", notes = "O2O第三方配送_上传excel文件批量同步配送门店")
    @PostMapping(value = "/synDeliveryStore")
    public ResponseBase<StoreDeliveryResDTO> synDeliveryStore(DsSynDeliveryStoreQueryDTO dto,
                                                              @RequestParam("file") MultipartFile file,
                                                              @RequestHeader("merCode") String merCode) throws IOException {
        List<DsSyncDeliveryStoreEO> results = ExcelUtil.read(file, DsSyncDeliveryStoreEO.class);
        deliveryStoreService.synDeliveryStore(merCode,dto,results);
        return generateSuccess(null);
    }

    @ApiOperation(value = "顺丰同城_上传excel文件批量创建配送门店", notes = "顺丰同城_上传excel文件批量创建配送门店")
    @PostMapping(value = "/synSFDeliveryStore")
    public ResponseBase<StoreDeliveryResDTO> synSFDeliveryStore(DsSynDeliveryStoreQueryDTO dto,
                                                              @RequestParam("file") MultipartFile file,
                                                              @RequestHeader("merCode") String merCode) throws IOException {
        List<SynSFDeliveryStoreEO> sfDeliveryStore = ExcelUtil.read(file, SynSFDeliveryStoreEO.class);
        deliveryStoreService.synSFDeliveryStore(merCode,dto,sfDeliveryStore);
        return generateSuccess(null);
    }

	@ApiOperation(value = "O2O门店管理_分页查询线上门店", notes = "O2O门店管理_分页查询线上门店")
    @PostMapping("/queryDsOnlineStore")
    public ResponseBase<PageDTO<DsOnlineStoreResDTO>> queryDsOnlineStore(
            @Valid @RequestBody DsOnlineStoreQueryDTO dto,
            @RequestHeader("merCode") String merCode,
            @RequestHeader("userId") String userId,
            BindingResult result
    ) throws IllegalAccessException, InstantiationException {
        checkValid(result);

//        log.info("merCode:{},userId:{},dto:{}",merCode,userId, JsonUtils.toJson(dto));
        if(queryDsOnlineStoreOnOff && dto.getPageSize()>1000){
            dto.setPageSize(1000);
        }

        return generateSuccess(dsOnlineStoreService.queryDsOnlineStore(merCode,userId,dto));
    }

	@ApiOperation(value = "平台已绑定机构的线上门店")
    @PostMapping("/getOnlineStoreListByPlatform")
    public ResponseBase<List<GetOnlineStoreByPlatResDto>> getOnlineStoreListByPlatform(@RequestHeader("merCode") String merCode, @Valid @RequestBody GetOnlineStoreListByPlatReqDto dto,BindingResult result){
        checkValid(result);
        dto.setMerCode(merCode);
        List<GetOnlineStoreByPlatResDto> list = dsOnlineStoreService.getOnlineStoreListByPlatform(dto);
        return generateSuccess(list);
    }

	@ApiOperation(value = "O2O门店管理_删除线上门店", notes = "O2O门店管理_删除线上门店")
    @GetMapping("/deleteOnlineStoreById/{id}")
    public ResponseBase<Boolean> deleteOnlineStoreById(@PathVariable Long id){
        return generateSuccess(dsOnlineStoreService.deleteOnlineStoreById(id));
    }

	@ApiOperation(value = "查询已启用且已绑定配送门店的配送平台", notes = "查询已启用且已绑定配送门店的配送平台")
    @PostMapping("/queryDeliveryPlatform")
    public ResponseBase<List<DsDeliveryStoreResDTO>> queryDeliveryPlatform(@Valid @RequestBody  DsDeliveryPlatformReqDTO dto,
                                                                          @RequestHeader("merCode") String merCode,
                                                                          BindingResult result){
        checkValid(result);
        //增加订单是否修改门店校验，若是需要查询新门店的配送方式
        OrderInfo orderInfo = orderBasicManager.getOrderBaseInfo(dto.getOrderNo());
        String newStoreCode = dto.getOnlineStoreCode();
        if(orderInfo != null && orderInfo.isUpdateStore()){
            DsOnlineStore dsOnlineStore = updateStoreDeliveryMatchManager.matchDeliveryStore(orderInfo);
            if(dsOnlineStore != null){
                newStoreCode = dsOnlineStore.getOnlineStoreCode();
            }
        }
		// 查询默认自配送方式（已启用且已绑定配送门店的）
        DsOnlineStore onlineStore = dsOnlineStoreService.queryOnlineStoreByStoreCode(merCode,dto.getPlatformCode(),newStoreCode,null);
        List<DsDeliveryStoreResDTO> dsDeliveryStoreResDTOS = deliveryStoreService.queryDeliveryTypeV2(merCode, onlineStore.getId(),orderInfo);
        dsDeliveryStoreResDTOS = this.filterFlag1(dsDeliveryStoreResDTOS, dto);
        return generateSuccess(dsDeliveryStoreResDTOS);
    }

    private List<DsDeliveryStoreResDTO> filterFlag1(List<DsDeliveryStoreResDTO> dsDeliveryStoreResDTOS, DsDeliveryPlatformReqDTO dto) {
        if (CollUtil.isEmpty(dsDeliveryStoreResDTOS)) {
            return dsDeliveryStoreResDTOS;
        }
        if (!DsConstants.INTEGER_ONE.equals(dto.getFilterFlag())) {
            return dsDeliveryStoreResDTOS;
        }
        if (dto.getOrderNo() == null) {
            return dsDeliveryStoreResDTOS;
        }
        OrderDeliveryRecord orderDeliveryRecord = orderDeliveryRecordService.selectByOrderNo(dto.getOrderNo());
        if (orderDeliveryRecord == null || !DeliveryTypeEnum.checkSelfDistribution(orderDeliveryRecord.getDeliveryType())) {
            return dsDeliveryStoreResDTOS;
        }
        DeliveryPlatformEnum byName = DeliveryPlatformEnum.getByName(orderDeliveryRecord.getDeliveryPlatName());
        if (byName == null) {
            return dsDeliveryStoreResDTOS;
        }
        if (!DeliveryPlatformEnum.checkThirdRider(byName.getName())) {
            return dsDeliveryStoreResDTOS;
        }
        return dsDeliveryStoreResDTOS.stream().filter(it -> byName.getCode().equals(it.getPlatformCode())).collect(Collectors.toList());
    }

	@ApiOperation(value = "开发自用_根据merCode删除所有店铺平台的表数据", notes = "开发自用_根据merCode删除所有店铺平台的表数据")
    @GetMapping("/deleteInfoByMerCode/{merCode}")
    public ResponseBase deleteInfoByMerCode(@PathVariable String merCode){
        return generateSuccess(onlineClientService.deleteInfoByMerCode(merCode));
    }


	@ApiOperation(value = "商品中台特供_根据门店code查询门店信息", notes = "查找线上门店名称")
    @PostMapping("/getOnlineStoreName")
    public ResponseBase<DsOnlineStore> queryOnlineStoreName(@RequestParam String merCode,@RequestParam String platformCode, @RequestParam String onlineStoreCode) {
        DsOnlineStore onlineStore = dsOnlineStoreService.queryOnlineStoreByStoreCode(merCode,platformCode,onlineStoreCode,null);
        return generateSuccess(onlineStore);
    }

	@ApiOperation(value = "商品中台特供_根据门店code集合查询门店信息V2", notes = "查找线上门店名称")
    @PostMapping("/getOnlineStoresByCodes")
    public ResponseBase<List<DsOnlineStore>> getOnlineStoresByCodes(@Valid @RequestBody OnlineStoreCodeDto dto) {
        List<DsOnlineStore> onlineStores = dsOnlineStoreServiceAuto.getOnlineStores(dto.getMerCode(),dto.getPlatformCode(), dto.getOnlineStoreCode());
        return generateSuccess(onlineStores);
    }

	@ApiOperation(value = "商品中台特供_根据门店code集合查询门店信息", notes = "查找线上门店名称")
    @PostMapping("/getOnlineStores")
    public ResponseBase<List<DsOnlineStore>> getOnlineStores(@RequestParam String merCode, @RequestParam List<String> onlineStoreCode) {
        List<DsOnlineStore> onlineStores = dsOnlineStoreServiceAuto.getOnlineStores(merCode,null, onlineStoreCode);
        return generateSuccess(onlineStores);
    }

	@ApiOperation(value = "O2O门店管理_设置线上门店停业、营业", notes = "O2O门店管理_设置线上门店停业、营业")
    @PutMapping("/setOnlineStoreOpenStatus")
    public ResponseBase<Integer> setOnlineStoreOpenStatus(@Valid @RequestBody DsOnlineStoreSetStatusDTO dto,
                                                          @RequestHeader("merCode") String merCode,
                                                          @RequestHeader("userId") String userId,
                                                          BindingResult result){
        this.checkValid(result);
        String sessionKey = null;
        DsMerchantGroupInfo dsMerchantGroupInfo = dsMerchantGroupInfoService.querySessionkeyByMerCode(merCode);
        if(dsMerchantGroupInfo != null){
            sessionKey = dsMerchantGroupInfo.getSessionKey();
        }
        return generateSuccess(dsOnlineStoreService.setOnlineStoreOpenStatus(merCode, sessionKey, dto, userId));
    }

	@ApiOperation(value = "O2O网店管理_上传excel文件批量同步平安中心仓门店", notes = "O2O网店管理_上传excel文件批量同步平安中心仓门店")
    @PostMapping(value = "/synSafeCenterStore")
    public ResponseBase<StoreDeliveryResDTO> synSafeCenterStore(@Valid DsSynOnlineStoreQueryDTO dto,
                                                                BindingResult result,
                                                                @RequestParam("file") MultipartFile file,
                                                                @RequestHeader("merCode") String merCode) throws IOException {
        checkValid(result);
        List<DsSyncSafeCenterStoreEO> results = ExcelUtil.read(file, DsSyncSafeCenterStoreEO.class);
        dsOnlineStoreService.synSafeCenterStore(merCode,dto,results);
        return generateSuccess(null);
    }

	@ApiOperation(value = "O2O配送网店管理_设置默认配送服务", notes = "O2O配送网店管理_设置默认配送服务")
    @PutMapping(value = "/updateDefaultServiceCode")
    public ResponseBase<Integer> updateDefaultServiceCode(@Valid @RequestBody DefaultServiceCodeReqDTO dto,
                                                          @RequestHeader("merCode") String merCode,
                                                          BindingResult result){
        this.checkValid(result);
        return generateSuccess(deliveryStoreService.updateDefaultServiceCode(merCode,dto));
    }


    @ApiOperation(value = "单个查询平台，网店，门店配置信息", notes = "单个查询平台，网店，门店配置信息")
    @PostMapping("/queryStoreConfig")
    public ResponseBase<DsOnlineStoreConfResDTO> queryStoreConfig(@Valid @RequestBody StoreConfigReqDTO reqDTO,
                                                               BindingResult result){
        checkValid(result);
        DsOnlineStoreConfResDTO dsOnlineStoreConfResDTO = dsOnlineStoreConfigService.queryStoreConfig(reqDTO);
        return generateObjectSuccess(dsOnlineStoreConfResDTO);
    }

    @ApiOperation(value = "B2C结算模式设置商户是否需要", notes = "B2C结算模式设置商户是否需要")
    @GetMapping("/isNeedSettleMode")
    public ResponseBase<Boolean> isNeedSettleMode(@RequestParam("merCode") String merCode){
        Boolean flag = dsOnlineStoreConfigService.isNeedSettleMode(merCode);
        return generateObjectSuccess(flag);
    }

    @ApiOperation(value = "微商城同步线上门店",notes = "微商城同步线上门店")
    @PostMapping("/wscSyncStore")
    public ResponseBase<Integer> wscSyncStore(@RequestParam("merCode") String merCode,@RequestParam("platformCode")String platformCode,
                                           @RequestParam("onlineStoreCodes") List<String> onlineStoreCodes){
        log.info("wscSyncStore request: {}, {}, {}",merCode,platformCode,onlineStoreCodes);
        if(!PlatformCodeEnum.YD_JIA.getCode().equals(platformCode)){
            throw ExceptionUtil.getWarnException(DsErrorType.PARAM_PLATCODE_VALID_ERROR.getCode(),DsErrorType.PARAM_PLATCODE_VALID_ERROR.getMsg());
        }
        String sessionKey = null;
        DsMerchantGroupInfo dsMerchantGroupInfo = dsMerchantGroupInfoService.querySessionkeyByMerCode(merCode);
        if(dsMerchantGroupInfo != null){
            sessionKey = dsMerchantGroupInfo.getSessionKey();
        }
        Integer result = dsOnlineStoreService.wscSyncStore(merCode,platformCode,onlineStoreCodes,sessionKey);
        log.info("wscSyncStore response: " + result);
        return generateSuccess(result);
    }


    @ApiOperation(value = "获取门店配置状态，三方配送&店铺配置",notes = "获取门店配置状态，三方配送&店铺配置")
    @PostMapping("/getWscStoreConfigInfo")
    public ResponseBase<DsStoreConfInfoResDTO> getStoreConfigInfo(@RequestParam("merCode") String merCode){
	    if(StringUtils.isEmpty(merCode)){
            throw ExceptionUtil.getWarnException(DsErrorType.PARAM_MERCODE_VALID_ERROR.getCode(),DsErrorType.PARAM_MERCODE_VALID_ERROR.getMsg());
        }
	    String platformCode = PlatformCodeEnum.YD_JIA.getCode();
        DsStoreConfInfoResDTO result = dsOnlineStoreService.getStoreConfigInfo(merCode,platformCode);
        log.info("getStoreConfigInfo request: {}, {},resp: {}",merCode,platformCode, JSONObject.toJSONString(result));
        return generateSuccess(result);
    }


    @ApiOperation(value = "根据零售单号查询平台订单", notes = "适用于工业方案")
    @GetMapping("/queryInfoBySaleNos")
    public ResponseBase<List<GyInfoDTO>> queryInfoBySaleNos(@RequestParam("merCode") String merCode,
                                                            @RequestParam("saleNos") List<String> saleNos){
	    if(StringUtils.isEmpty(merCode)){
	        return generateSuccess(Collections.emptyList());
        }
        if(CollectionUtils.isEmpty(saleNos)){
            return generateSuccess(Collections.emptyList());
        }
        saleNos = saleNos.stream().filter(saleNo -> !StringUtils.isEmpty(saleNo)).collect(Collectors.toList());
        if(CollectionUtils.isEmpty(saleNos)){
            return generateSuccess(Collections.emptyList());
        }
        if(saleNos.size() > 1000){
            throw  ExceptionUtil.getWarnException(DsErrorType.ERP_ORDER_OVER_ERROR);
        }
        return generateObjectSuccess(orderBasicManager.queryInfoBySaleNos(merCode, saleNos));
    }


    @ApiOperation(value = "根据平台单号查询销售单号（微商城）", notes = "适用于工业方案")
    @GetMapping("/queryInfoByThirdOrderNos")
    public ResponseBase<List<GyInfoDTO>> queryInfoByThirdOrderNos(@RequestParam("merCode") String merCode,
                                                            @RequestParam("thirdOrderNos") List<String> thirdOrderNos){
        if(StringUtils.isEmpty(merCode)){
            return generateSuccess(Collections.emptyList());
        }
        if(CollectionUtils.isEmpty(thirdOrderNos)){
            return generateSuccess(Collections.emptyList());
        }
        thirdOrderNos = thirdOrderNos.stream().filter(thirdOrderNo -> !StringUtils.isEmpty(thirdOrderNo)).collect(Collectors.toList());
        if(CollectionUtils.isEmpty(thirdOrderNos)){
            return generateSuccess(Collections.emptyList());
        }
        if(thirdOrderNos.size() > 1000){
            throw  ExceptionUtil.getWarnException(DsErrorType.ERP_ORDER_OVER_ERROR);
        }
        return generateObjectSuccess(orderBasicManager.queryInfoByThirdOrderNos(merCode, thirdOrderNos));
    }

    @ApiOperation(value = "获取服务商网店信息",notes = "获取服务商网店信息")
    @GetMapping("/getSupplierClient")
    public ResponseBase<DsOnlineClient> getSupplierClient(@RequestParam("merCode") String merCode,
                                                          @RequestParam("platformCode") String platformCode){
        return generateSuccess(onlineClientService.querySupplierClient(merCode,platformCode,null));
    }

    @ApiOperation(value = "获取所有三方配送方式",notes = "获取所有三方配送方式")
    @GetMapping("/getAllDeliveryType")
    public ResponseBase<List<DeliveryTypeRspDto>> getAllDeliveryType(){
        return generateSuccess(DeliveryPlatformEnum.getAllDeliveryType());
    }

    @ApiOperation(value = "根据平台编码、门店编码查询所属商户信息(服务商模式-商品专用)",notes = "根据平台编码、门店编码查询所属商户信息(服务商模式-商品专用)")
    @GetMapping("/getSupplierStore")
    public ResponseBase<DsOnlineStore> getSupplierStore(@RequestParam("platformCode") String platformCode,
                                                        @RequestParam("storeCode") String storeCode){
        return generateSuccess(onlineClientService.querySupplierStore(platformCode,storeCode));
    }

    @ApiOperation(value = "根据商户编码查询可用平台（缓存）",notes = "根据商户编码查询可用平台（缓存）")
    @PostMapping("/getAuthorityPlatByMerCode")
    public ResponseBase<List<String>> getAuthorityPlatByMerCode(@RequestHeader("merCode") String merCode,
                                                                @RequestHeader(value = "userId",required = false) String userId,
                                                                @RequestParam(value = "serviceMode",required = false) String serviceMode){
        return generateSuccess(onlineClientService.getAuthorityPlatByMerCode(merCode,userId,serviceMode));
    }


    @ApiOperation(value = "根据外部门店编码storeId查询网店信息(商家模式)", notes = "根据外部门店编码storeId查询网店信息(商家模式)")
    @PostMapping("/getByOnlineClientCode")
    public ResponseBase<DsOnlineClient> getByOnlineClientOutCode(@Valid @RequestBody DsOnlineClientReqDTO dto) {
        return generateSuccess(onlineClientService.getByOnlineClientOutCode(dto));
    }

    @ApiOperation(value = "根据外部门店编码storeId查询网店信息(商家模式)", notes = "根据外部门店编码storeId查询网店信息(商家模式)")
    @GetMapping("/getOnlineStoreByPlatformShopId")
    public ResponseBase<The3DsOnlineClientResDTO> getOnlineStoreByPlatformShopId(@RequestParam("merCode") String merCode,
                                                                 @RequestParam("platformCode") String platformCode,
                                                                 @RequestParam("platformShopId") String platformShopId) {
        return generateSuccess(onlineClientService.getOnlineStoreByPlatformShopId(merCode, platformCode, platformShopId, null));
    }
}