/*  
 * Project Name:hydee-business-order  
 * File Name:AccountCheckClean.java  
 * Package Name:cn.hydee.middle.business.order.account.check.base.model  
 * Date:2020年11月6日下午1:46:17  
 * Copyright (c) 2020, Shanghai Hydee Software Corp., Ltd. All Rights Reserved.  
 *  
*/  
  
package cn.hydee.middle.business.order.account.check.base.model;

import java.math.BigDecimal;
import java.util.Date;

import org.springframework.beans.BeanUtils;

import cn.hydee.middle.business.order.account.check.annotation.CompareProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;

/**  
 * ClassName:AccountCheckClean <br/>  
 * 
 * Date:     2020年11月6日 下午1:46:17 <br/>  
 * <AUTHOR>  
 */
@Data
@Builder
@AllArgsConstructor
public class AccountCheckClean {
	/**
     * 商户编码
     */
    @CompareProperty(index = 0)
    private String merCode;

    /**
     * 平台编码
     */
    @CompareProperty(index = 1)
    private String thirdPlatformCode;

    /**
     * 平台名称
     */
    private String thirdPlatformName;

    /**
     * 门店编码
     */
    private String onlineStoreCode;
    
    /**
     * 门店名称
     */
    private String onlineStoreName;

    /**
     * 外部门店ID
     */
    private String outShopId;

    /**
     * 所属机构编码
     */
    private String organizationCode;

    /**
     * 所属机构名称
     */
    private String organizationName;

    /**
     * 系统订单号
     */
    private Long orderNo;

    /**
     * 第三方平台订单号
     */
    @CompareProperty(index = 2)
    private String thirdOrderNo;

    /**
     * 商家实收
     */
    private BigDecimal merchantActualAmount;

    /**
     * 商品总金额
     */
    private BigDecimal totalAmount;

    /**
     * 平台优惠
     */
    private BigDecimal platformDiscount;

    /**
     * 商家优惠
     */
    private BigDecimal merchantDiscount;

    /**
     * 交易佣金
     */
    private BigDecimal brokerageAmount;

    /**
     * 平台配送费
     */
    private BigDecimal platformDeliveryFee;

    /**
     * 商家配送费
     */
    private BigDecimal merchantDeliveryFee;

    /**
     * 平台打包费
     */
    private BigDecimal platformPackFee;

    /**
     * 商家打包费
     */
    private BigDecimal merchantPackFee;
    
    /**
     * 配送费 1-平台收取 2-商家收取
     */
    private Integer freightFeeFetch;
    
    /**
     * 包装费 1-平台收取 2-商家收取
     */
    private Integer packageFeeFetch;

    /**
     * 下单时间
     */
    private Date orderTime;

    /**
     * 下账时间
     */
    private Date billTime;

    /**
     * 账单日期
     */
    private Date accountTime;
    
    public AccountCheckClean() {}
    public static AccountCheckClean copyBean(AccountCheckOrigin source) {
    	AccountCheckClean target = new AccountCheckClean();
    	BeanUtils.copyProperties(source, target);
    	return target;
    }
}
  
