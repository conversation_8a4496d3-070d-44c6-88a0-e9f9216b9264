package cn.hydee.middle.business.order.mapper;

import cn.hydee.middle.business.order.entity.DsStoreSoundConfig;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import org.apache.ibatis.annotations.Param;
import org.springframework.stereotype.Repository;

/**
 * <p>
 * 声音设置表 Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2019-12-31
 */
@Repository
public interface DsStoreSoundConfigRepo extends BaseMapper<DsStoreSoundConfig> {

    DsStoreSoundConfig getDsStoreSoundConfig(@Param("merCode") String merCode,
                                             @Param("platformCode") String platformCode,
                                             @Param("onlineClientCode") String onlineClientCode,
                                             @Param("onlineStoreCode") String onlineStoreCode);

}
