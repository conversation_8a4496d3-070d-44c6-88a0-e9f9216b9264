package cn.hydee.middle.business.order.repository.fence;

import cn.hutool.core.collection.CollectionUtil;
import cn.hydee.middle.business.order.entity.DsStoreFenceSyncConfig;
import cn.hydee.middle.business.order.mapper.DsStoreFenceSyncConfigMapper;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import java.util.Collections;
import java.util.List;
import lombok.AllArgsConstructor;
import org.springframework.stereotype.Repository;
import org.springframework.transaction.annotation.Transactional;

/**
 * <AUTHOR>
 * @date 2024/06/24 15:14
 **/
@Repository
@AllArgsConstructor
public class DsStoreFenceSyncConfigRepository {

    private final DsStoreFenceSyncConfigMapper dsStoreFenceSyncConfigMapper;


    public void upEnableByStoreCodes(List<String> storeCode, String enable) {
        LambdaUpdateWrapper<DsStoreFenceSyncConfig> updateWrapper = new LambdaUpdateWrapper<DsStoreFenceSyncConfig>().set(
                DsStoreFenceSyncConfig::getEnable, enable)
            .in(DsStoreFenceSyncConfig::getStoreCode, storeCode);
        dsStoreFenceSyncConfigMapper.update(null, updateWrapper);
    }

    public List<DsStoreFenceSyncConfig> listByStoreCodes(List<String> storeCodes) {
        if (CollectionUtil.isEmpty(storeCodes)) {
            return Collections.emptyList();
        }
        return dsStoreFenceSyncConfigMapper.selectList(new LambdaQueryWrapper<DsStoreFenceSyncConfig>().in(DsStoreFenceSyncConfig::getStoreCode, storeCodes));
    }

    @Transactional
    public void batchSave(List<DsStoreFenceSyncConfig> dsStoreFenceSyncConfigs) {
        if (CollectionUtil.isEmpty(dsStoreFenceSyncConfigs)) {
            return;
        }
         dsStoreFenceSyncConfigs.forEach(dsStoreFenceSyncConfigMapper::insert);
    }

    @Transactional
    public void batchUpdate(List<DsStoreFenceSyncConfig> dsStoreFenceSyncConfigs) {
        if (CollectionUtil.isEmpty(dsStoreFenceSyncConfigs)) {
            return;
        }
        dsStoreFenceSyncConfigs.forEach(dsStoreFenceSyncConfigMapper::updateById);
    }

    public DsStoreFenceSyncConfig getByStoreCode(String storeCode) {
        return dsStoreFenceSyncConfigMapper.selectOne(new LambdaQueryWrapper<DsStoreFenceSyncConfig>().eq(DsStoreFenceSyncConfig::getStoreCode, storeCode));
    }
}

