package cn.hydee.middle.business.order.v2.service.impl;

import cn.hydee.middle.business.order.Enums.DsConstants;
import cn.hydee.middle.business.order.Enums.DsErrorType;
import cn.hydee.middle.business.order.Enums.OrderStateEnum;
import cn.hydee.middle.business.order.autopick.enums.PickTypeEnum;
import cn.hydee.middle.business.order.domain.OrderInfoAllDomain;
import cn.hydee.middle.business.order.dto.req.OrderHandleReqDto;
import cn.hydee.middle.business.order.dto.req.OrderPickInfoReqDto;
import cn.hydee.middle.business.order.dto.req.RobotAutoPickConfirmReqDto;
import cn.hydee.middle.business.order.dto.rsp.OnlineStoreInfoRspDto;
import cn.hydee.middle.business.order.entity.OrderDeliveryRecord;
import cn.hydee.middle.business.order.entity.OrderDetail;
import cn.hydee.middle.business.order.interceptor.GlobalInterceptor;
import cn.hydee.middle.business.order.util.ExceptionUtil;
import cn.hydee.middle.business.order.v2.annotation.OrderLock;
import cn.hydee.middle.business.order.v2.manager.ErpBillManager;
import cn.hydee.middle.business.order.v2.manager.OrderPickManager;
import cn.hydee.middle.business.order.v2.manager.base.BaseInfoManager;
import cn.hydee.middle.business.order.v2.manager.base.OrderBasicManager;
import cn.hydee.middle.business.order.v2.manager.base.OrderDeliveryBaseManager;
import cn.hydee.middle.business.order.v2.service.PickConfirmService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;

@Slf4j
@Service("robotAutoPickConfirmService")
public class RobotAutoPickConfirmServiceImpl implements PickConfirmService {

    @Autowired
    private OrderBasicManager orderBasicManager;
    @Autowired
    private OrderDeliveryBaseManager orderDeliveryBaseManager;
    @Autowired
    private BaseInfoManager baseInfoManager;
    @Autowired
    private ErpBillManager erpBillManager;
    @Autowired
    private OrderPickManager orderPickManager;

    @Override
    @OrderLock
    // 逻辑优化，合并接口调用
    public String pickConfirm(String merCode, String userId, OrderHandleReqDto orderHandleReqBaseDto, Integer source, String orderNo) {
        // 代码迁移自 cn.hydee.middle.business.order.v2.service.impl.PickConfirmServiceImpl.pickConfirm
        String billMessage = "";
        RobotAutoPickConfirmReqDto orderHandleReqDto = (RobotAutoPickConfirmReqDto)orderHandleReqBaseDto;
        OrderInfoAllDomain orderInfo = orderBasicManager.checkOrderAllLock(orderHandleReqDto.getOrderNo(), true);
        GlobalInterceptor.tObject.set(orderInfo);
        OrderDeliveryRecord orderDeliveryRecord =
                orderDeliveryBaseManager.selectByOrderNo(orderHandleReqDto.getOrderNo());
        OnlineStoreInfoRspDto storeInfo = baseInfoManager.getOnlineStoreInfo(orderInfo.getMerCode(),
                orderInfo.getThirdPlatformCode(), orderInfo.getClientCode(), orderInfo.getOnlineStoreCode());
        if (!OrderStateEnum.UN_PICK.getCode().equals(orderInfo.getOrderState())) {
            return billMessage;
        }
        if(orderHandleReqDto.getPickDetailList().isEmpty()){
            throw ExceptionUtil.getWarnException(DsErrorType.ORDER_ERP_NOT_PICK.getCode(), "拣货集合为空");
        }
        // 设置发货平台
        String deliveryPlatformLog = orderHandleReqDto.getDeliveryPlatform();
        String deliveryPlatform =
            orderPickManager.getPickConfirmDeliveryType(orderDeliveryRecord, orderHandleReqDto.getDeliveryPlatform(),source);
        if (null != deliveryPlatform) {
            deliveryPlatformLog = deliveryPlatform;
            orderHandleReqDto.setDeliveryPlatform(deliveryPlatform);
        }

        List<OrderDetail> detailList = orderInfo.getOrderDetailList();
        List<OrderPickInfoReqDto> pickInfoReqDtoList = orderHandleReqDto.getPickDetailList();
        for (OrderDetail orderDetail : detailList) {
            // 检验商品数量是否一致
            Integer count = orderHandleReqDto.getPickDetailList().stream()
                    .filter(p -> p.getOrderDetailId().equals(orderDetail.getId())).map(OrderPickInfoReqDto::getCount)
                    .reduce(0, (a, b) -> a + b);
            if (!orderDetail.getPickCount().equals(count)) {
                throw ExceptionUtil.getWarnException(DsErrorType.PICK_COUNT_ERROR);
            }
        }
        erpBillManager.erpPick(orderInfo.getMerCode(), DsConstants.SYSTEM, orderInfo, pickInfoReqDtoList, PickTypeEnum.ROBOT_AUTO_PICK);
        orderPickManager.pickConfirmUpdateForRobot(orderInfo,orderHandleReqDto.getOrderAutoPickInfo(),pickInfoReqDtoList,deliveryPlatformLog);
        // 对接erp拣货后自动下账（满足条件时）
        //billMessage = orderPickManager.autoEnterAccountAfterPick(userId,orderInfo.getOrderNo());

        //拣货后置处理
        orderPickManager.orderPickBehindDeal(userId,merCode,orderInfo,storeInfo,orderHandleReqDto,orderDeliveryRecord);
        return billMessage;
    }
}
