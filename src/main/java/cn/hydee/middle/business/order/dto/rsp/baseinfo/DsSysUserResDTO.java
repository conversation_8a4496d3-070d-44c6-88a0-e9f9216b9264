package cn.hydee.middle.business.order.dto.rsp.baseinfo;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

@Data
public class DsSysUserResDTO {

    @ApiModelProperty(value = "商户编码")
    private String merCode;

    @ApiModelProperty(value = "用户编码")
    private String userCode;

    @ApiModelProperty(value = "账户")
    private String account;

    @ApiModelProperty(value = "密码")
    private String password;

    @ApiModelProperty(value = "头像")
    private String avatarPath;

    @ApiModelProperty(value = "手机号")
    private String mobile;

    @ApiModelProperty(value = "邮箱")
    private String mail;

    @ApiModelProperty(value = "类型（1：会员，2：运营平台，3：商户，4：合作商，5：员工）")
    private Integer userType;

    @ApiModelProperty(value = "是否是超管")
    private Integer admin;

    @ApiModelProperty(value = "状态（0：未激活，1：启用，2：禁用）")
    private Integer userStatus;
}
