/*  
 * Project Name:hydee-business-order  
 * File Name:CheckResultEnum.java  
 * Package Name:cn.hydee.middle.business.order.account.check.base.enums  
 * Date:2020年11月13日下午3:34:13  
 * Copyright (c) 2020, Shanghai Hydee Software Corp., Ltd. All Rights Reserved.  
 *  
*/  
  
package cn.hydee.middle.business.order.account.check.base.enums;  
/**  
 * ClassName:CheckResultEnum <br/>  
 * 
 * Date:     2020年11月13日 下午3:34:13 <br/>  
 * <AUTHOR>  
 */
public enum CheckResultEnum {
	
	NORMAL(0, "正常"),
	AMOUNT_DIFF(1, "金额差异"),
	UNILATERAL_THIRD(2, "三方单边"),
	UNILATERAL_OMS(3, "oms单边"),
	;

    private Integer code;
    private String msg;

    CheckResultEnum(Integer code, String msg) {
        this.code = code;
        this.msg = msg;
    }

    public Integer getCode() {
        return code;
    }

    public String getMsg() {
        return msg;
    }
}
  
