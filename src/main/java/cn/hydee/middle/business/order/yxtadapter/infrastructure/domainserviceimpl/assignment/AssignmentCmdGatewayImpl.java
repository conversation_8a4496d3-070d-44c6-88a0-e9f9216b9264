package cn.hydee.middle.business.order.yxtadapter.infrastructure.domainserviceimpl.assignment;

import cn.hydee.middle.business.order.yxtadapter.constant.AssignmentStatus;
import cn.hydee.middle.business.order.yxtadapter.domain.assignment.Assignment;
import cn.hydee.middle.business.order.yxtadapter.domainservice.assignment.gateway.AssignmentCmdGateway;
import cn.hydee.middle.business.order.yxtadapter.infrastructure.db.mysql.mapper.AssignmentEngineAssignmentMapper;
import cn.hydee.middle.business.order.yxtadapter.infrastructure.db.mysql.model.AssignmentEngineAssignment;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.Date;
import java.util.List;

/**
 * Created by Intellij IDEA.
 *
 * <AUTHOR>
 * Date:  2023/9/1
 */
@Component
public class AssignmentCmdGatewayImpl implements AssignmentCmdGateway {


    @Autowired
    private AssignmentEngineAssignmentMapper assignmentMapper;

    @Override
    public boolean updateStatusToEnqueue(Long assignmentId) {
        int num = assignmentMapper.updateStatusToEnqueue(assignmentId, AssignmentStatus.FAILED.getKey(), AssignmentStatus.ENQUEUE.getKey());
        return num > 0;
    }

    @Override
    public void updateToSuccess(Long assignmentId) {
        assignmentMapper.updateToSuccess(assignmentId, AssignmentStatus.SUCCESS.getKey(), AssignmentStatus.ENQUEUE.getValue()   );
    }

    @Override
    public void updateNextRetry(Long id, Integer retryTimes, Date nextExecuteTime, String respContent) {
        assignmentMapper.updateNextRetry(id,AssignmentStatus.FAILED.getKey(), AssignmentStatus.SUCCESS.getKey(), retryTimes, nextExecuteTime, respContent);
    }

    @Override
    public boolean removeSucceedAssignmentList(List<Long> ids) {
        int num = assignmentMapper.removeSucceedAssignmentList(ids);
        return num > 0;
    }

    @Override
    public boolean recoverIdleAssignmentList(List<Long> ids) {
        int num = assignmentMapper.recoverIdleAssignmentList(ids,AssignmentStatus.ENQUEUE.getKey(),"处理超时,回滚成失败状态.");
        return num > 0;
    }


}
