package cn.hydee.middle.business.order.dto.req;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

@Data
public class BaseInfoOrgTreeReqDto {

    @ApiModelProperty(value = "商户号")
    private String merCode;
    @ApiModelProperty(value = "机构类型(1-公司，2-门店)")
    private Integer orType;
    @ApiModelProperty(value = "部门名称")
    private String orgName;
    @ApiModelProperty(value = "组织机构根ID，没有父级传first")
    private String rootId;
    @ApiModelProperty(value = "机构名称或机构编码")
    private String searchKey;
    @ApiModelProperty(value = "状态：0-禁用，1-启用")
    private Integer status;
    @ApiModelProperty(value = "用户Id为空表示不用鉴权")
    private String userId;

}
