package cn.hydee.middle.business.order.service;

import cn.hydee.middle.business.order.dto.req.QueryOrganizationReqDto;
import cn.hydee.middle.business.order.dto.req.VerifyReqDto;
import cn.hydee.middle.business.order.dto.req.batch.BaseStoreBatchReqDto;
import cn.hydee.middle.business.order.dto.rsp.AvailablePCAORspDTO;
import cn.hydee.middle.business.order.dto.rsp.AvailablePCARspDTO;
import cn.hydee.middle.business.order.dto.rsp.VerifyRspDto;
import cn.hydee.middle.business.order.dto.rsp.baseinfo.StoreResDTO;
import io.micrometer.core.lang.Nullable;

import java.util.Date;
import java.util.List;

/**
 * cn.hydee.middle.business.order.service
 *
 * <AUTHOR> <PERSON>
 * @version 1.0
 * @date 2020/5/11 9:28
 **/
public interface VerifyService {

    List<String> verifyPlatformCodeAndGet(String merCode, String userId, List<String> platformCodeList);

    /**
     * 过滤门店信息，有权限的才给操作
     */
    List<BaseStoreBatchReqDto.BaseStore> storeInfoFilter(String userId,
                                                         String merCode,
                                                         List<BaseStoreBatchReqDto.BaseStore> storeInfo);
    /**
     * 批量获取用户所属的机构
     * @param merCode
     * @param userId
     * @return
     */
    List<String> storeInfoFilter(String merCode,String userId);

    /** 验证并获取机构 **/
    VerifyRspDto verifyOrganizationAndGet(VerifyReqDto reqDto);

    /** 封装验证时间与判断是否属于该机构 返回机构和时间 **/
    VerifyRspDto verifyTimeAndOrganization(VerifyReqDto reqDto);

    /**  验证时间是否符合业务规则 最初开发所做 只支持月的时间返回获取**/
    VerifyRspDto verifyTime(@Nullable Date beginTime, @Nullable Date endTime);

    /** 验证用户是否属于该机构 **/
    Boolean verifyOrganization(@Nullable String merCode,@Nullable String organization,@Nullable String userId,@Nullable Integer flag);

    /** 获取用户所属机构列表 **/
    List<String> verifyOrganizationList(@Nullable String merCode,@Nullable String organization,@Nullable String userId,@Nullable Integer flag);

    /**  验证时间是否符合业务规则并更新时间、返回时间（某个时间段的时间） 即获取年/月/日/时  **/
    VerifyRspDto verifyTimeToGet(Date beginTime, Date endTime,Integer type, Integer year, Integer month, Integer day, Integer hour);

    AvailablePCAORspDTO getPCAO(String merCode, String userId);

    AvailablePCARspDTO getPCA(String merCode, String userId);

    List<AvailablePCAORspDTO.Organization> getAllOrganizations(String merCode, String userId);

    List<AvailablePCAORspDTO.Organization> getOrganizationsWithPCA(String merCode, String userId, QueryOrganizationReqDto reqDto);

    List<StoreResDTO> getAllStoreResDTOs(String merCode, String userId);

    List<String> verifyOrgList(String merCode, String userId, List<String> organizationCodeList);
}
