package cn.hydee.middle.business.order.service;

import cn.hydee.middle.business.order.domain.ErpOrderInfoAllDomain;
import cn.hydee.middle.business.order.dto.req.AddErpOrderInfoReqDto;
import cn.hydee.middle.business.order.dto.req.ErpIntentSelectReqDto;
import cn.hydee.middle.business.order.dto.req.SearchOrderErpInfoDtoReq;
import cn.hydee.middle.business.order.entity.ErpOrderInfo;
import com.baomidou.mybatisplus.core.metadata.IPage;

import java.util.List;

/**
 * <p>
 * ERP会员订单信息 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2020-05-09
 */
public interface ErpOrderInfoService {

    void saveErpOrder(List<AddErpOrderInfoReqDto> addErpOrderInfoReqDto);

    /** 根据时间段查询ERP会员订单信息 */
    ErpOrderInfoAllDomain queryErpOrderByTime(String merCode,ErpIntentSelectReqDto erpIntentSelectReqDto);

    /**分页获取erpOrderInfo 信息**/
    IPage<ErpOrderInfo> queryErpOrderByTimeWithPage(SearchOrderErpInfoDtoReq req);

    /**根据lastId查询数据信息**/
    List<ErpOrderInfo> selectErpOrderByLastId(SearchOrderErpInfoDtoReq req);

    /**定时同步数据到ES**/
    void syncErpOrder2Es();

    /**定时创建下个月的index（）**/
    void createNextMonthIndex();
}
