package cn.hydee.middle.business.order.dto.req.baseinfo;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.Valid;
import javax.validation.constraints.NotNull;

/**
 * <AUTHOR>
 * @date 2020/11/20
 */
@Data
public class DeliveryStoreReqDTO {

    @NotNull(message = "平台编码不能为空")
    @ApiModelProperty(value = "平台编码不能为空",required = true)
    private String platformCode;

    @NotNull(message = "配送网店编码不能为空")
    @ApiModelProperty(value = "配送网店编码不能为空",required = true)
    private String deliveryClientCode;

    @ApiModelProperty(value = "配送网店名称")
    private String deliveryClientName;

}
