package cn.hydee.middle.business.order.util;

import org.springframework.stereotype.Component;
import org.springframework.util.DigestUtils;

import java.util.*;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2019/12/25 19:15
 */
@Component
public class SignUtil {

    public static String netSign(String sessionKey, Map<String,Object> map) {
        SortedSet<String> sortedSet = new TreeSet<>();
        for (String key : map.keySet()){
            sortedSet.add(key);
        }

        StringBuilder sb = new StringBuilder();
        sb.append(sessionKey);
        Iterator<String> it = sortedSet.iterator();
        while(it.hasNext()){
            String e = it.next();
            sb.append(e);
            sb.append(map.get(e));
        }
        sb.append(sessionKey);
        String md5 = DigestUtils.md5DigestAsHex(sb.toString().getBytes());
        return md5.toUpperCase();
    }

    public static String sign(String sessionKey, Map<String,Object> map) {
        SortedSet<String> sortedSet = new TreeSet<>();
        for (String key : map.keySet()){
            sortedSet.add(key);
        }

        StringBuilder sb = new StringBuilder();
        sb.append(sessionKey);
        Iterator<String> it = sortedSet.iterator();
        while(it.hasNext()){
            String e = it.next();
            sb.append(e);
            sb.append(map.get(e));
        }
        sb.append(sessionKey);
        String md5 = DigestUtils.md5DigestAsHex(sb.toString().getBytes());
        return md5.toUpperCase();
    }

    /**
     * byte数组转hex
     * @param bytes
     * @return
     */
    public static String byteToHex(byte[] bytes){
        String strHex = "";
        StringBuilder sb = new StringBuilder("");
        for (int n = 0; n < bytes.length; n++) {
            strHex = Integer.toHexString(bytes[n] & 0xFF);
            //sb.append((strHex.length() == 1) ? "0" + strHex : strHex); // 每个字节由两个字符表示，位数不够，高位补0
            sb.append(strHex);
        }
        return sb.toString().trim();
    }

    public static void main(String[]args) {
        Map<String,Object> map = new HashMap<>();
        map.put("method","hems.item.category.batch.add");
        map.put("groupid","kfzy");
        map.put("eccode","11");
        map.put("clientid","kfzy01");
        map.put("timestamp","2019-12-16 9:56:00");
        map.put("v","1.0");
        map.put("body","11111");
        String res = netSign("c20c4a59abe04445bb6d5ccf3d8c5e16", map);
        System.out.println(res);


    }
}
