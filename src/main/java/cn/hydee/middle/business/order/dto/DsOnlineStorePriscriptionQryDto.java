package cn.hydee.middle.business.order.dto;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

@Data
public class DsOnlineStorePriscriptionQryDto {

    @ApiModelProperty(value = "商户编码")
    private String merCode;
    @ApiModelProperty(value = "平台编码")
    private String platformCode;
    @ApiModelProperty(value = "网店编码")
    private String onlineClientCode;
    @ApiModelProperty(value = "门店编码")
    private String onlineStoreCode;

}
