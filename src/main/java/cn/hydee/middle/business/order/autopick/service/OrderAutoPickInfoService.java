package cn.hydee.middle.business.order.autopick.service;

import cn.hydee.middle.business.order.autopick.entity.OrderAutoPickInfo;
import com.baomidou.mybatisplus.extension.service.IService;

import java.util.List;

/**
 * <p>
 * 订单自动拣货信息 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2022-07-11
 */
public interface OrderAutoPickInfoService extends IService<OrderAutoPickInfo> {

    /**
    * @Description: 录入或更新数据
    * @Param: [dataList]
    * @return: void
    * @Author: syu
    * @Date: 2022-7-11
    */
    void saveUpdateData(List<OrderAutoPickInfo> dataList);

    /**
    * @Description: 根据订单号查询数据
    * @Param: [orderNo]
    * @return: cn.hydee.middle.business.order.autopick.entity.OrderAutoPickInfo
    * @Author: syu
    * @Date: 2022-7-12
    */
    OrderAutoPickInfo queryByOrderNo(Long orderNo);
}
