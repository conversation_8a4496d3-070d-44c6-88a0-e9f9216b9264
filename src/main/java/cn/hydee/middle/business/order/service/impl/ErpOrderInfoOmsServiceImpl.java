package cn.hydee.middle.business.order.service.impl;

import cn.hydee.middle.business.order.entity.ErpOrderInfoOms;
import cn.hydee.middle.business.order.entity.TmpErpAccount;
import cn.hydee.middle.business.order.mapper.ErpOrderInfoOmsMapper;
import cn.hydee.middle.business.order.service.ErpOrderInfoOmsService;
import cn.hydee.middle.business.order.service.TmpErpAccountServiceImpl;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @date 2021年03月26日 18:17
 */
@Service("erpOrderInfoOmsService")
public class ErpOrderInfoOmsServiceImpl extends ServiceImpl<ErpOrderInfoOmsMapper, ErpOrderInfoOms> implements ErpOrderInfoOmsService {
    @Autowired
    private TmpErpAccountServiceImpl tmpErpAccountService;

    @Override
    public List<ErpOrderInfoOms> getErpOrderInfoOmsOnTimeRange(long begin, long end) {
        return baseMapper.getRecordsOnTimeRange(begin, end);
    }

    @Override
    public void insertErpTmpRecords(List<ErpOrderInfoOms> items) {
        // 再查一次，捞全量记录. 合并可能的退款单下账，结果进临时表 tmp_erp_account
        List<Long> orderNoList = items.stream().map(ErpOrderInfoOms::getOrderNo).collect(Collectors.toList());
        List<TmpErpAccount> tmpErpAccounts = new ArrayList<>();

        List<ErpOrderInfoOms> erpOrderInfoOmsList = baseMapper.selectList(new QueryWrapper<ErpOrderInfoOms>().lambda().in(ErpOrderInfoOms::getOrderNo, orderNoList));
        Map<Long, List<ErpOrderInfoOms>> collect = erpOrderInfoOmsList.stream().collect(Collectors.groupingBy(ErpOrderInfoOms::getOrderNo));
        for (Map.Entry<Long, List<ErpOrderInfoOms>> longListEntry : collect.entrySet()) {
            List<ErpOrderInfoOms> ErpOrderInfoOmsList = longListEntry.getValue();
            TmpErpAccount tmpErpAccount = new TmpErpAccount();
            ErpOrderInfoOms newRecord = ErpOrderInfoOmsList.get(0);
            if (ErpOrderInfoOmsList.size() > 1) {
                BigDecimal totalFee = ErpOrderInfoOmsList.stream().map(ErpOrderInfoOms::getTotalFee).reduce(BigDecimal.ZERO, BigDecimal::add);
                newRecord.setTotalFee(totalFee);
            }
            BeanUtils.copyProperties(newRecord,tmpErpAccount);
            tmpErpAccounts.add(tmpErpAccount);
        }
        tmpErpAccountService.saveBatchIgnore(tmpErpAccounts);
    }

    @Override
    public List<TmpErpAccount> getTmpErpAccountsOnTimeRange(long begin, long end) {
        return tmpErpAccountService.getRecordsOnTimeRange(begin, end);
    }

    @Override
    public ErpOrderInfoOms selectReBillByOrderNo(Long orderNo) {
        ErpOrderInfoOms erpOrderInfoOms = baseMapper.selectReBillByOrderNo(orderNo);
        return erpOrderInfoOms;
    }

    @Override
    public TmpErpAccount getTmpErpAccountByOrderNo(Long orderNo) {
        List<ErpOrderInfoOms> erpOrderInfoOmsList = baseMapper.selectList(new QueryWrapper<ErpOrderInfoOms>().lambda().eq(ErpOrderInfoOms::getOrderNo, orderNo));
        Map<Long, List<ErpOrderInfoOms>> collect = erpOrderInfoOmsList.stream().collect(Collectors.groupingBy(ErpOrderInfoOms::getOrderNo));
        for (Map.Entry<Long, List<ErpOrderInfoOms>> longListEntry : collect.entrySet()) {
            List<ErpOrderInfoOms> ErpOrderInfoOmsList = longListEntry.getValue();
            TmpErpAccount tmpErpAccount = new TmpErpAccount();
            ErpOrderInfoOms newRecord = ErpOrderInfoOmsList.get(0);
            if (ErpOrderInfoOmsList.size() > 1) {
                BigDecimal totalFee = ErpOrderInfoOmsList.stream().map(ErpOrderInfoOms::getTotalFee).reduce(BigDecimal.ZERO, BigDecimal::add);
                newRecord.setTotalFee(totalFee);
            }
            BeanUtils.copyProperties(newRecord,tmpErpAccount);
            return tmpErpAccount;
        }
        return null;
    }

    @Override
    public List<Long> selcetExists(List<Long> billedOrderNos) {
        return baseMapper.selcetExists(billedOrderNos);
    }

}
