package cn.hydee.middle.business.order.eventtracking.dto.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

import java.io.Serializable;
import java.util.Date;
import java.util.Objects;

/**
 * 埋点基础信息
 */
@Data
@EqualsAndHashCode(callSuper = false)
@Accessors(chain = true)
public class EventBaseInfo implements Serializable {

    private static final long serialVersionUID = 1682775056742988723L;

    @TableId(value = "id",type = IdType.AUTO)
    private Long id;

    /**
     * 应用名称
     */
    private String appName;
    /**
     * 商户号
     */
    private String merCode;
    /**
     * 用户id
     */
    private String userId;
    /**
     * sessionid
     */
    private String sessionId;
    /**
     * 设备id
     */
    private String deviceId;
    /**
     * 单页面应用唯一id，页面生命周期内不变
     */
    private String pageId;
    /**
     * 设备宽度
     */
    private Double deviceWidth;
    /**
     * 设备高度
     */
    private Double deviceHeight;
    /**
     * 页面宽度
     */
    private Double pageWidth;
    /**
     * 页面高度
     */
    private Double pageHeight;
    /**
     * SDK版本号
     */
    private String sdkVersion;
    /**
     * 浏览器
     */
    private String vendor;
    /**
     * 平台
     */
    private String platform;
    /**
     * 数据发送时间戳
     */
    private Date sendTime;

    /**
     * 创建时间
     */
    private Date createTime;
    /**
     * 修改时间
     */
    private Date modifyTime;

    public String uniqueKey(){
        return this.merCode + "_" + this.userId + "_" + this.sessionId;
    }

    @Override
    public boolean equals(Object o) {
        if (this == o) {
            return true;
        }
        if (o == null || getClass() != o.getClass()) {
            return false;
        }
        EventBaseInfo that = (EventBaseInfo) o;
        return Objects.equals(merCode, that.merCode) && Objects.equals(userId, that.userId) && Objects.equals(sessionId, that.sessionId);
    }

    @Override
    public int hashCode() {
        return Objects.hash(merCode, userId, sessionId);
    }
}
