package cn.hydee.middle.business.order.v2.manager;

import cn.hutool.core.util.StrUtil;
import cn.hydee.middle.business.order.Enums.DsConstants;
import cn.hydee.middle.business.order.Enums.PlatformCodeEnum;
import cn.hydee.middle.business.order.Enums.refundReason.MeiRefundReasonEnum;
import cn.hydee.middle.business.order.dto.req.RefundReturnOperationHandleReq;
import cn.hydee.middle.business.order.dto.rsp.OnlineStoreInfoRspDto;
import cn.hydee.middle.business.order.refund.context.RefundOrderContext;
import cn.hydee.middle.business.order.refund.handle.agree.entity.RefundHandleParams;
import cn.hydee.middle.business.order.service.SupplierCallConvert;
import cn.hydee.middle.business.order.v2.feign.hems.HemsCommonClient;
import cn.hydee.unified.model.HemsBaseData;
import cn.hydee.unified.model.aftersale.AfterSaleOpenReq;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

/**
 * 美团售后
 * @author: xin.tu
 * @date: 2023/3/29 16:59
 * @menu:
 */
@Component
public class MeiTuanAterManager {

    @Autowired
    private HemsCommonClient hemsCommonClient;

    @Autowired
    private SupplierCallConvert supplierCallConvert;

    /**
     * 同意退款/退货
     *
     * @param context
     * @param storeInfo
     * @return
     */
    public boolean notifyMeiTuanAfterAgree(RefundOrderContext context, Integer approveType, OnlineStoreInfoRspDto storeInfo) {
        String platformCode = context.getRefundOrder().getThirdPlatformCode();
        if (!PlatformCodeEnum.MEITUAN.getCode().equals(platformCode)) {
            return Boolean.FALSE;
        }
        HemsBaseData baseData = hemsCommonClient.constructHemsBaseData(context.getOrderInfo().getMerCode(), storeInfo.getPlatformCode(),
                storeInfo.getOnlineClientCode(), storeInfo.getSessionKey());
        supplierCallConvert.convertBaseData(context.getOrderInfo().getOnlineStoreCode(), baseData);
        AfterSaleOpenReq afterSaleOpenReq = new AfterSaleOpenReq();
        afterSaleOpenReq.setAfsServiceOrder(context.getRefundOrder().getThirdOrderNo());
        afterSaleOpenReq.setApproveType(approveType.toString());
        afterSaleOpenReq.setOlshop_id(context.getOrderInfo().getOnlineStoreCode());
        hemsCommonClient.afterSaleOpen(afterSaleOpenReq, baseData, context.getRefundOrder());
        return Boolean.TRUE;
    }
    /**
     * 退款拒绝
     * @param context
     * @return
     */
    public boolean notifyMeiTuanAfterReject(RefundOrderContext context,RefundHandleParams handleParams, OnlineStoreInfoRspDto storeInfo) {
        String platformCode = context.getRefundOrder().getThirdPlatformCode();
        if (!PlatformCodeEnum.MEITUAN.getCode().equals(platformCode)) {
            return Boolean.FALSE;
        }
        if (!DsConstants.INTEGER_THREE.equals(handleParams.getType())) {
            return Boolean.FALSE;
        }
        String reason = (StrUtil.isBlank(handleParams.getReasonCode()) || MeiRefundReasonEnum.OTHER.getCode().toString().equals(handleParams.getReasonCode()))
                && StringUtils.isNotBlank(handleParams.getRemark()) ? handleParams.getRemark() : handleParams.getReasonMsg();
        HemsBaseData baseData = hemsCommonClient.constructHemsBaseData(handleParams.getMerCode(), storeInfo.getPlatformCode(),
                storeInfo.getOnlineClientCode(), storeInfo.getSessionKey());
        supplierCallConvert.convertBaseData(context.getOrderInfo().getOnlineStoreCode(), baseData);
        AfterSaleOpenReq afterSaleOpenReq = new AfterSaleOpenReq();
        afterSaleOpenReq.setAfsServiceOrder(context.getRefundOrder().getThirdOrderNo());
        afterSaleOpenReq.setApproveType(DsConstants.STRING_THREE);
        afterSaleOpenReq.setRejectReason(reason);
        afterSaleOpenReq.setRejectReasonCode(handleParams.getReasonCode());
        afterSaleOpenReq.setOlshop_id(context.getOrderInfo().getOnlineStoreCode());
        hemsCommonClient.afterSaleOpen(afterSaleOpenReq,baseData, context.getRefundOrder());
        return Boolean.TRUE;
    }

    /**
     * 退货拒绝
     * @param context
     * @return
     */
    public boolean notifyMeiTuanAfterRefundGoodsReject(RefundOrderContext context, RefundReturnOperationHandleReq refundHandle, OnlineStoreInfoRspDto storeInfo) {
        String platformCode = context.getRefundOrder().getThirdPlatformCode();
        Integer afSaleType = context.getRefundOrder().getAfterSaleType();
        if (!PlatformCodeEnum.MEITUAN.getCode().equals(platformCode)) {
            return Boolean.FALSE;
        }
        if (afSaleType == null){
            return Boolean.FALSE;
        }
        if (!DsConstants.INTEGER_TWO.equals(refundHandle.getType())) {
            return Boolean.FALSE;
        }
        String reason = MeiRefundReasonEnum.OTHER.getCode().toString().equals(refundHandle.getReasonCode())
                && StringUtils.isNotBlank(refundHandle.getRemark())? refundHandle.getRemark() : refundHandle.getReasonMsg();
        HemsBaseData baseData = hemsCommonClient.constructHemsBaseData(context.getOrderInfo().getMerCode(), storeInfo.getPlatformCode(),
                storeInfo.getOnlineClientCode(), storeInfo.getSessionKey());

        supplierCallConvert.convertBaseData(context.getOrderInfo().getOnlineStoreCode(), baseData);
        AfterSaleOpenReq afterSaleOpenReq = new AfterSaleOpenReq();
        afterSaleOpenReq.setAfsServiceOrder(context.getRefundOrder().getThirdOrderNo());
        afterSaleOpenReq.setApproveType(DsConstants.STRING_THREE);
        afterSaleOpenReq.setRejectReason(reason);
        afterSaleOpenReq.setRejectReasonCode(refundHandle.getReasonCode());
        afterSaleOpenReq.setOlshop_id(context.getOrderInfo().getOnlineStoreCode());
        hemsCommonClient.afterSaleOpen(afterSaleOpenReq, baseData, context.getRefundOrder());
        return Boolean.TRUE;
    }
}
