package cn.hydee.middle.business.order.dto.rsp;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import java.math.BigDecimal;
import lombok.Data;

@Data
@ApiModel(description = "节约配送总额")
public class DeliveryFeeEconomizeTotalRspDto {

  @ApiModelProperty(value = "累计预期节省配送费")
  private BigDecimal predictEconomizeDeliveryFeeTotal;

  @ApiModelProperty(value = "累计实际节省配送费")
  private BigDecimal realEconomizeDeliveryFeeTotal;
}
