package cn.hydee.middle.business.order.v3.action.create;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.text.CharSequenceUtil;
import cn.hutool.core.util.NumberUtil;
import cn.hutool.core.util.StrUtil;
import cn.hydee.middle.business.order.Enums.*;
import cn.hydee.middle.business.order.Enums.integral.IntegralOrderTypeEnum;
import cn.hydee.middle.business.order.Enums.newcustomer.NewCustomerTypeEnums;
import cn.hydee.middle.business.order.Enums.oborder.ObOrderBusinessFlagEnum;
import cn.hydee.middle.business.order.Enums.oborder.ObOrderDirectDeliveryTypeEnum;
import cn.hydee.middle.business.order.Enums.oborder.ObOrderTypeEnum;
import cn.hydee.middle.business.order.autopick.enums.PickTypeEnum;
import cn.hydee.middle.business.order.dto.DetailsDiscount;
import cn.hydee.middle.business.order.dto.req.*;
import cn.hydee.middle.business.order.dto.rsp.CommodityRspDto;
import cn.hydee.middle.business.order.dto.rsp.OnlineStoreInfoRspDto;
import cn.hydee.middle.business.order.entity.*;
import cn.hydee.middle.business.order.entity.b2c.*;
import cn.hydee.middle.business.order.feign.MiddleIdClient;
import cn.hydee.middle.business.order.route.db.mysql.model.RouteDivideOrderLog;
import cn.hydee.middle.business.order.service.OrderAssembleCommodityRelationService;
import cn.hydee.middle.business.order.service.image.FileUpLoadService;
import cn.hydee.middle.business.order.util.CoordinateUtil;
import cn.hydee.middle.business.order.util.JsonUtil;
import cn.hydee.middle.business.order.v2.feign.hems.HemsCommonClient;
import cn.hydee.middle.business.order.v2.manager.DsOnlineStoreManager;
import cn.hydee.middle.business.order.v2.manager.base.MerchandiseBaseManager;
import cn.hydee.middle.business.order.v2.manager.base.StoreBillConfigManager;
import cn.hydee.middle.business.order.v3.biz.RelatedOrderDataBiz;
import cn.hydee.middle.business.order.v3.facade.NewOrderParamFacade;
import cn.hydee.starter.dto.ResponseBase;
import cn.hydee.starter.util.DateUtil;
import cn.hydee.unified.model.HemsBaseData;
import cn.hydee.unified.model.order.OrderConformReq;
import com.alibaba.fastjson.JSON;
import com.google.common.collect.Lists;
import com.yxt.lang.exception.YxtBizException;
import lombok.extern.slf4j.Slf4j;
import org.apache.logging.log4j.util.Strings;
import org.jetbrains.annotations.NotNull;
import org.jetbrains.annotations.Nullable;
import org.springframework.beans.BeanUtils;
import org.springframework.util.CollectionUtils;
import org.springframework.util.StringUtils;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.*;
import java.util.stream.Collectors;

@Slf4j
public abstract class AbstractCreateOrderAction implements ICreateOrder {


    @Resource
    private MiddleIdClient middleIdClient;


    @Resource
    private FileUpLoadService fileUploadService;

    @Resource
    private DsOnlineStoreManager dsOnlineStoreManager;

    @Resource
    private StoreBillConfigManager storeBillConfigManager;

    @Resource
    private MerchandiseBaseManager merchandiseBaseManager;

    @Resource
    private OrderAssembleCommodityRelationService orderAssembleCommodityRelationService;

    @Resource
    private HemsCommonClient hemsCommonClient;


    @Resource
    private RelatedOrderDataBiz relatedOrderDataBiz;


    private final List<String> deliveryTypeByMeiTuanQKP = Lists.newArrayList("4001", "4011", "4012", "4015");

    /**
     * 自动创建渠道推送的订单流程
     *
     * @param salesOrder 统一的新订单报文对象
     */
    @Override
    public void automaticallyCreatedProcess(final AddOrderInfoReqDto salesOrder) {
        // 构建新订单消息 门面类 前置校验等
        NewOrderParamFacade facade=commonlyCreatedProcess(salesOrder);
        // 执行保存流程
        saveTheOrderInfo(facade);
    }


    /**
     * 通用的保存订单流程
     *
     * @param salesOrder 统一的新订单报文对象
     */
    public NewOrderParamFacade commonlyCreatedProcess(final AddOrderInfoReqDto salesOrder) {
        // 前置校验 门店信息 商品信息等
        checkValidity(salesOrder);
        // 前置 如果订单还是接单再次调用三方的确认接单接口补偿
        orderConfirmAgain(salesOrder);
        // 判断订单类型 B2C/O2O
        // 其中包含特殊逻辑 B2C 订单需要重新设置 o2o_shop_id  clientid
        salesOrder.setServiceModeEnum(getOrderServiceMode(salesOrder));
        // 预处理 组合商品过滤 主要是拆分 更新原始请求DTO中的订单详情列表为处理后的新列表
        salesOrder.setOrderdetaillist(validateSuitDetail(salesOrder));
        // 预处理 特殊渠道台优惠分摊  美团 elm 感觉没必要
        convertDetailDiscount(salesOrder);
        // 处方图片处理 现在只有微商城
        cfpicUtlHandler(salesOrder);
        // 经纬度处理
        longLatHandler(salesOrder);
        // 转换门面类
        return buildOrderInfo(salesOrder);
    }


    /**
     * [公共实现] 保存订单信息到数据库
     * @param orderInfo 订单相关信息实体包装对象
     */
    public void saveTheOrderInfo(NewOrderParamFacade orderInfo) {
        // 执行保存动作
        saveOrderInfoHandler(orderInfo);
        // 保存订单之后执行的逻辑
        afterSaved(orderInfo);
    }



    public void afterSaved(NewOrderParamFacade orderInfo) {
        // 获取 最优配送方式
        OrderDeliveryRecord orderDeliveryRecord = orderInfo.getOrderDeliveryRecord();
        if(DeliveryTypeEnum.SELLER_SELF.getCode().equals(orderDeliveryRecord.getDeliveryType())){






        }








        // 门店转单
        // 声音广播
    }





    public void saveOrderInfoHandler(final NewOrderParamFacade orderInfo) {
        try {
            // 带分布式可重入锁和事务控制执行订单保存
            relatedOrderDataBiz.saveNewOrderInfo(orderInfo);
        } catch (YxtBizException e) {
            throw e;
        } catch (Exception e){
            // 企微告警
            throw e;
        }
    }


    private void orderConfirmAgain(AddOrderInfoReqDto salesOrder) {
        if (OrderThirdStateEnum.WAIT_TAKE.getCode().equals(salesOrder.getOmsstatus())) {

            OnlineStoreInfoRspDto onlineStoreInfoRspDto = dsOnlineStoreManager.checkValidityAndGet(salesOrder.getGroupid(), salesOrder.getEctype(), salesOrder.getClientid(), salesOrder.getO2o_shop_id());
            HemsBaseData baseData = hemsCommonClient.constructHemsBaseData(
                    salesOrder.getGroupid(), salesOrder.getEctype(),
                    salesOrder.getClientid(), onlineStoreInfoRspDto.getSessionKey());
            OrderConformReq orderConfirmReq = new OrderConformReq();
            orderConfirmReq.setConfirmType(DsConstants.STRING_THREE);
            orderConfirmReq.setDeliveryId("");
            orderConfirmReq.setOlOrderNo(salesOrder.getOlorderno());
            orderConfirmReq.setOlShopId(salesOrder.getO2o_shop_id());
            orderConfirmReq.setShopId(onlineStoreInfoRspDto.getOutShopId());
            //操作人 【京东健康必填】
            orderConfirmReq.setOperateMan("system");
            try {
                hemsCommonClient.orderConfirm(orderConfirmReq, baseData);
                salesOrder.setOmsstatus(OrderThirdStateEnum.WAIT_POST.getCode());
            } catch (Exception e) {
                log.error(
                        "SaveOrderBehindHandler:saveOrder-订单确认异常 - addOrderInfoReqDto:{}",
                        salesOrder);
            }

        }
    }


    /**
     * [公共实现] 检查订单SalesOrderDTO报文参数, 如必填项等关键参数为空, 则抛出YxtBizException异常, 并执行后续动作
     *
     * @param salesOrder 新订单报文参数DTO
     */
    public void checkValidity(AddOrderInfoReqDto salesOrder) {
        try {
            if (salesOrder == null) {
                throw new YxtBizException("订单信息不能为空");
            }
            // 检查订单报文对象的各项属性, 如检查不通过, 则将错误信息以BizException的形式抛出
            if (salesOrder.getOlorderno() == null || salesOrder.getOlorderno().isEmpty()) {
                throw new YxtBizException("订单号不能为空");
            }
            if (CollUtil.isEmpty(salesOrder.getOrderdetaillist())) {
                throw new YxtBizException("订单明细不能为空");
            }
//            OrderInfo orderInfo = orderInfoMapper.selectBaseByUnique(salesOrder.getOlorderno(), salesOrder.getEctype());
            OrderInfo orderInfo = new OrderInfo();
            if (Objects.nonNull(orderInfo)) {
                throw new YxtBizException("订单已经存在不能重复创建");
            }
            // 获取线上门店信息，包含对应的线下门店信息 并且校验门店信息
            dsOnlineStoreManager.checkValidityAndGet(salesOrder.getGroupid(), salesOrder.getEctype(), salesOrder.getClientid(), salesOrder.getO2o_shop_id());
        } catch (YxtBizException e) {
            // 检查订单信息完整性、正确性不通过时, 执行后续的动作, 例如发报警消息, 记录日志信息等
            throw e;
        }
    }


    public NewOrderParamFacade buildOrderInfo(AddOrderInfoReqDto salesOrder) {
        try {
            // 生成订单号
            final Long orderNo = middleIdClient.getId(1).get(0);

            // 查询门店下账配置 根据不同订单类型（B2C/O2O）获取账单配置
            StoreBillConfig storeBillConfig = storeBillConfigManager
                    .getStoreBillConfigByDeliveryTypeV2(salesOrder);
            // 多支付方式 设置医保标识
            buildPublicMultiPay(salesOrder, storeBillConfig);
            // 查询商品信息 拆分组合商品 设置最新订单明细
            final Map<String, CommodityRspDto> commodityRspDtoMap = getCommodityRspDtoMap(salesOrder);
            // 是否B2C 订单信息
            NewOrderParamFacade facade = new NewOrderParamFacade();
            facade.setPlatformOrderInfo(new PlatformOrderInfo());
            facade.setPlatformOrderCoupon(Lists.newArrayList());
            facade.setPlatformOrderItem(Lists.newArrayList());
            facade.setPlatformOrderPayment(new PlatformOrderPayment());
            facade.setPlatformOrderInfoExt(new PlatformOrderInfoExt());
            facade.setOmsOrderInfo(new OmsOrderInfo());

            // 单基本信息，包括订单号、下单时间等
            facade.setOrderInfo(parseToOrderInfo(salesOrder, commodityRspDtoMap, orderNo));
            // 订单详情列表，包含订单中每个商品的详细信息 + 赠品 原代码逻辑加了赠品后续考虑去掉
            facade.setOrderDetailList(parseToOrderDetail(salesOrder, commodityRspDtoMap, orderNo));
            // 订单赠品信息列表，记录订单中的赠品详情
            facade.setOrderGiftInfoList(parseToOrderGiftInfo(salesOrder, commodityRspDtoMap, orderNo));
            // 订单支付信息，详细记录订单的支付情况
            facade.setOrderPayInfo(parseToOrderPayInfo(salesOrder, orderNo));
            // 订单多支付关系表
            facade.setOrderMultiPayInfoList(parseToOrderMultiPayInfoList(salesOrder, orderNo));
            // 订单配送地址信息，记录收货人的地址详情
            facade.setOrderDeliveryAddress(parseToOrderDeliveryAddress(salesOrder, orderNo));
            // 订单配送记录，跟踪订单的配送状态和历史
            facade.setOrderDeliveryRecord(parseToOrderDeliveryRecord(salesOrder, orderNo));
            // 订单配送日志，记录配送过程中的重要事件
            facade.setOrderDeliveryLog(parseToOrderDeliveryLog(orderNo));
            // 订单处方列表，记录需要处方才能购买的商品信息
            facade.setOrderPrescriptionList(parseToOrderPrescription(salesOrder, orderNo));
            // 订单优惠券信息列表，记录订单中每个优惠券的使用情况
            facade.setOrderCouponInfoList(parseOrderCouponInfo(salesOrder, orderNo));
            // 原始第三方订单详情列表，保存未加工的第三方订单数据
            facade.setOriThirdOrderDetails(parseToOriThirdOrderDetail(salesOrder));
            // 路由分割订单日志，记录订单分割和路由的历程
            facade.setRouteDivideOrderLog(new RouteDivideOrderLog());
            // 后置 重要方法 根据下账配置 设置额外的一些金额 必须先处理 在生成下账ErpBillInfo
            setAdditionalOrderPayInfo(facade, storeBillConfig);
            // ERP系统账单信息，用于财务处理和报告
            facade.setErpBillInfo(parseToErpBillInfo(facade, storeBillConfig, orderNo));
            return facade;
        } catch (YxtBizException e) {
            // 将SalesOrderDTO转换为NewOrderParamFacade出错时执行的动作, 例如发报警消息、发送日志消息等
            throw e;
        }
    }

    /**
     * @param salesOrder
     * @return
     * @return java.util.Map<java.lang.String, cn.hydee.middle.business.order.dto.rsp.CommodityRspDto>
     * <AUTHOR>
     * @Description
     * @Date 16:07 2025/5/19
     * @Param
     **/
    @NotNull
    private Map<String, CommodityRspDto> getCommodityRspDtoMap(AddOrderInfoReqDto salesOrder) {
        List<AddOrderDetailReqDto> addOrderdetaillist = salesOrder.getOrderdetaillist();
        Set<String> erpCodeSet = addOrderdetaillist.stream()
                .map(AddOrderDetailReqDto::getOuter_iid).collect(Collectors.toSet());

        if (CollUtil.isNotEmpty(salesOrder.getOrdergiftlist())) {
            salesOrder.getOrdergiftlist().forEach(addOrderGiftReqDto -> erpCodeSet.add(addOrderGiftReqDto.getGift_outer_iid()));
        }
        Map<String, CommodityRspDto> commodityRspDtoMap = new HashMap<>();
        ResponseBase<List<CommodityRspDto>> baseCommodity = merchandiseBaseManager.batchGetCommodityInfoNoExceptionV2(salesOrder.getGroupid(), erpCodeSet);
        if (baseCommodity != null && baseCommodity.checkSuccess() && baseCommodity.getData() != null) {
            List<CommodityRspDto> commodityRspDtoList = baseCommodity.getData();
            // 拆分组合商品 设置最新订单明细
            List<CommodityRspDto> commodityRspDtos = orderAssembleCommodityRelationService.checkOrderV2(salesOrder, commodityRspDtoList);
            commodityRspDtoMap = commodityRspDtos.stream().collect(Collectors.toMap(CommodityRspDto::getErpCode, commodity -> commodity, (a, b) -> a));
        }
        return commodityRspDtoMap;
    }

    protected PlatformOrderInfo parseToPlatformOrderInfo(AddOrderInfoReqDto salesOrder) {

        return null;
    }

    protected List<PlatformOrderCoupon> parseToPlatformOrderCoupon(AddOrderInfoReqDto salesOrder) {

        return null;
    }

    protected List<PlatformOrderItem> parseToPlatformOrderItem(AddOrderInfoReqDto salesOrder) {

        return null;
    }

    protected PlatformOrderPayment parseToPlatformOrderPayment(AddOrderInfoReqDto salesOrder) {

        return null;
    }

    protected PlatformOrderInfoExt parseToPlatformOrderInfoExt(AddOrderInfoReqDto salesOrder) {

        return null;
    }

    protected OmsOrderInfo parseToOmsOrderInfo(AddOrderInfoReqDto salesOrder) {

        return null;
    }


    protected OrderInfo parseToOrderInfo(AddOrderInfoReqDto salesOrder, final Map<String, CommodityRspDto> commodityRspDtoMap,final Long orderNo) {
        OnlineStoreInfoRspDto onlineStoreInfoRspDto = dsOnlineStoreManager
                .checkValidityAndGet(salesOrder.getGroupid(), salesOrder.getEctype(), salesOrder.getClientid(), salesOrder.getO2o_shop_id());
        OrderInfo orderBase = new OrderInfo();
        // 门店信息
        orderBase.setOnlineStoreName(onlineStoreInfoRspDto.getOnlineStoreName());
        orderBase.setSourceOnlineStoreName(onlineStoreInfoRspDto.getOnlineStoreName());
        orderBase.setOrganizationCode(onlineStoreInfoRspDto.getOrganizationCode());
        orderBase.setOrganizationName(onlineStoreInfoRspDto.getOrganizationName());
        orderBase.setSourceOrganizationCode(onlineStoreInfoRspDto.getOrganizationCode());
        orderBase.setSourceOrganizationName(onlineStoreInfoRspDto.getOrganizationName());
        // 先默认初始为正常
        orderBase.setOrderNo(orderNo);
        orderBase.setOrderIsNew(DsConstants.STRING_ONE);
        orderBase.setOrderState(OrderStateEnum.UN_TAKE.getCode());
        if (OrderThirdStateEnum.WAIT_POST.getCode().equals(salesOrder.getOmsstatus())) {
            orderBase.setOrderState(OrderStateEnum.UN_PICK.getCode());
        }
        // 异常状态
        OrderLockFlagEnum lockFlag = getOrderLockFlagEnum(commodityRspDtoMap, onlineStoreInfoRspDto);

        orderBase.setLockFlag(lockFlag.getCode());
        //设置是否调用ERP接口标识 占时默认都需要调用erp
//        MerchantConfig merchantConfig = merchantConfigManager.queryMerchantConfByMerCode(orderBase.getMerCode());
//        //如果不走ERP流程 设置erpState状态为已取消
//        Integer erpState = erpStateEnum.getCode();
//        if (!IntYesNoEnum.YES.getCode().equals(orderBase.getCallErpFlag())) {
//            erpState = ErpStateEnum.CANCELED.getCode();
//        }
        orderBase.setCallErpFlag(IntYesNoEnum.YES.getCode());
        orderBase.setErpState(ErpStateEnum.WAIT_PICK.getCode());
        orderBase.setServiceMode(salesOrder.getServiceModeEnum().getCode());

        orderBase.setOrderType(OrderTypeEnum.NORMAL_ORDER.getCode());
        // 机器自动拣货订单 后续更改
        if (PickTypeEnum.ROBOT_AUTO_PICK.getCode().equals(onlineStoreInfoRspDto.getPickType())) {
            orderBase.setOrderType(OrderTypeEnum.ROBOT_AUTO_PICK.getCode());
        }
        // 付费会员订单
        if (DsConstants.VIP_ORDER.equals(salesOrder.getOrdertype())) {
            orderBase.setOrderType(OrderTypeEnum.VIP_ORDER.getCode());
        }

        orderBase.setThirdPlatformCode(salesOrder.getEctype());
        orderBase.setThirdOrderNo(salesOrder.getOlorderno());
        orderBase.setThirdOrderId(salesOrder.getOrderid());
        orderBase.setThirdOrderState(salesOrder.getStatus());
        orderBase.setOffState(Integer.parseInt(salesOrder.getOmsstatus()));

        // 商户编码统一
        orderBase.setMerCode(salesOrder.getGroupid());
        orderBase.setClientCode(salesOrder.getClientid());
        orderBase.setOnlineStoreCode(salesOrder.getO2o_shop_id());
        orderBase.setSourceOnlineStoreCode(salesOrder.getO2o_shop_id());
        // ID1019506【新增】OMS订单转HEMS发货
        if (DsConstants.INTEGER_ONE.equals(onlineStoreInfoRspDto.getAutoToHems())) {
            orderBase.setTransferDelivery(DsConstants.INTEGER_ONE);
        }

        orderBase.setBuyerRemark(salesOrder.getBuyer_memo());
        orderBase.setSellerRemark(salesOrder.getSeller_memo());
        orderBase.setBuyerMessage(salesOrder.getBuyer_message());
        orderBase.setBuyerName(salesOrder.getBuyer_nick());
        orderBase.setReceiverLat(salesOrder.getReceiver_lat());
        orderBase.setReceiverLng(salesOrder.getReceiver_lng());
        orderBase.setCreated(salesOrder.getCreated());
        orderBase.setDayNum(salesOrder.getDaynum());
        orderBase.setModified(salesOrder.getModified());
        orderBase.setDeliveryTimeType(salesOrder.getDelivery_time_type() == null ? 0 : salesOrder.getDelivery_time_type());
        String deliveryTime = null;
        if (StringUtils.hasText(salesOrder.getDelivery_time())) {
            deliveryTime = salesOrder.getDelivery_time().replace("请于", "").replace("送达", "").trim();
        }
        orderBase.setDeliveryTimeDesc(deliveryTime);
        orderBase.setPrescriptionFlag(salesOrder.getCfycheck());
        orderBase.setSelfVerifyCode(salesOrder.getSelfverifycode());

        // 初始医保订单标识
        int medicalInsurance = DsConstants.INTEGER_ZERO;
        if (!StringUtils.isEmpty(salesOrder.getExtrainfo())) {
            AddOrderInfoReqDto.ExtraInfo invoice = JSON.parseObject(salesOrder.getExtrainfo(), AddOrderInfoReqDto.ExtraInfo.class);
            orderBase.setInvoiceContent(invoice.getInvoiceContent());
            orderBase.setInvoiceTitle(invoice.getInvoiceTitle());
            orderBase.setInvoiceType(invoice.getInvoiceType());
            orderBase.setNeedInvoice(invoice.getNeedInvoice());
            orderBase.setTaxerId(invoice.getTaxerId());

            if (!StringUtils.isEmpty(invoice.getOrderEmpCode())) {
                orderBase.setPickOperatorId(invoice.getOrderEmpCode());
                orderBase.setPickOperatorName(invoice.getOrderEmpName());
            }

            // OMS接收新单时，如果是微商城订单，则需要判断是否有发货门店值，如果有，则更新为OMS订单的发货门店，如果没有，则OMS的发货门店默认为下单门店
            this.sendOrganizationHandler(salesOrder, orderBase, invoice);

            // 【*********】【新增】支持多支付方式下账  0-非医保订单,1-医保订单
            medicalInsurance = Optional.ofNullable(invoice.getMedicalInsurance()).orElse(DsConstants.INTEGER_ZERO);

            // 增加推荐人
            if (!StringUtils.isEmpty(invoice.getExtJson())) {
                orderBase.setWscExtJson(invoice.getExtJson());
            }
            // 【*********】京东到家订单为OC00006（小时购药急送），打订单打标为【京东渠道】
            if (JdHealthConstants.ONE_DINGSHIDA_FLAG.equals(invoice.getSourceFrom())) {
                orderBase.setSourceChannelType(SourceChannelType.JD_CHANNEL.getCode());
            }
        }

        //医保金额和医保订单号存在都可以认为是医保订单
        if (CharSequenceUtil.isNotEmpty(salesOrder.getMedicareOrderId())
                || salesOrder.getMedicareAmount().compareTo(BigDecimal.ZERO) > 0) {
            medicalInsurance = DsConstants.INTEGER_ONE;
        }
        orderBase.setMedicalInsurance(medicalInsurance);
        orderBase.setAcceptTime(new Date());
        orderBase.setAcceptorName("系统自动");


        //会员卡号
        orderBase.setMemberNo(salesOrder.getMembercard());
//        orderBase.setClientConfId(storeBillConfig.getStoreBillConfigId());
        // 处方订单的设置
        setIsPrescriptionInfo(salesOrder, orderBase, onlineStoreInfoRspDto);
        // 预约单
        int appointment = ObOrderTypeEnum.NORMAL_ORDER.getCode();
        if (!StringUtils.isEmpty(salesOrder.getOrdertype())
                && DsConstants.APPOINTMENT.equalsIgnoreCase(salesOrder.getOrdertype())) {
            appointment = ObOrderTypeEnum.APPOINTMENT_ORDER.getCode();
        }
        orderBase.setAppointment(appointment);
        orderBase.setAppointmentBusinessFlag(ObOrderBusinessFlagEnum.WAIT_HANDEL.getCode());
        // 新客标识
        String newCustomerFlag = NewCustomerTypeEnums.NOT_NEW_CUSTOMER.getKey();
        if (!StringUtils.isEmpty(salesOrder.getIsfirstorder())) {
            newCustomerFlag = salesOrder.getIsfirstorder();
        }
        orderBase.setNewCustomerFlag(newCustomerFlag);
        // 积分订单标识
        String integralFlag = IntegralOrderTypeEnum.NOT_INTEGRAL_ORDER.getKey();
        if (DsConstants.INTEGRAL_ORDER.equalsIgnoreCase(salesOrder.getOrdertype())) {
            integralFlag = IntegralOrderTypeEnum.INTEGRAL_ORDER.getKey();
        }
        orderBase.setIntegralFlag(integralFlag);

        // 淘宝 同城购设置 取货吗 和 daynum
        setSelfVerifyCode(salesOrder, orderBase);

        // 【【万和】【天猫同城呼叫运力】：处方订单，需要审方后再呼叫运力】 https://www.tapd.cn/61969829/prong/stories/view/1161969829001030641
        // TOP拦截标识，0不拦截，1拦截 null=平台无此标识
        if (!StringUtils.isEmpty(salesOrder.getTop_hold())) {
            orderBase.setTopHold(salesOrder.getTop_hold());
        } else {
            // 为null默认呼叫
            orderBase.setTopHold(DsConstants.STRING_ZERO);
        }
        OrderInfoExtendInfo extendInfo = new OrderInfoExtendInfo();
        extendInfo.setDeliveryType(salesOrder.getDelivery_type());
        if (!StringUtils.isEmpty(salesOrder.getDelivery_type()) && PlatformCodeEnum.MEITUAN.getCode().equals(salesOrder.getEctype())
                && deliveryTypeByMeiTuanQKP.contains(salesOrder.getDelivery_type())) {
            extendInfo.setIsMeiTuanQKP("1");
        }
        orderBase.setExtendInfo(JSON.toJSONString(extendInfo));
        orderBase.setPayTime(salesOrder.getPay_time());

        return orderBase;
    }

    /**
     * <AUTHOR>
     * @Description  门店编码为NULL 直接门店异常 无须执行下面逻辑 门店异常优先级最高
     * @Date 16:51 2025/5/22
     * @Param
     * @param commodityRspDtoMap
     * @param onlineStoreInfoRspDto
     * @return
 * @return cn.hydee.middle.business.order.Enums.OrderLockFlagEnum
     **/
    @NotNull
    private static OrderLockFlagEnum getOrderLockFlagEnum(Map<String, CommodityRspDto> commodityRspDtoMap, OnlineStoreInfoRspDto onlineStoreInfoRspDto) {
        OrderLockFlagEnum lockFlag = OrderLockFlagEnum.NOT_LOCK;
        if (Strings.isEmpty(onlineStoreInfoRspDto.getOrganizationCode())) {
            lockFlag = OrderLockFlagEnum.LOCK_SHOP_EX;
        } else if(CollUtil.isEmpty(commodityRspDtoMap)){
            lockFlag = OrderLockFlagEnum.LOCK_THIRD_COMMODITY_UNMATCH;
        } else if (CollUtil.isNotEmpty(commodityRspDtoMap)) {
            boolean isExist = commodityRspDtoMap.values().stream()
                    .anyMatch(commodityRspDto -> DsConstants.INTEGER_ZERO.equals(commodityRspDto.getExist()));
            if (isExist) {
                lockFlag = OrderLockFlagEnum.LOCK_THIRD_COMMODITY_UNMATCH;
            }
        }
        return lockFlag;
    }

    /**
     * @param salesOrder
     * @param orderBase
     * @param onlineStoreInfoRspDto
     * @return
     * <AUTHOR>
     * @Description
     * @Date 10:39 2025/5/20
     * @Param
     **/
    protected void setIsPrescriptionInfo(AddOrderInfoReqDto salesOrder, OrderInfo orderBase, OnlineStoreInfoRspDto onlineStoreInfoRspDto) {
        if (DsConstants.INTEGER_ONE.equals(salesOrder.getCfycheck())) {
            // 微商城订单进来就是审核通过了的
            if (PlatformCodeEnum.YD_JIA.getCode().equals(orderBase.getThirdPlatformCode())) {
                orderBase.setIsPrescription(DsConstants.INTEGER_ONE);
                orderBase.setPrescriptionStatus(DsConstants.INTEGER_ONE);
            }
            if (PlatformCodeEnum.JD_HEALTH.getCode().equals(orderBase.getThirdPlatformCode())) {
                orderBase.setIsPrescription(DsConstants.INTEGER_ONE);
                orderBase.setPrescriptionStatus(DsConstants.INTEGER_ONE);
            }

            // 京东健康接单处方就审核通过了
            if (!PlatformCodeEnum.YD_JIA.getCode().equals(orderBase.getThirdPlatformCode()) && DsConstants.INTEGER_ONE.equals(onlineStoreInfoRspDto.getWhetherNeedPrescription())) {
                orderBase.setIsPrescription(DsConstants.INTEGER_ONE);
                orderBase.setPrescriptionStatus(DsConstants.INTEGER_ZERO);
                orderBase.setIsPushCheck(DsConstants.INTEGER_ZERO);
            }

        }
    }


    /**
     * @param salesOrder
     * @param orderBase
     * <AUTHOR>
     * @Description 淘宝 同城购设置 取货吗 和 daynum
     * @Date 9:53 2025/5/6
     **/
    private static void setSelfVerifyCode(AddOrderInfoReqDto salesOrder, OrderInfo orderBase) {
        boolean isTaoBao = PlatformCodeEnum.INHERIT_TM_TCG.getCode().equals(salesOrder.getEctype());
        if (Boolean.TRUE.equals(isTaoBao) && !StringUtils.isEmpty(salesOrder.getOmni_package())) {
            List<AddOrderInfoReqDto.OmniPackage> sameCityShopInfoList = JsonUtil.json2ObjectList(salesOrder.getOmni_package(), AddOrderInfoReqDto.OmniPackage.class);
            if (CollUtil.isNotEmpty(sameCityShopInfoList)) {
                AddOrderInfoReqDto.OmniPackage sameCityShopInfo = sameCityShopInfoList.get(0);
                // 取件码
                if (sameCityShopInfo != null && orderBase != null) {
                    orderBase.setSelfVerifyCode(sameCityShopInfo.getPickup_code());
                    orderBase.setDayNum(sameCityShopInfo.getPickup_code());
                }
            }
        }

    }

    protected OrderPayInfo parseToOrderPayInfo(AddOrderInfoReqDto salesOrder, final Long orderNo) {


        OrderPayInfo orderPayInfo = new OrderPayInfo();
        orderPayInfo.setOrderNo(orderNo);
        //商品总金额
        orderPayInfo.setTotalAmount(new BigDecimal(salesOrder.getTotal_fee()));
        orderPayInfo.setPayStatus("1");
        orderPayInfo.setPayType(salesOrder.getPaytype());

        //买家实付金额
        BigDecimal customerPayment = salesOrder.getCustomerpayment() == null ? BigDecimal.ZERO
                : new BigDecimal(salesOrder.getCustomerpayment());
        //卖家到付服务费
        BigDecimal sellerCodFee = salesOrder.getSeller_cod_fee() == null ? BigDecimal.ZERO
                : new BigDecimal(salesOrder.getSeller_cod_fee());
        //买家到付服务费
        BigDecimal buyerCodFee = salesOrder.getBuyer_cod_fee() == null ? BigDecimal.ZERO
                : new BigDecimal(salesOrder.getBuyer_cod_fee());
        //商家实收金额
        BigDecimal payment = salesOrder.getPayment() == null ? BigDecimal.ZERO
                : new BigDecimal(salesOrder.getPayment());
        //商家整单优惠
        BigDecimal discountFeeSum = salesOrder.getDiscount_fee_sum() == null ? BigDecimal.ZERO
                : new BigDecimal(salesOrder.getDiscount_fee_sum());
        //商品明细折扣汇总 淘宝特殊处理
        BigDecimal discountFeeDtl = getDiscountFeeDtl(salesOrder);
        //商家配送费优惠金额
        BigDecimal merchantDeliveryFeeDiscount = salesOrder.getShop_delivery_fee() == null ? BigDecimal.ZERO
                : salesOrder.getShop_delivery_fee();
        //商家订单总优惠
        BigDecimal merchantTotalDiscountSum = discountFeeSum.add(discountFeeDtl);
        //商家订单商品优惠
        BigDecimal merchantTotalDiscountSumNotDeliveryFee = merchantTotalDiscountSum.subtract(merchantDeliveryFeeDiscount);
        //交易佣金
        BigDecimal commissionFee = salesOrder.getCommission_fee() == null ? BigDecimal.ZERO
                : new BigDecimal(salesOrder.getCommission_fee());

        //手工调整费
        BigDecimal adjustFee = salesOrder.getAdjust_fee() == null ? BigDecimal.ZERO
                : new BigDecimal(salesOrder.getAdjust_fee());
        //打包费 特殊处理 现在只有美团
        BigDecimal packageFee = getPackageFee(salesOrder);
        //配送费 特殊处理 现在只京东到家
        BigDecimal postFee = getPostFee(salesOrder);
        //代收平台费
        BigDecimal settlementAmount = salesOrder.getSettlement_amount() == null ? BigDecimal.ZERO
                : new BigDecimal(salesOrder.getSettlement_amount());
        //到付金额
        BigDecimal codPayment = salesOrder.getCod_payment() == null ? BigDecimal.ZERO
                : new BigDecimal(String.valueOf(salesOrder.getCod_payment()));
        //运费优惠
        BigDecimal postFeeDiscount = salesOrder.getPostfee_dis() == null ? BigDecimal.ZERO
                : new BigDecimal(salesOrder.getPostfee_dis());
        if (!StringUtils.isEmpty(salesOrder.getExtrainfo())) {
            AddOrderInfoReqDto.ExtraInfo extraInfo = JSON.parseObject(salesOrder.getExtrainfo(), AddOrderInfoReqDto.ExtraInfo.class);
            orderPayInfo.setHealthNum(extraInfo.getTotalActualHb() == null ? DsConstants.INTEGER_ZERO : Integer.parseInt(extraInfo.getTotalActualHb()));
            orderPayInfo.setHealthValue(extraInfo.getHealthValue());
        }

        orderPayInfo.setMedicareOrderId(salesOrder.getMedicareOrderId());
        orderPayInfo.setMedicareAmount(salesOrder.getMedicareAmount());
        orderPayInfo.setBuyerActualAmount(customerPayment);
        orderPayInfo.setSellerCodServiceFee(sellerCodFee);
        orderPayInfo.setBuyerCodServiceFee(buyerCodFee);
        orderPayInfo.setDiscountFeeDtl(discountFeeDtl);
        orderPayInfo.setPlatformDiscount(salesOrder.getDiscount_fee_eccode());
        orderPayInfo.setBrokerageAmount(commissionFee);
        orderPayInfo.setRemainBrokerageAmount(commissionFee);
        orderPayInfo.setPostFeeDiscount(postFeeDiscount);
        orderPayInfo.setManualFixAmount(adjustFee);
        orderPayInfo.setPlatformFeeCollection(settlementAmount);
        orderPayInfo.setBuyerCodAmount(codPayment);
        //记录原始包装费、配送费
        orderPayInfo.setDeliveryFee(postFee);
        orderPayInfo.setPackFee(packageFee);
        //
        orderPayInfo.setMerchantActualAmount(salesOrder.getMerchantActualAmount());
        orderPayInfo.setPlatformPackFee(BigDecimal.ZERO);
        orderPayInfo.setMerchantPackFee(BigDecimal.ZERO);
        orderPayInfo.setPlatformDeliveryFee(BigDecimal.ZERO);
        orderPayInfo.setMerchantDeliveryFee(BigDecimal.ZERO);

        orderPayInfo.setPlatformDeliveryFeeDiscount(salesOrder.getPlatform_delivery_fee());
        orderPayInfo.setMerchantDeliveryFeeDiscount(merchantDeliveryFeeDiscount);
        BigDecimal detailTotalDis = BigDecimal.ZERO;
        BigDecimal detailTotal = BigDecimal.ZERO;
        if (salesOrder.getOrderdetaillist() != null && !salesOrder.getOrderdetaillist().isEmpty()) {
            for (AddOrderDetailReqDto addDetail : salesOrder.getOrderdetaillist()) {
                detailTotal = detailTotal.add(new BigDecimal(addDetail.getTotal_fee()));
                detailTotalDis = detailTotalDis.add(new BigDecimal(addDetail.getDiscount_fee()));
            }
        }
        orderPayInfo.setDetailDiscountCollect(detailTotalDis);
        orderPayInfo.setDifferentAmount(orderPayInfo.getTotalAmount().subtract(detailTotal));
        orderPayInfo.setMerchantDiscountSum(discountFeeSum);
        orderPayInfo.setMerchantTotalDiscountSumNotDeliveryFee(merchantTotalDiscountSumNotDeliveryFee);
        orderPayInfo.setMerchantTotalDiscountSum(merchantTotalDiscountSum);
        // 订单总优惠
        orderPayInfo.setTotalDiscount(orderPayInfo.getPlatformDiscount().add(orderPayInfo.getMerchantTotalDiscountSum()));
        // 没有的字段默认为0
        orderPayInfo.setPlatformFeeCollection(BigDecimal.ZERO);

        //线上支付 支付编码默认为：809080
        if (DsConstants.STRING_ONE.equals(orderPayInfo.getPayType()) || "fixed".equals(orderPayInfo.getPayType())) {
            orderPayInfo.setPayType(DsConstants.STRING_ONE);
            orderPayInfo.setPayCode(DsConstants.PAY_ONLINE_CODE);
        }

        // 设置支付券信息
        if (!StringUtils.isEmpty(salesOrder.getExtrainfo())) {
            AddOrderInfoReqDto.ExtraInfo invoice = JSON.parseObject(salesOrder.getExtrainfo(), AddOrderInfoReqDto.ExtraInfo.class);
            if (Objects.nonNull(invoice)) {
                AddOrderInfoReqDto.ExtJson extraJson = JSON.parseObject(invoice.getExtJson(), AddOrderInfoReqDto.ExtJson.class);
                if (Objects.nonNull(extraJson) && Objects.nonNull(extraJson.getPaySaleInfo())) {
                    orderPayInfo.setPaySaleInfo(extraJson.getPaySaleInfo());
                }
            }
        }
        return orderPayInfo;
    }


//    protected void setAdditionalOrderDetailInfo(NewOrderParamFacade facade) {
//        final OrderInfo orderInfo = facade.getOrderInfo();
//        List<OrderDetail> orderDetailList = facade.getOrderDetailList();
//        ErpBillInfo erpBillInfo = facade.getErpBillInfo();
//        InnerStoreDictionary innerStoreDictionary = innerStoreDictionaryMapper.selectByOrganizationCode(orderInfo.getOrganizationCode());
//        if(innerStoreDictionary.getPosMode().equals(PosModeEnum.POS_HD_H2.getCode())){
//            BigDecimal baseAccountAmount = BigDecimal.valueOf(orderDetailList.stream()
//                    .mapToInt(OrderDetail::getCommodityCount).sum() * 0.01);
//            if(erpBillInfo.getBillTotalAmount().compareTo(baseAccountAmount) < 0){
//                log.info("商品下账金额小于0.01，总下账金额小于0.01类订单特殊处理：{}", orderNo);
//                // 海典下账：如果下账总金额小于0.01元的最小下账金额，则下账总金额与商品下账金额与下账单价置为0，走赠品默认下账0.01元逻辑
//                erpBillInfo.setBillTotalAmount(BigDecimal.ZERO);
//                erpBillInfo.setBillCommodityAmount(BigDecimal.ZERO);
//                LambdaUpdateWrapper<OrderDetail> updateWrapper = new LambdaUpdateWrapper<>();
//                updateWrapper.eq(OrderDetail::getOrderNo, orderNo);
//                updateWrapper.set(OrderDetail::getActualNetAmount, BigDecimal.ZERO);
//                updateWrapper.set(OrderDetail::getBillPrice, BigDecimal.ZERO);
//                orderDetailMapper.update(null, updateWrapper);
//            }
//        }
//    }

    /**
     * @param facade
     * @param storeBillConfig
     * @return
     * <AUTHOR>
     * @Description 设置额外的支付金额信息   final 不能在次修改除了orderPayInfo以外的数据源信息
     * @Date 16:51 2025/5/14
     **/
    protected void setAdditionalOrderPayInfo(NewOrderParamFacade facade, StoreBillConfig storeBillConfig) {
        final List<OrderDetail> orderDetailList = facade.getOrderDetailList();
        final OrderInfo orderInfo = facade.getOrderInfo();
        OrderPayInfo orderPayInfo = facade.getOrderPayInfo();
        final OrderDeliveryRecord orderDeliveryRecord = facade.getOrderDeliveryRecord();

        BigDecimal totalFeeSum = orderDetailList.stream()
                .filter(v -> v.getTotalAmount().compareTo(BigDecimal.ZERO) != 0).map(OrderDetail::getTotalAmount)
                .map(total_fee -> new BigDecimal(String.valueOf(total_fee))).reduce(BigDecimal.ZERO, BigDecimal::add);

        // 包装费
        BigDecimal platformPackFee = BigDecimal.ZERO;
        BigDecimal merchantPackFee = BigDecimal.ZERO;
        // 平台收取：OMS包装费保存在平台包装费中 商家收取：OMS包装费保存在商家包装费中
        if (DsConstants.INTEGER_ONE.equals(storeBillConfig.getPackageFeeFetch())) {
            platformPackFee = orderPayInfo.getPackFee();
        } else if (DsConstants.INTEGER_TWO.equals(storeBillConfig.getPackageFeeFetch())) {
            merchantPackFee = orderPayInfo.getPackFee();
        }


        // 配送费
        BigDecimal platformDeliveryFee = BigDecimal.ZERO;
        BigDecimal merchantDeliveryFee = BigDecimal.ZERO;

        // 平台收取：OMS配送费保存在平台配送费中 商家收取：OMS配送费保存在商家配送费中
        // 配送费值修改 ：放入实际配送费=原始配送费-配送优惠
        if (DsConstants.INTEGER_ONE.equals(storeBillConfig.getFreightFeeFetch())) {
            platformDeliveryFee = orderPayInfo.getDeliveryFee().subtract(orderPayInfo.getPlatformDeliveryFeeDiscount());
        } else if (DsConstants.INTEGER_TWO.equals(storeBillConfig.getFreightFeeFetch())) {
            merchantDeliveryFee = orderPayInfo.getDeliveryFee().subtract(orderPayInfo.getMerchantDeliveryFeeDiscount());
        }


        /** 重新计算商家实收金额 */
        BigDecimal merchantActualAmount = BigDecimal.ZERO;
        if (PlatformCodeEnum.JD_DAOJIA.getCode().equals(orderInfo.getThirdPlatformCode())) {
            // 京东到家 商家自配送时 ： 实收金额-包装费-佣金+平台优惠，
            if (Objects.equals(orderDeliveryRecord.getDeliveryType(), DeliveryTypeEnum.SELLER_SELF.getCode())
                    || Objects.equals(orderDeliveryRecord.getDeliveryType(), DeliveryTypeEnum.BUYER_SELF.getCode())) {
                merchantActualAmount = orderPayInfo.getBuyerActualAmount()
                        .subtract(orderPayInfo.getPackFee())
                        .subtract(orderPayInfo.getBrokerageAmount())
                        .add(orderPayInfo.getPlatformDiscount());
            } else {
                // 平台配送：实付金额-包装费-应收运费-佣金+平台优惠
                merchantActualAmount = orderPayInfo.getBuyerActualAmount()
                        .subtract(orderPayInfo.getPackFee())
                        .subtract(orderPayInfo.getBrokerageAmount())
                        .subtract(orderPayInfo.getDeliveryFee())
                        .add(orderPayInfo.getPlatformDiscount());
            }
        } else if (orderInfo.getThirdPlatformCode().equals(PlatformCodeEnum.MEITUAN.getCode())
                || orderInfo.getThirdPlatformCode().equals(PlatformCodeEnum.E_BAI.getCode())) {
            // 美团和饿了么 使用接口返回得数据 merchantActualAmount
            merchantActualAmount = orderPayInfo.getMerchantActualAmount();
        } else {
            //计算商家实收金额 商家实收金额 = 商品总金额 - 商家优惠金额 + 商家配送费 + 商家包装费 - 佣金 - 商品明细折扣汇总
            merchantActualAmount = merchantActualAmount
                    .add(totalFeeSum.subtract(orderPayInfo.getMerchantDiscountSum())
                            .add(merchantDeliveryFee)
                            .add(merchantPackFee)
                            .subtract(orderPayInfo.getBrokerageAmount())
                            .subtract(orderPayInfo.getDetailDiscountCollect()));
        }

        orderPayInfo.setPlatformDeliveryFee(platformDeliveryFee);
        orderPayInfo.setMerchantDeliveryFee(merchantDeliveryFee);
        orderPayInfo.setMerchantActualAmount(merchantActualAmount);
        orderPayInfo.setPlatformPackFee(platformPackFee);
        orderPayInfo.setMerchantPackFee(merchantPackFee);


        // 天猫同城购外层接受到的 discount_fee_dtl 为 0 ，需要根据orderDetail的值 汇总更新
        if (PlatformCodeEnum.INHERIT_TM_TCG.getCode().equals(orderInfo.getThirdPlatformCode())) {
            BigDecimal orderPayDiscountFeeDtl = orderDetailList.stream()
                    .map(orderDetail -> new BigDecimal(String.valueOf(orderDetail.getDiscountAmount()))).reduce(BigDecimal.ZERO, BigDecimal::add);
            orderPayInfo.setDiscountFeeDtl(orderPayDiscountFeeDtl);
        }

    }


    /**
     * 获取包装费
     * <p>
     * 根据销售订单信息计算并返回包装费如果包装费未设置，则默认为0；
     * 特别地，如果销售订单是美团单且包装费为0，则默认设置为0.5元
     *
     * @param salesOrder 销售订单信息，包含包装费和电商平台类型等信息
     * @return 计算得到的包装费
     */
    @NotNull
    private static BigDecimal getPackageFee(AddOrderInfoReqDto salesOrder) {
        // 初始化包装费，如果未设置则默认为0
        BigDecimal packageFee = salesOrder.getPackage_fee() == null ? BigDecimal.ZERO
                : new BigDecimal(salesOrder.getPackage_fee());

        // 如果是美团单且包装费为0，则设置包装费为0.5元
        if (PlatformCodeEnum.MEITUAN.getCode().equals(salesOrder.getEctype()) && packageFee.doubleValue() == 0) {
            packageFee = new BigDecimal("0.5");
        }

        // 返回计算得到的包装费
        return packageFee;
    }

    /**
     * 获取订单的配送费用
     * 此方法用于计算和获取订单中的配送费用，首先会检查订单信息中的配送费用是否为空，
     * 如果为空，则默认为0；如果不为空，则转换为BigDecimal类型以进行精确计算
     * 特别地，对于京东到家平台的订单，使用特定的配送费用字段
     *
     * @param salesOrder 销售订单信息，包含订单的各种数据
     * @return 订单的配送费用，以BigDecimal形式返回，确保计算精度
     */
    @NotNull
    private static BigDecimal getPostFee(AddOrderInfoReqDto salesOrder) {
        // 初始化配送费用，如果订单中的配送费用为空，则设为0
        BigDecimal postFee = salesOrder.getPost_fee() == null ? BigDecimal.ZERO
                : new BigDecimal(salesOrder.getPost_fee());

        // 针对京东到家平台的特殊处理逻辑，如果订单来源是京东到家，
        // 则使用该平台特定的配送费用字段进行处理
        if (PlatformCodeEnum.JD_DAOJIA.getCode().equals(salesOrder.getEctype())) {
            postFee = salesOrder.getPostfee_nodis() == null ? BigDecimal.ZERO
                    : new BigDecimal(salesOrder.getPostfee_nodis());
        }
        // 返回计算后的配送费用
        return postFee;
    }


    @NotNull
    private static BigDecimal getDiscountFeeDtl(AddOrderInfoReqDto salesOrder) {
        //商品明细折扣汇总
        BigDecimal discountFeeDtl = salesOrder.getDiscount_fee_dtl() == null ? BigDecimal.ZERO
                : new BigDecimal(salesOrder.getDiscount_fee_dtl());

        if (PlatformCodeEnum.INHERIT_TM_TCG.getCode().equals(salesOrder.getEctype())) {
            BigDecimal newDiscountFeeDtl = BigDecimal.ZERO;
            for (AddOrderDetailReqDto addOrderDetailReqDto : salesOrder.getOrderdetaillist()) {
                if (addOrderDetailReqDto.getDiscount_fee() != null) {
                    newDiscountFeeDtl = newDiscountFeeDtl.add(new BigDecimal(addOrderDetailReqDto.getDiscount_fee()));
                }
            }
            return newDiscountFeeDtl;
        }

        // 返回计算得到的包装费
        return discountFeeDtl;
    }


    protected List<OrderMultiPayInfo> parseToOrderMultiPayInfoList(AddOrderInfoReqDto salesOrder, final Long orderNo) {

        if (StringUtils.isEmpty(salesOrder.getExtrainfo())) {
            return Collections.emptyList();
        }

        AddOrderInfoReqDto.ExtraInfo extraInfo;
        try {
            extraInfo = JsonUtil.json2Object(salesOrder.getExtrainfo(), AddOrderInfoReqDto.ExtraInfo.class);
        } catch (Exception e) {
            // 记录日志并返回空列表，防止因异常导致下单失败
            log.warn("解析 extrainfo 异常: {}", e.getMessage());
            return Collections.emptyList();
        }

        List<AddOrderInfoReqDto.PayType> payDataList = extraInfo.getPayTypeList();
        if (CollectionUtils.isEmpty(payDataList)) {
            return Collections.emptyList();
        }

        return payDataList.stream()
                .filter(Objects::nonNull) // 避免 null 元素导致 NPE
                .map(payData -> {
                    OrderMultiPayInfo payInfo = new OrderMultiPayInfo();
                    payInfo.setOrderNo(orderNo);
                    payInfo.setPayCode(payData.getPayCode());
                    payInfo.setPayName(payData.getPayName());
                    payInfo.setPayPrice(payData.getPayPrice() == null ? BigDecimal.ZERO : payData.getPayPrice());
                    return payInfo;
                })
                .collect(Collectors.toList());
    }

    protected List<OrderDetail> parseToOrderDetail(AddOrderInfoReqDto salesOrder, final Map<String, CommodityRspDto> commodityRspDtoMap, final Long orderNo) {


        List<OrderDetail> orderDetailList = new ArrayList<>();
        // 设置三方订单明细id使用
        int num = 0;
        for (AddOrderDetailReqDto detailDto : salesOrder.getOrderdetaillist()) {
            OrderDetail orderDetail = new OrderDetail();
            orderDetail.setBillPrice(BigDecimal.ZERO);
            orderDetail.setOrderNo(orderNo);
            orderDetail.setPlatformSkuId(detailDto.getNum_iid());
            orderDetail.setErpCode(detailDto.getOuter_iid());
            orderDetail.setBarCode(detailDto.getUpc());
            // 默认商品不存在等待审核
            orderDetail.setStatus(OrderDetailStatusEnum.NOT_EXIST.getCode());
            // 拆零商品标示
            orderDetail.setChailing(StringUtils.isEmpty(detailDto.getChaiLingOriginalErpCode()) ? DsConstants.INTEGER_ONE : DsConstants.INTEGER_TWO);
            orderDetail.setChaiLingOriginalErpCode(detailDto.getChaiLingOriginalErpCode());


            // BigDecimal 初始化
            BigDecimal dPrice = getBigDecimalOrDefault(detailDto.getPrice(), BigDecimal.ZERO);
            BigDecimal dTotalAmount = getBigDecimalOrDefault(detailDto.getTotal_fee(), BigDecimal.ZERO);
            BigDecimal dDiscountAmount = getBigDecimalOrDefault(detailDto.getDiscount_fee(), BigDecimal.ZERO);
            BigDecimal dActualAmount = getBigDecimalOrDefault(detailDto.getPayment(), BigDecimal.ZERO);
            BigDecimal adjustAmount = getBigDecimalOrDefault(detailDto.getAdjust_fee(), BigDecimal.ZERO);

            orderDetail.setOriginalPrice(dPrice);
            orderDetail.setTotalAmount(dTotalAmount);
            // 折扣金额
            orderDetail.setDiscountAmount(dDiscountAmount);
            orderDetail.setActualAmount(dActualAmount);
            // 调整金额
            orderDetail.setAdjustAmount(adjustAmount);

            orderDetail.setCommodityName(detailDto.getTitle());
            orderDetail.setCommodityCount(parseCommodityCount(detailDto.getNum()));
            // 金额取值需要判断
            BigDecimal price = getPrice(salesOrder.getEctype(), detailDto);
            orderDetail.setPrice(price);

            //
            BigDecimal discountShare = getDiscountShare(salesOrder.getEctype(), detailDto);
            orderDetail.setDiscountShare(discountShare);


            // 是否是医保item
            orderDetail.setIsMedicareItem(detailDto.getIsMedicareItem());

            // 是否是赠品（0，不是赠品1，是赠品）
            Integer isGift = DsConstants.INTEGER_ZERO;
            if (!StringUtils.isEmpty(detailDto.getIsgift()) && DsConstants.STING_TRUE.equals(detailDto.getIsgift())) {
                isGift = DsConstants.INTEGER_ONE;
            }
            orderDetail.setIsGift(isGift);
            // 订单详情ID
            if (!Strings.isEmpty(detailDto.getOrderdetailid())) {
                orderDetail.setThirdDetailId(detailDto.getOrderdetailid());
            } else {
                num++;
                orderDetail.setThirdDetailId(String.format("%d_%d", orderNo, num));
            }

            // 预约单明细
            if (!StringUtils.isEmpty(detailDto.getOrder_store_source())) {
                // 1-云货架来源，2-DC仓来源
                orderDetail.setOriginType(Integer.valueOf(detailDto.getOrder_store_source()));
            }
            if (!StringUtils.isEmpty(detailDto.getStore_code())) {
                // 组织机构编码（云货架商家编码或DC仓编码）
                orderDetail.setStCode(detailDto.getStore_code());
            }
            if (!StringUtils.isEmpty(detailDto.getExpectdeliverytime())) {
                // 预计送达时间
                orderDetail.setExpectDeliveryTime(DateUtil.parseDate(detailDto.getExpectdeliverytime()
                        , DateUtil.CN_LONG_FORMAT));
            }
            Integer directDeliveryType = ObOrderDirectDeliveryTypeEnum.NOT_DIRECT_DELIVERY.getCode();
            if (!StringUtils.isEmpty(detailDto.getExtraInfoObj().getIsDirectDelivery())) {
                directDeliveryType = Integer.valueOf(detailDto.getExtraInfoObj().getIsDirectDelivery());
            }
            // 供应商代发标识，0-非供应商代发，1-供应商代发
            orderDetail.setDirectDeliveryType(directDeliveryType);
            orderDetail.setOriginalErpCode(detailDto.getOriginalErpCode());
            if (!StringUtils.isEmpty(detailDto.getOriginalErpCodeNum())) {
                orderDetail.setOriginalErpCodeNum(parseCommodityCount(detailDto.getOriginalErpCodeNum()));
            }
            // 健康贝
            BigDecimal healthValue = BigDecimal.ZERO;
            if (detailDto.getExtraInfoObj() != null && !StringUtils.isEmpty(detailDto.getExtraInfoObj().getHealthValue())) {
                healthValue = detailDto.getExtraInfoObj().getHealthValue();
            }
            orderDetail.setHealthValue(healthValue);
            // 商品实付金额(汇总)
            if (!StringUtils.isEmpty(detailDto.getPayment())) {
                orderDetail.setPayment(new BigDecimal(detailDto.getPayment()));
            }
            orderDetail.setDetailDiscount(detailDto.getDetailDiscount());
            //  商家优惠=明细级别的优惠 抽象
            BigDecimal merchantDiscountFee = getMerchantDiscountFee(detailDto);
            orderDetail.setMerchantDiscountFee(merchantDiscountFee);
            orderDetailList.add(orderDetail);
        }
        // 赠品信息
        List<OrderDetail> orderDetails = parseToGiftIntoOrderDetail(salesOrder, orderNo, num);
        // 重要 设置订单明细的下账金额 分摊金额 商品金额 商品实付金额(汇总) 赠品的不设置
        updateOrderDetailListAmount(orderDetailList);
        orderDetailList.addAll(orderDetails);
        // 重要-补全商品信息
        completeProductInformation(commodityRspDtoMap, orderDetailList);
        return orderDetailList;
    }


    private static void completeProductInformation(Map<String, CommodityRspDto> commodityRspDtoMap, List<OrderDetail> orderDetailList) {
        if (CollUtil.isNotEmpty(commodityRspDtoMap)) {
            // 补全商品信息
            orderDetailList.forEach(orderDetail -> {
                if (commodityRspDtoMap.containsKey(orderDetail.getErpCode())) {
                    CommodityRspDto commodityRspDto = commodityRspDtoMap.get(orderDetail.getErpCode());
                    if (Objects.nonNull(commodityRspDto) && DsConstants.INTEGER_ONE.equals(commodityRspDto.getExist())) {
                        // 根据erpCode获取条形码
                        orderDetail.setBarCode(commodityRspDto.getBarCode());
                        // 根据erpCode获取规格
                        orderDetail.setCommoditySpec(commodityRspDto.getSpecJoins());
                        // 生产商
                        orderDetail.setManufacture(commodityRspDto.getManufacture());
                        // 批准文号
                        orderDetail.setApprovalNumber(commodityRspDto.getApprovalNumber());
                        // 主图
                        orderDetail.setMainPic(commodityRspDto.getMainPic());
                        // erp商品类型，1普通商品，2erp赠品
                        orderDetail.setGoodsType(commodityRspDto.getErpGoodsType());
                        // 拆零系数
                        orderDetail.setChaiLingNum(commodityRspDto.getChiLingNum());
                        // 商品分类
                        orderDetail.setFirstTypeName(commodityRspDto.getFirstTypeName());
                        orderDetail.setSecondTypeName(commodityRspDto.getSecondTypeName());
                        orderDetail.setTypeName(commodityRspDto.getTypeName());
                        orderDetail.setStorageType(commodityRspDto.getFreightType());
                        orderDetail.setFiveClass(commodityRspDto.getFiveClass());
                        orderDetail.setFiveClassName(commodityRspDto.getFiveClassName());
                    }
                }
            });
        }
    }


    /**
     * @param orderDetailList
     * @return
     * <AUTHOR> 设置订单明细的下账金额 分摊金额 商品金额 商品实付金额(汇总)
     * @Description
     * @Date 16:12 2025/5/14
     * @Param
     **/
    private static void updateOrderDetailListAmount(List<OrderDetail> orderDetailList) {


        orderDetailList = orderDetailList.stream().sorted(Comparator.comparing(OrderDetail::getActualAmount)).collect(Collectors.toList());
        for (OrderDetail orderDetail : orderDetailList) {
            // 下账价格
            BigDecimal billPrice = BigDecimal.ZERO;
            // 商品售出单价
            BigDecimal price = orderDetail.getPrice() == null ? BigDecimal.ZERO : orderDetail.getPrice();
            // 下账金额
            BigDecimal actualNetAmount = BigDecimal.ZERO;
            BigDecimal differentShare = BigDecimal.ZERO;
            // 商家承担优惠
            BigDecimal discountShare = orderDetail.getDiscountShare() == null ? BigDecimal.ZERO : orderDetail.getDiscountShare();
            // 商品总金额
            BigDecimal totalAmount = BigDecimal.ZERO;
            if (DsConstants.INTEGER_ZERO.equals(orderDetail.getIsGift())) {
                // 计算商品金额(小计金额售出单价*数量)
                totalAmount = price.multiply(new BigDecimal(orderDetail.getCommodityCount()));

                // 计算单商品下账金额
                // 单商品下账金额 = 商品售出原单价*数量-商家承担金额
                actualNetAmount = price.multiply(new BigDecimal(orderDetail.getCommodityCount())).subtract(discountShare);
//                actualNetAmount = orderDetail.getActualAmount().subtract(discountShare);
                // 计算差异分摊
//                if (orderPayInfo.getDifferentAmount().compareTo(BigDecimal.ZERO) != 0) {
//                    differentShare =
//                            orderDetail.getTotalAmount().divide(orderPayInfo.getTotalAmount(), 2, RoundingMode.HALF_UP)
//                                    .multiply(orderPayInfo.getDifferentAmount());
//                }

                // 计算下账价格
                // 下账价格 = 下账金额/商品数量 保留两位小数，四舍五入
                BigDecimal commodityCount = new BigDecimal(orderDetail.getCommodityCount().toString());
                billPrice = actualNetAmount.divide(commodityCount, 4, RoundingMode.HALF_UP);
            }

            // 商品金额
            orderDetail.setTotalAmount(totalAmount);
            orderDetail.setPayment(totalAmount);
            // 下账金额
            orderDetail.setActualNetAmount(actualNetAmount);
            // 占时设置为O 感觉这个值不重要
            orderDetail.setDifferentShare(differentShare);
            // 下账价格
            orderDetail.setBillPrice(billPrice);
        }
    }


    // 封装 BigDecimal 创建逻辑
    private BigDecimal getBigDecimalOrDefault(String value, BigDecimal defaultValue) {
        if (value == null || value.trim().isEmpty()) {
            return defaultValue;
        }
        try {
            return new BigDecimal(value);
        } catch (NumberFormatException e) {
            return defaultValue; // 或者记录日志后返回默认值
        }
    }

    private int parseCommodityCount(String num) {
        try {
            return Integer.parseInt(num);
        } catch (NumberFormatException e) {
            return 0; // 默认值
        }
    }


    protected BigDecimal getDiscountShare(String ectype, AddOrderDetailReqDto detailDto) {
        if (PlatformCodeEnum.MEITUAN.getCode().equals(ectype) || PlatformCodeEnum.E_BAI.getCode().equals(ectype)) {
            return detailDto.getMerchantDiscount() == null ? BigDecimal.ZERO : detailDto.getMerchantDiscount();
        }
        return BigDecimal.ZERO;
    }


    protected BigDecimal getPrice(String ectype, AddOrderDetailReqDto detailDto) {
        if (PlatformCodeEnum.MEITUAN.getCode().equals(ectype) || PlatformCodeEnum.E_BAI.getCode().equals(ectype)) {
            return detailDto.getOriginPrice() == null ? BigDecimal.ZERO : detailDto.getOriginPrice();
        }
        return detailDto.getPrice() == null ? BigDecimal.ZERO : new BigDecimal(detailDto.getPrice());
    }

    protected BigDecimal getMerchantDiscountFee(AddOrderDetailReqDto detailDto) {
        return BigDecimal.ZERO;
    }


    protected List<OrderDetail> parseToGiftIntoOrderDetail(AddOrderInfoReqDto salesOrder, final Long orderNo, int num) {
        List<AddOrderGiftReqDto> ordergiftlist = salesOrder.getOrdergiftlist();
        if (CollUtil.isEmpty(ordergiftlist)) {
            return Collections.emptyList();
        }
        List<OrderDetail> orderDetailList = new ArrayList<>(ordergiftlist.size());
        for (AddOrderGiftReqDto addOrderGiftReqDto : ordergiftlist) {
            if (!StringUtils.isEmpty(addOrderGiftReqDto.getGift_outer_iid())) {
                num++;
                OrderDetail orderDetail = new OrderDetail();
                orderDetail.setOrderNo(orderNo);
                orderDetail.setBillPrice(BigDecimal.ZERO);
                // 第三方平台编码
                orderDetail.setPlatformSkuId(addOrderGiftReqDto.getGift_sku_id());
                // ERP编码
                orderDetail.setErpCode(addOrderGiftReqDto.getGift_outer_iid());
                orderDetail.setStatus(OrderDetailStatusEnum.NOT_EXIST.getCode());

                orderDetail.setCommodityName(addOrderGiftReqDto.getGift_name());

                // 安全地解析数量，默认设为 0 或抛出异常可根据业务需求调整
                int giftNum = 0;
                try {
                    giftNum = Integer.parseInt(addOrderGiftReqDto.getGift_num());
                } catch (NumberFormatException e) {
                    log.error("parseToGiftIntoOrderDetail解析数量异常", e);
                }

                orderDetail.setCommodityCount(giftNum);
                orderDetail.setIsGift(DsConstants.INTEGER_ONE);
                // 订单详情ID
                orderDetail.setThirdDetailId(orderNo + "_" + num);
                orderDetailList.add(orderDetail);
            }
        }
        return orderDetailList;
    }


    protected OrderDeliveryAddress parseToOrderDeliveryAddress(AddOrderInfoReqDto salesOrder, final Long orderNo) {
        OrderDeliveryAddress orderDeliveryAddress = new OrderDeliveryAddress();
        orderDeliveryAddress.setOrderNo(orderNo);
        orderDeliveryAddress.setReceiverName(salesOrder.getReceiver_name());
        orderDeliveryAddress.setReceiverMobile(salesOrder.getReceiver_mobile());
        orderDeliveryAddress.setReceiverTelephone(salesOrder.getReceiver_phone());
        orderDeliveryAddress.setProvince(salesOrder.getReceiver_state());
        orderDeliveryAddress.setCity(salesOrder.getReceiver_city());
        orderDeliveryAddress.setDistrict(salesOrder.getReceiver_district());
        orderDeliveryAddress.setTown(salesOrder.getReceiver_town());
        StringBuilder fullAddressBuilder = new StringBuilder();
        if (!StringUtils.isEmpty(salesOrder.getReceiver_state())) {
            fullAddressBuilder.append(salesOrder.getReceiver_state());
        }
        if (!StringUtils.isEmpty(salesOrder.getReceiver_city())) {
            fullAddressBuilder.append(salesOrder.getReceiver_city());
        }
        if (!StringUtils.isEmpty(salesOrder.getReceiver_district())) {
            fullAddressBuilder.append(salesOrder.getReceiver_district());
        }
        if (!StringUtils.isEmpty(salesOrder.getReceiver_town())) {
            fullAddressBuilder.append(salesOrder.getReceiver_town());
        }
        if (!StringUtils.isEmpty(salesOrder.getReceiver_address())) {
            fullAddressBuilder.append(salesOrder.getReceiver_address());
        }
        orderDeliveryAddress.setAddress(salesOrder.getReceiver_address());
        orderDeliveryAddress.setFullAddress(fullAddressBuilder.toString());
        // 【【O2O机器拣货】：脱敏手机号下传】https://www.tapd.cn/61969829/prong/stories/view/1161969829001057661
        if (!StringUtils.isEmpty(salesOrder.getExtrainfo())) {
            AddOrderInfoReqDto.ExtraInfo extraInfo = JSON.parseObject(salesOrder.getExtrainfo(), AddOrderInfoReqDto.ExtraInfo.class);
            orderDeliveryAddress.setPrivacyPhone(extraInfo.getPrivacy_phone());
        }
        //添加隐私信息 手机号 姓名 地址 为三方平台原始隐私地址
        orderDeliveryAddress.setReceiverNamePrivacy(salesOrder.getReceiver_name_privacy());
        orderDeliveryAddress.setReceiverPhonePrivacy(salesOrder.getReceiver_phone_privacy());
        orderDeliveryAddress.setReceiverAddressPrivacy(salesOrder.getReceiver_address_privacy());
        return orderDeliveryAddress;
    }

    protected OrderDeliveryRecord parseToOrderDeliveryRecord(AddOrderInfoReqDto salesOrder, final Long orderNo) {

        OrderDeliveryRecord orderDeliveryRecord = new OrderDeliveryRecord();
        orderDeliveryRecord.setOrderNo(orderNo);

        // 【【521220万和】【新业务】：药急送对接一个商家自配送的端口】 https://www.tapd.cn/61969829/prong/stories/view/1161969829001034007
        // 京东健康商家自配送
        String omsDeliveryType = getOmsDeliveryType(salesOrder);
        orderDeliveryRecord.setDeliveryType(omsDeliveryType);
        orderDeliveryRecord.setState(DeliveryStateEnum.UN_CALL.getCode());
        orderDeliveryRecord.setExtraInfo(salesOrder.getOmni_package());
        // 获取 初始配送 后续最优配送在审核后触发
        orderDeliveryRecord.setDeliveryPlatName(getBasicDeliveryPlatName(omsDeliveryType));
        return orderDeliveryRecord;
    }

    @NotNull
    private static String getOmsDeliveryType(AddOrderInfoReqDto salesOrder) {
        String omsDeliveryType = StringUtils.isEmpty(salesOrder.getOmsdeliverytype()) ? DsConstants.STRING_ONE : salesOrder.getOmsdeliverytype();
        if (PlatformCodeEnum.JD_HEALTH.getCode().equals(salesOrder.getEctype()) && DeliveryTypeEnum.SELLER_SELF.getCode().equals(salesOrder.getDelivery_type())) {
            omsDeliveryType = DeliveryTypeEnum.SELLER_SELF.getCode();
        }
        return omsDeliveryType;
    }


    protected OrderDeliveryLog parseToOrderDeliveryLog(Long orderNo) {
        OrderDeliveryLog orderDeliveryLog = new OrderDeliveryLog();
        //系统订单号
        orderDeliveryLog.setOrderNo(orderNo);
        //状态，包括1待接单，2待取货，3配送中，4已签收, 5已取消，6已过期，7异常
        orderDeliveryLog.setState(DeliveryStateEnum.UN_CALL.getCode());
        //描述
        orderDeliveryLog.setDescription("接单（保存订单配送记录）");

        return orderDeliveryLog;
    }

    protected List<OrderPrescription> parseToOrderPrescription(AddOrderInfoReqDto salesOrder, final Long orderNo) {
        if (CollUtil.isEmpty(salesOrder.getOrderprescriptionlist())) {
            return Collections.emptyList();
        }
        List<OrderPrescription> orderPrescriptionList = new ArrayList<>();
        List<AddOrderPrescriptionReqDto> addOrderPrescriptionReqDtoList = salesOrder.getOrderprescriptionlist();
        for (AddOrderPrescriptionReqDto item : addOrderPrescriptionReqDtoList) {
            Integer useAge = null;
            if (!StringUtils.isEmpty(item.getAge())) {
                useAge = Integer.valueOf(item.getAge());
            }
            OrderPrescription orderPrescription = new OrderPrescription();
            orderPrescription.setOrderNo(orderNo);
            orderPrescription.setOmsOrderNo(0L);
            orderPrescription.setBirthday(item.getBirthday());
            //V3.12.2版本 将审方图片上传到oss
            orderPrescription.setPicurl(item.getPicurl());
            orderPrescription.setEctype(item.getEctype());
            orderPrescription.setIdentityNumber(item.getIdentitynumber());
            orderPrescription.setPhoneNumber(item.getPhonenumber());
            orderPrescription.setUsedrugName(item.getUsedrugname());
            orderPrescription.setSex(item.getSex());
            orderPrescription.setStatus(0);
            if (PlatformCodeEnum.YD_JIA.getCode().equals(salesOrder.getEctype())) {
                //微商城单子先在药师云审方通过后才会进订单中通，所以默认审方通过
                orderPrescription.setStatus(1);
            }
            orderPrescription.setUseAge(useAge);
            orderPrescription.setPrescriptionType(1);
            String cfPicUrl = item.getCfpicurl();
            if (!StringUtils.isEmpty(item.getCfpicurl())) {
                String name = salesOrder.getO2o_shop_id() + "_" + orderPrescription.getUsedrugName() + "_" + PlatformCodeEnum.getNameByCode(salesOrder.getEctype())
                        + "_" + salesOrder.getDaynum() + "_" + cn.hutool.core.date.DateUtil.formatDate(new Date()) + "_" + new Random().nextInt(10000);

                // 【*********】兼容多组处方图片场景
                cfPicUrl = fileUploadService.uploadForMultiPic(item.getCfpicurl(), name);
            }
            orderPrescription.setCfpicurl(cfPicUrl);
            orderPrescription.setLastPushTime(cn.hydee.middle.business.order.util.DateUtil.parseStrToDate("2000-3-10 20:23:11", cn.hydee.middle.business.order.util.DateUtil.DATE_TIME_FORMAT_YYYY_MM_DD_HH_MI_SS));
            orderPrescriptionList.add(orderPrescription);
        }
        return orderPrescriptionList;
    }

    protected List<OrderGiftInfo> parseToOrderGiftInfo(AddOrderInfoReqDto salesOrder, final Map<String, CommodityRspDto> commodityRspDtoMap, final Long orderNo) {

        List<OrderGiftInfo> orderGiftInfoList = new ArrayList<>();
        if (CollUtil.isEmpty(salesOrder.getOrdergiftlist())) {
            return Collections.emptyList();
        }
        for (AddOrderGiftReqDto addOrderGiftReqDto : salesOrder.getOrdergiftlist()) {
            if (StringUtils.isEmpty(addOrderGiftReqDto.getGift_outer_iid())) {
                OrderGiftInfo orderGiftInfo = new OrderGiftInfo();
                orderGiftInfo.setOrderNo(orderNo);
                String giftErpCode = StringUtils.isEmpty(addOrderGiftReqDto.getGift_outer_iid()) ? "NULL" : addOrderGiftReqDto.getGift_outer_iid();
                orderGiftInfo.setGiftErpCode(giftErpCode);
                orderGiftInfo.setMainErpCode(addOrderGiftReqDto.getMain_outer_iid());
                orderGiftInfo.setGiftNum(Integer.parseInt(addOrderGiftReqDto.getGift_num()));
                orderGiftInfo.setGiftName(addOrderGiftReqDto.getGift_name());
                orderGiftInfo.setMainSkuId(addOrderGiftReqDto.getMain_sku_id());
                orderGiftInfo.setGiftSkuId(addOrderGiftReqDto.getGift_sku_id());
                if (CollUtil.isNotEmpty(commodityRspDtoMap) && commodityRspDtoMap.containsKey(addOrderGiftReqDto.getGift_outer_iid())) {
                    CommodityRspDto commodityRspDto = commodityRspDtoMap.get(addOrderGiftReqDto.getGift_outer_iid());
                    if (Objects.nonNull(commodityRspDto) && DsConstants.INTEGER_ONE.equals(commodityRspDto.getExist())) {
                        // 根据erpCode获取条形码
                        orderGiftInfo.setBarCode(commodityRspDto.getBarCode());
                        // 根据erpCode获取规格
                        orderGiftInfo.setCommoditySpec(commodityRspDto.getSpecJoins());
                        // 生产商
                        orderGiftInfo.setManufacture(commodityRspDto.getManufacture());
                        // 主图
                        orderGiftInfo.setMainPic(commodityRspDto.getMainPic());
                    }
                }
                orderGiftInfoList.add(orderGiftInfo);
            }
        }
        return orderGiftInfoList;
    }

    protected List<OrderCouponInfo> parseOrderCouponInfo(AddOrderInfoReqDto salesOrder, final Long orderNo) {
        if (StringUtils.isEmpty(salesOrder.getExtrainfo())) {
            return Collections.emptyList();
        }
        AddOrderInfoReqDto.ExtraInfo extraInfo = JSON.parseObject(salesOrder.getExtrainfo(), AddOrderInfoReqDto.ExtraInfo.class);
        List<OrderCouponInfoReqDto> couponList = extraInfo.getCouponList();
        if (CollectionUtils.isEmpty(couponList)) {
            Collections.emptyList();
        }
        List<OrderCouponInfo> orderCouponInfoList = new ArrayList<>();
        for (OrderCouponInfoReqDto coupon : couponList) {
            OrderCouponInfo orderCouponInfo = new OrderCouponInfo();
            orderCouponInfo.setCouponCode(coupon.getCouponCode());
            orderCouponInfo.setCouponContent(coupon.getCouponContent());
            orderCouponInfo.setCouponId(coupon.getCouponId());
            orderCouponInfo.setCouponName(coupon.getCouponName());
            orderCouponInfo.setCouponPrice(coupon.getCouponPrice());
            orderCouponInfo.setCouponScene(coupon.getCouponScene());
            orderCouponInfo.setOrderNo(orderNo);
            orderCouponInfo.setType(DsConstants.INTEGER_ONE);
            orderCouponInfoList.add(orderCouponInfo);
        }
        return orderCouponInfoList;
    }

    protected ErpBillInfo parseToErpBillInfo(NewOrderParamFacade facade, StoreBillConfig storeBillConfig, final Long orderNo) {

        final List<OrderDetail> orderDetailList = facade.getOrderDetailList();
        final OrderPayInfo orderPayInfo = facade.getOrderPayInfo();

        //计算商品总金额
        BigDecimal totalFeeSum = orderDetailList.stream()
                .filter(v -> v.getTotalAmount().compareTo(BigDecimal.ZERO) != 0)
                .map(OrderDetail::getTotalAmount)
                .map(total_fee -> new BigDecimal(String.valueOf(total_fee)))
                .reduce(BigDecimal.ZERO, BigDecimal::add);

        //计算商品明细的下账金额汇总
        BigDecimal billAmountSum = orderDetailList.stream()
                .filter(v -> v.getActualNetAmount().compareTo(BigDecimal.ZERO) != 0)
                .map(OrderDetail::getActualNetAmount)
                .map(billAmount -> new BigDecimal(String.valueOf(billAmount)))
                .reduce(BigDecimal.ZERO, BigDecimal::add);

        ErpBillInfo saveBillInfo = new ErpBillInfo();
        //下账配置ID
        saveBillInfo.setClientConfId(storeBillConfig.getId());
        //系统单号
        saveBillInfo.setOrderNo(orderNo);

        // 平台优惠金额: 下账+下账平台优惠金额：零售下账下传平台优惠金额 （负数下账） 下账+分摊至商品明细：零售下账不传平台优惠金额 (0)平台优惠金额按商品比例分摊  不下账：零售下账不传平台优惠金额(0)
        BigDecimal platDiscount = BigDecimal.ZERO;
        if (storeBillConfig.getPtfDiscountInventory() && !storeBillConfig.getPtfDiscountShare()) {
            platDiscount = orderPayInfo.getPlatformDiscount().negate();
        }
        saveBillInfo.setPlatformDiscount(platDiscount);

        // 商家优惠金额: 下账+下账商家优惠金额：零售下账下传商家优惠金额 （负数下账） 下账+分摊至商品明细：零售下账不传商家优惠金额 商家优惠金额按商品比例分摊  不下账：零售下账不传商家优惠金额
        BigDecimal merchantDiscount = BigDecimal.ZERO;
        if (storeBillConfig.getMcDiscountInventory() && !storeBillConfig.getMcDiscountShare()) {
            merchantDiscount = orderPayInfo.getMerchantDiscountSum().subtract(orderPayInfo.getMerchantDeliveryFeeDiscount()).negate();
        }
        saveBillInfo.setMerchantDiscount(merchantDiscount);

        // 配送费： 平台收取+不下账 零售下账不传配送费  平台收取+下账，零售下账下传配送费
        // 配送费：商家收取+不下账 零售下账不传配送费  商家收取+下账，零售下账下传配送费
        BigDecimal platformDeliveryFee = BigDecimal.ZERO;
        BigDecimal merchantDeliveryFee = BigDecimal.ZERO;
        if (DsConstants.INTEGER_ONE.equals(storeBillConfig.getFreightFeeFetch()) && storeBillConfig.getFreightFeeInventory()) {
            platformDeliveryFee = orderPayInfo.getPlatformDeliveryFee();
        }
        if (DsConstants.INTEGER_TWO.equals(storeBillConfig.getFreightFeeFetch()) && storeBillConfig.getFreightFeeInventory()) {
            merchantDeliveryFee = orderPayInfo.getMerchantDeliveryFee();
        }
        //商家配送费
        saveBillInfo.setMerchantDeliveryFee(merchantDeliveryFee);
        //平台配送费
        saveBillInfo.setPlatformDeliveryFee(platformDeliveryFee);

        // 包装费：平台收取+不下账：零售下账不传配送费  平台收取+下账：零售下账下传包装费
        // 包装费：商家收取+不下账：零售下账不传配送费  商家收取+下账：零售下账下传包装费
        BigDecimal platformPackFee = BigDecimal.ZERO;
        BigDecimal merchantPackFee = BigDecimal.ZERO;
        if (DsConstants.INTEGER_ONE.equals(storeBillConfig.getPackageFeeFetch()) && storeBillConfig.getPackageFeeInventory()) {
            platformPackFee = orderPayInfo.getPlatformPackFee();
        }
        if (DsConstants.INTEGER_TWO.equals(storeBillConfig.getPackageFeeFetch()) && storeBillConfig.getPackageFeeInventory()) {
            merchantPackFee = orderPayInfo.getMerchantPackFee();
        }
        //商家包装费
        saveBillInfo.setMerchantPackFee(merchantPackFee);
        //平台包装费
        saveBillInfo.setPlatformPackFee(platformPackFee);

        // 商品明细优惠金额  下账+下账商品商品明细优惠金额：零售下账下传商品明细优惠金额（负金额） 下账+分摊至商品明细：零售下账不传商品明细优惠金额，金额按比例分摊  不下账：零售下账不传商品明细优惠金额
        BigDecimal detailDiscountAmount = BigDecimal.ZERO;
        if (storeBillConfig.getMcDtlDiscountInventory() && !storeBillConfig.getMcDtlDiscountShare()) {
            detailDiscountAmount = orderPayInfo.getDiscountFeeDtl().negate();
        }
        //商品明细优惠金额
        saveBillInfo.setDetailDiscountAmount(detailDiscountAmount);

        // 平台收取佣金： 下账+下账平台佣金：零售下账下传平台收取佣金金额 （负数下账） 下账+分摊至商品明细：零售下账不传台收取佣金金额  台收取佣金金额 按商品比例分摊  不下账：零售下账不传台收取佣金金额
        BigDecimal brokerageAmount = BigDecimal.ZERO;
        if (storeBillConfig.getPtfCommissionInventory() && !storeBillConfig.getPtfCommissionShare()) {
            brokerageAmount = orderPayInfo.getBrokerageAmount().negate();
        }
        saveBillInfo.setPlatBrokerageAmount(brokerageAmount);

        // 订单总额 = 商品明细的商品金额汇总（商品总金额）+商家配送+平台配送费+商家包装费+平台包装费
        BigDecimal orderTotalAmount = BigDecimal.ZERO;
        orderTotalAmount = orderTotalAmount
                .add(totalFeeSum)
                .add(saveBillInfo.getMerchantDeliveryFee())
                .add(saveBillInfo.getPlatformDeliveryFee())
                .add(saveBillInfo.getMerchantPackFee())
                .add(saveBillInfo.getPlatformPackFee());
        saveBillInfo.setOrderTotalAmount(orderTotalAmount);

        // 商家实收金额 = 商品明细的下账金额汇总+商家配送+平台配送费+商家包装费+平台包装费+商家优惠金额+平台优惠金额+商品明细优惠金额+平台收取佣金
        BigDecimal merchantActualAmount = BigDecimal.ZERO;
        merchantActualAmount = merchantActualAmount
                .add(billAmountSum)
                .add(saveBillInfo.getMerchantDeliveryFee())
                .add(saveBillInfo.getPlatformDeliveryFee())
                .add(saveBillInfo.getMerchantPackFee())
                .add(saveBillInfo.getPlatformPackFee())
                .add(saveBillInfo.getMerchantDiscount())
                .add(saveBillInfo.getPlatformDiscount())
                .add(saveBillInfo.getDetailDiscountAmount())
                .add(saveBillInfo.getPlatBrokerageAmount());
        saveBillInfo.setMerchantActualAmount(merchantActualAmount);

        // 下账总金额:下账至ERP的下账总金额；下账总金额金额 = 商品明细的下账金额汇总+商家配送+平台配送费+商家包装费+平台包装费+商家优惠金额+平台优惠金额+商品明细优惠金额+平台收取佣金
        BigDecimal billTotalAmount = billAmountSum
                .add(saveBillInfo.getMerchantDeliveryFee())
                .add(saveBillInfo.getPlatformDeliveryFee())
                .add(saveBillInfo.getMerchantPackFee())
                .add(saveBillInfo.getPlatformPackFee())
                .add(saveBillInfo.getMerchantDiscount())
                .add(saveBillInfo.getPlatformDiscount())
                .add(saveBillInfo.getDetailDiscountAmount())
                .add(saveBillInfo.getPlatBrokerageAmount());

        saveBillInfo.setBillTotalAmount(billTotalAmount);
        saveBillInfo.setBillCommodityAmount(billAmountSum);
        return saveBillInfo;
    }


    /**
     * 将销售订单信息转换为原始第三方订单详情列表
     * 此方法主要用于将前端传入的销售订单信息解析成原始第三方订单详情对象列表，以便后续处理
     *
     * @param salesOrder 销售订单信息请求DTO，包含订单详细信息
     * @return 返回转换后的原始第三方订单详情列表如果输入无效或为空，则返回空列表
     */
    protected List<OriThirdOrderDetail> parseToOriThirdOrderDetail(AddOrderInfoReqDto salesOrder) {
        // 输入参数校验
        if (salesOrder == null || salesOrder.getOrderdetaillist() == null) {
            return new ArrayList<>();
        }

        List<OriThirdOrderDetail> result = new ArrayList<>();
        Date now = new Date();
        for (AddOrderDetailReqDto addOrderDetailReqDto : salesOrder.getOrderdetaillist()) {
            // 根据销售订单信息和订单详情信息生成原始第三方订单详情对象
            OriThirdOrderDetail oriThirdOrderDetail = getOriThirdOrderDetail(salesOrder, addOrderDetailReqDto, now);
            // 如果生成的原始第三方订单详情对象为空，则跳过当前循环
            if (oriThirdOrderDetail == null) continue;
            result.add(oriThirdOrderDetail);
        }
        return result;
    }

    @Nullable
    private static OriThirdOrderDetail getOriThirdOrderDetail(AddOrderInfoReqDto salesOrder, AddOrderDetailReqDto addOrderDetailReqDto, Date now) {
        try {

            if (DsConstants.STING_TRUE.equals(addOrderDetailReqDto.getIsgift())) {
                return null;
            }
            OriThirdOrderDetail oriThirdOrderDetail = new OriThirdOrderDetail();
            oriThirdOrderDetail.setNumIid(addOrderDetailReqDto.getNum_iid());
            oriThirdOrderDetail.setOuterIid(addOrderDetailReqDto.getOuter_iid());
            oriThirdOrderDetail.setUpc(addOrderDetailReqDto.getUpc());
            oriThirdOrderDetail.setTitle(addOrderDetailReqDto.getTitle());
            if (org.apache.commons.lang3.StringUtils.isNotBlank(addOrderDetailReqDto.getNum())) {
                oriThirdOrderDetail.setNum(Integer.parseInt(addOrderDetailReqDto.getNum()));
            } else {
                oriThirdOrderDetail.setNum(0);
            }
            oriThirdOrderDetail.setOid(addOrderDetailReqDto.getOid());
            oriThirdOrderDetail.setThirdDetailId(addOrderDetailReqDto.getOrderdetailid());
            oriThirdOrderDetail.setDeliveryType(salesOrder.getDelivery_type());
            oriThirdOrderDetail.setPrice(NumberUtil.toBigDecimal(addOrderDetailReqDto.getPrice()));
            oriThirdOrderDetail.setOriginalPrice(NumberUtil.toBigDecimal(addOrderDetailReqDto.getOriginPrice()));
            oriThirdOrderDetail.setPayment(NumberUtil.toBigDecimal(addOrderDetailReqDto.getPaymentPrice()));
            oriThirdOrderDetail.setCreateTime(now);
            oriThirdOrderDetail.setModifyTime(now);
            return oriThirdOrderDetail;
        } catch (YxtBizException e) {
            // 记录异常日志
            log.error("Failed to create OriThirdOrderDetail from AddOrderDetailReqDto: {}", addOrderDetailReqDto, e);
            return null; // 返回 null 表示当前条目处理失败
        }

    }


    /**
     * @param addOrderInfoReqDto
     * @return
     * <AUTHOR>
     * @Description 组合商品拆分 设置 新的商品明细信息 商品编码处理
     * @Date 17:17 2025/4/29
     **/
    private List<AddOrderDetailReqDto> validateSuitDetail(AddOrderInfoReqDto addOrderInfoReqDto) {
        List<AddOrderDetailReqDto> orderdetaillist = addOrderInfoReqDto.getOrderdetaillist();
        // 创建一个新的订单详情列表，用于存放处理后的订单详情
        List<AddOrderDetailReqDto> newDetailList = new ArrayList<>();
        for (AddOrderDetailReqDto detail : orderdetaillist) {
            // 处理空编码
            String erpCode = StringUtils.isEmpty(detail.getOuter_iid()) ? "NULL" : detail.getOuter_iid();
            detail.setOuter_iid(erpCode);
            // 如果当前订单详情没有组合商品集合，直接添加到新列表中
            if (CollectionUtils.isEmpty(detail.getOrdersuitlist())) {
                newDetailList.add(detail);
                continue;
            }
            // 尝试处理包含组合商品的订单详情，如果发生异常则记录错误日志
            try {
                newDetailList.addAll(processSuitDetails(detail));
            } catch (Exception e) {
                log.error("处理组合商品明细失败，订单详情ID: {}, 错误信息: {}", detail.getOrderdetailid(), e.getMessage(), e);
            }
        }
        // 处理空编码
        if(CollUtil.isNotEmpty(addOrderInfoReqDto.getOrdergiftlist())){
            addOrderInfoReqDto.getOrdergiftlist().forEach(gift -> {
                if (StringUtils.isEmpty(gift.getGift_outer_iid())) {
                    gift.setGift_outer_iid("NULL");
                    gift.setMain_outer_iid("NULL");
                }

            });
        }
        return newDetailList;
    }


    private List<AddOrderDetailReqDto> processSuitDetails(AddOrderDetailReqDto detail) {
        List<AddOrderDetailSuitReqDto> ordersuitlist = detail.getOrdersuitlist();
        List<AddOrderDetailReqDto> suitDetailList = new ArrayList<>();
        BigDecimal totalSuitPrice = BigDecimal.ZERO;

        int detailNum = parseDetailNum(detail.getNum());
        for (AddOrderDetailSuitReqDto suitReqDto : ordersuitlist) {
            AddOrderDetailReqDto newDetail = createNewDetail(detail, suitReqDto, detailNum);
            totalSuitPrice = totalSuitPrice.add(new BigDecimal(newDetail.getTotal_fee()));
            suitDetailList.add(newDetail);
        }

        balanceSalesPrice(
                new BigDecimal(detail.getTotal_fee()),
                new BigDecimal(detail.getDiscount_fee()),
                new BigDecimal(detail.getAdjust_fee()),
                totalSuitPrice,
                suitDetailList
        );
        return suitDetailList;
    }

    private int parseDetailNum(String numStr) {
        try {
            return Integer.parseInt(numStr);
        } catch (NumberFormatException e) {
            log.warn("解析数量失败，使用默认值1，数量字符串: {}", numStr);
            return 1;
        }
    }

    private AddOrderDetailReqDto createNewDetail(AddOrderDetailReqDto detail, AddOrderDetailSuitReqDto suitReqDto, int detailNum) {
        AddOrderDetailReqDto newDetail = new AddOrderDetailReqDto();
        BeanUtils.copyProperties(detail, newDetail);
        newDetail.setTitle(suitReqDto.getTitle());
        newDetail.setTotal_fee(StringUtils.isEmpty(suitReqDto.getTotal_fee()) ? "1" : suitReqDto.getTotal_fee());
        newDetail.setNum(String.valueOf(parseSuitNum(suitReqDto.getNum()) * detailNum));
        newDetail.setPrice(StringUtils.isEmpty(suitReqDto.getPrice()) ? "1" : suitReqDto.getPrice());
        newDetail.setNum_iid(suitReqDto.getNum_iid());
        String erpCode = StringUtils.isEmpty(suitReqDto.getOuter_iid()) ? "NULL" : suitReqDto.getOuter_iid();
        newDetail.setOuter_iid(erpCode);

        return newDetail;
    }

    private int parseSuitNum(String numStr) {
        try {
            return Integer.parseInt(numStr);
        } catch (NumberFormatException e) {
            log.warn("解析组合商品数量失败，使用默认值1，数量字符串: {}", numStr);
            return 1;
        }
    }


    /**
     * 平衡价格计算
     */
    public void balanceSalesPrice(BigDecimal totalFee, BigDecimal totalDiscount, BigDecimal totalAdjust, BigDecimal totalSuitPrice, List<AddOrderDetailReqDto> suitDetailList) {

        BigDecimal otherTotalFee = BigDecimal.ZERO;
        BigDecimal otherDiscountFee = BigDecimal.ZERO;
        BigDecimal otherAdjustFee = BigDecimal.ZERO;
        for (int i = 0; i < suitDetailList.size(); i++) {
            AddOrderDetailReqDto detail = suitDetailList.get(i);
            // 尽可能使金额接近真实值规避金额负数，调整计算精度到6位小数
            BigDecimal detailRate = new BigDecimal(detail.getTotal_fee()).divide(totalSuitPrice, 6, RoundingMode.HALF_UP);
            if (i != suitDetailList.size() - 1) {
                // 非最后一个元素
                BigDecimal detailTotalFee = scaleValue(totalFee.multiply(detailRate));
                BigDecimal discountFee = scaleValue(totalDiscount.multiply(detailRate));
                BigDecimal adjustFee = scaleValue(totalAdjust.multiply(detailRate));

                detail.setTotal_fee(detailTotalFee.toPlainString());
                detail.setDiscount_fee(discountFee.toPlainString());
                detail.setAdjust_fee(adjustFee.toPlainString());
                otherTotalFee = otherTotalFee.add(detailTotalFee);
                otherDiscountFee = otherDiscountFee.add(discountFee);
                otherAdjustFee = otherAdjustFee.add(adjustFee);
            } else {
                //最后一个
                detail.setTotal_fee(totalFee.subtract(otherTotalFee).toPlainString());
                detail.setDiscount_fee(totalDiscount.subtract(otherDiscountFee).toPlainString());
                detail.setAdjust_fee(totalAdjust.subtract(otherAdjustFee).toPlainString());
            }
            detail.setPayment(detail.getTotal_fee());
        }
    }

    // 辅助方法：缩放值到两位小数
    private BigDecimal scaleValue(BigDecimal value) {
        return value.setScale(2, RoundingMode.HALF_UP);
    }

    protected void cfpicUtlHandler(AddOrderInfoReqDto addOrderInfoReqDto) {
        // 不同渠道不同的处方图片处理 现在只有微商城
    }


    protected void sendOrganizationHandler(AddOrderInfoReqDto addOrderInfoReqDto, OrderInfo orderBase, AddOrderInfoReqDto.ExtraInfo invoice) {

    }


    /**
     * 坐标转换
     *
     * @param addOrderInfoReqDto
     */
    protected void longLatHandler(AddOrderInfoReqDto addOrderInfoReqDto) {
        //处理平安020 平安中心仓 平安城市仓 百度系坐标转换为火星坐标
        if (PlatformCodeEnum.PA_COMMON_O2O.getCode().equals(addOrderInfoReqDto.getEctype())
                || PlatformCodeEnum.PING_AN_CENTRAL.getCode().equals(addOrderInfoReqDto.getEctype())
                || PlatformCodeEnum.PA_CITY.getCode().equals(addOrderInfoReqDto.getEctype())) {
            String lng = addOrderInfoReqDto.getReceiver_lng();
            String lat = addOrderInfoReqDto.getReceiver_lat();
            if (StrUtil.isBlank(lng) || StrUtil.isBlank(lat)) {
                return;
            }
            try {
                double[] doubles = CoordinateUtil.bd09_To_Gcj02(Double.parseDouble(lng), Double.parseDouble(lat));
                addOrderInfoReqDto.setReceiver_lng(String.valueOf(doubles[0]));
                addOrderInfoReqDto.setReceiver_lat(String.valueOf(doubles[1]));
            } catch (Exception e) {
                String msg = StrUtil.format("bd09_To_Gcj02 convert fail,lng:{},lat:{}", lng, lat);
                log.error(msg, e);
            }
        }
    }


    /**
     * @param addOrderInfoReqDto
     * @return
     * <AUTHOR>
     * @Description 转换平台优惠分摊 暂只支持美团、饿百
     * @Date 15:47 2025/5/6
     * @Param
     **/
    protected void convertDetailDiscount(AddOrderInfoReqDto addOrderInfoReqDto) {
        //非美团饿百直接返回
        if (!PlatformCodeEnum.MEITUAN.getCode().equals(addOrderInfoReqDto.getEctype())
                && !PlatformCodeEnum.E_BAI.getCode().equals(addOrderInfoReqDto.getEctype())) {
            return;
        }
        List<AddOrderDetailReqDto> detailListDto = addOrderInfoReqDto.getOrderdetaillist();
        long assembleNum = detailListDto.stream().filter(item -> !StringUtils.isEmpty(item.getOriginalErpCode())).count();
        if (assembleNum > 0) {
            return;
        }
        //如果存在同编码多明细，直接返回
        long distinctSize = detailListDto.stream().map(AddOrderDetailReqDto::getOuter_iid).distinct().count();
        if (distinctSize != detailListDto.size()) {
            return;
        }
        //主表额外信息为空直接返回
        if (StringUtils.isEmpty(addOrderInfoReqDto.getExtrainfo())) {
            return;
        }
        //主表明细优惠分摊为空直接返回
        AddOrderInfoReqDto.ExtraInfo extraInfo = JSON.parseObject(addOrderInfoReqDto.getExtrainfo(), AddOrderInfoReqDto.ExtraInfo.class);
        if (StringUtils.isEmpty(extraInfo.getDetails_discount())) {
            return;
        }
        List<DetailsDiscount> detailsDiscounts = JSON.parseArray(extraInfo.getDetails_discount(), DetailsDiscount.class);
        if (CollectionUtils.isEmpty(detailsDiscounts)) {
            return;
        }
        //饿百根据三方明细id匹配
        if (PlatformCodeEnum.E_BAI.getCode().equals(addOrderInfoReqDto.getEctype())) {
            Map<String, DetailsDiscount> detailsDiscountMap = detailsDiscounts.stream()
                    .collect(Collectors.toMap(DetailsDiscount::getOrderdetailid, item -> item, (a, b) -> a));
            for (AddOrderDetailReqDto detail : detailListDto) {
                DetailsDiscount detailsDiscount = detailsDiscountMap.get(detail.getOrderdetailid());
                if (detailsDiscount == null) {
                    continue;
                }
                detail.setDetailDiscount(JSON.toJSONString(detailsDiscount));
            }
        }
        //美团根据商品编码、条码匹配
        else if (PlatformCodeEnum.MEITUAN.getCode().equals(addOrderInfoReqDto.getEctype())) {
            Map<String, DetailsDiscount> detailsDiscountMap = detailsDiscounts.stream()
                    .collect(Collectors.toMap(item -> String.format("%s_%s", item.getOutskuid(), item.getUpc()), item -> item, (a, b) -> a));
            for (AddOrderDetailReqDto detail : detailListDto) {
                DetailsDiscount detailsDiscount = detailsDiscountMap.get(String.format("%s_%s", detail.getOuter_iid(), detail.getUpc()));
                if (detailsDiscount == null) {
                    continue;
                }
                detail.setDetailDiscount(JSON.toJSONString(detailsDiscount));
            }
        }
    }


    public ServiceModeEnum getOrderServiceMode(AddOrderInfoReqDto orderMessage) {
        ServiceModeEnum serviceModeEnum = getServiceModeEnum(orderMessage);
        // B2C 订单需要替换部分值
        if (ServiceModeEnum.B2C.equals(serviceModeEnum)) {
            String onlineStoreCode = PlatformCodeEnum.YD_JIA.getCode().equals(orderMessage.getEctype()) ? DsConstants.B2C_ONLINE_STORE_CODE : orderMessage.getO2o_shop_id();
            OnlineStoreInfoRspDto onlineStoreInfoRspDto = dsOnlineStoreManager
                    .checkValidityAndGet(orderMessage.getGroupid(), orderMessage.getEctype(), orderMessage.getClientid(), onlineStoreCode);
            if (Objects.nonNull(onlineStoreInfoRspDto) && !StringUtils.isEmpty(onlineStoreInfoRspDto.getOutB2cClientId())) {
                orderMessage.setB2cClientId(onlineStoreInfoRspDto.getOutB2cClientId());
                orderMessage.setClientid(onlineStoreInfoRspDto.getOutB2cClientId());
                orderMessage.setO2o_shop_id(onlineStoreInfoRspDto.getOutB2cClientId());
                return ServiceModeEnum.B2C;
            }
        }
        return serviceModeEnum;
    }

    @NotNull
    private ServiceModeEnum getServiceModeEnum(AddOrderInfoReqDto orderMessage) {
        if (PlatformCodeEnum.YD_JIA.getCode().equals(orderMessage.getEctype())) {
            // O2O
            // VIP订单不转发到B2C 优先级最高
            if (DsConstants.VIP_ORDER.equalsIgnoreCase(orderMessage.getOrdertype())) {
                return ServiceModeEnum.O2O;
            }

            // 云仓订单必须转B2C
            if (DsConstants.WSC_CLOUD.equalsIgnoreCase(orderMessage.getOrdertype())) {
                return ServiceModeEnum.B2C;
            }

            // 积分兑换云仓商品必须转b2c 并且不强制返回 继续走 B2C 逻辑
            if (DsConstants.INTEGRAL_ORDER.equalsIgnoreCase(orderMessage.getOrdertype())) {
                if (hasSpCode(orderMessage)) {
                    return ServiceModeEnum.B2C;
                }
                return ServiceModeEnum.O2O;
            }

            // 内购云仓订单必须转B2C 并且不强制返回 继续走 B2C 逻辑
            if (DsConstants.IN_PURCHASE_ORDER.equalsIgnoreCase(orderMessage.getOrdertype())) {
                if (hasSpCode(orderMessage)) {
                    return ServiceModeEnum.B2C;
                }
                return ServiceModeEnum.O2O;
            }

            // 2、订单为【快递配送】订单
            if (DeliveryTypeEnum.checkExpress(orderMessage.getOmsdeliverytype())) {
                return ServiceModeEnum.B2C;
            }

            // 3、订单为【中心仓发货】标识订单
            if (centralWarehouseDeliveryFlag(orderMessage)) {
                return ServiceModeEnum.B2C;
            }

            //
            AddOrderInfoReqDto.ExtraInfo extraInfo = JsonUtil.json2Object(orderMessage.getExtrainfo(), AddOrderInfoReqDto.ExtraInfo.class);
            if (null == extraInfo) {
                return ServiceModeEnum.O2O;
            }
            AddOrderInfoReqDto.ExtJson extJson = JSON.parseObject(orderMessage.getExtrainfo(), AddOrderInfoReqDto.ExtJson.class);
            if (Objects.nonNull(extJson) && DsConstants.B2C_ONLINE_STORE_CODE.equals(extJson.getB2cOnlineStore())) {
                return ServiceModeEnum.B2C;
            }
            return ServiceModeEnum.O2O;
        } else if (PlatformCodeEnum.MEITUAN.getCode().equals(orderMessage.getEctype())
                || PlatformCodeEnum.E_BAI.getCode().equals(orderMessage.getEctype())
                || PlatformCodeEnum.JD_DAOJIA.getCode().equals(orderMessage.getEctype())) {
            OnlineStoreInfoRspDto onlineStoreInfoRspDto = dsOnlineStoreManager
                    .checkValidityAndGet(orderMessage.getGroupid(), orderMessage.getEctype(), orderMessage.getClientid(), orderMessage.getO2o_shop_id());

            if (Objects.nonNull(onlineStoreInfoRspDto) && !StringUtils.isEmpty(onlineStoreInfoRspDto.getOutB2cClientId())) {
                return ServiceModeEnum.B2C;
            }
            return ServiceModeEnum.O2O;
        }
        return ServiceModeEnum.O2O;
    }

    // 检查是否有SpCode
    private static boolean hasSpCode(AddOrderInfoReqDto orderMessage) {
        try {
            AddOrderInfoReqDto.ExtraInfo extraInfo = JSON.parseObject(orderMessage.getExtrainfo(), AddOrderInfoReqDto.ExtraInfo.class);
            if (Objects.nonNull(extraInfo)) {
                AddOrderInfoReqDto.ExtJson extraJson = JSON.parseObject(extraInfo.getExtJson(), AddOrderInfoReqDto.ExtJson.class);
                return Objects.nonNull(extraJson) && !StringUtils.isEmpty(extraJson.getSpCode());
            }
        } catch (Exception e) {
            log.info("解析SpCode异常,订单号:{}", orderMessage.getOlorderno());
        }
        return false;
    }


    /**
     * @Description: 校验是否中心仓发货订单
     * @Param: [orderMessage]
     * @return: boolean
     * @Author: syuson
     * @Date: 2021-8-18
     */
    private static boolean centralWarehouseDeliveryFlag(AddOrderInfoReqDto orderMessage) {
        if (StringUtils.isEmpty(orderMessage.getExtrainfo())) {
            return Boolean.FALSE;
        }
        AddOrderInfoReqDto.ExtraInfo extraInfo = JSON.parseObject(orderMessage.getExtrainfo(), AddOrderInfoReqDto.ExtraInfo.class);
        if (null == extraInfo) {
            return Boolean.FALSE;
        }
        AddOrderInfoReqDto.ExtJson extraJson = JSON.parseObject(extraInfo.getExtJson(), AddOrderInfoReqDto.ExtJson.class);
        // 0-非中心仓发货订单,1-中心仓发货订单
        if (DsConstants.STRING_ONE.equals(extraJson.getCentralWarehouseDeliveryFlag())) {
            return Boolean.TRUE;
        }
        return Boolean.FALSE;
    }


    /**
     * @param addOrderInfoReqDto
     * @param storeBillConfig
     * @return
     * <AUTHOR>
     * @Description 多支付方式 设置医保标识
     * @Date 16:04 2025/5/13
     * @Param
     **/
    private void buildPublicMultiPay(AddOrderInfoReqDto addOrderInfoReqDto, StoreBillConfig storeBillConfig) {
        if (StringUtils.isEmpty(addOrderInfoReqDto.getExtrainfo())) {
            return;
        }
        AddOrderInfoReqDto.ExtraInfo extraInfo;
        try {
            extraInfo = JsonUtil.json2Object(addOrderInfoReqDto.getExtrainfo(), AddOrderInfoReqDto.ExtraInfo.class);
        } catch (Exception e) {
            // 记录日志并返回
            log.warn("解析 extrainfo JSON 异常", e);
            return;
        }

        if (PlatformCodeEnum.YD_JIA.getCode().equals(addOrderInfoReqDto.getEctype())
                || !DsConstants.INTEGER_ONE.equals(storeBillConfig.getOnlinePayType())
                || StringUtils.isEmpty(extraInfo.getMedicare_fee())) {
            return;
        }

        BigDecimal mediateFee;
        try {
            mediateFee = new BigDecimal(extraInfo.getMedicare_fee());
        } catch (NumberFormatException e) {
            log.warn("医保费用字段不是合法数字: {}", extraInfo.getMedicare_fee());
            return;
        }

        if (mediateFee.compareTo(BigDecimal.ZERO) <= 0) {
            return;
        }

        BigDecimal customerPayment;
        try {
            customerPayment = new BigDecimal(addOrderInfoReqDto.getCustomerpayment());
        } catch (NumberFormatException e) {
            log.warn("客户支付金额不是合法数字: {}", addOrderInfoReqDto.getCustomerpayment());
            return;
        }
        BigDecimal onlinePrice = customerPayment.subtract(mediateFee);
        extraInfo.setMedicalInsurance(DsConstants.INTEGER_ONE);
        List<AddOrderInfoReqDto.PayType> payTypeList = new ArrayList<>();
        //取医保支付方式，平台对应固定809084
        AddOrderInfoReqDto.PayType medicalPayType = new AddOrderInfoReqDto.PayType();
        medicalPayType.setPayCode(DsConstants.YB_PAY_ONLINE_CODE);
        medicalPayType.setPayName(DsConstants.YB_PAY_ONLINE_NAME);
        medicalPayType.setPayPrice(mediateFee);
        payTypeList.add(medicalPayType);
        //线上支付方式固定为809080
        AddOrderInfoReqDto.PayType onlinePayType = new AddOrderInfoReqDto.PayType();
        onlinePayType.setPayCode(DsConstants.PAY_ONLINE_CODE);
        onlinePayType.setPayName(DsConstants.PAY_ONLINE_NAME);
        onlinePayType.setPayPrice(onlinePrice);
        payTypeList.add(onlinePayType);

        extraInfo.setPayTypeList(payTypeList);
        addOrderInfoReqDto.setExtrainfo(JsonUtil.object2JsonNull(extraInfo));
    }


    /**
     * 获取基础配送平台名称
     * 根据配送类型返回对应的配送平台名称
     *
     * @param deliveryType 配送类型
     * @return 配送平台名称
     */
    private String getBasicDeliveryPlatName(String deliveryType) {
        if (DeliveryTypeEnum.PLAT.getCode().equals(deliveryType) ||
                DeliveryTypeEnum.PLAT_THIRD.getCode().equals(deliveryType)) {
            return DeliveryPlatformEnum.PLAT_DELIVERY.getName();
        } else if (DeliveryTypeEnum.BUYER_SELF.getCode().equals(deliveryType)) {
            return DeliveryPlatformEnum.BUYER.getName();
        } else if (DeliveryTypeEnum.EXPRESS.getCode().equals(deliveryType)) {
            return DeliveryPlatformEnum.EXPRESS.getName();
        }
        return "";
    }
}
