package cn.hydee.middle.business.order.yxtadapter.domain.middle;

import cn.hydee.starter.domain.DomainBase;
import lombok.Data;

import java.math.BigDecimal;

/**
 * 支付信息
 */
@Data
public class OrderPaymentLog extends DomainBase {


    private String merCode;

    private String businessType;

    private String businessId;

    private Integer paymentTypeId;

    private String paymentNo;

    private String origPaymentNo;

    private BigDecimal paymentFee;

    private BigDecimal paidFee;

    private String paymentTime;

    private Long memberId;

    private String memberNo;

    private String backNo;

    private String backTime;

    private String backStatus;

    private String backNotes;

    private String returnUrl;

    private String reqTime;

    private String openId;

    private Integer queryStatus;

    private Integer paidStatus;

    private Integer refundStatus;

    private String orderBody;

    private String ip;

    private String sftInstOrderNo;

    private Integer collectionAccountType;

    private BigDecimal transacationFee;
}
