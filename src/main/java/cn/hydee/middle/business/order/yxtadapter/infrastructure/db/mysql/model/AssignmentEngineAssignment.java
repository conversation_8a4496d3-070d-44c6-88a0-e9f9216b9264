package cn.hydee.middle.business.order.yxtadapter.infrastructure.db.mysql.model;

import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

import java.util.Date;

/**
 * Created by Intellij IDEA.
 *
 * <AUTHOR>
 * Date:  2023/8/30
 */
@Data
//@TableName("assignment_engine_assignment_o2o")
public class AssignmentEngineAssignment {

    /**
     * id
     */
    private Long id;

    /**
     * 业务类型
     *
     */
    private Integer businessType;

    /**
     * businessType下唯一id
     */
    private String businessUniqueId;

    /**
     * 任务状态
     * 1:ENQUEUE, 2:FAIELD, 3:SUCCEED'
     */
    private Integer assignmentStatus;

    /**
     * 任务子状态
     */
    private Integer subAssignmentStatus;

    /**
     * 暂停状态列表 二进制
     */
    private Long pauseStatusBit;

    /**
     * 下次执行时间
     */
    private Date nextExecuteTime;

    /**
     * 重试次数
     */
    private Integer retryTimes;


    /**
     * 重试次数
     */
    private Integer totalExecuteTimes;

    private Integer enableStatus;

    /**
     * 错误码
     */
    private Integer failureCode;

    /**
     * json字段，用于扩展查询，也可添加generated字段来添加索引查询提高效率
     */
    private String extendSearchParam;

    /**
     * 请求参数
     */
    private String reqContent;

    /**
     * 响应数据，或报错数据
     */
    private String respContent;


    /**
     * 失败任务标(-1 默认标)
     */
    private Integer assignmentFlag;


    /**
     * 分片编号
     */
    private Integer shardingNum;

    /**
     * 备注(停用订单的时候使用)
     */
    private String remark;


    /**
     * 创建日期
     */
    private Date created;

    /**
     * 最后修改日期
     */
    private Date modified;


    /**
     * groupId
     */
    private Long groupId;



    /**
     * groupName
     */
    private String groupName;

    /**
     * tenantId
     */
    private Long tenantId;

    /**
     * yn
     */
    private Integer yn;

    /**
     * 处方单状态：0 非处方单，1 处方单待审核 ，2处方单审核通过，3处方单审核不通过
     */
    private Integer prescriptionStatus;

}
