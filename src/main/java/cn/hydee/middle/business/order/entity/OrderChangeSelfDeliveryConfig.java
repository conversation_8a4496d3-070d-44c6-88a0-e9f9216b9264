package cn.hydee.middle.business.order.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import lombok.Data;

import java.io.Serializable;
import java.util.Date;

/**
 * 订单转自配送配置信息
 *
 * <AUTHOR>
 * @date 2020/12/25 下午3:17
 */
@Data
public class OrderChangeSelfDeliveryConfig implements Serializable {
    private static final long serialVersionUID = -1762407839684802026L;

    /**
     * 自增主键
     */
    @TableId(value = "id", type = IdType.AUTO)
    private Integer id;

    /**
     * 商户编码
     */
    private String merCode;

    /**
     * o2o平台标记
     * {@link cn.hydee.middle.business.order.Enums.PlatformCodeEnum}
     */
    private String thirdPlatformCode;

    /**
     * 线上门店code
     */
    private String onlineStoreCode;

    /**
     * 线上门店ID
     */
    private Long onlineStoreId;

    /**
     * 状态 0：关闭 1：开启
     */
    private Integer state;

    /**
     * 延迟分钟数
     */
    private Integer delayMinutes;

    /**
     * o2o线上门店与配送门店关联信息Id
     * {@link DsOnlineStoreDelivery#id}
     */
    private Long onlineStoreDeliveryId;

    /**
     * 创建时间
     */
    private Date createTime;

    /**
     * 末次修改时间
     */
    private Date modifyTime;

    //规则描述？

}
