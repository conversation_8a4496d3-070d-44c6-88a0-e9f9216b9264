
package cn.hydee.middle.business.order.service.ob.cloud.shelf;

import cn.hydee.middle.business.order.dto.req.OrderHandleReqDto;
import cn.hydee.middle.business.order.dto.req.ob.cloud.shelf.DeliverGoodsReusltDto;
import cn.hydee.middle.business.order.dto.req.ob.cloud.shelf.OrderHandleRequestDeliveryGoodsDto;
import cn.hydee.middle.business.order.dto.req.ob.cloud.shelf.RequestDeliverGoodsDto;
import cn.hydee.middle.business.order.entity.OrderInfo;
import cn.hydee.starter.dto.ResponseBase;


public interface CloudShelfService {
	
	/**
	 * 代发校验
	 * @param orderInfo
	 */
	void obOrderAllowOperateCheck(OrderInfo orderInfo);
	
	/**
	 * 推送供货商发货结果到OMS
	 * @param deliverGoodsReusltDto {@link DeliverGoodsReusltDto}
	 */
	ResponseBase<String> pushDeliverGoodsReuslt(String userId,DeliverGoodsReusltDto deliverGoodsReusltDto);
	
	/**
	 * 代发商品申请供货商发货
	 * @param userId 用户id
	 * @param req {@link OrderHandleReqDto}
	 * @param orderNo 订单锁
	 */
	void requestDeliverGoods(String userId, OrderHandleRequestDeliveryGoodsDto req, String orderNo);

	/**
	 * OMS取消供货商发货
	 * @param req {@link OrderHandleReqDto}
	 * @param exception2LogFlag 异常是否转日志
	 */
	void cancelDeliverGoods(OrderHandleReqDto req,Boolean exception2LogFlag);
	
	/**
	 * 状态为待拣货的预约订单且业务类型为【代发】的订单，订单变更为【已取消、已关闭】状态时,推送代发订单取消发货消息给服务商平台
	 * @param orderInfo {@link OrderInfo}
	 */
	void cancelDeliverGoodsWhenStateChange(OrderInfo orderInfo);

	/**
	* @Description: 校验并获取SRM请求参数
	* @Param: [merCode, orderNo]
	* @return: cn.hydee.middle.business.order.dto.req.ob.cloud.shelf.RequestDeliverGoodsDto
	* @Author: syuson
	* @Date: 2022-3-11
	*/
	RequestDeliverGoodsDto validateAndBuildRequestDto(String merCode, Long orderNo);

	/**
	* @Description: 更新转单操作记录自动转单日志
	* @Param: [orderInfo, srmMessage]
	* @return: void
	* @Author: syuson
	* @Date: 2022-3-11
	*/
	void updateHandlerFlagAndRecordLog(OrderInfo orderInfo,RequestDeliverGoodsDto srmMessage);
}
  
