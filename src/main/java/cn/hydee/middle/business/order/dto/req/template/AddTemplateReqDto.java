
package cn.hydee.middle.business.order.dto.req.template;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import org.hibernate.validator.constraints.Length;

import javax.validation.constraints.NotNull;

@Data
public class AddTemplateReqDto {
	
	@ApiModelProperty(value="商户编码",hidden = true)
    private String merCode;

    @ApiModelProperty(value="模板类型,1：评价回复，2：退款审核回复，3：打印模板")
    @NotNull(message="模板类型不能为空")
    private Integer templateType;

    @ApiModelProperty(value="模板名称")
    @Length(min=1,max=20,message = "模板名称必须在1到20位之间")
    private String templateName;

    @ApiModelProperty(value="模板内容")
    private String templateContent;
}
  
