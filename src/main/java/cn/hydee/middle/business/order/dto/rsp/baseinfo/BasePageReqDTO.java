package cn.hydee.middle.business.order.dto.rsp.baseinfo;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * Created by Intellij IDEA.
 *
 * <AUTHOR> Date:  2024/6/13
 */
@EqualsAndHashCode(callSuper = true)
@Data
public class BasePageReqDTO extends BaseRPCReqDTO {

  @ApiModelProperty("当前页.默认为1")
  private int currentPage = 1;
  @ApiModelProperty("每页条数，默认20")
  private int pageSize = 20;




}
