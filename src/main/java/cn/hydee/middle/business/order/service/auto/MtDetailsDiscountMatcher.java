package cn.hydee.middle.business.order.service.auto;

import cn.hydee.middle.business.order.Enums.OrderDetailStatusEnum;
import cn.hydee.middle.business.order.dto.DetailsDiscount;
import cn.hydee.middle.business.order.entity.OrderDetail;
import cn.hydee.middle.business.order.entity.RefundDetail;
import cn.hydee.unified.model.order.MeituanRefundRecord;
import com.alibaba.fastjson.JSON;
import org.apache.commons.lang3.StringUtils;
import org.springframework.util.CollectionUtils;

import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

public class MtDetailsDiscountMatcher {

    public static void convertMtRefundDetailDiscount(List<OrderDetail> orderDetailList, List<RefundDetail> refundDetailList, MeituanRefundRecord record){
        if(CollectionUtils.isEmpty(refundDetailList)){
            return;
        }
        if(StringUtils.isEmpty(record.getSkuBenefitDetails())){
            return;
        }
        List<MeituanRefundRecord.SkuBenefitDetail> skuBenefitDetails = JSON.parseArray(record.getSkuBenefitDetails(), MeituanRefundRecord.SkuBenefitDetail.class);
        Map<String, DetailsDiscount> discountMap = skuBenefitDetails.stream().map(item ->{
            DetailsDiscount discount = new DetailsDiscount();
            discount.setSkuid(item.getSkuId());
            discount.setOutskuid(item.getAppMedicineCode());
            discount.setName(item.getName());
            discount.setUpc(item.getUpc());
            discount.setCount(item.getCount());
            if(!StringUtils.isEmpty(item.getTotalPoiCharge())){
                discount.setMerchant_discount(item.getTotalPoiCharge());
            }
            if(!StringUtils.isEmpty(item.getTotalMtCharge())){
                discount.setPlatform_discount(item.getTotalMtCharge());
            }
            return discount;
        }).collect(Collectors.toMap(item -> String.format("%s_%s",item.getOutskuid(),item.getUpc()),data -> data,(a,b) -> a));

        convertMtRefundDetailDiscount(orderDetailList,refundDetailList,discountMap);
    }

    public static void convertMtRefundDetailDiscount(List<OrderDetail> orderDetailList, List<RefundDetail> refundDetailList,
                                                      Map<String, DetailsDiscount> discountMap){
        Map<String,OrderDetail> swapMap = orderDetailList.stream()
                .filter(item -> OrderDetailStatusEnum.REPLACE.getCode().equals(item.getStatus()))
                .collect(Collectors.toMap(item -> String.format("%s_%s",item.getErpCode(),item.getBarCode()), item -> item, (a, b) -> a));
        Map<String,OrderDetail> normalMap = orderDetailList.stream()
                .filter(item -> OrderDetailStatusEnum.REPLACE.getCode().compareTo(item.getStatus()) > 0)
                .collect(Collectors.toMap(item -> String.format("%s_%s",item.getErpCode(),item.getBarCode()),item -> item, (a,b) -> a));

        for(RefundDetail refundDetail : refundDetailList){
            if(StringUtils.isNotBlank(refundDetail.getDetailDiscount())){{
                continue;
            }}
            String discountKey = String.format("%s_%s",refundDetail.getErpCode(),refundDetail.getBarCode());
            OrderDetail orderDetail = normalMap.get(discountKey);
            if(orderDetail != null){
                OrderDetail swapDetail = swapMap.get(String.format("%s_%s",orderDetail.getErpCode(),orderDetail.getBarCode()));
                if(swapDetail != null){
                    discountKey = String.format("%s_%s",swapDetail.getErpCode(),swapDetail.getBarCode());
                }
            }
            DetailsDiscount discount = discountMap.get(discountKey);
            if(discount == null){
                continue;
            }
            refundDetail.setDetailDiscount(JSON.toJSONString(discount));
        }
    }

}
