package cn.hydee.middle.business.order.dto;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotBlank;

/**
 * <AUTHOR> hua❀
 * @Date 2024-11-21 19:25
 * @Version 1.0
 **/
@Data
public class MedicalTraceCodeBatchNoReqDto {

    @NotBlank(message = "商品编码不能为空")
    @ApiModelProperty(value = "商品编码")
    private String erpCode;

    @NotBlank(message = "门店编码不能为空")
    @ApiModelProperty(value = "门店编码")
    private String storeCode;
}
