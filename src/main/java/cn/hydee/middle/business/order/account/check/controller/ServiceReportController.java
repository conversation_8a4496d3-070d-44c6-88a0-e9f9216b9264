
package cn.hydee.middle.business.order.account.check.controller;

import cn.hydee.middle.business.order.Enums.DsConstants;
import cn.hydee.middle.business.order.account.check.base.dto.req.QueryOrganizationServiceReportReqDto;
import cn.hydee.middle.business.order.account.check.base.dto.req.QueryStoreServiceReportReqDto;
import cn.hydee.middle.business.order.account.check.service.IOrganizationServiceReportService;
import cn.hydee.middle.business.order.account.check.service.IStoreServiceReportService;
import cn.hydee.middle.business.order.dto.StoreServiceReportAllSnapshotDto;
import cn.hydee.middle.business.order.dto.req.VerifyReqDto;
import cn.hydee.middle.business.order.dto.rsp.StoreServiceReportResDto;
import cn.hydee.middle.business.order.dto.rsp.VerifyRspDto;
import cn.hydee.middle.business.order.entity.OrganizationServiceReport;
import cn.hydee.middle.business.order.entity.StoreServiceReport;
import cn.hydee.middle.business.order.service.VerifyService;
import cn.hydee.middle.business.order.util.RedisKeyUtil;
import cn.hydee.starter.controller.AbstractController;
import cn.hydee.starter.dto.ResponseBase;
import com.alibaba.fastjson.JSON;
import com.baomidou.mybatisplus.core.metadata.IPage;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.redis.core.StringRedisTemplate;
import org.springframework.util.CollectionUtils;
import org.springframework.web.bind.annotation.*;

import javax.validation.Valid;
import java.time.Duration;
import java.time.LocalDateTime;
import java.time.ZoneOffset;
import java.util.List;

@RestController
@RequestMapping("/${api.version}/ds/service")
@Api(tags = "服务报表控制器")
@Slf4j
public class ServiceReportController extends AbstractController {

    @Autowired
    private IOrganizationServiceReportService organizationServiceReportService;
    @Autowired
    private IStoreServiceReportService storeServiceReportService;
    @Autowired
    private VerifyService verifyService;
    @Autowired
    private StringRedisTemplate stringRedisTemplate;

	@ApiOperation(value = "生成服务报表", notes = "生成服务报表")
    @PostMapping("/Job/generateReport")
    public ResponseBase<String> generateReport(){
        Boolean setValue = stringRedisTemplate.opsForValue().setIfAbsent(RedisKeyUtil.SERVICE_REPORT_GENEARATE, "lock", Duration.ofHours(6));
        if (setValue == null || !setValue) {
            return generateSuccess("generateReport is still running");
        }
        try {
            organizationServiceReportService.generateReport();
            return generateSuccess("generateReport finished");
        } finally {
            stringRedisTemplate.opsForValue().getOperations().delete(RedisKeyUtil.SERVICE_REPORT_GENEARATE);
        }
    }

    @ApiOperation(value = "删除服务报表Redis key-紧急使用-key(o2o-service-report-generate)", notes = "删除服务报表Redis key-紧急使用")
    @PostMapping("/generateReport/redisDeleteKey/{key}")
    public ResponseBase deleteRedisKey(@PathVariable("key") String key){
        try {
            stringRedisTemplate.delete(key);
        } catch (Exception e) {
            log.error("generateReport deleteRedisKey error ",e);
        }
        return new ResponseBase();
    }

    @ApiOperation(value = "重新生成服务报表-某一天的数据-参数为当前日期减第几天", notes = "重新生成服务报表-某一天的数据-参数为当前日期减第几天")
    @PostMapping("/reGenerateReport/{minDay}")
    public ResponseBase<String> reGenerateReport(@PathVariable("minDay") Integer minday){
        if (minday.intValue() <= 0) {
            ResponseBase responseBase = new ResponseBase();
            responseBase.setMsg("minday can not <= 0");
            responseBase.setCode(DsConstants.FAILED);
            return responseBase;
        }

        Boolean setValue = stringRedisTemplate.opsForValue().setIfAbsent(RedisKeyUtil.SERVICE_REPORT_GENEARATE, "lock", Duration.ofHours(5));
        if (setValue == null || !setValue) {
            return generateSuccess("reGenerateReport is still running");
        }
        try {
            organizationServiceReportService.reGenerateReport(minday);
            return generateSuccess("reGenerateReport finished");
        } finally {
            stringRedisTemplate.opsForValue().getOperations().delete(RedisKeyUtil.SERVICE_REPORT_GENEARATE);
        }

    }

    @ApiOperation(value = "查询商户所有线下门店服务报表", notes = "查询商户所有线下门店服务报表")
    @PostMapping("/mer/qryReport")
    public ResponseBase<StoreServiceReportResDto> qryReport(@RequestHeader("merCode") String merCode, @RequestHeader("userId") String userId, @Valid @RequestBody QueryOrganizationServiceReportReqDto req){
        req.setMerCode(merCode);
        if (CollectionUtils.isEmpty(req.getOrganizationCodeList())) {
            VerifyRspDto verifyDto = verifyService.verifyOrganizationAndGet(VerifyReqDto.builder()
                    .merCode(merCode)
                    .organizationCode(DsConstants.ORGANIZATION_CODE_ALL)
                    .userId(userId)
                    .build());
            req.setOrganizationCodeList(verifyDto.getOrganizatioinList());
        } else {
            List<String> orgList = verifyService.verifyOrgList(merCode, userId, req.getOrganizationCodeList());
            if (CollectionUtils.isEmpty(orgList)) {
                return generateSuccess(StoreServiceReportResDto.buildDefaultData());
            }
            req.setOrganizationCodeList(orgList);
        }
        IPage<OrganizationServiceReport> pageData = storeServiceReportService.qryOrganizationServiceReport(req);
        StoreServiceReportAllSnapshotDto totalData = req.getTotalData();
        return generateSuccess(StoreServiceReportResDto.builder().pageData(pageData).totalData(totalData).build());
    }

    @ApiOperation(value = "查询一个线下门店下的所有门店服务报表", notes = "查询一个线下门店下的所有门店服务报表")
    @PostMapping("/store/qryReport")
    public ResponseBase<IPage<StoreServiceReport>> qryReport(@RequestHeader("merCode") String merCode, @Valid @RequestBody QueryStoreServiceReportReqDto req){
        if (CollectionUtils.isEmpty(req.getOrganizationCodeList()) || req.getOrganizationCodeList().size() > 1) {
            ResponseBase responseBase = new ResponseBase();
            responseBase.setMsg("param organizationCodeList {"+ JSON.toJSONString(req.getOrganizationCodeList())+"} error");
            responseBase.setCode(DsConstants.FAILED);
            responseBase.setTimestamp(LocalDateTime.now().toEpochSecond(ZoneOffset.of("+8")));
            return responseBase;
        }
        req.setMerCode(merCode);
        return generateSuccess(storeServiceReportService.qryStoreServiceReportByOrg(req));
    }
}
