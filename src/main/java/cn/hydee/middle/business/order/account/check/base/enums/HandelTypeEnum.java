
package cn.hydee.middle.business.order.account.check.base.enums;  

public enum HandelTypeEnum {
	
	UN_HANDEL(0, "未处置"),
	HANDELED(1, "已处置"),
	;

    private Integer code;
    private String msg;

    HandelTypeEnum(Integer code, String msg) {
        this.code = code;
        this.msg = msg;
    }

    public Integer getCode() {
        return code;
    }

    public String getMsg() {
        return msg;
    }
}
  
