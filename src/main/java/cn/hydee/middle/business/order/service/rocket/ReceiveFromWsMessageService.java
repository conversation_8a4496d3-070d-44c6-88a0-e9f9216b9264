
package cn.hydee.middle.business.order.service.rocket;

import cn.hydee.middle.business.order.client.ws.constant.ClientWsConstants;
import cn.hydee.middle.business.order.client.ws.dto.ReceiveFromWsMessageDTO;
import cn.hydee.middle.business.order.client.ws.handler.WsMessageHandler;
import cn.hydee.middle.business.order.util.SpringBeanUtils;
import org.apache.rocketmq.common.message.MessageExt;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;
import java.util.stream.Stream;

/**
 * 
 * <AUTHOR>
 * @date 2021/03/16
 */
@Service
public class ReceiveFromWsMessageService extends AbstractConsumerService {

    private static final Logger log = LoggerFactory.getLogger(ReceiveFromWsMessageService.class);

    private static final Map<String, ClientWsConstants.MessageType> MSGTYPESERVICE_MAP =
        Stream.of(ClientWsConstants.MessageType.values())
            .collect(Collectors.toMap(ClientWsConstants.MessageType::getType, e -> e));

    @Override
    public void dealBody(List<MessageExt> msgList) throws Exception {
        for (MessageExt msg : msgList) {
            ReceiveFromWsMessageDTO message = transMsgToEntity(msg, ReceiveFromWsMessageDTO.class);
            WsMessageHandler messageHanlder = checkAndGetService(message.getMsgType());
            String validMsg = "";
            if (null == messageHanlder) {
                validMsg = "消息类型非法，不是已有消息类型";
                messageHanlder = SpringBeanUtils.getBean("defaultOmsMessageHandler", WsMessageHandler.class);
            }
            // 处理业务
            messageHanlder.handle(message, validMsg);
        }
    }

    private WsMessageHandler checkAndGetService(String msgType) {
        ClientWsConstants.MessageType enums = MSGTYPESERVICE_MAP.get(msgType);
        if (null == enums) {
            return null;
        }
        String serviceName = enums.getServiceName();
        return SpringBeanUtils.getBean(serviceName, WsMessageHandler.class);
    }
}
