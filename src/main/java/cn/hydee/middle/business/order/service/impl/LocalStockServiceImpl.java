package cn.hydee.middle.business.order.service.impl;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.StrUtil;
import cn.hydee.middle.business.order.Enums.DsConstants;
import cn.hydee.middle.business.order.Enums.IntYesNoEnum;
import cn.hydee.middle.business.order.Enums.LocalStockEnum;
import cn.hydee.middle.business.order.Enums.StockSourceEnum;
import cn.hydee.middle.business.order.dto.req.OccupyStockMessageDTO;
import cn.hydee.middle.business.order.entity.LocalStock;
import cn.hydee.middle.business.order.entity.OrderDetail;
import cn.hydee.middle.business.order.entity.OrderInfo;
import cn.hydee.middle.business.order.feign.MiddleDataSyncClient;
import cn.hydee.middle.business.order.mapper.LocalStockMapper;
import cn.hydee.middle.business.order.mapper.OrderInfoMapper;
import cn.hydee.middle.business.order.service.LocalStockService;
import cn.hydee.middle.business.order.service.suport.OrderBasicService;
import cn.hydee.middle.business.order.v2.aop.RedisLockHandler;
import cn.hydee.starter.dto.ResponseBase;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.redis.core.StringRedisTemplate;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.*;
import java.util.stream.Collectors;

/**
 * @author: xin.tu
 * @date: 2023/1/6 10:10
 * @menu:
 */
@Service
@Slf4j
public class LocalStockServiceImpl extends ServiceImpl<LocalStockMapper, LocalStock> implements LocalStockService {


    @Resource
    private RedisLockHandler redisLockHandler;

    @Autowired
    private LocalStockMapper localStockMapper;

    @Autowired
    private StringRedisTemplate redisTemplate;

    @Autowired
    private OrderBasicService orderBasicService;

    @Autowired
    private MiddleDataSyncClient middleDataSyncClient;

    @Autowired
    private OrderInfoMapper orderInfoMapper;

    @Override
    public void lockStock(OrderInfo orderInfo, List<OrderDetail> orderDetailList) {
        if (orderInfo == null || CollUtil.isEmpty(orderDetailList)) {
            return;
        }
        log.info("lockStock param orderNo:{}", orderInfo.getOrderNo());
        //根据商品详情ID+订单号去重
        orderDetailList = orderDetailList.stream().collect(Collectors.collectingAndThen(Collectors.toCollection(() -> new TreeSet<>(
                Comparator.comparing(item -> item.getId() + item.getOrderNo())
        )), ArrayList::new));

        //根据订单号获取库存锁 30s超时
        if (redisLockHandler.getStockLock(orderInfo.getOrderNo().toString(), null)) {
            try {
                //查询已锁库存
                LambdaQueryWrapper<LocalStock> queryWrapper = new LambdaQueryWrapper<>();
                queryWrapper.eq(LocalStock::getOrderNo, orderInfo.getOrderNo());
                queryWrapper.eq(LocalStock::getType, LocalStockEnum.ADD.getType());
                List<LocalStock> existList = this.list(queryWrapper);
                Map<String, LocalStock> existMap = existList.stream().collect(Collectors.toMap(it -> it.getOrderNo() + "_" + it.getOrderDetailId(), a -> a));

                List<LocalStock> saveLocalStocks = new ArrayList<>();

                for (OrderDetail orderDetail : orderDetailList) {
                    String key = orderDetail.getOrderNo() + "_" + orderDetail.getId();
                    if (existMap.containsKey(key)) {
                        // 已锁库存过滤
                        log.info("lockStock exist this key,key:{}", key);
                        continue;
                    }
                    if (orderDetail.getPickCount() <= 0) {
                        continue;
                    }
                    LocalStock localStock = new LocalStock();
                    localStock.setMerCode(orderInfo.getMerCode());
                    localStock.setErpCode(orderDetail.getErpCode());
                    localStock.setOrderNo(orderDetail.getOrderNo().toString());
                    localStock.setOrderDetailId(orderDetail.getId());
                    localStock.setOnlineStoreCode(orderInfo.getOnlineStoreCode());
                    localStock.setOrganizationCode(orderInfo.getOrganizationCode());
                    localStock.setStockQty(orderDetail.getPickCount());
                    localStock.setIsWriteRedis(IntYesNoEnum.NO.getCode());
                    localStock.setType(LocalStockEnum.ADD.getType());
                    localStock.setCreateTime(new Date());
                    localStock.setSource(StockSourceEnum.JIE_DAN.getMsg());
                    saveLocalStocks.add(localStock);
                }
                //批量保持库存
                this.saveBatch(saveLocalStocks);
            } catch (Exception e) {
                String errorMsg = StrUtil.format("lockStock error! orderNo:{}", orderInfo.getOrderNo());
                log.error(errorMsg, e);
            } finally {
                //释放锁
                redisLockHandler.delStockLock(orderInfo.getOrderNo().toString());
            }
        } else {
            log.warn("lockStock failed to get lock! orderNo:{}", orderInfo.getOrderNo());
        }
    }

    @Override
    public void unlockStock(OrderInfo orderInfo, List<OrderDetail> orderDetailList, StockSourceEnum stockSourceEnum) {
        if (orderInfo == null || StrUtil.isNotBlank(orderInfo.getErpAdjustNo())) {
            return;
        }
        log.info("unlockStock param orderNo:{}", orderInfo.getOrderNo());
        //根据订单号获取库存锁 30s超时
        if (redisLockHandler.getStockLock(orderInfo.getOrderNo().toString(), null)) {
            try {
                //查询已锁库存
                LambdaQueryWrapper<LocalStock> queryWrapper = new LambdaQueryWrapper<>();
                queryWrapper.eq(LocalStock::getOrderNo, orderInfo.getOrderNo());
                queryWrapper.eq(LocalStock::getType, LocalStockEnum.ADD.getType());
                List<LocalStock> lockExistList = this.list(queryWrapper);
                if (CollUtil.isEmpty(lockExistList)) {
                    return;
                }
                Map<String, LocalStock> lockExistMap = lockExistList.stream().collect(Collectors.toMap(it -> it.getOrderNo() + "_" + it.getOrderDetailId(), a -> a));

                //查询已解锁库存
                queryWrapper = new LambdaQueryWrapper<>();
                queryWrapper.eq(LocalStock::getOrderNo, orderInfo.getOrderNo());
                queryWrapper.eq(LocalStock::getType, LocalStockEnum.SUB.getType());
                List<LocalStock> unlockExistList = this.list(queryWrapper);

                //已解锁库存数量
                Map<String, Integer> unLockQtyMap = new HashMap<>();
                for (LocalStock localStock : unlockExistList) {
                    String key = localStock.getOrderNo() + "_" + localStock.getOrderDetailId();
                    int qty = unLockQtyMap.getOrDefault(key, 0);
                    qty = qty + localStock.getStockQty();
                    unLockQtyMap.put(key, qty);
                }
                //如果对象为空 取锁定订单全部解锁
                if (CollUtil.isEmpty(orderDetailList)) {
                    orderDetailList = new ArrayList<>();
                    for (LocalStock localStock : lockExistList) {
                        String key = localStock.getOrderNo() + "_" + localStock.getOrderDetailId();
                        Integer unLockQty = unLockQtyMap.getOrDefault(key, 0);
                        // 剩余解锁库存 = 锁定库存-已解锁库存
                        int surplusStockQty = localStock.getStockQty() - unLockQty;
                        if (surplusStockQty <= 0) {
                            continue;
                        }
                        OrderDetail orderDetail = new OrderDetail();
                        orderDetail.setErpCode(localStock.getErpCode());
                        orderDetail.setOrderNo(Long.valueOf(localStock.getOrderNo()));
                        orderDetail.setId(localStock.getOrderDetailId());
                        orderDetail.setCommodityCount(surplusStockQty);
                        orderDetail.setRefundCount(0);
                        orderDetailList.add(orderDetail);
                    }
                }

                List<LocalStock> saveLocalStocks = new ArrayList<>();


                for (OrderDetail orderDetail : orderDetailList) {
                    String key = orderDetail.getOrderNo() + "_" + orderDetail.getId();
                    if (!lockExistMap.containsKey(key)) {
                        //不存在锁定
                        log.info("unlockStock not exist this key,key:{}", key);
                        continue;
                    }
                    if (orderDetail.getPickCount() <= 0) {
                        continue;
                    }
                    LocalStock lockStock = lockExistMap.get(key);
                    Integer qty = unLockQtyMap.getOrDefault(key, 0);
                    unLockQtyMap.put(key, qty + orderDetail.getPickCount());
                    qty = unLockQtyMap.get(key);
                    // 当前解锁数量+本次解锁数量>已锁定数量  continue
                    if (qty > lockStock.getStockQty()) {
                        log.info("unlockStock this qty > lockStock count qty, key:{}", key);
                        continue;
                    }
                    LocalStock localStock = new LocalStock();
                    localStock.setMerCode(orderInfo.getMerCode());
                    localStock.setErpCode(orderDetail.getErpCode());
                    localStock.setOrderNo(orderDetail.getOrderNo().toString());
                    localStock.setOrderDetailId(orderDetail.getId());
                    localStock.setOnlineStoreCode(orderInfo.getOnlineStoreCode());
                    localStock.setOrganizationCode(orderInfo.getOrganizationCode());
                    localStock.setStockQty(orderDetail.getPickCount());
                    localStock.setIsWriteRedis(IntYesNoEnum.NO.getCode());
                    localStock.setCreateTime(new Date());
                    localStock.setType(LocalStockEnum.SUB.getType());
                    localStock.setSource(stockSourceEnum.getMsg());
                    saveLocalStocks.add(localStock);
                }
                this.saveBatch(saveLocalStocks);
            } catch (Exception e) {
                String errorMsg = StrUtil.format("unlockStock error! orderNo:{}", orderInfo.getOrderNo());
                log.error(errorMsg, e);
            } finally {
                //释放锁
                redisLockHandler.delStockLock(orderInfo.getOrderNo().toString());
            }
        } else {
            log.warn("unlockStock failed to get lock! orderNo:{}", orderInfo.getOrderNo());
        }
    }

    @Override
    public void executeStockToRedis() {
        LambdaQueryWrapper<LocalStock> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(LocalStock::getIsWriteRedis, IntYesNoEnum.NO.getCode());
        // 先加再减
        queryWrapper.orderByAsc(LocalStock::getType);
        List<LocalStock> localStockList = this.list(queryWrapper);
        for (LocalStock localStock : localStockList) {
            this.toRedis(localStock);
        }
    }

    private void toRedis(LocalStock localStock) {
//        if (redisLockHandler.getToRedisStockLock(localStock.getMerCode(), localStock.getOnlineStoreCode(), localStock.getErpCode(), null)) {
        int i = localStockMapper.updateIsWriteRedis(localStock.getId());
        if (i <= 0) {
            return;
        }
//        String key = RedisKeyUtil.getLocalStock(localStock.getMerCode(), localStock.getOnlineStoreCode());
//        int stockQty = 0;
//        if (localStock.getType() == LocalStockEnum.ADD.getType()) {
//            stockQty = localStock.getStockQty();
//        }
//        if (localStock.getType() == LocalStockEnum.SUB.getType()) {
//            stockQty = -localStock.getStockQty();
//        }
//        redisTemplate.opsForHash().increment(key, localStock.getErpCode(), stockQty);
        // 同步reids改为接口到商品
        OrderInfo orderInfo = orderBasicService.getOrderBaseInfo(Long.parseLong(localStock.getOrderNo()));
        if (orderInfo == null) {
            return;
        }
        List<OrderDetail> orderDetails = orderBasicService.getOrderDetailListWithCheck(Long.parseLong(localStock.getOrderNo()));
        Map<Long, OrderDetail> orderDetailMap = orderDetails.stream().collect(Collectors.toMap(OrderDetail::getId, v -> v, (a, b) -> a));
        Integer stockQty = localStock.getStockQty();
        if (orderDetailMap.containsKey(localStock.getOrderDetailId())) {
            OrderDetail orderDetail = orderDetailMap.get(localStock.getOrderDetailId());
            if (DsConstants.INTEGER_TWO.equals(orderDetail.getChailing())) {
                stockQty = (int) Math.ceil(stockQty * 1.0 / orderDetail.getChaiLingNum());
            }
        }
        OccupyStockMessageDTO occupyStockMessageDTO = new OccupyStockMessageDTO();
        occupyStockMessageDTO.setOrderNo(localStock.getOrderNo());
        occupyStockMessageDTO.setMerCode(localStock.getMerCode());
        occupyStockMessageDTO.setOrgCode(localStock.getOrganizationCode() == null ? orderInfo.getOrganizationCode() : localStock.getOrganizationCode());
        occupyStockMessageDTO.setChannel("o2o");
        occupyStockMessageDTO.setPlatformCode(Integer.valueOf(orderInfo.getThirdPlatformCode()));
        occupyStockMessageDTO.setOccupyFlag(localStock.getType() == LocalStockEnum.ADD.getType() ? DsConstants.INTEGER_ONE : DsConstants.INTEGER_ZERO);
        occupyStockMessageDTO.setSource(localStock.getSource());
        OccupyStockMessageDTO.OccupyStock occupyStock = new OccupyStockMessageDTO.OccupyStock();
        occupyStock.setErpCode(localStock.getErpCode());
        occupyStock.setStock(stockQty);
        //查询是否是已切店订单，已切店订单强刷
//        occupyStockMessageDTO.setForceFlushStock(DsConstants.STRING_ONE.equals(orderInfo.getOrderIsNew()));
        occupyStockMessageDTO.setOccupyStockList(CollUtil.list(false, occupyStock));
        try {
            ResponseBase<Boolean> booleanResponseBase = middleDataSyncClient.occupyStock(occupyStockMessageDTO);
            log.info("sync stock to commodity,param:{},result:{}", JSON.toJSONString(occupyStockMessageDTO), JSON.toJSONString(booleanResponseBase));
        } catch (Exception e) {
            String errorMsg = StrUtil.format("sync stock to commodity error,orderNo:{},localStockId:{}", localStock.getOrderNo(), localStock.getId());
            log.error(errorMsg, e);
        }

//        }
    }
}
