package cn.hydee.middle.business.order.route.domain.impl;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.collection.CollectionUtil;
import cn.hydee.middle.business.order.Enums.DsDeliveryType;
import cn.hydee.middle.business.order.dto.req.OrgInfoCodesReqDTO;
import cn.hydee.middle.business.order.dto.req.StoreListReqDTO;
import cn.hydee.middle.business.order.dto.rsp.OrgInfoCodesResDTO;
import cn.hydee.middle.business.order.dto.rsp.StoreListResDTO;
import cn.hydee.middle.business.order.entity.DsOnlineStore;
import cn.hydee.middle.business.order.mapper.DsOnlineStoreDeliveryRepo;
import cn.hydee.middle.business.order.mapper.DsOnlineStoreRepo;
import cn.hydee.middle.business.order.mapper.InnerStoreDictionaryMapper;
import cn.hydee.middle.business.order.route.application.representation.GoodExcelRepresentationDto;
import cn.hydee.middle.business.order.route.application.representation.OnlineStoreRepresentationDto;
import cn.hydee.middle.business.order.route.application.representation.StoreInfoRepresentationDto;
import cn.hydee.middle.business.order.route.application.representation.RuleRepresentationDto;
import cn.hydee.middle.business.order.route.application.representation.StrategyDetailsRepresentationDto;
import cn.hydee.middle.business.order.route.application.representation.StrategyDetailsRepresentationDto.StrategyRuleRepresentationDto;
import cn.hydee.middle.business.order.route.application.representation.StrategyDetailsRepresentationDto.StrategyRuleRepresentationDto.RouteReceiveStoreRepresentationDto;
import cn.hydee.middle.business.order.route.application.representation.StrategyDetailsRepresentationDto.StrategyRuleRepresentationDto.RouteTimePeriodRepresentationDto;
import cn.hydee.middle.business.order.route.application.representation.StrategyRepresentationDto;
import cn.hydee.middle.business.order.route.application.representation.SubCompanyRepresentationDto;
import cn.hydee.middle.business.order.route.application.representation.TakeOrdersStoreRepresentationDto;
import cn.hydee.middle.business.order.route.db.mysql.model.RouteReceiveStoreSet;
import cn.hydee.middle.business.order.route.db.mysql.model.RouteRule;
import cn.hydee.middle.business.order.route.db.mysql.model.RouteScene;
import cn.hydee.middle.business.order.route.db.mysql.model.RouteStrategy;
import cn.hydee.middle.business.order.route.db.mysql.model.RouteStrategyRule;
import cn.hydee.middle.business.order.route.db.mysql.model.RouteTimePeriodSet;
import cn.hydee.middle.business.order.route.domain.StrategyAggregate;
import cn.hydee.middle.business.order.route.domain.StrategyAggregate.StrategyRuleAggregate;
import cn.hydee.middle.business.order.route.domain.StrategyAggregate.StrategyRuleAggregate.RouteReceiveStoreAggregate;
import cn.hydee.middle.business.order.route.domain.StrategyAggregate.StrategyRuleAggregate.RouteTimePeriodAggregate;
import cn.hydee.middle.business.order.route.domain.StrategyDomainService;
import cn.hydee.middle.business.order.route.domain.constants.RouteHandTypeConstants;
import cn.hydee.middle.business.order.route.domain.dto.StrategyChangeDto;
import cn.hydee.middle.business.order.route.domain.dto.StrategyChangeDto.StrategyTime;
import cn.hydee.middle.business.order.route.domain.dto.StrategyChangeDto.ThirdStore;
import cn.hydee.middle.business.order.route.domain.dto.StrategyDomainQueryDto;
import cn.hydee.middle.business.order.route.domain.dto.StrategyOpenDto;
import cn.hydee.middle.business.order.route.domain.dto.StrategyPageDto;
import cn.hydee.middle.business.order.route.domain.enums.DistanceTypeEnum;
import cn.hydee.middle.business.order.route.domain.external.RouteClientService;
import cn.hydee.middle.business.order.route.domain.enums.OnOffStatusEnum;
import cn.hydee.middle.business.order.route.domain.repository.SceneRepository;
import cn.hydee.middle.business.order.route.domain.repository.StrategyRepository;
import cn.hydee.middle.business.order.route.domain.util.ValidateUtil;
import cn.hydee.middle.business.order.util.ExcelUtil;
import cn.hydee.middle.business.order.util.ExceptionUtil;
import cn.hydee.middle.business.order.yxtadapter.domain.dictionary.InnerStoreDictionary;
import cn.hydee.starter.dto.ErrorType;
import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.google.common.collect.Lists;
import com.google.common.collect.Sets;
import com.yxt.middle.baseinfo.req.org.SysUserOrganizationLayerReqDTO;
import com.yxt.middle.baseinfo.res.org.SubOrgInfoDataResDTO;
import com.yxt.middle.baseinfo.res.store.StoreInfoDataResDTO;
import java.io.IOException;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Comparator;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Optional;
import java.util.Set;
import java.util.stream.Collectors;
import javax.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;
import org.springframework.web.multipart.MultipartFile;

@Service
@Slf4j
public class StrategyDomainServiceImpl implements StrategyDomainService {

  @Resource
  private StrategyRepository strategyRepository;

  @Resource
  private RouteClientService routeClientService;

  @Resource
  private DsOnlineStoreRepo dsOnlineStoreRepo;

  @Resource
  private SceneRepository sceneRepository;

  @Resource
  private DsOnlineStoreDeliveryRepo dsOnlineStoreDeliveryRepo;

  @Resource
  private InnerStoreDictionaryMapper innerStoreDictionaryMapper;

  @Override
  public IPage<StrategyRepresentationDto> pageStrategyInfo(StrategyDomainQueryDto strategyDomainQueryDto) {
    IPage<StrategyRepresentationDto> strategyInfoPage = strategyRepository.pageStrategyInfo(strategyDomainQueryDto);
    if(Objects.nonNull(strategyInfoPage) && CollUtil.isNotEmpty(strategyInfoPage.getRecords())){
      Set<String> onlineStoreIds = Sets.newHashSet();
      List<StrategyRepresentationDto> strategies = strategyInfoPage.getRecords();
      strategies.forEach(e->{
        onlineStoreIds.addAll(Arrays.asList(e.getOnlineStoreIds().split(";")));
      });
      List<DsOnlineStore> dsOnlineStores = dsOnlineStoreRepo.selectBatchIds(onlineStoreIds);
      if(CollectionUtil.isNotEmpty(dsOnlineStores)){
        Map<Long, String> storeNames = dsOnlineStores.stream().collect(Collectors.toMap(DsOnlineStore::getId, DsOnlineStore::getOnlineStoreName));
        strategies.forEach(e->{
          e.setOnlineStoreNames(Arrays.stream(e.getOnlineStoreIds().split(";")).map(storeId -> storeNames.get(Long.valueOf(storeId))).collect(Collectors.toList()));
        });
      }
    }
    return strategyInfoPage;
  }

  @Override
  public StrategyDetailsRepresentationDto getStrategyDetail(Long strategyId) {
    StrategyDetailsRepresentationDto strategyDetails = strategyRepository.getStrategyDetail(strategyId);
    if(Objects.nonNull(strategyDetails) && Objects.nonNull(strategyDetails.getStrategyRules())){
      //场景说明
      RouteScene routeScene = sceneRepository.getSceneById(strategyDetails.getSceneId());
      strategyDetails.setSceneMark(Objects.isNull(routeScene)?"":routeScene.getMark());
      List<StrategyRuleRepresentationDto> strategyRules = strategyDetails.getStrategyRules();
      List<Long> ruleIds = strategyRules.stream().map(StrategyRuleRepresentationDto::getRuleId).collect(Collectors.toList());
      List<RouteRule> routeRules = strategyRepository.listRuleByIds(ruleIds);
      Map<Long, String> ruleNames = routeRules.stream().collect(Collectors.toMap(RouteRule::getId,RouteRule::getRuleName));
      Map<Long, String> handleTypes = routeRules.stream().collect(Collectors.toMap(RouteRule::getId, RouteRule::getHandleType));
      strategyRules.forEach(e->{
        //规则名称
        e.setRuleName(ruleNames.get(e.getRuleId()));
        //规则处理类型
        e.setHandleType(handleTypes.get(e.getRuleId()));
      });
    }
    return strategyDetails;
  }

  @Override
  public int saveStrategy(StrategyAggregate strategyAggregate) {
    StrategyAggregate.strategyInfoCheck(strategyAggregate);
    StrategyAggregate.strategyInfoInit(strategyAggregate);
    this.checkStrategy(strategyAggregate);
    return strategyRepository.saveStrategy(strategyAggregate);
  }

  @Override
  public int updateStrategy(StrategyAggregate strategyAggregate) {
    StrategyAggregate.strategyInfoCheck(strategyAggregate);
    StrategyAggregate.strategyInfoInit(strategyAggregate);
    this.checkStrategy(strategyAggregate);
    int row = strategyRepository.updateStrategy(strategyAggregate);
    if(row < 1){
      throw ExceptionUtil.getWarnException(ErrorType.PARA_ERROR.getCode(),"更新策略失败，请刷新后重新编辑保存");
    }
    return row;
  }

  @Override
  public int deleteRouteStrategy(Long strategyId) {
    return strategyRepository.deleteStrategy(strategyId);
  }

  @Override
  public List<TakeOrdersStoreRepresentationDto> listTakeOrdersStore(String orgCode, String distanceTypeName) {
    if(StringUtils.isBlank(orgCode) || StringUtils.isBlank(distanceTypeName)){
      return null;
    }
    Integer distanceTypeValue = DistanceTypeEnum.getValue(distanceTypeName);
    if(!ValidateUtil.greaterThanZero(distanceTypeValue)){
      throw ExceptionUtil.getWarnException(ErrorType.PARA_ERROR.getCode(),"无效距离");
    }
    StoreInfoDataResDTO storeInfo = routeClientService.getStoreInfo(orgCode);
    StoreListReqDTO storeListReqDTO = new StoreListReqDTO();
    storeListReqDTO.setLatitude(storeInfo.getLatitude());
    storeListReqDTO.setLongitude(storeInfo.getLongitude());
    storeListReqDTO.setCurrentPage(1);
    storeListReqDTO.setPageSize(100);
    storeListReqDTO.setMerCode("500001");
    List<StoreListResDTO> storeResDTOList = routeClientService.getStoreResDTOList(storeListReqDTO);
    if(CollectionUtil.isEmpty(storeResDTOList)){
      return null;
    }

    List<TakeOrdersStoreRepresentationDto> takeOrdersStores = new ArrayList<>();
    List<StoreListResDTO> appointStoreListResDTO = storeResDTOList.stream()
        .filter(e -> StringUtils.isNotBlank(e.getDistance()) && Double.parseDouble(e.getDistance()) <= distanceTypeValue && !e.getStCode().equals(orgCode))
        .collect(Collectors.toList());
    log.info("指定{}门店:{} 附近{}公里门店信息：{}",orgCode, JSONObject.toJSON(storeListReqDTO),distanceTypeValue,JSONObject.toJSON(appointStoreListResDTO));
    if(CollectionUtil.isEmpty(appointStoreListResDTO)){
      return null;
    }
    appointStoreListResDTO.forEach(e->{
      TakeOrdersStoreRepresentationDto takeOrdersStore = new TakeOrdersStoreRepresentationDto();
      takeOrdersStore.setReStoreCode(e.getStCode());
      takeOrdersStore.setReStoreName(e.getStName());
      takeOrdersStore.setDistance(Double.parseDouble(e.getDistance()) * 1000);
      takeOrdersStores.add(takeOrdersStore);
    });
    return takeOrdersStores;
  }

  @Override
  public List<OnlineStoreRepresentationDto> listOnlineStore(String orgCode) {
    LambdaQueryWrapper<DsOnlineStore> queryWrapper = new LambdaQueryWrapper<DsOnlineStore>()
        .eq(DsOnlineStore::getServiceMode,"O2O")
        .eq(DsOnlineStore::getOrganizationCode,orgCode);
    List<DsOnlineStore> dsOnlineStores = dsOnlineStoreRepo.selectList(queryWrapper);
    if(CollUtil.isEmpty(dsOnlineStores)){
      return null;
    }
    List<OnlineStoreRepresentationDto> onlineStoreDtos = new ArrayList<>();
    dsOnlineStores.forEach(e->{
      OnlineStoreRepresentationDto  onlineStoreDto = BeanUtil.toBean(e, OnlineStoreRepresentationDto.class);
      onlineStoreDtos.add(onlineStoreDto);
    });
    return onlineStoreDtos;
  }

  @Override
  public int updateStrategyState(StrategyAggregate strategyAggregate) {
    StrategyAggregate.strategyInfoInit(strategyAggregate);
    this.checkStrategyStatus(strategyAggregate.getStrategyId().getRouteStrategyId(),strategyAggregate.getState(),strategyAggregate.getOnlineStoreIds());
    int row = strategyRepository.updateStrategyState(strategyAggregate);
    if(row < 1){
      throw ExceptionUtil.getWarnException(ErrorType.PARA_ERROR.getCode(),"更新策略状态失败，请刷新后重新编辑保存");
    }
    return row;
  }

  @Override
  public List<GoodExcelRepresentationDto> listImportGoodInfos(MultipartFile excelFile) {
    List<GoodExcelRepresentationDto> goodExcelRepresentationList = null;
    try{
      goodExcelRepresentationList = ExcelUtil.read(excelFile, GoodExcelRepresentationDto.class);
    }catch (IOException e){
      throw ExceptionUtil.getWarnException(ErrorType.PARA_ERROR.getCode(),"解析Excel文件失败："+e.getMessage());
    }
    if(CollectionUtil.isEmpty(goodExcelRepresentationList)){
      throw ExceptionUtil.getWarnException(ErrorType.PARA_ERROR.getCode(),"Excel中数据为空，导入失败");
    }
    if(goodExcelRepresentationList.size() > 2000){
      throw ExceptionUtil.getWarnException(ErrorType.PARA_ERROR.getCode(),"导入的数据量不能超过2000条");
    }
    List<GoodExcelRepresentationDto> NonErpCodeList = goodExcelRepresentationList.stream().filter(e -> StringUtils.isNotBlank(e.getErpCode())).collect(Collectors.toList());
    if(CollectionUtil.isEmpty(NonErpCodeList)){
      throw ExceptionUtil.getWarnException(ErrorType.PARA_ERROR.getCode(),"Excel中商品ID为必填项，请完善数据后重试");
    }
    Set<String> erpCodes = NonErpCodeList.stream().map(GoodExcelRepresentationDto::getErpCode).collect(Collectors.toSet());
    Map<String, String> goodNames = routeClientService.getGoodNames(erpCodes);
    if(goodNames.isEmpty()){
      throw ExceptionUtil.getWarnException(ErrorType.PARA_ERROR.getCode(),"商品ID有误，无对应的商品");
    }
    for (GoodExcelRepresentationDto goodExcel : NonErpCodeList) {
      goodExcel.setCommodityName(goodNames.get(goodExcel.getErpCode()));
    }
    return NonErpCodeList;
  }

  @Override
  public boolean checkStoreDelivery(String orgCode) {
    int deliveryCount = dsOnlineStoreDeliveryRepo.getDeliveryCountByCode(orgCode, DsDeliveryType.DD.getPlatformName());
    return deliveryCount > 0;
  }

  /**
   * 策略业务校验
   * @param strategyAggregate
   */
  private void checkStrategy(StrategyAggregate strategyAggregate) {
    List<StrategyRuleAggregate> strategyRules = strategyAggregate.getStrategyRules();
    String storeCode = strategyAggregate.getStoreCode();
    //接单门店校验
    strategyRules.stream()
        .filter(e->RouteHandTypeConstants.RECEIVE_SHOP_LIST_HANDLE.equals(e.getHandleType()))
        .forEach(f->{
          this.checkTransferDistance(storeCode,f.getRouteReceiveStores());
          this.checkPosMode(storeCode,f.getRouteReceiveStores());
        });
  }

  /**
   * 校验分单门店和接单门店的距离 不可超过5km
   * @param outStoreCode
   * @param receiveStores
   */
  private void checkTransferDistance(String outStoreCode,List<RouteReceiveStoreAggregate> receiveStores){
    if(Objects.isNull(outStoreCode) || Objects.isNull(receiveStores)){
      throw ExceptionUtil.getWarnException(ErrorType.PARA_ERROR.getCode(),"分单/接单门店编码参数异常");
    }
    List<String> reStoreCodes = receiveStores.stream().map(RouteReceiveStoreAggregate::getReStoreCode)
        .collect(Collectors.toList());
    if(reStoreCodes.contains(outStoreCode)){
      throw ExceptionUtil.getWarnException(ErrorType.PARA_ERROR.getCode(),"分单门店不能存在于接单门店中");
    }
    List<TakeOrdersStoreRepresentationDto> takeOrdersStoreRepresentationDtos = this.listTakeOrdersStore(
        outStoreCode, DistanceTypeEnum.FIVE_KM.name());
    if(CollectionUtil.isNotEmpty(takeOrdersStoreRepresentationDtos)){
      List<String> takeOrdersStoreCodes = takeOrdersStoreRepresentationDtos.stream()
          .map(TakeOrdersStoreRepresentationDto::getReStoreCode).collect(Collectors.toList());
      reStoreCodes.forEach(e->{
        if(!takeOrdersStoreCodes.contains(e)){
          throw ExceptionUtil.getWarnException(ErrorType.PARA_ERROR.getCode(),"分单距离不可超过5km");
        }
      });
    }
  }

  /**
   * 校验策略状态
   * @param strategyId
   * @param status
   * @param onlineStoreIds
   */
  private void checkStrategyStatus(Long strategyId,Boolean status,String onlineStoreIds){
    if(status){
      if(StringUtils.isBlank(onlineStoreIds)){
        throw ExceptionUtil.getWarnException(ErrorType.PARA_ERROR.getCode(),"无法获取待启用策略的店铺信息");
      }
      //获取已启用的策略
      StrategyAggregate strategyAggregate = new StrategyAggregate();
      strategyAggregate.setState(Boolean.TRUE);
      List<RouteStrategy> strategies = strategyRepository.listStrategy(strategyAggregate);
      if(CollectionUtil.isNotEmpty(strategies)){
        List<String> storeIds = new ArrayList<>();
        strategies.forEach(e->{
          if(!e.getId().equals(strategyId)){
            storeIds.addAll(Arrays.asList(e.getOnlineStoreIds().split(";")));
          }
        });
        for (String onlineStoreId : onlineStoreIds.split(";")) {
          if(storeIds.contains(onlineStoreId)){
            throw ExceptionUtil.getWarnException(ErrorType.PARA_ERROR.getCode(),"每个店铺只能有一条生效的策略");
          }
        }
      }
    }
  }

  /**
   * 分单门店和接单门店POS模式校验
   * @param outStoreCode
   * @param receiveStores
   */
  private void checkPosMode(String outStoreCode,List<RouteReceiveStoreAggregate> receiveStores){
    if(Objects.isNull(outStoreCode) || CollectionUtil.isEmpty(receiveStores)){
      throw ExceptionUtil.getWarnException(ErrorType.PARA_ERROR.getCode(),"分单/接单门店编码参数异常");
    }
    LambdaQueryWrapper<InnerStoreDictionary> queryWrapper;
    queryWrapper = new LambdaQueryWrapper<InnerStoreDictionary>()
        .eq(InnerStoreDictionary::getOrganizationCode,outStoreCode);
    InnerStoreDictionary outInnerStoreDictionary = innerStoreDictionaryMapper.selectOne(queryWrapper);
    if(Objects.isNull(outInnerStoreDictionary)){
      throw ExceptionUtil.getWarnException(ErrorType.PARA_ERROR.getCode(),"分单门店未配置POS机模式");
    }
    queryWrapper = new LambdaQueryWrapper<InnerStoreDictionary>()
        .in(InnerStoreDictionary::getOrganizationCode,receiveStores.stream().map(RouteReceiveStoreAggregate::getReStoreCode).collect(Collectors.toList()));
    List<InnerStoreDictionary> receiveInnerStoreDictionaries = innerStoreDictionaryMapper.selectList(queryWrapper);
    if(CollUtil.isEmpty(receiveInnerStoreDictionaries)){
      throw ExceptionUtil.getWarnException(ErrorType.PARA_ERROR.getCode(),"接单门店未配置POS机模式");
    }
    receiveInnerStoreDictionaries.forEach(e->{
      if(!e.getPosMode().equals(outInnerStoreDictionary.getPosMode())){
        throw ExceptionUtil.getWarnException(ErrorType.PARA_ERROR.getCode(),"分单门店和接单门店的POS机模式需一致");
      }
    });
  }

  public boolean checkStrategyChange(StrategyDetailsRepresentationDto oldStrategy,StrategyAggregate strategyAggregate){
    if(Objects.isNull(oldStrategy)){
      return true;
    }
    //对比分单时间是否有变化
    //旧的分单时间数据
    Optional<StrategyRuleRepresentationDto> oldOutTimeRule = oldStrategy.getStrategyRules().stream()
        .filter(e -> e.getHandleType().equals(RouteHandTypeConstants.OUT_TIME_FRAME_HANDLE))
        .findFirst();
    int oldOutTimeSize = 0;
    if(oldOutTimeRule.isPresent() && CollectionUtil.isNotEmpty(oldOutTimeRule.get().getRouteOutTimePeriods())){
      oldOutTimeSize = oldOutTimeRule.get().getRouteOutTimePeriods().size();
    }
    //新分单时间数据
    Optional<StrategyRuleAggregate> newOutTimeRule = strategyAggregate.getStrategyRules().stream()
        .filter(e -> e.getHandleType().equals(RouteHandTypeConstants.OUT_TIME_FRAME_HANDLE))
        .findFirst();
    int newOutTimeSize = 0;
    if(newOutTimeRule.isPresent() && CollectionUtil.isNotEmpty(newOutTimeRule.get().getRouteOutTimePeriods())){
      newOutTimeSize = newOutTimeRule.get().getRouteOutTimePeriods().size();
    }
    if(oldOutTimeSize != newOutTimeSize){
      return true;
    }
    if(oldOutTimeSize != 0){
      //根据分单时间开始时间排序
      List<RouteTimePeriodRepresentationDto> oldOutTime = oldOutTimeRule.get().getRouteOutTimePeriods().stream().sorted(Comparator.comparing(RouteTimePeriodRepresentationDto::getStartTime)).collect(Collectors.toList());
      List<RouteTimePeriodAggregate> newOutTime = newOutTimeRule.get().getRouteOutTimePeriods().stream().sorted(Comparator.comparing(RouteTimePeriodAggregate::getStartTime)).collect(Collectors.toList());
      for (int i = 0; i < oldOutTimeSize; i++) {
        RouteTimePeriodRepresentationDto oldOutTimePeriod = oldOutTime.get(i);
        RouteTimePeriodAggregate newOutTimePeriod = newOutTime.get(i);
        if((!oldOutTimePeriod.getStartTime().equals(newOutTimePeriod.getStartTime())
            || (!oldOutTimePeriod.getEndTime().equals(newOutTimePeriod.getEndTime())))){
          return true;
        }
      }
    }
    //对比兜底门店是否变化
    Optional<StrategyRuleRepresentationDto> oldReceiveStoreRule = oldStrategy.getStrategyRules().stream()
        .filter(e -> e.getHandleType().equals(RouteHandTypeConstants.RECEIVE_SHOP_LIST_HANDLE))
        .findFirst();
    Optional<StrategyRuleAggregate> newReceiveStoreRule = strategyAggregate.getStrategyRules().stream()
        .filter(e -> e.getHandleType().equals(RouteHandTypeConstants.RECEIVE_SHOP_LIST_HANDLE))
        .findFirst();
    //聚合根中做过非空检验
    RouteReceiveStoreRepresentationDto oldReceiveStore = oldReceiveStoreRule.get().getRouteReceiveStores().stream().filter(RouteReceiveStoreRepresentationDto::getAcquiesce).findFirst().get();
    RouteReceiveStoreAggregate newReceiveStore = newReceiveStoreRule.get().getRouteReceiveStores().stream().filter(RouteReceiveStoreAggregate::getAcquiesce).findFirst().get();
    return !oldReceiveStore.getReStoreCode().equals(newReceiveStore.getReStoreCode());
  }

  @Override
  public IPage<StrategyChangeDto> pageStrategyChange(StrategyPageDto dto) {
    StrategyDomainQueryDto queryDto = new StrategyDomainQueryDto();
    if (dto.getStatus() !=null) {
      queryDto.setState(dto.getStatus());
    }
    if(!CollectionUtils.isEmpty(dto.getSceneTypeList())) {
      queryDto.setSceneTypeList(dto.getSceneTypeList());
    }
    if (StringUtils.isNotEmpty(dto.getStoreCode())) {
      queryDto.setStoreCode(dto.getStoreCode());
    }
    queryDto.setCurrentPage(dto.getCurrentPage());
    queryDto.setPageSize(dto.getPageSize());
    IPage<StrategyRepresentationDto> page = strategyRepository.pageStrategyInfo(
        queryDto);
    if (CollectionUtils.isEmpty(page.getRecords())) {
      return new Page<>();
    }
    List<StrategyRepresentationDto> records = page.getRecords();
    //查询规则
    Map<String, Long> ruleMap = sceneRepository.listRule().stream()
        .collect(Collectors.toMap(RuleRepresentationDto::getHandleType, RuleRepresentationDto::getId));
    //查询分店时间段
    List<Long> strategyIds = records.stream().map(StrategyRepresentationDto::getId).collect(Collectors.toList());

    Map<Long, Long> strategyRuleTimeMap = strategyRepository.listRouteStrategyRuleByStrategyIdsAndRuleId(
            strategyIds, ruleMap.get(RouteHandTypeConstants.OUT_TIME_FRAME_HANDLE))
        .stream().collect(Collectors.toMap(RouteStrategyRule::getStrategyId,RouteStrategyRule::getId, (v1, v2)-> v1 ));

    Map<Long, List<RouteTimePeriodSet>> timePeriodGroup = strategyRepository.listTimePeriodSetByRuleIds(
        strategyRuleTimeMap.values()).stream().collect(Collectors.groupingBy(RouteTimePeriodSet::getStrategyRuleId));

    //查询接单门店
    Map<Long, Long> strategyRuleStoreMap = strategyRepository.listRouteStrategyRuleByStrategyIdsAndRuleId(
        strategyIds, ruleMap.get(RouteHandTypeConstants.RECEIVE_SHOP_LIST_HANDLE)).stream()
        .collect(Collectors.toMap(RouteStrategyRule::getStrategyId, RouteStrategyRule::getId, (v1, v2) -> v1));
    Map<Long, List<RouteReceiveStoreSet>> reStoreMap = strategyRepository.listReceiveStoreSetByStrategyByRuleIds(
            strategyRuleStoreMap.values()).stream()
        .collect(Collectors.groupingBy(RouteReceiveStoreSet::getStrategyRuleId));
    Map<Long, Long> idToSceneIdMap = records.stream()
        .collect(Collectors.toMap(StrategyRepresentationDto::getId, StrategyRepresentationDto::getSceneId));
    Map<Long, String> sceneMap = sceneRepository.getSceneByIds(idToSceneIdMap.values()).stream()
        .collect(Collectors.toMap(RouteScene::getId, RouteScene::getSceneType, (v1, v2) -> v1));

    List<Long> onlineIds = new ArrayList<>();
    //查询店铺
    records.stream().map(StrategyRepresentationDto::getOnlineStoreIds).forEach(x-> {
      Arrays.asList(x.split(";")).forEach(y-> {
         onlineIds.add(Long.valueOf(y));
      });
    });
    Map<Long, DsOnlineStore> onlineStoreMap = dsOnlineStoreRepo.selectBatchIds(onlineIds).stream()
        .collect(Collectors.toMap(DsOnlineStore::getId, x -> x));

    //组装数据
    List<StrategyChangeDto> strategyChangeDtos = new ArrayList<>();
    records.forEach(x-> {
      StrategyChangeDto resDto = new StrategyChangeDto();
      resDto.setStrategyId(x.getId());
      resDto.setStatus(OnOffStatusEnum.translate(x.getState()));
      resDto.setStoreCode(x.getStoreCode());
      resDto.setStrategyType(sceneMap.get(x.getSceneId()));
      resDto.setUpdateTime(x.getUpdatedTime());
      resDto.setVersion(x.getVersion());
      resDto.setCreateTime(x.getCreatedTime());
      //时间段添加
      Long timeRuleId = strategyRuleTimeMap.get(x.getId());
      Optional<List<RouteTimePeriodSet>> routeTimePeriodSets = Optional.ofNullable(timePeriodGroup.get(timeRuleId));
      if (routeTimePeriodSets.isPresent()) {
        List<StrategyTime> strategyTimeList = routeTimePeriodSets.get().stream().map(t -> {
          StrategyTime strategyTime = new StrategyTime();
          strategyTime.setStartTime(t.getStartTime().toString());
          strategyTime.setEndTime(t.getEndTime().toString());
          return strategyTime;
        }).collect(Collectors.toList());
        resDto.setStrategyTimelist(strategyTimeList);
      }
      //门店添加
      Long storeRuleId = strategyRuleStoreMap.get(x.getId());
      Optional<List<RouteReceiveStoreSet>> routeReceiveStoreSet = Optional.ofNullable(reStoreMap.get(storeRuleId));
        routeReceiveStoreSet.flatMap(routeReceiveStoreSets -> routeReceiveStoreSets.stream()
                .filter(RouteReceiveStoreSet::getAcquiesce).findFirst())
            .ifPresent(s -> resDto.setReStoreCode(s.getReStoreCode()));
      //店铺添加
      List<ThirdStore> strategyStores = new ArrayList<>();
      Arrays.asList(x.getOnlineStoreIds().split(";"))
          .forEach(y-> {
            if (onlineStoreMap.containsKey(Long.valueOf(y))) {
              ThirdStore strategyStore = new ThirdStore();
              strategyStore.setThirdStoreCode(onlineStoreMap.get(Long.valueOf(y)).getOnlineStoreCode());
              strategyStore.setThirdPlatformType(onlineStoreMap.get(Long.valueOf(y)).getPlatformCode());
              strategyStores.add(strategyStore);
            }
          });
      resDto.setThirdStoreList(strategyStores);

      strategyChangeDtos.add(resDto);
    });

    IPage<StrategyChangeDto> resPage = new Page<>();
    resPage.setRecords(strategyChangeDtos);
    resPage.setTotal(page.getTotal());
    resPage.setPages(page.getPages());
    resPage.setCurrent(page.getCurrent());
    resPage.setSize(page.getSize());

    return resPage;
  }

  @Override
  public List<SubCompanyRepresentationDto> listSubCompany(String userId) {
    SysUserOrganizationLayerReqDTO sysUserOrganizationLayerReqDTO = new SysUserOrganizationLayerReqDTO();
    sysUserOrganizationLayerReqDTO.setUserId(userId);
    sysUserOrganizationLayerReqDTO.setLayer(String.valueOf(1));
    List<SubOrgInfoDataResDTO> organizationInfoListByOrClass = routeClientService.getOrganizationInfoListByOrClass(sysUserOrganizationLayerReqDTO);
    if(CollectionUtil.isEmpty(organizationInfoListByOrClass)){
      return null;
    }
    return BeanUtil.copyToList(organizationInfoListByOrClass,SubCompanyRepresentationDto.class);
  }

  @Override
  public List<StoreInfoRepresentationDto> listStoreInfoCode(String orgCode) {
    OrgInfoCodesReqDTO orgInfoCodesReqDTO = new OrgInfoCodesReqDTO();
    orgInfoCodesReqDTO.setMerCode("500001");
    orgInfoCodesReqDTO.setLimit(5000);
    orgInfoCodesReqDTO.setOrgCodeList(Lists.newArrayList(orgCode));
    OrgInfoCodesResDTO orgInfoCodes = routeClientService.getOrgInfoCodes(orgInfoCodesReqDTO);
    if(Objects.isNull(orgInfoCodes) || CollectionUtil.isEmpty(orgInfoCodes.getStoreInfoList())){
      return null;
    }
    return BeanUtil.copyToList(orgInfoCodes.getStoreInfoList(), StoreInfoRepresentationDto.class);
  }

  @Override
  public StrategyOpenDto validOpenStrategy(List<String> organizationCodes) {
    return strategyRepository.validOpenStrategy(organizationCodes);
  }
}
