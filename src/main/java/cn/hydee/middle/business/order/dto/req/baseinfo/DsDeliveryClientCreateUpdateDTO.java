package cn.hydee.middle.business.order.dto.req.baseinfo;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 *
 * <AUTHOR>
 */
@Data
public class DsDeliveryClientCreateUpdateDTO {

    @ApiModelProperty(value = "配送网店主键ID")
    private Long id;

    @ApiModelProperty(value = "配送平台标记")
    private String platformCode;

    @ApiModelProperty(value = "o2o平台名")
    private String platformName;

    @ApiModelProperty(value = "appid")
    private String appid;

    @ApiModelProperty(value = "Secret Key")
    private String appSecret;

    @ApiModelProperty(value = "网店编码")
    private String clientCode;

    @ApiModelProperty(value = "网店名称")
    private String clientName;

    @ApiModelProperty(value = "商家ID(京东到家，微商城需要)")
    private String sellerId;
}
