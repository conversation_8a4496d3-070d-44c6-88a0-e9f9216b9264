package cn.hydee.middle.business.order.route.interfaces;

import cn.hutool.core.bean.BeanUtil;
import cn.hydee.middle.business.order.route.application.SceneApplicationService;
import cn.hydee.middle.business.order.route.application.command.SceneOperateCommand;
import cn.hydee.middle.business.order.route.application.command.SceneQueryCommand;
import cn.hydee.middle.business.order.route.application.representation.RuleRepresentationDto;
import cn.hydee.middle.business.order.route.application.representation.SceneConstantRepresentationDto;
import cn.hydee.middle.business.order.route.application.representation.SceneDetailsRepresentationDto;
import cn.hydee.middle.business.order.route.application.representation.SceneRepresentationDto;
import cn.hydee.middle.business.order.route.application.representation.SceneSelectedRepresentationDto;
import cn.hydee.middle.business.order.route.domain.external.RouteClientService;
import cn.hydee.middle.business.order.route.interfaces.request.SceneOperateDto;
import cn.hydee.middle.business.order.route.interfaces.request.SceneQueryDto;
import cn.hydee.starter.controller.AbstractController;
import cn.hydee.starter.dto.ErrorType;
import cn.hydee.starter.dto.PageBase;
import cn.hydee.starter.dto.ResponseBase;
import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.metadata.IPage;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import java.util.List;
import java.util.Objects;
import javax.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestHeader;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

/**
 * <AUTHOR>
 * @date 2024/03/20 14:03
 **/
@RestController
@RequestMapping(value = "/${api.version}/ds/scene")
@Api(tags = "订单路由场景")
@Slf4j
public class SceneController extends AbstractController {

  @Resource
  private SceneApplicationService sceneApplicationService;

  @Resource
  private RouteClientService routeClientService;
  @ApiOperation(value = "获取场景列表")
  @PostMapping(value = "/page")
  public ResponseBase<IPage<SceneRepresentationDto>> pageScene(@RequestBody SceneQueryDto sceneQueryDto){
    if(Objects.isNull(sceneQueryDto)){
      return generateError(ErrorType.PARA_ERROR);
    }
    SceneQueryCommand sceneQueryCommand = BeanUtil.toBean(sceneQueryDto,SceneQueryCommand.class);
    return generateSuccess(sceneApplicationService.pageSceneScene(sceneQueryCommand));
  }

  @ApiOperation(value = "获取场景详情")
  @GetMapping(value = "/detail")
  public ResponseBase<SceneDetailsRepresentationDto> getSceneDetail(@RequestParam("sceneId") String sceneId){
    if(!StringUtils.isNumeric(sceneId)){
      //isNumeric校验传入字符串是否为正整数
      return generateError(ErrorType.PARA_ERROR);
    }
    return generateSuccess(sceneApplicationService.getSceneDetail(sceneId));
  }

  @ApiOperation(value = "保存场景")
  @PostMapping(value = "/save")
  public ResponseBase<Integer> saveScene(@RequestHeader("userId") String userId, @RequestBody SceneOperateDto sceneOperateDto){
    if(Objects.isNull(sceneOperateDto)){
      return generateError(ErrorType.PARA_ERROR);
    }
    routeClientService.validAuthority(userId);
    SceneOperateCommand sceneOperateCommand = BeanUtil.toBean(sceneOperateDto,SceneOperateCommand.class);
    sceneOperateCommand.setCreatedBy(userId);
    sceneOperateCommand.setUpdatedBy(userId);
    return generateSuccess(sceneApplicationService.saveScene(sceneOperateCommand));
  }

  @ApiOperation(value = "更新场景")
  @PostMapping(value = "/update")
  public ResponseBase<Integer> updateScene(@RequestHeader("userId") String userId, @RequestBody SceneOperateDto sceneOperateDto){
    if(Objects.isNull(sceneOperateDto)){
      return generateError(ErrorType.PARA_ERROR);
    }
    routeClientService.validAuthority(userId);
    SceneOperateCommand sceneOperateCommand = BeanUtil.toBean(sceneOperateDto,SceneOperateCommand.class);
    sceneOperateCommand.setUpdatedBy(userId);
    return generateSuccess(sceneApplicationService.updateScene(sceneOperateCommand));
  }

  @ApiOperation(value = "删除场景")
  @GetMapping(value = "/delete")
  public ResponseBase<Integer> deleteScene(@RequestHeader("userId") String userId, @RequestParam("sceneId") String sceneId){
    if(!StringUtils.isNumeric(sceneId)){
      return generateError(ErrorType.PARA_ERROR);
    }
    routeClientService.validAuthority(userId);
    SceneDetailsRepresentationDto sceneDetails = sceneApplicationService.getSceneDetail(sceneId);
    log.info("删除场景，操作人：{}，删除内容：{}",userId, JSONObject.toJSONString(sceneDetails));
    return generateSuccess(sceneApplicationService.deleteScene(sceneId));
  }

  @ApiOperation(value = "获取场景常量")
  @GetMapping(value = "/constant")
  public ResponseBase<SceneConstantRepresentationDto> getSceneConstant(){
    return generateSuccess(SceneConstantRepresentationDto.builder());
  }
  @ApiOperation(value = "规则列表")
  @GetMapping(value = "/list/rule")
  public ResponseBase<List<RuleRepresentationDto>> listRule(){
    return generateSuccess(sceneApplicationService.listRule());
  }

  @ApiOperation(value = "获取可选择场景")
  @PostMapping(value = "/page/selected")
  public ResponseBase<IPage<SceneSelectedRepresentationDto>> pageSceneSelected(@RequestBody PageBase pageBase){
    return generateSuccess(sceneApplicationService.pageSceneSelected(pageBase));
  }

  @ApiOperation(value = "获取场景下规则明细")
  @GetMapping(value = "/rule/detail")
  public ResponseBase<List<RuleRepresentationDto>> sceneRuleDetail(@RequestParam("sceneId") String sceneId){
    if(!StringUtils.isNumeric(sceneId)){
      return generateError(ErrorType.PARA_ERROR);
    }
    return generateSuccess(sceneApplicationService.sceneRuleDetail(sceneId));
  }

}