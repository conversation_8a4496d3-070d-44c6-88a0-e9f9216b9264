package cn.hydee.middle.business.order.dto;

import cn.hydee.middle.business.order.dto.req.DeliveryStateList;
import cn.hydee.middle.business.order.dto.req.OrderStateList;
import cn.hydee.starter.dto.PageBase;
import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.Date;

@Data
public class DeliveryQuerySearchReqDto extends PageBase {
    @ApiModelProperty(value = "商家编码", hidden = true)
    private String merCode;
    @JsonFormat(shape = JsonFormat.Shape.STRING)
    @ApiModelProperty(value="订单号")
    private Long orderNo;
    @ApiModelProperty(value = "三方订单号")
    private String thirdOrderNo;
    @ApiModelProperty(value="配送单号")
    private String deliveryNo;
    @ApiModelProperty(value="系统配送单号")
    private String deliveryId;
    @ApiModelProperty(value = "配送状态：0 未呼叫，1 待接单，2 待取货 3 已取货 4 已完成 5 已取消 6 已过期 7 异常")
    private DeliveryStateList deliveryState;
    @ApiModelProperty(value = "线下门店编码")
    private String organizationCode;
    @ApiModelProperty(value = "平台编码")
    private String platformCode;
    @ApiModelProperty(value = "门店编码")
    private String onlineStoreCode;
    @ApiModelProperty(value = "查询创建开始时间")
    private Date beginTime;
    @ApiModelProperty(value = "查询创建结束时间")
    private Date endTime;
    @ApiModelProperty(value = "配送中心：蜂鸟骑手 美团骑手 达达骑手 顺丰同城 蜂鸟即配")
    private String deliveryPlatName;

}
