## 整单
```mysql
drop table order_model;
CREATE TABLE `order_model` (
  `id` bigint NOT NULL AUTO_INCREMENT,
	`mer_code` varchar(20) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '商户编码',
	`order_no` bigint NOT NULL COMMENT '订单号，雪花算法',
	`order_status` tinyint DEFAULT NULL COMMENT '订单状态:5待处理,10待接单,20待拣货,30待配送,40待收货,100已完成,102已取消,101已关闭',
	`order_type` tinyint DEFAULT '0' COMMENT '订单类型:0普通订单,1预约订单,2处方订单,3积分订单,5运费订单,30机器自动拣货订单',
	`online_store_code` varchar(60) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '下单线上门店编码',
  `online_store_name` varchar(128) CHARACTER SET utf8 COLLATE utf8_general_ci DEFAULT NULL COMMENT '下单线上门店名称',
  `organization_code` varchar(40) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '线下门店编码',
  `organization_name` varchar(128) CHARACTER SET utf8 COLLATE utf8_general_ci DEFAULT NULL COMMENT '线下门店名称',
	`source_online_store_code` varchar(60) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '来源线上门店编码',
  `source_online_store_name` varchar(128) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '来源线上门店名称',
  `source_organization_code` varchar(40) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '来源线下门店编码',
  `source_organization_name` varchar(128) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '来源线下门店名称',
	`pay_type` varchar(10) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '支付方式,1是在线支付,2是货到付款吧',
	`pay_time` datetime DEFAULT NULL COMMENT '支付时间',
	`delivery_type` varchar(20) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '配送方式',
	`total_actual_order_amount` decimal(16,2) DEFAULT '0.00' COMMENT '客户实付',
	`actual_freight_amount` decimal(16,2) DEFAULT '0.00' COMMENT '配送费',
	`member_card` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '会员编号',
	`created` datetime DEFAULT NULL COMMENT '订单实际创建时间',
	`create_time` datetime DEFAULT CURRENT_TIMESTAMP,
	`accept_time` datetime DEFAULT NULL COMMENT '接单时间',
	`complete_time` datetime DEFAULT NULL COMMENT '完成时间',
	`service_mode` varchar(10) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT 'O2O' COMMENT '服务模式',
  PRIMARY KEY (`id`) USING BTREE,
  UNIQUE KEY `u_order_no` (`order_no`) USING BTREE,
  KEY `idx_create_time` (`create_time`),
  KEY `i_mer_online_code` (`mer_code`,`online_store_code`) USING BTREE,
  KEY `idx_code` (`mer_code`,`organization_code`,`id` DESC),
  KEY `idx_created` (`created`,`mer_code`) USING BTREE,
  KEY `idx_source_organization_code` (`source_organization_code`) USING BTREE
) ENGINE=InnoDB AUTO_INCREMENT=1 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci ROW_FORMAT=DYNAMIC COMMENT='订单模型数据';

drop table order_detail_model;
CREATE TABLE `order_detail_model` (
  `id` bigint NOT NULL AUTO_INCREMENT,
  `order_no` bigint NOT NULL COMMENT '订单号',
	`commodity_code` varchar(60) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '商品erp编码',
	`commodity_name` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '商品名称',
	`commodity_count` int DEFAULT NULL COMMENT '商品数量',
	`total_actual_amount` decimal(16,2) DEFAULT '0.00' COMMENT '成交总额小计-优惠',
	`goods_type` tinyint(1) DEFAULT '1' COMMENT '商品类型，1普通商品，2erp赠品，3换货后的商品，4换货的源商品',
	 `is_joint` tinyint DEFAULT '0' COMMENT '是否是组合商品，0不是组合商品，1是组合商品',
	 `main_pic` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '商品图片',
	 `is_original` tinyint DEFAULT '0' COMMENT '是否是原始明细 0-是 1-否',
	 	`create_time` datetime DEFAULT CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`) USING BTREE,
  KEY `idx_order_no` (`order_no`) USING BTREE,
  KEY `idx_create_time` (`create_time`)
) ENGINE=InnoDB AUTO_INCREMENT=1 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci ROW_FORMAT=DYNAMIC COMMENT='订单明细模型数据';
```

              

## 退款
```mysql
drop table refund_order_model;
CREATE TABLE `refund_order_model` (
  `id` bigint NOT NULL AUTO_INCREMENT COMMENT '记录ID',
	  `mer_code` varchar(20) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '商户编码',
	 `refund_no` bigint NOT NULL COMMENT '退款单号，雪花算法',
	 `third_refund_no` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '三方平台退款ID',
	 `order_no` bigint NOT NULL COMMENT '订单号',
	 `third_order_no` varchar(45) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL COMMENT '第三方平台订单号',
		`complete_time` datetime DEFAULT NULL COMMENT '退款完成时间',
	`refund_status` tinyint DEFAULT NULL COMMENT '退款单状态,10-待退款，20-待退货，100-已完成，102-已拒绝，103-已取消',
	`member_card` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '会员编号',
	`organization_code` varchar(40) COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '线下门店编码',
  `online_store_code` varchar(40) COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '线上门店编码',
  `source_organization_code` varchar(40) COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '来源线下门店编码',
  `source_online_store_code` varchar(40) COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '来源线上门店编码',
	`consumer_refund` decimal(16,2) DEFAULT '0.00' COMMENT '退买家总金额',
	  `platform_refund_delivery_fee` decimal(16,2) NOT NULL DEFAULT '0.00' COMMENT '退平台配送费',
	 `merchant_refund_post_fee` decimal(16,2) NOT NULL DEFAULT '0.00' COMMENT '退商家配送费',
	  `create_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  PRIMARY KEY (`id`) USING BTREE,
  UNIQUE KEY `refund_no` (`refund_no`) USING BTREE,
  KEY `idx_order` (`order_no`),
  KEY `idx_mer_create_time` (`mer_code`,`create_time`),
  KEY `idx_store` (`mer_code`,`online_store_code`)
) ENGINE=InnoDB AUTO_INCREMENT=1 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci ROW_FORMAT=DYNAMIC COMMENT='退款单模型表';



drop table refund_detail_model;
CREATE TABLE `refund_detail_model` (
  `id` bigint NOT NULL AUTO_INCREMENT,
  `refund_no` bigint NOT NULL COMMENT '退款单号',
	`commodity_code` varchar(60) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '商品erp编码',
	`commodity_name` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci DEFAULT NULL COMMENT '商品名称',
	`commodity_count` int NOT NULL COMMENT '退款数量',
	`is_gift` tinyint NOT NULL DEFAULT '0' COMMENT '是否是赠品（0不是赠品，1是赠品）',
	`create_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  PRIMARY KEY (`id`) USING BTREE,
  KEY `index_no` (`refund_no`) USING BTREE COMMENT '退款款单索引'
) ENGINE=InnoDB AUTO_INCREMENT=1 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci ROW_FORMAT=DYNAMIC COMMENT='退款明细模型表';


```


```yaml
message-notify:
  order-model-topic: TP_ORDER_BUSINESS-ORDER_ORDER-MODEL
  refund-order-model-topic: TP_ORDER_BUSINESS-ORDER_REFUND-ORDER-MODEL
```
