package cn.hydee.middle.business.order.v3.facade;

import cn.hydee.middle.business.order.entity.*;
import cn.hydee.middle.business.order.entity.b2c.*;
import lombok.Data;

import java.util.List;

/**
 * 新订单参数门面类
 * 该类使用@Data注解，Lombok库会自动生成getter和setter方法，以及toString、equals和hashCode方法
 * 主要作用是减少样板代码，简化对象的处理
 */
@Data
public class NewOrderParamFacade {

    /**
     * 平台订单信息，包含订单的基本详情和状态
     */
    private PlatformOrderInfo platformOrderInfo;

    /**
     * 平台订单优惠券信息，记录订单使用的优惠券详情
     */
    private List<PlatformOrderCoupon> platformOrderCoupon;

    /**
     * 平台订单商品项，包含订单中各个商品的信息
     */
    private List<PlatformOrderItem> platformOrderItem;

    /**
     * 平台订单支付信息，包括支付方式和支付状态
     */
    private PlatformOrderPayment platformOrderPayment;

    /**
     * 平台订单扩展信息，记录额外的订单属性
     */
    private PlatformOrderInfoExt platformOrderInfoExt;

    /**
     * ERP系统订单信息，用于与企业内部系统进行交互
     */
    private OmsOrderInfo omsOrderInfo;

    /**
     * 订单基本信息，包括订单号、下单时间等
     */
    private OrderInfo orderInfo;

    /**
     * 订单支付信息，详细记录订单的支付情况
     */
    private OrderPayInfo orderPayInfo;

    /**
     * 订单详情列表，包含订单中每个商品的详细信息
     */
    private List<OrderDetail> orderDetailList;

    /**
     * 订单配送地址信息，记录收货人的地址详情
     */
    private OrderDeliveryAddress orderDeliveryAddress;

    /**
     * 订单配送记录，跟踪订单的配送状态和历史
     */
    private OrderDeliveryRecord orderDeliveryRecord;

    /**
     * 订单配送日志，记录配送过程中的重要事件
     */
    private OrderDeliveryLog orderDeliveryLog;

    /**
     * 订单处方列表，记录需要处方才能购买的商品信息
     */
    private List<OrderPrescription> orderPrescriptionList;

    /**
     * 订单赠品信息列表，记录订单中的赠品详情
     */
    private List<OrderGiftInfo> orderGiftInfoList;

    /**
     * 订单优惠券信息列表，记录订单中每个优惠券的使用情况
     */
    private List<OrderCouponInfo> orderCouponInfoList;

    /**
     * ERP系统账单信息，用于财务处理和报告
     */
    private ErpBillInfo erpBillInfo;

    /**
     * 原始第三方订单详情列表，保存未加工的第三方订单数据
     */
    private List<OriThirdOrderDetail> oriThirdOrderDetails;

    /**
     * 订单多支付关系表
     */
    private List<OrderMultiPayInfo> orderMultiPayInfoList;

    /**
     * 拆零商品记录表，记录订单中拆零商品的相关信息
     */
    private List<OrderAssembleCommodityRelation> relationList;

}
