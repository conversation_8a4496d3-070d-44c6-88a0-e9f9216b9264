package cn.hydee.middle.business.order.comment.base.dto;

import io.swagger.annotations.ApiModelProperty;
import java.util.Date;
import java.util.List;
import javax.validation.constraints.NotNull;
import lombok.Builder;
import lombok.Data;

/**
 * <AUTHOR>
 * @date: 2020年12月28日
 */
@Data
@Builder
public class CommentByDaysDto {

  @ApiModelProperty(value = "开始日期", required = true)
  @NotNull(message = "开始日期不能为空")
  private Date startTime;
  @ApiModelProperty(value = "结束日期", required = true)
  @NotNull(message = "结束日期不能为空")
  private Date endTime;
  @ApiModelProperty(value = "商家编码集合", required = true)
  private List<String> merCodeList;
  @ApiModelProperty(value = "三方平台编码集合", required = true)
  private List<String> platformCodeList;
  @ApiModelProperty(value = "门店编码集合", required = true)
  private List<String> onlineStoreCodeList;
}
  
