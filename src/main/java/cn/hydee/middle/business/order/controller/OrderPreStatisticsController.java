package cn.hydee.middle.business.order.controller;


import cn.hydee.middle.business.order.dto.orderPreStatistics.CalculateBillAmountComprehensiveDTO;
import cn.hydee.middle.business.order.service.OrderPreStatisticsService;
import cn.hydee.starter.controller.AbstractController;
import cn.hydee.starter.dto.ResponseBase;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.BindingResult;
import org.springframework.web.bind.annotation.*;

import javax.validation.Valid;

@RestController
@RequestMapping("/${api.version}/ds/orderPreStatistics")
@Api(tags = "订单预处理相关接口")
public class OrderPreStatisticsController extends AbstractController {

    @Autowired
    private OrderPreStatisticsService orderPreStatisticsService;

    @ApiOperation(value = "指定时间历史数据统计", notes = "一般是历史数据，建议晚上")
    @PostMapping("/calculateHistoryDataByTime")
    public ResponseBase<String> calculateHistoryDataByTime(
            @RequestHeader("userId") String userId,
            @RequestHeader("merCode") String merCode,
            @Valid @RequestBody CalculateBillAmountComprehensiveDTO reqDto,
            BindingResult result){
        checkValid(result);
        reqDto.setMerCode(merCode);
        orderPreStatisticsService.calculateHistoryDataByTime(reqDto);
        return generateSuccess(null);
    }
	
    @ApiOperation(value = "指定时间商户级别统计", notes = "一般是历史数据，建议晚上")
    @PostMapping("/calculateHistoryData")
    public ResponseBase<String> calculateHistoryData(
            @RequestHeader("userId") String userId,
            @RequestHeader("merCode") String merCode,
            @Valid @RequestBody CalculateBillAmountComprehensiveDTO reqDto,
    		BindingResult result){
    	checkValid(result);
        reqDto.setMerCode(merCode);
        orderPreStatisticsService.calculateHistoryData(reqDto);
        return generateSuccess(null);
    }
}

