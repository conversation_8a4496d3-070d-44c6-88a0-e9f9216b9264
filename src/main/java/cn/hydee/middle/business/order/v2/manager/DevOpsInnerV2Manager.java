package cn.hydee.middle.business.order.v2.manager;

import cn.hydee.middle.business.order.Enums.ErpStateEnum;
import cn.hydee.middle.business.order.Enums.RefundErpStateEnum;
import cn.hydee.middle.business.order.Enums.RefundStateEnum;
import cn.hydee.middle.business.order.dto.rsp.GoodMedicalTraceCodeDTO;
import cn.hydee.middle.business.order.dto.rsp.baseinfo.CheckScanTraceCodeReqDto;
import cn.hydee.middle.business.order.dto.rsp.baseinfo.CheckScanTraceCodeRespDto;
import cn.hydee.middle.business.order.entity.ErpBillInfo;
import cn.hydee.middle.business.order.entity.MedicalTraceCode;
import cn.hydee.middle.business.order.entity.OrderDetail;
import cn.hydee.middle.business.order.entity.OrderInfo;
import cn.hydee.middle.business.order.entity.OrderPickInfo;
import cn.hydee.middle.business.order.entity.RefundOrder;
import cn.hydee.middle.business.order.feign.MiddleMerchandiseClient;
import cn.hydee.middle.business.order.mapper.*;
import cn.hydee.middle.business.order.v2.manager.convert.DevOpsOrderInfoDtoConvert;
import cn.hydee.middle.business.order.yxtadapter.domain.correctiontool.qry.DevOpsOrderInfoDto;
import cn.hydee.middle.business.order.yxtadapter.domain.correctiontool.qry.DevOpsOrderInfoDto.DevOpsOrderDetail;
import cn.hydee.middle.business.order.yxtadapter.domain.correctiontool.qry.DevOpsOrderInfoDto.DevOpsOrderDetailPickInfo;
import cn.hydee.middle.business.order.yxtadapter.domain.correctiontool.qry.DevOpsOrderInfoReq;
import cn.hydee.middle.business.order.yxtadapter.domain.dictionary.InnerStoreDictionary;
import cn.hydee.middle.business.order.yxtadapter.domainservice.hdpos.HdPosGateway;
import cn.hydee.starter.dto.ResponseBase;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.google.common.base.Preconditions;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import java.util.List;
import java.util.Map;
import java.util.Set;
import java.util.stream.Collectors;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.transaction.support.TransactionTemplate;
import org.springframework.util.StringUtils;

/**
 * ErpBillInfoService
 *
 * <AUTHOR>
 * @since 2020/8/21 15:09
 */
@Slf4j
@Component
public class DevOpsInnerV2Manager {

  @Autowired
  private OrderInfoMapper orderInfoMapper;

  @Autowired
  private ErpBillInfoMapper erpBillInfoMapper;

  @Autowired
  private OrderDetailMapper orderDetailMapper;
  @Autowired
  private OrderPickInfoMapper orderPickInfoMapper;

  @Autowired
  private MedicalTraceCodeMapper medicalTraceCodeMapper;

  @Autowired
  private TransactionTemplate transactionTemplate;

  @Autowired
  private MiddleMerchandiseClient middleMerchandiseClient;

  @Autowired
  private RefundOrderMapper refundOrderMapper;

  @Autowired
  private InnerStoreDictionaryMapper innerStoreDictionaryMapper;

  @Autowired
  private HdPosGateway hdPosGateway;


  public ResponseBase<DevOpsOrderInfoDto> getOrderInfo(DevOpsOrderInfoReq req) {
    Long orderNo = Long.valueOf(req.getOrderNo());

    OrderInfo orderInfo = orderInfoMapper.selectOrderInfo(orderNo);
    Preconditions.checkArgument(orderInfo != null, "非法订单号");
    Preconditions.checkArgument(
        orderInfo.getErpState() == ErpStateEnum.HAS_SALE_FAIL.getCode().intValue(),
        "订单状态不为下账失败");
    List<OrderDetail> orderDetails = orderDetailMapper.selectListByOrderNo(orderNo);
    List<OrderPickInfo> orderPickInfoList = orderPickInfoMapper.selectPickInfoListByOrderNo(
        orderNo);
    List<MedicalTraceCode> medicalTraceCodes = medicalTraceCodeMapper.queryOrderTraceCode(orderNo);
    ErpBillInfo erpBillInfo = erpBillInfoMapper.getErpBillInfoByOrderNo(orderNo);
    DevOpsOrderInfoDto build = DevOpsOrderInfoDtoConvert.build(orderInfo, orderDetails,
        orderPickInfoList, medicalTraceCodes, erpBillInfo);
    ResponseBase<DevOpsOrderInfoDto> success = ResponseBase.success();
    success.setData(build);
    return success;
  }


  public ResponseBase<Boolean> updateByPickTrace(DevOpsOrderInfoDto reqDto) {
    Long orderNo = Long.valueOf(reqDto.getDevOpsOrderInfo().getOrderNo());

    List<DevOpsOrderDetail> devOpsOrderDetailList = reqDto.getDevOpsOrderDetailList();

    List<OrderPickInfo> orderPickInfoList = Lists.newArrayList();
    List<MedicalTraceCode> medicalTraceCodes = Lists.newArrayList();
    Map<String, GoodMedicalTraceCodeDTO> traceFlagMap = Maps.newHashMap();
    Set<String> erpCodeSet = devOpsOrderDetailList.stream().map(DevOpsOrderDetail::getErpCode)
        .collect(Collectors.toSet());

    CheckScanTraceCodeReqDto checkScanTraceCodeReqDto = new CheckScanTraceCodeReqDto();
    checkScanTraceCodeReqDto.setErpCodeList(erpCodeSet);
    checkScanTraceCodeReqDto.setOrderNo(reqDto.getDevOpsOrderInfo().getOrderNo());
    checkScanTraceCodeReqDto.setStoreCode(reqDto.getDevOpsOrderInfo().getOrganizationCode());
    ResponseBase<CheckScanTraceCodeRespDto> traceFlagRes = middleMerchandiseClient.checkScanTraceCode(
        checkScanTraceCodeReqDto);
    if (null != traceFlagRes.getData() && !CollectionUtils.isEmpty(
        traceFlagRes.getData().getErpCodeWithFlagList())) {
      for (GoodMedicalTraceCodeDTO dto : traceFlagRes.getData()
          .getErpCodeWithFlagList()) {
        traceFlagMap.put(dto.getErpCode(), dto);
      }
    }
    for (DevOpsOrderDetail devOpsOrderDetail : devOpsOrderDetailList) {

      GoodMedicalTraceCodeDTO dto = traceFlagMap.get(
          devOpsOrderDetail.getErpCode());

      for (DevOpsOrderDetailPickInfo devOpsOrderDetailPickInfo : devOpsOrderDetail.getDevOpsOrderDetailPickInfoList()) {
        OrderPickInfo orderPickInfo = new OrderPickInfo();
        orderPickInfo.setOrderDetailId(Long.valueOf(devOpsOrderDetail.getId()));
        orderPickInfo.setErpCode(devOpsOrderDetail.getErpCode());
        orderPickInfo.setCommodityBatchNo(devOpsOrderDetailPickInfo.getCommodityBatchNo());
        orderPickInfo.setCount(devOpsOrderDetailPickInfo.getCommodityCount());
        orderPickInfo.setIsValid(1);
        orderPickInfoList.add(orderPickInfo);
        if (!StringUtils.isEmpty(devOpsOrderDetailPickInfo.getEcode())) {
          MedicalTraceCode medicalTraceCode = new MedicalTraceCode();
          medicalTraceCode.setEcode(devOpsOrderDetailPickInfo.getEcode());
          medicalTraceCode.setOrderDetailId(Long.valueOf(devOpsOrderDetail.getId()));
          medicalTraceCode.setCommodityBatchNo(devOpsOrderDetailPickInfo.getCommodityBatchNo());
          medicalTraceCode.setOrderNo(orderNo);
          medicalTraceCode.setType(0);
          medicalTraceCode.setFlag(0);
          if (null != dto) {
            medicalTraceCode.setFlag(dto.getFlag());
          }
          medicalTraceCodes.add(medicalTraceCode);
        }
      }
    }

    transactionTemplate.execute(status -> {
      for (DevOpsOrderDetail devOpsOrderDetail : devOpsOrderDetailList) {
        orderPickInfoMapper.deleteDownByDetailId(Long.valueOf(devOpsOrderDetail.getId()));
        medicalTraceCodeMapper.deleterByOrderDetailId(orderNo,
            Long.valueOf(devOpsOrderDetail.getId()));
      }

      orderPickInfoMapper.insertBatch(orderPickInfoList);
      if (medicalTraceCodes.size() > 0) {
        for (MedicalTraceCode medicalTraceCode : medicalTraceCodes) {
          medicalTraceCodeMapper.insert(medicalTraceCode);
        }
      }
      sendOrderToPos(orderNo);
      return status;
    });

    ResponseBase<Boolean> success = ResponseBase.success();
    success.setData(true);
    return success;
  }

  private void sendOrderToPos(Long orderNo){
    OrderInfo orderInfo = orderInfoMapper.selectOne(new LambdaQueryWrapper<OrderInfo>().eq(OrderInfo::getOrderNo, orderNo));
    String organizationCode = orderInfo.getOrganizationCode();
    InnerStoreDictionary innerStoreDictionary = innerStoreDictionaryMapper.selectByOrganizationCode(organizationCode);
    Integer posMode = innerStoreDictionary.getPosMode();
    switch (posMode){
      case 1:
      case 2:
        hdPosGateway.orderAccounting(orderNo.toString());
      case 3:
        OrderInfo updateOrder = new OrderInfo();
        updateOrder.setId(orderInfo.getId());
        updateOrder.setErpState(ErpStateEnum.WAIT_SALE.getCode());
        orderInfoMapper.updateById(updateOrder);
        break;
    }
  }

  public ResponseBase<Boolean> updateExceedRefundOrder() {

    LambdaQueryWrapper<RefundOrder> queryWrapper = new QueryWrapper<RefundOrder>().lambda()
        .eq(RefundOrder::getMerCode, "500001")
        .eq(RefundOrder::getServiceMode, "O2O")
        .eq(RefundOrder::getState, RefundStateEnum.SUCCESS.getCode())
        .eq(RefundOrder::getErpState, RefundErpStateEnum.WAIT_REFUND.getCode());
    List<RefundOrder> refundOrders = refundOrderMapper.selectList(queryWrapper);
    for (RefundOrder refundOrder : refundOrders) {
      LambdaQueryWrapper<RefundOrder> eq = new QueryWrapper<RefundOrder>().lambda()
          .eq(RefundOrder::getThirdOrderNo, refundOrder.getThirdOrderNo())
          .eq(RefundOrder::getState, RefundStateEnum.SUCCESS.getCode())
          .eq(RefundOrder::getErpState, RefundErpStateEnum.HAS_REFUND.getCode())
          .eq(RefundOrder::getType, "1")
          .ne( RefundOrder::getId, refundOrder.getId());
      Integer i = refundOrderMapper.selectCount(eq);
      if (i > 0) {
        RefundOrder updatedRefundOrder =  new RefundOrder();
        LambdaUpdateWrapper<RefundOrder> lambdaUpdateWrapper = Wrappers.lambdaUpdate();
        lambdaUpdateWrapper.eq(RefundOrder::getId, refundOrder.getId());
        lambdaUpdateWrapper.eq(RefundOrder::getErpState, RefundErpStateEnum.WAIT_REFUND.getCode());
        updatedRefundOrder.setState(RefundStateEnum.REFUSED.getCode());
        updatedRefundOrder.setErpState(RefundErpStateEnum.CANCELED.getCode());
        int update = refundOrderMapper.update(updatedRefundOrder, lambdaUpdateWrapper);
        if(update>0){
          log.info("updateExceedRefundOrder Updated refundOrder id:{} refundNo {}  oldState:{}  oldErpState: {}", refundOrder.getId(), refundOrder.getRefundNo(), refundOrder.getState(), refundOrder.getErpState());
        }

      }
    }

    ResponseBase<Boolean> success = ResponseBase.success();
    success.setData(true);
    return success;
  }
}
