package cn.hydee.middle.business.order.dto.req.baseinfo;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import org.hibernate.validator.constraints.Range;

import javax.validation.constraints.NotNull;
import java.util.Date;

/**
 * <AUTHOR>
 */
@Data
public class DsStoreSoundConfCreateUpdateDTO {

    @ApiModelProperty(value = "线上门店声音配置主键")
    private Long id;

    @NotNull(message = "商家编码必填")
    @ApiModelProperty(value = "商家编码",required = true)
    private String merCode;

    @NotNull(message = "线上门店id必填")
    @ApiModelProperty(value = "线上门店id",required = true)
    private Long onlineStoreId;

    @ApiModelProperty(value = "新订单：0不提示，1提示一次，3提示三次，5循环提醒")
    private Integer newOrder;

    @ApiModelProperty(value = "预约单：0不提示，1提示一次，3提示三次，5循环提醒")
    private Integer bookingOrder;

    @ApiModelProperty(value = "退款单：0不提示，1提示一次，3提示三次，5循环提醒")
    private Integer refundOrder;

    @ApiModelProperty(value = "取消单：0不提示，1提示一次，3提示三次")
    private Integer cancelOrder;

    @ApiModelProperty(value = "催单：0不提示，1提示一次，3提示三次，5循环提醒")
    private Integer urgeOrder;

    @ApiModelProperty(value = "配送异常：0不提示，1提示一次，3提示三次，5循环提醒")
    private Integer deliveryException;

    @ApiModelProperty(value = "打印机断开：0不提示，1提示一次，3提示三次")
    private Integer printerDisconnect;

    @ApiModelProperty(value = "网络断开：0不提示，1提示一次")
    private Integer netDisconnect;

    @ApiModelProperty(value = "骑手取消订单：0不提示，1提示一次，3提示三次")
    private Integer riderCancelOrder;

    @ApiModelProperty(value = "创建时间")
    private Date createTime;

    @ApiModelProperty(value = "修改时间")
    private Date modifyTime;

    @ApiModelProperty(value = "骑手异常：1提示一次 3提示三次 0不提示")
    private Integer riderAbnormal;

    @ApiModelProperty(value = "待审方单：1提示一次 3提示3次 0不提示")
    private Integer waitTrialParty;

    @Range(min= 0L,max=9L,message="输入在0~9之间")
    @ApiModelProperty(value = "拣货提醒：1提示一次 3提示3次 0不提示")
    private Integer pickNotify;

    @ApiModelProperty(value = "拣货超时时间：分钟")
    private Integer pickNotifyMins;

    @Range(min= 0L,max=9L,message="输入在0~9之间")
    @ApiModelProperty(value = "发货提醒：1提示一次 3提示3次 0不提示")
    private Integer deliveryNotify;

    @ApiModelProperty(value = "发货超时时间：分钟")
    private Integer deliveryNotifyMins;

    @ApiModelProperty(value ="销售单待下账，0-不提示 其他-N次")
    private Integer needBillOrder;

    @ApiModelProperty(value ="退款单待下账，0-不提示 其他-N次")
    private Integer needBillRefund;

    @ApiModelProperty(value ="预约单提醒呼叫时间(分钟)")
    private Integer bookingRemindTime;
}
