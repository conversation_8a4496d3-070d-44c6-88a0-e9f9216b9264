 
package cn.hydee.middle.business.order.service.impl.ob.cloud.shelf;

import cn.hydee.middle.business.order.Enums.*;
import cn.hydee.middle.business.order.Enums.oborder.DeliverGoodsReusltStatusEnum;
import cn.hydee.middle.business.order.Enums.oborder.ObOrderBusinessFlagEnum;
import cn.hydee.middle.business.order.Enums.oborder.ObOrderBusinessTypeEnum;
import cn.hydee.middle.business.order.Enums.oborder.ObOrderDirectDeliveryTypeEnum;
import cn.hydee.middle.business.order.dto.req.AddOrderInfoReqDto;
import cn.hydee.middle.business.order.dto.req.OrderHandleReqDto;
import cn.hydee.middle.business.order.dto.req.ob.cloud.shelf.*;
import cn.hydee.middle.business.order.dto.rsp.OnlineStoreInfoRspDto;
import cn.hydee.middle.business.order.dto.rsp.baseinfo.LogisticsCompanyResDTO;
import cn.hydee.middle.business.order.dto.rsp.baseinfo.SysEmployeeResDTO;
import cn.hydee.middle.business.order.entity.OrderDeliveryAddress;
import cn.hydee.middle.business.order.entity.OrderDeliveryRecord;
import cn.hydee.middle.business.order.entity.OrderDetail;
import cn.hydee.middle.business.order.entity.OrderInfo;
import cn.hydee.middle.business.order.feign.MiddleBaseInfoClient;
import cn.hydee.middle.business.order.feign.YdjiaSrmDeliveryClient;
import cn.hydee.middle.business.order.mapper.OrderDeliveryAddressMapper;
import cn.hydee.middle.business.order.mapper.OrderDeliveryRecordMapper;
import cn.hydee.middle.business.order.mapper.OrderDetailMapper;
import cn.hydee.middle.business.order.mapper.OrderInfoMapper;
import cn.hydee.middle.business.order.service.async.HydeeEsSyncClientAsync;
import cn.hydee.middle.business.order.service.cache.BaseInfoCache;
import cn.hydee.middle.business.order.service.ob.cloud.shelf.CloudShelfService;
import cn.hydee.middle.business.order.util.ExceptionUtil;
import cn.hydee.middle.business.order.util.NotifyRedisHelper;
import cn.hydee.middle.business.order.v2.annotation.OrderLock;
import cn.hydee.middle.business.order.v2.feign.hems.HemsCommonClient;
import cn.hydee.middle.business.order.v2.manager.base.BaseInfoManager;
import cn.hydee.middle.business.order.v2.manager.base.OrderBasicManager;
import cn.hydee.middle.business.order.yxtadapter.domainservice.mdm.MiddleBaseInfoClientAdapter;
import cn.hydee.starter.dto.ResponseBase;
import cn.hydee.unified.model.HemsBaseData;
import cn.hydee.unified.model.order.OrderDeliveryReq;
import com.alibaba.fastjson.JSON;
import com.baomidou.mybatisplus.core.conditions.Wrapper;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import lombok.extern.slf4j.Slf4j;
import org.jetbrains.annotations.NotNull;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;
import org.springframework.util.StringUtils;

import javax.annotation.Resource;
import java.util.*;

@Service
@Slf4j
public class CloudShelfServiceImpl implements CloudShelfService {
	
	@Autowired
	private OrderInfoMapper orderInfoMapper;
	@Autowired
	private OrderBasicManager orderBasicManager;
	@Autowired
	private OrderDeliveryRecordMapper orderDeliveryRecordMapper;
	@Autowired
	private OrderDeliveryAddressMapper orderDeliveryAddressMapper;
	@Autowired
	private BaseInfoManager baseInfoManager;
    @Autowired
    private OrderDetailMapper orderDetailMapper;
	@Autowired
	private HemsCommonClient hemsCommonClient;
	@Autowired
	private YdjiaSrmDeliveryClient ydjiaSrmDeliveryClient;
    @Resource
	private MiddleBaseInfoClient middleBaseInfoClient;
    @Autowired
    private BaseInfoCache baseInfoCache;
	@Autowired
	private MiddleBaseInfoClientAdapter middleBaseInfoClientAdapter;

	@Override
	@Transactional(rollbackFor = Exception.class)
	public ResponseBase<String> pushDeliverGoodsReuslt(String userId,DeliverGoodsReusltDto dto) {
		
		if(DeliverGoodsReusltStatusEnum.REFUSE_ORDER.getCode().equals(dto.getStatus())) {
			return refuseOrder(dto);
		}else if(DeliverGoodsReusltStatusEnum.DELIVER_GOODS.getCode().equals(dto.getStatus())) {
			return deliverGoods(userId,dto);
		}else {
			throw ExceptionUtil.getWarnException(DsErrorType.CLOUD_SHELF_ERROR);
		}
	}
	
	@Override
	public void obOrderAllowOperateCheck(OrderInfo orderInfo) {

		// 处于代发货申请中
		if(ObOrderBusinessFlagEnum.HANDELED.getCode().equals(orderInfo.getAppointmentBusinessFlag())
				&& ObOrderBusinessTypeEnum.DELIVER_GOODS.getCode().equals(orderInfo.getAppointmentBusinessType())) {
			throw ExceptionUtil.getWarnException(DsErrorType.CLOUD_SHELF_OPERATE_NOT_ALLOW);
		}
	}

	@Override
	@OrderLock
	public void requestDeliverGoods(String userId, OrderHandleRequestDeliveryGoodsDto dto,String orderNo) {
		OrderInfo orderInfo = new OrderInfo();
		SysEmployeeResDTO sysEmployeeResDTO = new SysEmployeeResDTO();
		RequestDeliverGoodsDto requestDeliverGoodsDto = validateAndBuild(userId, dto, orderInfo, sysEmployeeResDTO);
		log.info("requestDeliverGoods param:{}",JSON.toJSONString(requestDeliverGoodsDto));
		ResponseBase<Boolean> result = ydjiaSrmDeliveryClient.requestDeliverGoods(requestDeliverGoodsDto);
		HydeeEsSyncClientAsync.sendLogToES(ServiceTypeEnum.YDJIA_SRM_DELIVERY,OperateEnum.RequestDeliverGoods, orderInfo,requestDeliverGoodsDto,result,Boolean.class);
		if(!result.checkSuccess()) {
			HydeeEsSyncClientAsync.orderLogToES(DsConstants.INTEGER_ZERO, orderInfo.getMerCode(), orderInfo.getOrderNo(),orderInfo.getOrderState(),orderInfo.getErpState(),
					OrderLogEnum.SUPPLIER_PROXY_DELIVERY_OUT.getAction(),DsErrorType.CLOUD_SHELF_REQUEST_DELIVER_GOODS_ERROR.getMsg()+result.getMsg(),
					sysEmployeeResDTO);
			throw ExceptionUtil.getWarnException(DsErrorType.CLOUD_SHELF_REQUEST_DELIVER_GOODS_ERROR.getCode(),
					DsErrorType.CLOUD_SHELF_REQUEST_DELIVER_GOODS_ERROR.getMsg()+result.getMsg());
		}

		String desc = "无";
		if (!StringUtils.isEmpty(dto.getAppointmentDesc())) {
			desc = dto.getAppointmentDesc().substring(0,dto.getAppointmentDesc().indexOf('-'));
		}
		HydeeEsSyncClientAsync.orderLogToES(DsConstants.INTEGER_ONE, orderInfo.getMerCode(), orderInfo.getOrderNo(),orderInfo.getOrderState(),orderInfo.getErpState(),
				OrderLogEnum.SUPPLIER_PROXY_DELIVERY_OUT.getAction(),OrderLogEnum.getSupplierProxyDeliveryOutInfo(desc),sysEmployeeResDTO);

		// 更新订单为已处理，处理类型为代发
		OrderInfo updateOrderInfo = new OrderInfo().setOrderNo(dto.getOrderNo())
				.setAppointmentBusinessFlag(ObOrderBusinessFlagEnum.HANDELED.getCode())
				.setAppointmentBusinessType(ObOrderBusinessTypeEnum.DELIVER_GOODS.getCode());
		orderInfoMapper.updateOrderWithState(updateOrderInfo,OrderStateEnum.UN_PICK.getCode());
	}

	@Override
	public void cancelDeliverGoods(OrderHandleReqDto dto,Boolean exception2LogFlag) {
		OrderInfo orderInfo = orderBasicManager.getOrderBaseWithCheck(dto.getOrderNo());
		OrderDeliveryRecord orderDeliveryRecord = orderBasicManager.getOrderDeliveryRecordWithCheck(orderInfo.getOrderNo());
		QueryWrapper<OrderDetail> orderDetailWrapper = new QueryWrapper<>();
		orderDetailWrapper.lambda().eq(OrderDetail::getOrderNo,dto.getOrderNo());
		List<OrderDetail> orderDetailList = orderDetailMapper.selectList(orderDetailWrapper);
		OrderDetail orderDetail = orderDetailList.get(0);
		if(!(DeliveryTypeEnum.EXPRESS.getCode().equals(orderDeliveryRecord.getDeliveryType())
				|| DeliveryTypeEnum.BUYER_SELF.getCode().equals(orderDeliveryRecord.getDeliveryType()))) {
			exception2Log(DsErrorType.CLOUD_SHELF_CANCEL_DELIVER_GOODS_NOT_ALLOW,dto.getOrderNo(),exception2LogFlag);
			return;
		}
		// 非供应商代发
		if(!ObOrderDirectDeliveryTypeEnum.DIRECT_DELIVERY.getCode().equals(orderDetail.getDirectDeliveryType())) {
			exception2Log(DsErrorType.CLOUD_SHELF_CANCEL_DELIVER_GOODS_NOT_ALLOW,dto.getOrderNo(),exception2LogFlag);
			return;
		}
		// 处理业务类似非代发
		if(!ObOrderBusinessTypeEnum.DELIVER_GOODS.getCode().equals(orderInfo.getAppointmentBusinessType())) {
			exception2Log(DsErrorType.CLOUD_SHELF_CANCEL_DELIVER_GOODS_NOT_ALLOW,dto.getOrderNo(),exception2LogFlag);
			return;
		}
		CancelDeliverGoodsDto cancelDeliverGoodsDto = new CancelDeliverGoodsDto();
		cancelDeliverGoodsDto.setOrderNo(dto.getOrderNo());
		cancelDeliverGoodsDto.setSupplierCode(orderDetail.getStCode());
		log.info("cancelDeliverGoods param:{}",JSON.toJSONString(cancelDeliverGoodsDto));
		ResponseBase<Boolean> result = ydjiaSrmDeliveryClient.cancelDeliverGoods(cancelDeliverGoodsDto);
		HydeeEsSyncClientAsync.sendLogToES(ServiceTypeEnum.YDJIA_SRM_DELIVERY, OperateEnum.CancelDeliverGoods, orderInfo,  cancelDeliverGoodsDto, result, Boolean.class);
		if(!result.checkSuccess()) {
			exception2Log(DsErrorType.CLOUD_SHELF_CANCEL_DELIVER_GOODS_ERROR.getCode(),
					DsErrorType.CLOUD_SHELF_CANCEL_DELIVER_GOODS_ERROR.getMsg()+result.getMsg(),
					dto.getOrderNo(),exception2LogFlag);
			return;
		}
		// 更新订单为未处理
		OrderInfo updateOrderInfo = new OrderInfo().setOrderNo(dto.getOrderNo())
				.setAppointmentBusinessFlag(ObOrderBusinessFlagEnum.WAIT_HANDEL.getCode())
				.setAppointmentBusinessType(ObOrderBusinessTypeEnum.NO_HANDEL.getCode());
		orderInfoMapper.updateOrder(updateOrderInfo);
	}
	
	@Override
	public void cancelDeliverGoodsWhenStateChange(OrderInfo orderInfo) {
		if(OrderStateEnum.CANCEL.getCode().equals(orderInfo.getOrderState())
        		|| OrderStateEnum.CLOSED.getCode().equals(orderInfo.getOrderState())) {
        	OrderHandleReqDto orderHandleReqDto = new OrderHandleReqDto();
        	orderHandleReqDto.setOrderNo(orderInfo.getOrderNo());
        	this.cancelDeliverGoods(orderHandleReqDto, Boolean.TRUE);
        }
	}

	@Override
	public RequestDeliverGoodsDto validateAndBuildRequestDto(String merCode,Long orderNo) {
		OrderInfo orderInfo = new OrderInfo();
		SysEmployeeResDTO sysEmployeeResDTO = new SysEmployeeResDTO();
		OrderHandleRequestDeliveryGoodsDto dto = new OrderHandleRequestDeliveryGoodsDto();
		dto.setMerCode(merCode);
		dto.setOrderNo(orderNo);
		return validateAndBuild(null, dto, orderInfo, sysEmployeeResDTO);
	}

	@Override
	public void updateHandlerFlagAndRecordLog(OrderInfo orderInfo,RequestDeliverGoodsDto srmMessage){

		// 更新订单为已处理，处理类型为代发
		QueryWrapper<OrderInfo> queryWrapper = new QueryWrapper<>();
		queryWrapper.lambda().eq(OrderInfo::getOrderNo,orderInfo.getOrderNo());
		OrderInfo updateOrderInfo = new OrderInfo().setAppointmentBusinessFlag(ObOrderBusinessFlagEnum.HANDELED.getCode())
				.setAppointmentBusinessType(ObOrderBusinessTypeEnum.DELIVER_GOODS.getCode());
		orderInfoMapper.update(updateOrderInfo,queryWrapper);

		// 记录自动转单日志
		HydeeEsSyncClientAsync.orderLogToES(DsConstants.INTEGER_ONE, orderInfo.getMerCode(), orderInfo.getOrderNo(),orderInfo.getOrderState(),orderInfo.getErpState(),
				OrderLogEnum.SRM_AUTO_TRANSFER_ORDER.getAction(),OrderLogEnum.getSrmAutoTransferOrder(srmMessage.getBusinessPayOrderCode()
						,srmMessage.getFacilitatorPlatformBusinessNo(),srmMessage.getMerchantPlatformBusinessNo(),srmMessage.getChannelSource(),srmMessage.getOrderStatus()),null);
	}

	@NotNull
	private RequestDeliverGoodsDto validateAndBuild(String userId, OrderHandleRequestDeliveryGoodsDto dto, OrderInfo orderInfo, SysEmployeeResDTO sysEmployeeResDTO) {
		RequestDeliverGoodsDto requestDeliverGoodsDto = new RequestDeliverGoodsDto();
		OrderInfo orderBasicInfo = orderBasicManager.getOrderBaseWithCheck(dto.getOrderNo());
		BeanUtils.copyProperties(orderBasicInfo,orderInfo);
		OrderDeliveryRecord orderDeliveryRecord = orderBasicManager.getOrderDeliveryRecordWithCheck(orderInfo.getOrderNo());
		List<OrderDetail> orderDetailList = orderBasicManager.getOrderDetailListWithCheck(dto.getOrderNo());
		OrderDetail orderDetail = orderDetailList.get(0);
		Wrapper<OrderDeliveryAddress> queryWrapper = Wrappers.<OrderDeliveryAddress>lambdaQuery()
				.eq(OrderDeliveryAddress::getOrderNo, dto.getOrderNo());
		List<OrderDeliveryAddress> orderDeliveryAddressList = orderDeliveryAddressMapper.selectList(queryWrapper);
		if(CollectionUtils.isEmpty(orderDeliveryAddressList)) {
			throw ExceptionUtil.getWarnException(DsErrorType.ORDER_DATA_ERROR.getCode(), "订单收货人信息不存在");
		}
		OrderDeliveryAddress orderDeliveryAddress = orderDeliveryAddressList.get(0);
		if(!OrderStateEnum.UN_PICK.getCode().equals(orderInfo.getOrderState())
				|| !(DeliveryTypeEnum.EXPRESS.getCode().equals(orderDeliveryRecord.getDeliveryType())
				|| DeliveryTypeEnum.BUYER_SELF.getCode().equals(orderDeliveryRecord.getDeliveryType()))) {
			throw ExceptionUtil.getWarnException(DsErrorType.CLOUD_SHELF_PUSH_DELIVER_GOODS_NOT_ALLOW);
		}
		// 非供应商代发
		if(!ObOrderDirectDeliveryTypeEnum.DIRECT_DELIVERY.getCode().equals(orderDetail.getDirectDeliveryType())) {
			throw ExceptionUtil.getWarnException(DsErrorType.CLOUD_SHELF_PUSH_DELIVER_GOODS_NOT_ALLOW);
		}
		// 已处理成其他业务
		if(ObOrderBusinessFlagEnum.HANDELED.getCode().equals(orderInfo.getAppointmentBusinessFlag())
				&& !ObOrderBusinessTypeEnum.DELIVER_GOODS.getCode().equals(orderInfo.getAppointmentBusinessType())) {
			throw ExceptionUtil.getWarnException(DsErrorType.CLOUD_SHELF_PUSH_DELIVER_GOODS_NOT_ALLOW);
		}
		// 处于代发货申请中
		obOrderAllowOperateCheck(orderInfo);
		// 到店自提参数校验
		obOrderBuySelfParamCheck(dto,orderDeliveryRecord.getDeliveryType());
		// 与商品供货商交互
		requestDeliverGoodsDto.setOrderNo(orderInfo.getOrderNo());
		requestDeliverGoodsDto.setThirdOrderNo(Long.valueOf(orderInfo.getThirdOrderNo()));
		requestDeliverGoodsDto.setMerCode(orderInfo.getMerCode());
		requestDeliverGoodsDto.setSupplierCode(orderDetail.getStCode());
		// 到店自提单取页面入参数据
		if(!DeliveryTypeEnum.BUYER_SELF.getCode().equals(orderDeliveryRecord.getDeliveryType())){
			requestDeliverGoodsDto.setRecipientName(orderDeliveryAddress.getReceiverName());
			requestDeliverGoodsDto.setRecipientPhone(Optional.ofNullable(orderDeliveryAddress.getReceiverMobile())
					.orElse(orderDeliveryAddress.getReceiverTelephone()));
			requestDeliverGoodsDto.setRecipientAddress(orderDeliveryAddress.getFullAddress());
			requestDeliverGoodsDto.setRecipientNotes(orderInfo.getBuyerRemark());
		}else {
			requestDeliverGoodsDto.setRecipientName(dto.getRecipientName());
			requestDeliverGoodsDto.setRecipientPhone(dto.getRecipientPhone());
			requestDeliverGoodsDto.setRecipientAddress(dto.getRecipientAddress());
			requestDeliverGoodsDto.setRecipientNotes("请务必送到收货地址门店，不要放到快递柜或其他取货点");
		}

		if(!StringUtils.isEmpty(userId)) {
			ResponseBase<SysEmployeeResDTO> employeeResDTO = middleBaseInfoClientAdapter.getEmployeeByUserId(userId);
			String userName = userId;
			if (!StringUtils.isEmpty(employeeResDTO.getData().getEmpName())) {
				userName = employeeResDTO.getData().getEmpName();
			} else {
				userName = employeeResDTO.getData().getAccount();
			}
			sysEmployeeResDTO.setEmpName(userName);
			sysEmployeeResDTO.setEmpId(userId);
			requestDeliverGoodsDto.setCreateName(userName);
		}else {
			requestDeliverGoodsDto.setCreateName(DsConstants.SYSTEM);
		}
		requestDeliverGoodsDto.setDeliveryOrderGoodsDTOList(Collections.singletonList(new DeliverGoodsDto(orderDetail.getErpCode(),
				orderDetail.getPickCount(),orderDetail.getPayment())));

		// 服务商参与分账新增字段
		String extraJsonStr = orderInfo.getWscExtJson();
		if(!StringUtils.isEmpty(extraJsonStr)){
			AddOrderInfoReqDto.ExtJson extraJson = JSON.parseObject(extraJsonStr, AddOrderInfoReqDto.ExtJson.class);
			if(null == extraJson){
				return requestDeliverGoodsDto;
			}
			requestDeliverGoodsDto.setBusinessPayOrderCode(extraJson.getBusinessPayOrderCode());
			requestDeliverGoodsDto.setFacilitatorPlatformBusinessNo(extraJson.getFacilitatorPlatformBusinessNo());
			requestDeliverGoodsDto.setMerchantPlatformBusinessNo(extraJson.getMerchantPlatformBusinessNo());
			requestDeliverGoodsDto.setChannelSource(extraJson.getChannelSource());
		}
		return requestDeliverGoodsDto;
	}

	private void obOrderBuySelfParamCheck(OrderHandleRequestDeliveryGoodsDto dto,String deliveryType) {
		if(!DeliveryTypeEnum.BUYER_SELF.getCode().equals(deliveryType)){
			return;
		}
		if(StringUtils.isEmpty(dto.getRecipientName())) {
			throw ExceptionUtil.getWarnException(DsErrorType.PARAM_VALID_ERROR.getCode(), "门店收货人不能为空");
		}
		if(StringUtils.isEmpty(dto.getRecipientPhone())) {
			throw ExceptionUtil.getWarnException(DsErrorType.PARAM_VALID_ERROR.getCode(), "联系电话不能为空");
		}
		if(StringUtils.isEmpty(dto.getRecipientAddress())) {
			throw ExceptionUtil.getWarnException(DsErrorType.PARAM_VALID_ERROR.getCode(), "收货地址不能为空");
		}
	}
	
	private void exception2Log(DsErrorType dsErrorType,Long orderNo,Boolean exception2LogFlag) {
		if(exception2LogFlag) {
			log.info("cancelDeliverGoods,orderNo:{},"+ dsErrorType.getMsg(),orderNo);
			return;
		}
		throw ExceptionUtil.getWarnException(dsErrorType);
	}
	
	private void exception2Log(String errorCode,String errorMsg,Long orderNo,Boolean exception2LogFlag) {
		if(exception2LogFlag) {
			log.info("cancelDeliverGoods,orderNo:{},"+ errorMsg,orderNo);
			return;
		}
		throw ExceptionUtil.getWarnException(errorCode,errorMsg);
	}
	
	/**
	 * 拒单
	 * @param dto
	 */
	private ResponseBase<String> refuseOrder(DeliverGoodsReusltDto dto) {
		if(StringUtils.isEmpty(dto.getRefuseReason())) {
			throw ExceptionUtil.getWarnException(DsErrorType.CLOUD_SHELF_REFUSE_REASON_NULL_ERROR);
		}
		OrderInfo orderInfo = orderBasicManager.getOrderBaseWithCheck(dto.getOrderNo());
		// 非代发申请中
		if(!deliverGooding(orderInfo)) {
			throw ExceptionUtil.getWarnException(DsErrorType.CLOUD_SHELF_NOT_UNDER_DELIVER_GOODS);
		}
		if(!OrderStateEnum.UN_PICK.getCode().equals(orderInfo.getOrderState())) {
			throw ExceptionUtil.getWarnException(DsErrorType.CLOUD_SHELF_ORDER_NOT_IN_UN_PICK);
		}
		// 清除预约单的【代发】业务类型，将预约订单重新显示在【预约单】列表中
		if(ObOrderBusinessTypeEnum.DELIVER_GOODS.getCode().equals(orderInfo.getAppointmentBusinessType())) {
			OrderInfo updateOrderInfo = new OrderInfo().setOrderNo(dto.getOrderNo())
					.setAppointmentBusinessFlag(ObOrderBusinessFlagEnum.WAIT_HANDEL.getCode())
					.setAppointmentBusinessType(ObOrderBusinessTypeEnum.NO_HANDEL.getCode());
			orderInfoMapper.updateOrder(updateOrderInfo);
		}
		if(!DeliverGoodsReusltStatusEnum.DELIVER_GOODS.getCode().equals(orderInfo.getRequestDeliverGoodsResult())) {
			OrderInfo updateOrderInfo = new OrderInfo().setOrderNo(dto.getOrderNo())
					.setRequestDeliverGoodsResult(DeliverGoodsReusltStatusEnum.REFUSE_ORDER.getCode())
					.setDeliverGoodsRefuseReason(dto.getRefuseReason());
			orderInfoMapper.updateOrder(updateOrderInfo);
		}

		HydeeEsSyncClientAsync.orderLogToES(DsConstants.INTEGER_ONE, orderInfo.getMerCode(), orderInfo.getOrderNo(),orderInfo.getOrderState(),orderInfo.getErpState(),
				OrderLogEnum.SUPPLIER.getAction(),OrderLogEnum.getSupplierInfo("供应商拒单"),null);
		return ResponseBase.success();
	}
	
	/**
	 * 发货
	 * @param dto
	 */
	private ResponseBase<String> deliverGoods(String userId,DeliverGoodsReusltDto dto) {
		if(CollectionUtils.isEmpty(dto.getLogisticsList())) {
			throw ExceptionUtil.getWarnException(DsErrorType.CLOUD_SHELF_LOGISTICS_LIST_NULL_ERROR);
		}
		OrderInfo orderInfo = orderBasicManager.getOrderBaseWithCheck(dto.getOrderNo());
		OrderDeliveryRecord orderDeliveryRecord = orderBasicManager.getOrderDeliveryRecordWithCheck(orderInfo.getOrderNo());
		// 非代发申请中
		if(!deliverGooding(orderInfo)) {
			throw ExceptionUtil.getWarnException(DsErrorType.CLOUD_SHELF_NOT_UNDER_DELIVER_GOODS);
		}
		// 订单发生变更，则不允许发货
		if(!OrderStateEnum.UN_PICK.getCode().equals(orderInfo.getOrderState())
				|| !(DeliveryTypeEnum.EXPRESS.getCode().equals(orderDeliveryRecord.getDeliveryType())
					|| DeliveryTypeEnum.BUYER_SELF.getCode().equals(orderDeliveryRecord.getDeliveryType()))
				|| !ObOrderBusinessTypeEnum.DELIVER_GOODS.getCode().equals(orderInfo.getAppointmentBusinessType())) {
			// 清除预约单的【代发】业务类型，将预约订单重新显示在【预约单】列表中
			if(ObOrderBusinessTypeEnum.DELIVER_GOODS.getCode().equals(orderInfo.getAppointmentBusinessType())) {
				OrderInfo updateOrderInfo = new OrderInfo().setOrderNo(dto.getOrderNo())
						.setAppointmentBusinessFlag(ObOrderBusinessFlagEnum.WAIT_HANDEL.getCode())
						.setAppointmentBusinessType(ObOrderBusinessTypeEnum.NO_HANDEL.getCode());
				orderInfoMapper.updateOrder(updateOrderInfo);
			}
			return ResponseBase.error(DsErrorType.CLOUD_SHELF_PUSH_DELIVER_GOODS_NOT_ALLOW.getCode(), 
					DsErrorType.CLOUD_SHELF_PUSH_DELIVER_GOODS_NOT_ALLOW.getMsg());
		}
		// 发货操作（快递配送出库）
		// 调用微商城发货接口，将发货及物流信息更新到微商城；
		OnlineStoreInfoRspDto storeInfo = baseInfoManager.getOnlineStoreInfo(orderInfo.getMerCode(),
				orderInfo.getThirdPlatformCode(), orderInfo.getClientCode(), orderInfo.getOnlineStoreCode());
		HemsBaseData baseData = hemsCommonClient.constructHemsBaseData(orderInfo.getMerCode(), storeInfo.getPlatformCode(),
				storeInfo.getOnlineClientCode(), storeInfo.getSessionKey());
		OrderDeliveryReq delivery = new OrderDeliveryReq();
		delivery.setOlOrderNo(orderInfo.getThirdOrderNo());
		delivery.setExpCmpId(orderDeliveryRecord.getDeliveryType());
		DeliverGoodsLogisticsDto deliverGoodsLogistics = dto.getLogisticsList().get(0);
		delivery.setExpressId(deliverGoodsLogistics.getLogisticsNo());
		delivery.setExpCmpCode(deliverGoodsLogistics.getLogisticsCompany());
		delivery.setOlShopId(orderInfo.getOnlineStoreCode());
		delivery.setShopId(storeInfo.getOutShopId());
		//操作人【京东健康必填】
		delivery.setOperateMan(userId);
		hemsCommonClient.orderDeliveryOut(delivery, baseData);
		
		// 更新订单状态为【配送中】，更新订单下账状态为【已取消】，预约订单转至【配送中】列表；（代发订单不在OMS中操作下账）
		OrderInfo updateOrderInfo = new OrderInfo().setOrderNo(dto.getOrderNo())
				.setOrderState(OrderStateEnum.POSTING.getCode())
				.setErpState(ErpStateEnum.CANCELED.getCode());
		// 【ID1027311】【优化】自提预约单供应商代发---供应商发货后，订单状态更新逻辑：更新订单状态为【待配送】，下账状态为【已取消】，同时保存供应商发货信息。
		if(DeliveryTypeEnum.BUYER_SELF.getCode().equals(orderDeliveryRecord.getDeliveryType())){
			updateOrderInfo.setOrderState(OrderStateEnum.UN_DELIVERY.getCode());
		}
		if(ObOrderBusinessTypeEnum.DELIVER_GOODS.getCode().equals(orderInfo.getAppointmentBusinessType())) {
			updateOrderInfo.setRequestDeliverGoodsResult(DeliverGoodsReusltStatusEnum.DELIVER_GOODS.getCode());
		}
		// 更新发货后的物流信息（物流公司、物流单号）
		DeliverGoodsLogisticsDto logistics = dto.getLogisticsList().get(0);
		OrderDeliveryRecord updateDelivery = new OrderDeliveryRecord();
		updateDelivery.setOrderNo(dto.getOrderNo());
		if (!StringUtils.isEmpty(logistics.getLogisticsCompany())) {
			updateDelivery.setLogisticsCompany(logistics.getLogisticsCompany());
			List<LogisticsCompanyResDTO> logisticsCompanyList = baseInfoCache.universalLogisticsCompanyData(orderInfo);
			Map<String,String> logisticsCompanyMap = new HashMap<String,String>();
			if(!CollectionUtils.isEmpty(logisticsCompanyList)) {
				logisticsCompanyList.forEach(data ->{{
					logisticsCompanyMap.put(data.getLogisticsCode(), specialUnicode(data.getLogisticsName()));
				}});
			}
			String logisticsName = logisticsCompanyMap.get(logistics.getLogisticsCompany());
			updateDelivery.setLogisticsName(Optional.ofNullable(logisticsName).orElse(""));
		}
		if (!StringUtils.isEmpty(logistics.getLogisticsNo())) {
			updateDelivery.setLogisticsNo(logistics.getLogisticsNo());
		}
        updateDelivery.setPickTime(new Date());
        int num = orderInfoMapper.updateOrderWithState(updateOrderInfo,OrderStateEnum.UN_PICK.getCode());
        if (num > 0){
            orderDeliveryRecordMapper.updateDeliveryRecord(updateDelivery);
            // 订单日志
            String extra = String.format("saleNo:%s", orderInfo.getErpSaleNo());
            orderBasicManager.saveOrderInfoLog(orderInfo, OrderStateEnum.POSTING.getCode(), "",
                    DsConstants.DELIVERY_GOODS, extra,ErpStateEnum.CANCELED.getCode(),null);
			NotifyRedisHelper.removePickNotify(orderInfo);
        }

		HydeeEsSyncClientAsync.orderLogToES(DsConstants.INTEGER_ONE, orderInfo.getMerCode(), orderInfo.getOrderNo(),OrderStateEnum.POSTING.getCode(),ErpStateEnum.CANCELED.getCode(),
				OrderLogEnum.SUPPLIER.getAction(),OrderLogEnum.getSupplierInfo("供应商发货"),null);
		return ResponseBase.success();
	}

	private boolean deliverGooding(OrderInfo orderInfo) {

		// 处于代发货申请中
		if(ObOrderBusinessFlagEnum.HANDELED.getCode().equals(orderInfo.getAppointmentBusinessFlag())
				&& ObOrderBusinessTypeEnum.DELIVER_GOODS.getCode().equals(orderInfo.getAppointmentBusinessType())) {
			return Boolean.TRUE;
		}
		return Boolean.FALSE;
	}
	
	private static String specialUnicode(String str){
		str = str.toLowerCase();
	    str = str.replace("\ufeff", "");
	    return str;
	}
}
  
