package cn.hydee.middle.business.order.route.domain.repository;

import cn.hydee.middle.business.order.dto.message.NetRefundNotifyMessage.DeliveryFee;
import cn.hydee.middle.business.order.route.application.representation.DeliveryFeeRuleRepresentationDto;
import cn.hydee.middle.business.order.route.db.mysql.model.DeliveryFeeRule;
import cn.hydee.middle.business.order.route.db.mysql.model.DeliveryFeeRuleAddprice;
import cn.hydee.middle.business.order.route.domain.DeliveryFeeRuleAggregate;
import cn.hydee.middle.business.order.route.domain.dto.DeliveryFeeRuleDomainQueryDto;
import com.baomidou.mybatisplus.core.metadata.IPage;
import java.util.Collection;
import java.util.List;


/**
 * <AUTHOR>
 * @date 2024/3/21
 * @since 1.0
 * @description 自配送配送费计算规则Repository
 */
public interface DeliveryFeeRuleRepository {

    /***
     * 新增配送费配置
     *
     * @param deliveryFeeRuleAggregate
     * @return void
     */
    void saveDeliveryFeeRule(DeliveryFeeRuleAggregate deliveryFeeRuleAggregate);

    /***
     * 修改配送费配置
     *
     * @param deliveryFeeRuleAggregate
     * @return void
     */
    void updateDeliveryFeeRule(DeliveryFeeRuleAggregate deliveryFeeRuleAggregate);

    /***
     * 删除配送费配置
     *
     * @param deliveryFeeRuleId
     * @return void
     */
    void deleteDeliveryFeeRule(DeliveryFeeRuleAggregate aggregate);

    /***
     * 查询配送费配置列表
     *
     * @param deliveryFeeRuleDomainQueryDto
     * @return List<DeliveryFeeRuleRepresentationDto>
     */
    IPage<DeliveryFeeRuleRepresentationDto> selectDeliveryFeeRulePage(DeliveryFeeRuleDomainQueryDto deliveryFeeRuleDomainQueryDto);

    /***
     * 查询配送费配置详情
     *
     * @param deliveryFeeRuleId
     * @return DeliveryFeeRuleRepresentationDto
     */
    DeliveryFeeRuleRepresentationDto selectDeliveryFeeRuleInfo(String deliveryFeeRuleId);

    /***
     * 根据城市和类型查询配送费配置
     *
     * @param deliveryFeeRuleId
     * @return DeliveryFeeRule
     */
    List<DeliveryFeeRuleAddprice>  listFeeRuleAddpriceByCityAndType(String cityName,String deliveryFeeRuleId);

    List<DeliveryFeeRule> listFeeRuleByIdsAndPlatFormCode(Collection<Long> ids, String platformCode);

    /**
     *  根据id查询配送费配置
     * */
    DeliveryFeeRule getDeliveryFeeRuleById(Long id);

    /**
     * 根据配置Id查询整个聚合根对象
     *
     * @param deliveryFeeRuleId
     * @return DeliveryFeeRuleAggregate
     */
    DeliveryFeeRuleAggregate getDeliveryFeeRuleByRuleId(String deliveryFeeRuleId);

}
