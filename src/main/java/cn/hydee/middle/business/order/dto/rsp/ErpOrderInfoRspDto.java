package cn.hydee.middle.business.order.dto.rsp;

import cn.hydee.middle.business.order.dto.req.AddErpOrderDetailReqDto;
import lombok.Data;

import java.math.BigDecimal;
import java.util.List;

@Data
public class ErpOrderInfoRspDto{

    /**
     * 第三方订单编号
     */
    private String thirdOrderNo;

    /**
     * 商户编码
     */
    private String merCode;

    /**
     * 门店编码
     */
    private String storeCode;

    /**
     * 门店名称
     */
    private String storeName;

    /**
     * 会员卡号
     */
    private String memberCardNo;

    /**
     * 会员手机号
     */
    private String memberMobilePhone;

    /**
     * 订单来源 (1 ：线下）
     */
    private Integer fromType;

    /**
     * 支付方式 ,1 是在线支付 ,2 是货到付款
     */
    private Integer payType;

    /**
     * 订单总金额
     */
    private BigDecimal totalFee;

    /**
     * 交易时间
     */
    private String tradeTime;

    /** erp订单明细 */
    private List<AddErpOrderDetailReqDto> orderDetailList;

}
