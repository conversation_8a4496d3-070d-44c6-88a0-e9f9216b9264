package cn.hydee.middle.business.order.util;

import org.springframework.data.redis.core.StringRedisTemplate;

public class RedisConstUtil {

    /** 拣货、发货超时redis key */
    public final static String NOTIFY_KEY_PREFIX = "o2o-notify-";

    /** ERP调用统计 redis key */
    public final static String ERP_STATISTICS_PREFIX = "o2o-erp-statistics_";
    /** ERP调用统计 成功 redis hashkey */
    public final static String ERP_SUCC = "succ";
    /** ERP调用统计 失败 redis hashkey */
    public final static String ERP_FAIL = "fail";

    private RedisConstUtil() {}
}
