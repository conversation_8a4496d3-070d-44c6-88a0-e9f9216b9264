package cn.hydee.middle.business.order.mapper;

import cn.hydee.middle.business.order.Enums.DsConstants;
import cn.hydee.middle.business.order.batch.export.dto.OrderQueryExportReqDTO;
import cn.hydee.middle.business.order.batch.export.dto.OverallOrderDetailExportDTO;
import cn.hydee.middle.business.order.batch.export.dto.OverallOrderExportDTO;
import cn.hydee.middle.business.order.entity.OrderInfo;
import com.baomidou.dynamic.datasource.annotation.DS;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import org.apache.ibatis.annotations.Param;
import org.springframework.stereotype.Repository;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2021/2/25 上午9:16
 */
@Repository
public interface OrderInfoExportMapper extends BaseMapper<OrderInfo> {

    int countWithOrganizationList(@Param("param") OrderQueryExportReqDTO reqDTO, @Param("platformCodeList") List<String> platFromCodes, @Param("organizationCodeList") List<String> organizationList);

    @DS(DsConstants.DB_ORDER_SLAVE)
    List<OverallOrderExportDTO> listOverall(@Param("param") OrderQueryExportReqDTO reqDTO, @Param("platformCodeList") List<String> platFromCodes, @Param("organizationCodeList") List<String> organizationList);

    @DS(DsConstants.DB_ORDER_SLAVE)
    List<OverallOrderExportDTO> listOverallByOrderNos(@Param("orderNos") List<Long> orderNos);

    @DS(DsConstants.DB_ORDER_SLAVE)
    List<OverallOrderDetailExportDTO> selectDetailByOrderNoList(@Param("orderNoList") List<Long> orderNoList);

    @DS(DsConstants.DB_ORDER_SLAVE)
    List<String> listPrescriptionUrl(@Param("param") OrderQueryExportReqDTO reqDTO, @Param("platformCodeList") List<String> platFromCodes, @Param("organizationCodeList") List<String> organizationList);
}
