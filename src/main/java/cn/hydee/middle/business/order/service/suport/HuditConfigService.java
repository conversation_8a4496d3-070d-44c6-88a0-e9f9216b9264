package cn.hydee.middle.business.order.service.suport;

import cn.hydee.middle.business.order.Enums.DsConstants;
import cn.hydee.middle.business.order.Enums.DsErrorType;
import cn.hydee.middle.business.order.dto.rsp.HuditConfigRestDto;
import cn.hydee.middle.business.order.feign.MiddleBaseInfoClient;
import cn.hydee.middle.business.order.util.ExceptionUtil;
import cn.hydee.starter.dto.ResponseBase;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2019/12/23 16:39
 */
@Slf4j
@Service
public class HuditConfigService {
    @Autowired
    private MiddleBaseInfoClient middleBaseInfoClient;

    public HuditConfigRestDto getHuditConfigByMerCode(String merCode){
        ResponseBase<HuditConfigRestDto> base = middleBaseInfoClient.getHuditConfigByMerCode(merCode,
                DsConstants.URL_TYPE_DS_CLOUD);
        if (base!=null && base.checkSuccess() && base.getData() != null){
            return base.getData();
        }
        log.error("HuditConfig: getHuditConfig, merCode:{}, type:{}", merCode,DsConstants.URL_TYPE_DS_CLOUD);
        throw ExceptionUtil.getWarnException(DsErrorType.HUDIT_URL_NULL_ERROR);
    }

    public HuditConfigRestDto getHuditConfigByMerCodeInner(String merCode){
        ResponseBase<HuditConfigRestDto> base = middleBaseInfoClient.getHuditConfigByMerCode(merCode,
                DsConstants.URL_TYPE_DS_CLOUD);
        if (base!=null && base.checkSuccess() && base.getData() != null){
            return base.getData();
        }
        log.error("HuditConfig: getHuditConfig, merCode:{}, type:{}", merCode, DsConstants.URL_TYPE_DS_CLOUD);
        return null;
    }
}
