package cn.hydee.middle.business.order.service;

import cn.hydee.middle.business.order.domain.RefundOrderInfoAllDomain;
import cn.hydee.middle.business.order.dto.message.NetRefundNotifyMessage;
import cn.hydee.middle.business.order.dto.req.*;
import cn.hydee.middle.business.order.dto.rsp.MemberVipRefundOrderVo;
import cn.hydee.middle.business.order.dto.rsp.PartRefundCheck;
import cn.hydee.middle.business.order.dto.rsp.RefundDetailRsp;
import cn.hydee.middle.business.order.entity.RefundOrder;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;

import java.util.List;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2019/12/31 18:53
 */
public interface RefundService {
    /** 从三方通知消息创建退款单 */
    void createRefundFromMessage(NetRefundNotifyMessage netRefundNotifyMessage);

    /**
     * 创建退款单
     * @param message 退款单消息体
     * @return 退款单
     */
    RefundOrder createRefundBill(NetRefundNotifyMessage message);

    /** 退款单分页 */
    IPage<RefundDetailRsp> getRefundPage(RefundPageReqDto refundPageReqDto);

    /** 查询所有退款单列表 **/
    IPage<RefundDetailRsp> getRefundAllPage(RefundPageReqDto refundPageReqDto);

    void updateRefundOrder(RefundOrder refundOrder, NetRefundNotifyMessage message);

    /** 退款中退款单分页 */
    IPage<RefundDetailRsp> getRefundingPage(RefundPageReqDto refundPageReqDto);

    /** 退款单详情 */
    RefundOrderInfoAllDomain getRefundDetail(Long refundNo);
    /** 根据订单号查询退款单详情 */
    RefundOrderInfoAllDomain getRefundDetailByOrderNo(Long orderNo);
    /** 卖家同意仅退款 */
    void refundAgreeOnlyMoney(String merCode, String userId, RefundAuditHandleReq refundHandle,String orderNo);
    /** 卖家同意退货退款 */
    void refundAgreeWithGoods(String merCode, String userId, RefundAuditHandleReq refundHandle,String orderNo);
    /** 卖家拒绝退货审核 */
    void refundRefuse(String merCode, String userId, RefundAuditHandleReq refundHandle,String orderNo);
    /** 卖家退库 或者 报损 */
    void refundStockOperation(String merCode, String userId, RefundReturnOperationHandleReq refundHandle,String orderNo);
    /** 修改退款金额以及邮费退款金额 **/
    void updateRefundAmount(RefundAmountReqDTO refundAmountReqDTO);
    /**获取退款单下账列表数量**/
    Integer getRefundLedgerCount(RefundLedgerReqDto req,String userId);
    /** 获取下账单列表**/
    IPage<RefundDetailRsp> getRefundLedgerList(RefundLedgerReqDto req,List<String> platformCodeList);

    /** 退款单下账 */
    void refundBill(String merCode,String userId,RefundBillReqDto refundHandleBaseReq);

    /** 原逻辑退款单下账 */
    void oldRefundBill(String merCode,String userId,RefundBillReqDto refundHandleBaseReq);

    /** 平安退款单下账 */
    void platRefundBill(String merCode,String userId,PlatRefundBillReqDto refundHandleBaseReq);

    /** 获取某用户所属门店的下账列表 **/
    IPage<RefundDetailRsp> getRefundLedgerAllList(RefundLedgerReqDto req,List<String> organizationList,List<String> platformCodeList);
    
    /**获取微商城退货单号列表**/
    List<String> getYdjRefundNoList(List<String> reqYdjRefundNoList);


    List<RefundDetailRsp> selectRefundAllPageUpload(Page<RefundPageReqDto> page, RefundPageReqDto refundPageReqDto, List<String> organizatioinList);

    List<RefundDetailRsp> selectRefundPageUpload(Page<RefundPageReqDto> page,RefundPageReqDto refundPageReqDto);

    /** 阿里退款单下账 **/
    void aliRefundBill(String merCode, String userId, PlatRefundBillReqDto req);

    /**
     * 检查部分退款合法性
     * @param platformCode 标识
     * @return 结果
     */
    PartRefundCheck checkPartRefundValid(String platformCode, String orderNo);

    /**
     * 手工创建京东、阿里健康退款单
     * @Param:  * @param null
     * @Return: {@link null}
     * @Author: chufeng(2910)
     * @Date: 2020/8/28 14:58
     */
    void createJdAliRefundOrder(String merCode, String userId, RefundOrderCreateDto req);

    /**
     * 针对复杂换货部分退款创建退款单
     */
    void createComplexModifyDetailPartRefundOrder(String merCode, String userId, RefundOrderCreateDto req);

    void agree(Long refundNo, Long orderNo);

    /**
     * 获取退款单信息
     * @param refundNo
     * @return
     */
    RefundOrder getRefundByRefundNoWhithCheck(Long refundNo);

    int exportCount(RefundPageReqDto reqDTO, List<String> platFromCodes, List<String> organizationCodeList);

    List<RefundDetailRsp> exportOverall(RefundPageReqDto reqDTO, List<String> platFromCodes, List<String> organizationCodeList);

    /** 退款单详情 */
    RefundOrder selectByThirdPlatAndThirdRefundNo(String refundNo);


    /** 退款单详情 */
    List<MemberVipRefundOrderVo> getVipRefundOrder(List<String> thirdRefundNo);



    /** 同意VIP订单退款 */
    void agreeVipRefund(String merCode, String userId, RefundAuditHandleReq refundHandle,String orderNo);
}
