package cn.hydee.middle.business.order.util;

import cn.hydee.middle.business.order.Enums.DsConstants;
import org.apache.commons.lang3.StringUtils;
import org.apache.logging.log4j.util.Strings;

import java.math.BigDecimal;
import java.text.NumberFormat;
import java.util.Optional;

public class StringUtil {
	
	private static final String NULL_STR = "null";
	
    /**
     * 判断正整数
     * @param str
     * @return
     */
    public static boolean isNumeric(String str) {
        if(Strings.isEmpty(str)){
            return false;
        }
        for (int i = str.length(); --i >= 0; ) {
            if (!Character.isDigit(str.charAt(i))) {
                return false;
            }
        }
        return true;
    }
    
    /**
     * 
     * isBlankContainNullCharSequence:空判断. <br/>  
     *  
     *  <pre>
     * StringUtils.isBlank(null)      = true
     * StringUtils.isBlank("")        = true
     * StringUtils.isBlank(" ")       = true
     * StringUtils.isBlank("bob")     = false
     * StringUtils.isBlank("  bob  ") = false
     * StringUtils.isBlank("null")    = true
     * </pre>
     *
     *  
     *  
     * @param str 字符串
     * @return  布尔结果
     * <AUTHOR>  
     * @date 2020年8月24日 下午3:28:22
     */
    public static boolean isBlankContainNullCharSequence(String str) {
    	return StringUtils.isBlank(str) || NULL_STR.equalsIgnoreCase(str);
    }

    /**
    * @Description: 百分比
    * @Param: [moleculeAmount, denominatorAmount]
    * @return: java.lang.String
    * @Author: syuson
    * @Date: 2021-6-21
    */
    public static String percentFormat(BigDecimal moleculeAmount,BigDecimal denominatorAmount){
        NumberFormat numberFormat = NumberFormat.getPercentInstance();
        numberFormat.setMaximumFractionDigits(2);
        BigDecimal divide = moleculeAmount.divide(denominatorAmount,4,BigDecimal.ROUND_HALF_UP);
        return  numberFormat.format(Math.abs(divide.doubleValue()));
    }

    /**
    * @Description: 字符串转BigDecimal（安全）
    * @Param: [moneyStr]
    * @return: java.math.BigDecimal
    * @Author: syuson
    * @Date: 2021-8-3
    */
    public static BigDecimal convertString2BigDecimalSafe(String moneyStr){
        moneyStr = Optional.ofNullable(moneyStr).orElse(DsConstants.STRING_ZERO);
        if(StringUtils.isEmpty(moneyStr)){
            moneyStr = DsConstants.STRING_ZERO;
        }
        return new BigDecimal(moneyStr);
    }
    
    /**
    * @Description: 字符串转Integer（安全）
    * @Param: [str, defaultValue]
    * @return: java.lang.Integer
    * @Author: syuson
    * @Date: 2021-8-18
    */
    public static Integer convertString2IntegerSafe(String str,Integer defaultValue){
        if(StringUtils.isEmpty(str)){
            return defaultValue;
        }
        Integer value;
        try{
            value = Integer.valueOf(str);
            return value;
        }catch (Exception e){
        }
        return defaultValue;
    }
}
