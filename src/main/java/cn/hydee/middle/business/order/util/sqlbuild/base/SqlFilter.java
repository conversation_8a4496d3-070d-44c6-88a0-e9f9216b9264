package cn.hydee.middle.business.order.util.sqlbuild.base;

import org.springframework.util.StringUtils;

import java.util.ArrayList;
import java.util.List;

/**
 * cn.hydee.middle.business.order.util.sqlbuild.base
 *
 * <AUTHOR>
 * @version 1.0
 * @date 2020/8/27 14:45
 **/
public class SqlFilter {
    /**
     * sql注入过滤
     * @param value 待验证的字符串
     * @return  true 验证无注入  false 验证有注入
     */
    public static boolean sqlInject(String value){
        String str = value;
        if(StringUtils.isEmpty(str)){
            return true;
        }
        //去掉'|"|;|\字符
        str = StringUtils.replace(str,"'","");
        str = StringUtils.replace(str,"\"","");
        str = StringUtils.replace(str,";","");
        str = StringUtils.replace(str,"\\","");
        //转换成小写 方便验证
        str = str.toLowerCase();
        //非法字符
        List<String> keywords = new ArrayList<>();
        keywords.add("master");
        keywords.add("truncate");
        keywords.add("insert");
        keywords.add("delete");
        keywords.add("update");
        keywords.add("declare");
        keywords.add("alert");
        keywords.add("drop");
        for(String item:keywords){
            if(str.contains(item)){
                return false;
            }
        }
        return true;
    }
}
