package cn.hydee.middle.business.order.service.image;

import cn.hutool.core.date.DateTime;
import cn.hutool.core.date.DateUtil;
import cn.hydee.middle.business.order.util.AliOssUtil;
import cn.hydee.middle.business.order.util.HttpManager;
import cn.hydee.middle.business.order.yxtadapter.constant.AssignmentBizType;
import cn.hydee.middle.business.order.yxtadapter.domainservice.assignment.AssignmentEngine;
import com.alibaba.fastjson.JSONObject;
import java.util.Date;
import lombok.extern.slf4j.Slf4j;
import okhttp3.Call;
import okhttp3.Request;
import okhttp3.Response;
import org.apache.commons.lang3.RandomUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.pdfbox.pdmodel.PDDocument;
import org.apache.pdfbox.rendering.PDFRenderer;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.retry.annotation.Recover;
import org.springframework.retry.annotation.Retryable;
import org.springframework.stereotype.Service;

import javax.imageio.ImageIO;
import java.awt.image.BufferedImage;
import java.io.*;
import java.net.SocketTimeoutException;
import java.util.Random;
import java.util.UUID;

@Slf4j
@Service
public class UploadService {

    @Autowired
    private AssignmentEngine assignmentEngine;
    private final  Random r = new Random();

    private static final String ERROR = "error";

    // 允许上传的格式
    private static final String[] IMAGE_TYPE = new String[]{".bmp", ".jpg",
            ".jpeg", ".gif", ".png"};

    @Value("${spring.application.name}")
    private String appName;

    @Value("${domain.name:order}")
    private String domainName;

    private static final String SLASH = "/";

    private static final String IMAGES = "images";


    /**
     * 上传
     * @param url 网络资源
     * @return
     */
    @Retryable(value = SocketTimeoutException.class)
    public FileUploadResult upload(String url,String name) throws SocketTimeoutException {
        //不是图片类型不进行上传操作
        FileUploadResult fileUploadResult = new FileUploadResult();
        if (StringUtils.isBlank(name)) {
            int random = r.nextInt(1000);
            name = System.currentTimeMillis() + "" + random;
        }
        String nameFile = name+".jpg";
        Request request = new Request.Builder()
                .get()
                .url(url)
                .build();
        Call call = HttpManager.client.newCall(request);
        try (Response response = call.execute()) {
            if (!response.isSuccessful() || response.body() == null) {
                fileUploadResult.setStatus(ERROR);
                return fileUploadResult;
            }
            InputStream inputStream = response.body().byteStream();
            if (StringUtils.endsWithIgnoreCase(request.url().uri().getPath(), ".pdf")) {
                inputStream = transferToJPG(inputStream);
            }
            return upload(nameFile, inputStream);
        } catch (IOException e) {
            throw new SocketTimeoutException();
        }
    }

    @Recover
    public FileUploadResult createAssignment(SocketTimeoutException e,String url) {
        log.error("上传阿里云OSS失败,原URL:【{}】【{}】",url,e);
        FileUploadResult fileUploadResult = new FileUploadResult();
        JSONObject jsonObject = new JSONObject();
        jsonObject.put("url",url);
        assignmentEngine.createAssignment(AssignmentBizType.UPLOAD_PICTURE_URL, UUID.randomUUID().toString(),jsonObject.toJSONString(),"");
        return fileUploadResult;
    }

    /**
     * 将pdf转为JPG
     * @param inputStream
     * @return
     */
    public static InputStream transferToJPG(InputStream inputStream) {
        ByteArrayInputStream jpgInputStream = null;
        try {
            // 加载 PDF 文件
            PDDocument document = PDDocument.load(inputStream);

            // 创建 PDF 渲染器
            PDFRenderer renderer = new PDFRenderer(document);

            // 遍历每一页并将其转换为图像
            for (int pageIndex = 0; pageIndex < document.getNumberOfPages(); pageIndex++) {
                BufferedImage image = renderer.renderImageWithDPI(pageIndex, 300);

                // 创建字节数组输出流
                ByteArrayOutputStream outputStream = new ByteArrayOutputStream();

                // 将图像保存为 JPG 文件
                ImageIO.write(image,"jpg",outputStream);

                // 将字节数组输出流转换为输入流
                jpgInputStream  = new ByteArrayInputStream(outputStream.toByteArray());
            }
            // 关闭 PDF 文档
            document.close();
            return jpgInputStream;
        } catch (IOException e) {
            log.error("transfer pdf to jpg error:{}", e);
            return null;
        }
    }



    public boolean checkFileType(String fileName){
        for (String type : IMAGE_TYPE) {
            if (StringUtils.endsWithIgnoreCase(fileName,
                    type)) {
                return true;
            }
        }
        return false;
    }
    /**
     * 上传
     * @param fileName
     * @param inputStream
     * @return
     */
    public FileUploadResult upload(String fileName,InputStream inputStream) {

        //封装Result对象，并且将文件的byte数组放置到result对象中
        FileUploadResult fileUploadResult = new FileUploadResult();
        // 校验图片格式
        if(!checkFileType(fileName)){
            fileUploadResult.setStatus(ERROR);
            return fileUploadResult;
        }
        //文件新路径
        String filePath = getFilePath(fileName);
        // 上传到阿里云
        try {
            AliOssUtil.upload(inputStream,filePath);
        } catch (Exception e) {
            log.error("上传oss失败【{}】,【{}】",fileName,e);
            //上传失败
            fileUploadResult.setStatus(ERROR);
            return fileUploadResult;
        }
        fileUploadResult.setStatus("done");
        fileUploadResult.setResponse("success");
        fileUploadResult.setName(AliOssUtil.downloadURLNoExpireAndDecode(filePath));
        fileUploadResult.setUid(String.valueOf(System.currentTimeMillis()));
        return fileUploadResult;
    }
    /**
     * 通过源文件获取 路径和文件名
     *
     * @param sourceFileName
     * @return
     */
    private String getFilePath(String sourceFileName) {
        DateTime dateTime = new DateTime();
//        return "images/" + dateTime.toString("yyyy")
//                + "/" + dateTime.toString("MM") + "/"
//                + dateTime.toString("dd") + "/" +sourceFileName;
        return domainName + SLASH
            + appName + SLASH
            + IMAGES + SLASH
            + dateTime.toString("yyyyMM") + SLASH
            + dateTime.toString("dd") + SLASH
            + sourceFileName;
    }
}
