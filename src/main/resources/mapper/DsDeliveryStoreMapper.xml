<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="cn.hydee.middle.business.order.mapper.DsDeliveryStoreRepo">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="cn.hydee.middle.business.order.entity.DsDeliveryStore">
        <id column="id" property="id" />
        <result column="mer_code" property="merCode" />
        <result column="platform_code" property="platformCode" />
        <result column="platform_name" property="platformName" />
        <result column="delivery_client_code" property="deliveryClientCode" />
        <result column="delivery_client_name" property="deliveryClientName" />
        <result column="delivery_store_code" property="deliveryStoreCode" />
        <result column="delivery_store_name" property="deliveryStoreName" />
        <result column="longitude" property="longitude" />
        <result column="latitude" property="latitude" />
        <result column="address" property="address" />
        <result column="create_time" property="createTime" />
        <result column="modify_time" property="modifyTime" />
        <result column="contact_name" property="contactName" />
        <result column="contact_phone" property="contactPhone" />
        <result column="city" property="city" />
        <result column="area_name" property="areaName" />
        <result column="position_source" property="positionSource" />
        <result column="status" property="status" />
        <result column="service_code" property="serviceCode" />
        <result column="default_service_code" property="defaultServiceCode" />
        <result column="open_delivery_platform_id" property="openDeliveryPlatformId" />
        <result column="pay_type_codes" property="payTypeCodes" />

    </resultMap>


    <!--配送方式ResultMap-->
    <resultMap id="DeliveryTypeResultMap" type="cn.hydee.middle.business.order.dto.rsp.baseinfo.DeliveryTypeDTO">
        <result column="deliveryName" property="deliveryName" />
    </resultMap>

    <sql id="deliveryStoreColumns">
        `id`,
        `mer_code`,
        `platform_code`,
        `platform_name`,
        `delivery_client_code`,
        `delivery_client_name`,
        `delivery_store_code`,
        `delivery_store_name`,
        `longitude`,
        `latitude`,
        `address`,
        `create_time`,
        `modify_time`,
        `contact_name`,
        `contact_phone`,
        `city`,
        `area_name`,
        `position_source`,
        `status`,
        `service_code`,
        `default_service_code`,
        `open_delivery_platform_id`,
        `pay_type_codes`
    </sql>

    <!--批量新增或者修改配送门店信息-->
    <insert id="insertOrUpdate">
        INSERT INTO ds_delivery_store (
        `mer_code`,
        `platform_code`,
        `platform_name`,
        `delivery_client_code`,
        `delivery_client_name`,
        `delivery_store_code`,
        `delivery_store_name`,
        `longitude`,
        `latitude`,
        `address`,
        `contact_name`,
        `contact_phone`,
        `city`,
        `area_name`,
        `position_source`,
        `status`,
        `service_code`,
        `default_service_code`,
        `open_delivery_platform_id`,
        `pay_type_codes`)
        values
        <foreach collection="list" item="item"  separator=",">
            (#{item.merCode},
            #{item.platformCode},
            #{item.platformName},
            #{item.deliveryClientCode},
            #{item.deliveryClientName},
            #{item.deliveryStoreCode},
            #{item.deliveryStoreName},
            #{item.longitude},
            #{item.latitude},
            #{item.address},
            #{item.contactName},
            #{item.contactPhone},
            #{item.city},
            #{item.areaName},
            #{item.positionSource},
            #{item.status},
            #{item.serviceCode},
            #{item.defaultServiceCode},
            #{item.openDeliveryPlatformId},
            #{item.payTypeCodes})
        </foreach>
        ON DUPLICATE KEY UPDATE
        mer_code=values(mer_code),
        platform_code=values(platform_code),
        platform_name=values(platform_name),
        delivery_client_code=values(delivery_client_code),
        delivery_client_name=values(delivery_client_name),
        delivery_store_code=values(delivery_store_code),
        delivery_store_name=values(delivery_store_name),
        longitude=values(longitude),
        latitude=values(latitude),
        address=values(address),
        contact_name=values(contact_name),
        contact_phone=values(contact_phone),
        city=values(city),
        area_name=values(area_name),
        position_source=values(position_source),
        status=values(status),
        service_code=values(service_code),
        default_service_code=values(default_service_code),
        open_delivery_platform_id=values(open_delivery_platform_id)
        pay_type_codes=values(pay_type_codes)
    </insert>


    <!--批量新增配送门店-->
    <insert id="insertBatch" useGeneratedKeys="true" keyProperty="list.id" keyColumn="id">
        INSERT INTO ds_delivery_store (
        `mer_code`,
        `platform_code`,
        `platform_name`,
        `delivery_client_code`,
        `delivery_client_name`,
        `delivery_store_code`,
        `delivery_store_name`,
        `longitude`,
        `latitude`,
        `address`,
        `contact_name`,
        `contact_phone`,
        `city`,
        `area_name`,
        `position_source`,
        `status`,
        `service_code`,
        `default_service_code`,
        `open_delivery_platform_id`,
        `pay_type_codes`,
        `scope`
        )
        values
        <foreach collection="list" item="item"  separator=",">
            (#{item.merCode},
            #{item.platformCode},
            #{item.platformName},
            #{item.deliveryClientCode},
            #{item.deliveryClientName},
            #{item.deliveryStoreCode},
            #{item.deliveryStoreName},
            #{item.longitude},
            #{item.latitude},
            #{item.address},
            #{item.contactName},
            #{item.contactPhone},
            #{item.city},
            #{item.areaName},
            #{item.positionSource},
            #{item.status},
            #{item.serviceCode},
            #{item.defaultServiceCode},
            #{item.openDeliveryPlatformId},
            #{item.payTypeCodes},
            #{item.scope})
        </foreach>
    </insert>

    <!--批量修改配送门店-->
    <update id="updateByBatch">
        <foreach close=";" collection="list" index="index" item="item" open="" separator=";">
            update ds_delivery_store
            <trim prefix="set" suffixOverrides=",">
                    <if test="item.merCode != null and item.merCode != ''">
                        mer_code = #{item.merCode},
                    </if>
                    <if test="item.platformCode != null and item.platformCode != ''">
                        platform_code = #{item.platformCode},
                    </if>
                    <if test="item.platformName != null and item.platformName != ''">
                        platform_name = #{item.platformName},
                    </if>
                    <if test="item.deliveryClientCode != null and item.deliveryClientCode != ''">
                        delivery_client_code = #{item.deliveryClientCode},
                    </if>
                    <if test="item.deliveryClientName != null and item.deliveryClientName != ''">
                        delivery_client_name = #{item.deliveryClientName},
                    </if>
                    <if test="item.deliveryStoreCode != null and item.deliveryStoreCode != ''">
                        delivery_store_code = #{item.deliveryStoreCode},
                    </if>
                    <if test="item.deliveryStoreName != null and item.deliveryStoreName != ''">
                        delivery_store_name = #{item.deliveryStoreName},
                    </if>
                    <if test="item.longitude != null and item.longitude != ''">
                        longitude = #{item.longitude},
                    </if>
                    <if test="item.latitude != null and item.latitude != ''">
                        latitude = #{item.latitude},
                    </if>
                    <if test="item.address != null and item.address != ''">
                        address = #{item.address},
                    </if>
                    <if test="item.contactName != null and item.contactName != ''">
                        contact_name = #{item.contactName},
                    </if>
                    <if test="item.contactPhone != null and item.contactPhone != ''">
                        contact_phone = #{item.contactPhone},
                    </if>
                    <if test="item.city != null and item.city != ''">
                        city = #{item.city},
                    </if>
                    <if test="item.areaName != null and item.areaName != ''">
                        area_name = #{item.areaName},
                    </if>
                    <if test="item.positionSource != null">
                        position_source = #{item.positionSource},
                    </if>
                    <if test="item.status != null">
                        status = #{item.status},
                    </if>
                    <if test="item.serviceCode != null and item.serviceCode != ''">
                        service_code = #{item.serviceCode},
                    </if>
                    <if test="item.defaultServiceCode != null and item.defaultServiceCode != ''">
                        default_service_code = #{item.defaultServiceCode},
                    </if>
                    <if test="item.openDeliveryPlatformId != null">
                        open_delivery_platform_id = #{item.openDeliveryPlatformId},
                    </if>
                    <if test="item.payTypeCodes != null">
                        pay_type_codes = #{item.payTypeCodes},
                    </if>
                    <if test="item.scope != null and item.scope !=''">
                        `scope` = #{item.scope}
                    </if>
            </trim>
             where id = #{item.id}
        </foreach>
    </update>


    <!--查询配送方式(已启用且已绑定配送门店的)-->
    <select id="queryDeliveryType" resultMap="BaseResultMap">
        select
        ds.id,
        ds.mer_code,
        ds.platform_code,
        ds.platform_name,
        ds.delivery_client_code,
        ds.delivery_client_name,
        ds.delivery_store_code,
        ds.delivery_store_name,
        ds.longitude,
        ds.latitude,
        ds.address,
        ds.create_time,
        ds.modify_time,
        ds.contact_name,
        ds.contact_phone,
        ds.city,
        ds.area_name,
        ds.position_source,
        ds.status,
        ds.service_code,
        ds.default_service_code,
        ds.open_delivery_platform_id,
        ds.pay_type_codes
        from ds_delivery_store ds
        LEFT JOIN ds_online_store_delivery sd  on ds.id = sd.delivery_store_id
        where
        sd.mer_code =#{merCode}
        and sd.online_store_id = #{onlineStoreId}
        and sd.status = 1
    </select>

    <!--根据线上门店id以及mercode查询线下门店信息-->
    <select id="selectDeliveryStoreInfo" parameterType="map" resultType="cn.hydee.middle.business.order.entity.DsDeliveryStore">
        select
        ds.id,
        ds.mer_code,
        ds.platform_code,
        ds.platform_name,
        ds.delivery_client_code,
        ds.delivery_client_name,
        ds.delivery_store_code,
        ds.delivery_store_name,
        ds.longitude,
        ds.latitude,
        ds.address,
        ds.create_time,
        ds.modify_time,
        ds.contact_name,
        ds.contact_phone,
        ds.city,
        ds.area_name,
        ds.position_source,
        ds.status,
        ds.service_code,
        ds.default_service_code,
        ds.open_delivery_platform_id,
        ds.pay_type_codes
        from ds_delivery_store ds
        LEFT JOIN ds_online_store_delivery sd  on ds.id = sd.delivery_store_id
        <where>
            sd.online_store_id = #{onlineStoreId}
            <if test="merCode != null">
               and sd.mer_code = #{merCode}
            </if>
        </where>
    </select>


    <select id="queryDeliveryStore" resultMap="BaseResultMap">
        select
        <include refid="deliveryStoreColumns"/>
        from ds_delivery_store
        where mer_code = #{merCode} and platform_code = #{platformCode} and delivery_client_code = #{deliveryClientCode}
    </select>
    <select id="listByPlatFormCodeAndStoreCodes"
      resultType="cn.hydee.middle.business.order.entity.DsDeliveryStore">
        select
        <include refid="deliveryStoreColumns"/>
        from ds_delivery_store
        where mer_code = #{merCode}
          <if test="platformCode != null">
          and platform_code = #{platformCode}
          </if>
          and delivery_store_code in
        <foreach collection="storeCodes" item="item" index="index" open="(" close=")" separator=",">
            #{item}
        </foreach>
    </select>
    <select id="listByIdsAndPlatformCode"
            resultType="cn.hydee.middle.business.order.entity.DsDeliveryStore">
        select
        <include refid="deliveryStoreColumns"/>
        from ds_delivery_store
        where id in
        <foreach collection="ids" item="item" index="index" open="(" close=")" separator=",">
            #{item}
        </foreach>
        <if test="platformCode != null">
          and platform_code = #{platformCode}
        </if>
    </select>
    <select id="getByOnlineStoreIdAndPlatformCodes"
            resultType="cn.hydee.middle.business.order.entity.DsDeliveryStore">
        select
        dds.*
        from ds_delivery_store dds left join ds_online_store_delivery dosd on dds.id = dosd.delivery_store_id
        where  dosd.mer_code = #{merCode} and dosd.online_store_id in
        <foreach collection="onlineStoreIds" item="item" index="index" open="(" close=")" separator=",">
            #{item}
        </foreach>
        and dds.platform_code = #{platformCode} limit 1
    </select>
    <select id="listByDeliveryStoreCode" resultType="cn.hydee.middle.business.order.entity.DsDeliveryStore"
      parameterType="java.lang.String">
        select
        <include refid="deliveryStoreColumns"/>
        from ds_delivery_store
        where delivery_store_code = #{deliveryStoreCode}
    </select>

</mapper>
