<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="cn.hydee.middle.business.order.mapper.MeituanSyncBillStatusMapper">
  <resultMap id="BaseResultMap" type="cn.hydee.middle.business.order.thirdbill.entity.MeituanSyncBillStatus">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    <id column="id" jdbcType="INTEGER" property="id" />
    <result column="mer_code" jdbcType="VARCHAR" property="merCode" />
    <result column="meituan_store_code" jdbcType="VARCHAR" property="meituanStoreCode" />
    <result column="bill_date" jdbcType="DATE" property="billDate" />
    <result column="sync_status" jdbcType="TINYINT" property="syncStatus" />
    <result column="exception_desc" jdbcType="VARCHAR" property="exceptionDesc" />
    <result column="gmt_create" jdbcType="TIMESTAMP" property="gmtCreate" />
    <result column="gmt_last_update" jdbcType="TIMESTAMP" property="gmtLastUpdate" />
  </resultMap>
  <delete id="deleteById" parameterType="java.lang.Integer">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    delete from meituan_sync_bill_status
    where id = #{id,jdbcType=INTEGER}
  </delete>
    <delete id="forceRemoveStatusInstance">
      delete from meituan_sync_bill_status
      where mer_code = #{merCode} and bill_date between #{fromDate} and #{toDate}
    </delete>
    <insert id="insert" parameterType="cn.hydee.middle.business.order.thirdbill.entity.MeituanSyncBillStatus">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    <selectKey keyProperty="id" order="AFTER" resultType="java.lang.Integer">
      SELECT LAST_INSERT_ID()
    </selectKey>
    insert into meituan_sync_bill_status (mer_code, meituan_store_code, bill_date, 
      sync_status, exception_desc, gmt_create, 
      gmt_last_update)
    values (#{merCode,jdbcType=VARCHAR}, #{meituanStoreCode,jdbcType=VARCHAR}, #{billDate,jdbcType=DATE}, 
      #{syncStatus,jdbcType=TINYINT}, #{exceptionDesc,jdbcType=VARCHAR}, #{gmtCreate,jdbcType=TIMESTAMP}, 
      #{gmtLastUpdate,jdbcType=TIMESTAMP})
  </insert>
  <insert id="initYesterdayBillSyncStatus">
    insert ignore into
        meituan_sync_bill_status
        (mer_code, meituan_store_code, bill_date)
    (select
            os.mer_code, os.online_store_code, DATE_SUB(curdate(),INTERVAL 1 DAY)
    from ds_online_store os
        INNER JOIN merchant_config mc
            on mc.mer_code = os.mer_code
    where mc.sync_meituan_bill = true
      and os.platform_code = 27)
  </insert>
  <insert id="initBillSyncStatus">
    insert ignore into
      meituan_sync_bill_status
      (mer_code, meituan_store_code, bill_date)
      (select
         os.mer_code, os.online_store_code, #{dayStr}
       from ds_online_store os
              INNER JOIN merchant_config mc
                         on mc.mer_code = os.mer_code
       where mc.sync_meituan_bill = true
         and os.platform_code = 27)
  </insert>
  <insert id="initBillSyncStatusByMerCode">
    insert ignore into
      meituan_sync_bill_status
      (mer_code, meituan_store_code, bill_date)
      (select
         os.mer_code, os.online_store_code, #{dayStr}
       from ds_online_store os
              INNER JOIN merchant_config mc
                         on mc.mer_code = os.mer_code
       where mc.sync_meituan_bill = true
         and os.platform_code = 27
         and os.mer_code = #{merCode})
  </insert>
  <update id="updateById" parameterType="cn.hydee.middle.business.order.thirdbill.entity.MeituanSyncBillStatus">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    update meituan_sync_bill_status
    set mer_code = #{merCode,jdbcType=VARCHAR},
      meituan_store_code = #{meituanStoreCode,jdbcType=VARCHAR},
      bill_date = #{billDate,jdbcType=DATE},
      sync_status = #{syncStatus,jdbcType=TINYINT},
      exception_desc = #{exceptionDesc,jdbcType=VARCHAR},
      gmt_create = #{gmtCreate,jdbcType=TIMESTAMP},
      gmt_last_update = #{gmtLastUpdate,jdbcType=TIMESTAMP}
    where id = #{id,jdbcType=INTEGER}
  </update>
  <update id="updateSyncStatusLock">
    update
        meituan_sync_bill_status
    set sync_status = #{status}
    where mer_code = #{merCode}
      and meituan_store_code = #{onlineStoreCode}
      and bill_date = #{date}
      and sync_status = #{oldStatus}
  </update>
  <select id="selectById" parameterType="java.lang.Integer" resultMap="BaseResultMap">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    select id, mer_code, meituan_store_code, bill_date, sync_status, exception_desc, 
    gmt_create, gmt_last_update
    from meituan_sync_bill_status
    where id = #{id,jdbcType=INTEGER}
  </select>
  <select id="selectAll" resultMap="BaseResultMap">
    <!--
      WARNING - @mbg.generated
      This element is automatically generated by MyBatis Generator, do not modify.
    -->
    select id, mer_code, meituan_store_code, bill_date, sync_status, exception_desc, 
    gmt_create, gmt_last_update
    from meituan_sync_bill_status
  </select>
</mapper>