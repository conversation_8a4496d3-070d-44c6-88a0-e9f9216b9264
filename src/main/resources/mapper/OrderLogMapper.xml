<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="cn.hydee.middle.business.order.mapper.OrderLogMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="cn.hydee.middle.business.order.entity.OrderLog">
        <id column="id" property="id" />
        <result column="order_no" property="orderNo" />
        <result column="operator_id" property="operatorId" />
        <result column="operate_desc" property="operateDesc" />
        <result column="state_before" property="stateBefore" />
        <result column="state" property="state" />
        <result column="extra_info" property="extraInfo" />
        <result column="create_time" property="createTime" />
        <result column="modify_time" property="modifyTime" />
        <result column="erp_state" property="erpState" />
    </resultMap>

    <sql id="InsertBaseColumn">
        order_no,
        operator_id,
        operate_desc,
        state_before,
        state,
        extra_info,
        create_time,
        erp_state
    </sql>

    <insert id="insertLogBatch">
        insert into `order_log`(order_no, operator_id, operate_desc, state_before, state, extra_info, create_time, erp_state)
        values
        <foreach collection="param" item="item" separator="," open="" close="">
            (#{item.orderNo},#{item.operatorId},#{item.operateDesc}
            ,#{item.stateBefore},#{item.state},#{item.extraInfo},#{item.createTime},#{item.erpState})
        </foreach>
    </insert>
    <select id="selectListByOrderNo" resultMap="BaseResultMap">
        select
            id,order_no,operator_id,operate_desc,state_before,state,extra_info,create_time,erp_state
        from order_log
        where `order_no`=#{orderNo, jdbcType=BIGINT}
        order by id desc
    </select>

    <select id="qryPickOvertimeOrder" resultType="java.lang.Long">
        select ol.order_no from order_log ol
        join order_info oi on oi.order_no = ol.order_no  <![CDATA[ and oi.order_state <= 100 ]]>
        where ol.order_no in
        <foreach collection="param" item="orderNo" separator="," open="(" close=")">
            #{orderNo}
        </foreach>
        and ol.operate_desc = 'pick-notify'
    </select>

    <select id="qryDeliveryOvertimeOrder" resultType="java.lang.Long">
        select ol.order_no from order_log ol
        join order_info oi on oi.order_no = ol.order_no  <![CDATA[ and oi.order_state <= 100 ]]>
        where ol.order_no in
        <foreach collection="param" item="orderNo" separator="," open="(" close=")">
            #{orderNo}
        </foreach>
        and ol.operate_desc = 'delivery-notify'
    </select>

</mapper>
