<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="cn.hydee.middle.business.order.mapper.DsStoreFenceSyncLogMapper">

    <resultMap id="BaseResultMap" type="cn.hydee.middle.business.order.entity.DsStoreFenceSyncLog">
            <id property="id" column="id" jdbcType="BIGINT"/>
            <result property="storeCode" column="store_code" jdbcType="VARCHAR"/>
            <result property="platformCode" column="platform_code" jdbcType="VARCHAR"/>
            <result property="action" column="action" jdbcType="VARCHAR"/>
            <result property="status" column="status" jdbcType="VARCHAR"/>
            <result property="msg" column="msg" jdbcType="VARCHAR"/>
            <result property="operatorId" column="operator_id" jdbcType="VARCHAR"/>
            <result property="createTime" column="create_time" jdbcType="TIMESTAMP"/>
            <result property="updateTime" column="update_time" jdbcType="TIMESTAMP"/>
    </resultMap>

    <sql id="Base_Column_List">
        id,store_code,platform_code,
        action,status,msg,
        operator_id,create_time,update_time
    </sql>
</mapper>
