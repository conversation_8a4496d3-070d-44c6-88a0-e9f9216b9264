<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="cn.hydee.middle.business.order.yxtadapter.infrastructure.db.mysql.mapper.YnOmsOrderRxReviewMapper">



    <select id="selectListOrderRxReview" resultType="cn.hydee.middle.business.order.yxtadapter.domain.oms.transform.OrderRxReview">
        SELECT
            `guid`,
            `ordercode`,
            `orderfrom`,
            `status`,
            `reviewtime`,
            `rx_img` as rxImg,
            `bz0`,
            `bz1`,
            `bz2`,
            `bz3`,
            `bz4`
        FROM
            ${tableName}
        WHERE ordercode = #{orderCode}
        AND rx_img !=''
        ORDER BY `status` DESC  LIMIT 1
    </select>

</mapper>
