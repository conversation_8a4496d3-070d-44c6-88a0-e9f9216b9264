<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="cn.hydee.middle.business.order.mapper.NotifyThirdContentMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="cn.hydee.middle.business.order.entity.NotifyThirdContent">
        <id column="id" property="id" />
        <result column="method" property="method" />
        <result column="mer_code" property="merCode" />
        <result column="third_platform_code" property="thirdPlatformCode" />
        <result column="client_code" property="clientCode" />
        <result column="session_key" property="sessionKey" />
        <result column="body" property="body" />
        <result column="create_time" property="createTime" />
        <result column="modify_time" property="modifyTime" />
    </resultMap>
    <sql id="NotifyThirdContentColumns">
        `id`,
        `method`,
        `mer_code`,
        `third_platform_code`,
        `client_code`,
        `session_key`,
        `body`
    </sql>

    <select id="selectPageList" resultMap="BaseResultMap">
        select
        <include refid="NotifyThirdContentColumns"></include>
        from notify_third_content
        where id>#{lastId, jdbcType=BIGINT}
        order by id
        limit #{size, jdbcType=INTEGER}
    </select>

</mapper>
