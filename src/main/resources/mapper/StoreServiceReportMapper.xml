<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="cn.hydee.middle.business.order.mapper.StoreServiceReportMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="cn.hydee.middle.business.order.entity.StoreServiceReport">
        <id column="id" property="id" />
        <result column="mer_code" property="merCode" />
        <result column="organization_code" property="organizationCode" />
        <result column="organization_name" property="organizationName" />
        <result column="online_store_code" property="onlineStoreCode" />
        <result column="third_platform_code" property="thirdPlatformCode" />
        <result column="online_store_name" property="onlineStoreName" />
        <result column="statistics_date" property="statisticsDate" />
        <result column="pick_time_used" property="pickTimeUsed" />
        <result column="picked_orders" property="pickedOrders" />
        <result column="pick_overtime_orders" property="pickOvertimeOrders" />
        <result column="delivery_overtime_orders" property="deliveryOvertimeOrders" />
        <result column="deliveried_orders" property="deliveriedOrders" />
        <result column="delivery_time_used" property="deliveryTimeUsed" />
        <result column="valide_orders" property="valideOrders" />
        <result column="invalide_orders" property="invalideOrders" />
        <result column="lack_commidity_orders" property="lackCommidityOrders" />
    </resultMap>

    <sql id="BaseColumn">
        id,
        mer_code,
        organization_code,
        organization_name,
        online_store_code,
        online_store_name,
        third_platform_code,
        statistics_date,
        pick_time_used,
        picked_orders,
        pick_overtime_orders,
        delivery_overtime_orders,
        deliveried_orders,
        delivery_time_used,
        valide_orders,
        invalide_orders,
        lack_commidity_orders
    </sql>

    <update id="updateValidOrderCount">
        update store_service_report
        set valide_orders = #{validOrderCount} , invalide_orders = #{invalidOrderCount}
        where id = #{param.id}
    </update>

    <delete id="deleteByDay">
        delete from store_service_report where statistics_date  <![CDATA[ >= ]]> #{date} and statistics_date <![CDATA[ < ]]> #{endDate}
    </delete>

    <select id="qryOnlineStoreServiceReportByOrg" resultType="cn.hydee.middle.business.order.entity.StoreServiceReport">
        select organization_code,third_platform_code,online_store_code,
        sum(pick_overtime_orders) pickOvertimeOrders,  sum(delivery_overtime_orders) deliveryOvertimeOrders,
        sum(valide_orders) valideOrders, sum(invalide_orders) invalideOrders, sum(lack_commidity_orders) lackCommidityOrders,
        sum(picked_orders) pickedOrders, sum(deliveried_orders) deliveriedOrders,
        sum(pick_time_used) pickTimeUsed, sum(delivery_time_used) deliveryTimeUsed
        from store_service_report
        where  mer_code = #{param.merCode}
        and <![CDATA[  statistics_date >= #{param.orderTimeStart} and statistics_date <= #{param.orderTimeEnd} ]]>
        <if test="param.organizationCodeList != null and param.organizationCodeList.size() > 0">
            and organization_code in
            <foreach collection="param.organizationCodeList" item="organizationCode" index="index" open="(" close=")" separator=",">
                #{organizationCode}
            </foreach>
        </if>
        group by organization_code,third_platform_code,online_store_code
    </select>

    <select id="countReport" resultType="java.lang.Integer">
        select count(1) from
        (
        select
            id
        from store_service_report
        where  mer_code = #{param.merCode}
        and <![CDATA[  statistics_date >= #{param.orderTimeStart} and statistics_date < #{param.orderTimeEnd} ]]>
        <if test="param.organizationCodeList != null and param.organizationCodeList.size() > 0">
            and organization_code in
            <foreach collection="param.organizationCodeList" item="organizationCode" index="index" open="(" close=")" separator=",">
                #{organizationCode}
            </foreach>
        </if>
            ) g;
    </select>

    <select id="getOrganizationReport"
            resultType="cn.hydee.middle.business.order.entity.OrganizationServiceReport">
        select organization_code,
        sum(pick_overtime_orders) pickOvertimeOrders,  sum(delivery_overtime_orders) deliveryOvertimeOrders,
        sum(valide_orders) valideOrders, sum(invalide_orders) invalideOrders, sum(lack_commidity_orders) lackCommidityOrders,
        sum(picked_orders) pickedOrders, sum(deliveried_orders) deliveriedOrders,
        sum(pick_time_used) pickTimeUsed, sum(delivery_time_used) deliveryTimeUsed
        from store_service_report
        where  mer_code = #{param.merCode}
        and statistics_date = #{statisticDate}
        and organization_code=#{param.organizationCode}
        group by organization_code
    </select>

    <select id="selectStoreReportByStatisticDate"
            resultType="cn.hydee.middle.business.order.entity.StoreServiceReport">
        select
        <include refid="BaseColumn"/>
        from store_service_report
        where statistics_date = #{statisticDate}
    </select>

    <select id="getValidOrderCount" resultType="java.lang.Integer">
        select sum(valide_orders)
        from store_service_report
        where mer_code = #{param.merCode}
        and organization_code = #{param.organizationCode}
        and statistics_date = #{param.statisticsDate}
    </select>

    <select id="qryOrganizationServiceReport"
            resultType="cn.hydee.middle.business.order.entity.OrganizationServiceReport">
        select organization_code organizationCode, sum(pick_time_used) pickTimeUsed, sum(picked_orders) pickedOrders, sum(pick_overtime_orders) pickOvertimeOrders,
               sum(deliveried_orders) deliveriedOrders, sum(delivery_time_used) deliveryTimeUsed, sum(delivery_overtime_orders) deliveryOvertimeOrders,
               sum(valide_orders) valideOrders, sum(invalide_orders) invalideOrders, sum(lack_commidity_orders) lackCommidityOrders
        from store_service_report
        where mer_code = #{param.merCode}
          and <![CDATA[  statistics_date >= #{param.orderTimeStart} and statistics_date <= #{param.orderTimeEnd} ]]>
        <if test="param.organizationCodeList != null and param.organizationCodeList.size() > 0">
            and organization_code in
            <foreach collection="param.organizationCodeList" item="organizationCode" index="index" open="(" close=")" separator=",">
                #{organizationCode}
            </foreach>
        </if>
        group by organization_code
    </select>

    <select id="countOrganizationServiceReport" resultType="java.lang.Integer">
        select count(1) from (
            select 1 from store_service_report where mer_code = #{param.merCode}
                and <![CDATA[  statistics_date >= #{param.orderTimeStart} and statistics_date < #{param.orderTimeEnd} ]]>
                <if test="param.organizationCodeList != null and param.organizationCodeList.size() > 0">
                    and organization_code in
                    <foreach collection="param.organizationCodeList" item="organizationCode" index="index" open="(" close=")" separator=",">
                        #{organizationCode}
                    </foreach>
                </if>
                group by organization_code
            ) g
    </select>

</mapper>
