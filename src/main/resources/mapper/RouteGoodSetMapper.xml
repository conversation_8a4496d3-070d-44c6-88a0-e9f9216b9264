<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="cn.hydee.middle.business.order.route.db.mysql.mapper.RouteGoodSetMapper">

    <resultMap id="BaseResultMap" type="cn.hydee.middle.business.order.route.db.mysql.model.RouteGoodSet">
            <id property="id" column="id" jdbcType="BIGINT"/>
            <result property="strategyRuleId" column="strategy_rule_id" jdbcType="BIGINT"/>
            <result property="reStoreCode" column="re_store_code" jdbcType="VARCHAR"/>
            <result property="reStoreName" column="re_store_name" jdbcType="VARCHAR"/>
            <result property="erpCode" column="erp_code" jdbcType="VARCHAR"/>
            <result property="commodityName" column="commodity_name" jdbcType="VARCHAR"/>
            <result property="createdBy" column="created_by" jdbcType="VARCHAR"/>
            <result property="updatedBy" column="updated_by" jdbcType="VARCHAR"/>
            <result property="createdTime" column="created_time" jdbcType="TIMESTAMP"/>
            <result property="updatedTime" column="updated_time" jdbcType="TIMESTAMP"/>
            <result property="version" column="version" jdbcType="BIGINT"/>
    </resultMap>

    <sql id="Base_Column_List">
        id,strategy_rule_id,re_store_code,
        re_store_name,erp_code,commodity_name,
        created_by,updated_by,created_time,
        updated_time,version
    </sql>
    <insert id="batchInsert" parameterType="java.util.List">
      insert into route_good_set (id, strategy_rule_id, re_store_code, re_store_name, erp_code, commodity_name, created_by,updated_by)
      values
      <foreach collection="list" item="item" separator=",">
        (
        #{item.id,jdbcType=BIGINT},
        #{item.strategyRuleId,jdbcType=BIGINT},
        #{item.reStoreCode,jdbcType=VARCHAR},
        #{item.reStoreName,jdbcType=VARCHAR},
        #{item.erpCode,jdbcType=VARCHAR},
        #{item.commodityName,jdbcType=VARCHAR},
        #{item.createdBy,jdbcType=VARCHAR},
        #{item.updatedBy,jdbcType=VARCHAR}
        )
      </foreach>
    </insert>
</mapper>
