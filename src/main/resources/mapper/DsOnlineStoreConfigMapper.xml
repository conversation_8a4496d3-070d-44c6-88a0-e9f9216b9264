<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="cn.hydee.middle.business.order.mapper.DsOnlineStoreConfigRepo">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap" type="cn.hydee.middle.business.order.entity.DsOnlineStoreConfig">
        <id column="id" property="id" />
        <result column="mer_code" property="merCode" />
        <result column="online_store_id" property="onlineStoreId" />
        <result column="service_type" property="serviceType" />
        <result column="self_delivery_type" property="selfDeliveryType" />
        <result column="delivery_fee_to" property="deliveryFeeTo" />
        <result column="package_fee_to" property="packageFeeTo" />
        <result column="platform_benefit_owner" property="platformBenefitOwner" />
        <result column="brokerage_rate" property="brokerageRate" />
        <result column="brokerage_minimum" property="brokerageMinimum" />
        <result column="sync_stock" property="syncStock" />
        <result column="sync_price" property="syncPrice" />
        <result column="create_time" property="createTime" />
        <result column="modify_time" property="modifyTime" />
        <result column="retail_time" property="retailTime" />
        <result column="whether_discount_share" property="whetherDiscountShare" />
        <result column="whether_commission_share" property="whetherCommissionShare" />
        <result column="whether_inventory_locked" property="whetherInventoryLocked" />
        <result column="whether_need_prescription" property="whetherNeedPrescription" />
        <result column="print_template_id" property="printTemplateId" />
        <result column="settle_mode" property="settleMode" />
        <result column="pick_type" property="pickType" />
    </resultMap>

    <resultMap id="Bill_Setting_Colunm_List" type="cn.hydee.middle.business.order.entity.DsOnlineStoreConfig" extends="BaseResultMap">
        <result column="platform_code" property="platformCode" />
        <result column="online_client_code" property="clientCode" />
    </resultMap>

    <sql id="BaseColumnSql">
        id,mer_code,online_store_id,service_type,self_delivery_type,delivery_fee_to,package_fee_to,
        platform_benefit_owner,brokerage_rate,brokerage_minimum,sync_stock,sync_price,create_time,
        modify_time,retail_time,whether_discount_share,whether_commission_share,whether_inventory_locked,whether_need_prescription,settle_mode,pick_type
    </sql>

    <select id="getAllMerchantClientBillSetting" resultMap="Bill_Setting_Colunm_List">
               SELECT  DISTINCT
                dos.mer_code,dos.platform_code,
                dos.online_client_code,
                dosc.delivery_fee_to,
                dosc.package_fee_to,
                dosc.whether_discount_share,
                dosc.whether_commission_share,
                dosc.whether_inventory_locked,
                dosc.whether_need_prescription
        FROM
                ds_online_store dos,
                ds_online_store_config dosc,
                (
                        SELECT DISTINCT
                                CONCAT(
                                        dos.online_client_code,
                                        dosc.delivery_fee_to,
                                        dosc.package_fee_to,
                                        dosc.whether_discount_share,
                                        dosc.whether_commission_share,
                                        dosc.whether_inventory_locked,
                                        dosc.whether_need_prescription
                                ) AS flag_fee
                        FROM
                                ds_online_store dos,
                                ds_online_store_config dosc
                        WHERE
                                dosc.online_store_id = dos.id
                ) AS t
        WHERE
                dos.online_client_code = SUBSTRING(t.flag_fee, 1, 32)
        AND dosc.online_store_id = dos.id
        ORDER BY
                    dos.`mer_code`,dos.platform_code
    </select>

    <!-- 为了兼容商品中台老接口，迫不得已 -->
    <resultMap id="storeConfigMap" type="cn.hydee.middle.business.order.entity.DsOnlineStore">
        <id column="id" property="id" />
        <result column="mer_code" property="merCode" />
        <result column="platform_code" property="platformCode" />
        <result column="platform_name" property="platformName" />
        <result column="online_client_code" property="onlineClientCode" />
        <result column="online_client_name" property="onlineClientName" />
        <result column="online_store_code" property="onlineStoreCode" />
        <result column="online_store_name" property="onlineStoreName" />
        <result column="organization_code" property="organizationCode" />
        <result column="status" property="status" />
        <result column="create_time" property="createTime" />
        <result column="modify_time" property="modifyTime" />
        <result column="open_status" property="openStatus" />
        <result column="address" property="address" />
        <result column="longitude" property="longitude" />
        <result column="latitude" property="latitude" />
        <result column="contact_phone" property="contactPhone" />
        <result column="city" property="city" />
        <result column="organization_name" property="organizationName"/>
        <result column="out_shop_id" property="outShopId"/>
        <result column="service_mode" property="serviceMode"/>
        <result column="default_config" property="defaultConfig"/>
        <result column="online_client_out_code" property="onlineClientOutCode"/>
        <result column="sync_price" property="syncPrice"/>
        <result column="sync_stock" property="syncStock"/>
        <result column="inherit_B2C" property="inheritB2C"/>
    </resultMap>

    <sql id="onlineStoreColumns">
        dos.id,dos.mer_code,dos.platform_code,dos.platform_name,dos.online_client_code,
        dos.online_client_name,dos.online_store_code,dos.online_store_name,
        dos.organization_code,dos.organization_name,dos.status,dos.inherit_B2C,
        dos.open_status,dos.address,dos.longitude,dos.latitude,dos.contact_phone,
        dos.city,dos.out_shop_id,dos.service_mode,dos.default_config,dos.online_client_out_code,dos.platform_shop_id,
        dosc.sync_price,dosc.sync_stock,dosc.sync_stock_ratio,dos.access_type
    </sql>

    <select id="queryStoreConfig" resultMap="storeConfigMap">
        SELECT <include refid="onlineStoreColumns"/>
        FROM ds_online_store dos,ds_online_store_config dosc
        WHERE
        dos.mer_code = #{merCode,jdbcType=VARCHAR} and dosc.mer_code = #{merCode,jdbcType=VARCHAR}
        AND dos.platform_code = #{platformCode,jdbcType=VARCHAR}
        AND CONCAT(dos.online_client_code,dos.online_store_code) IN
        <foreach collection="onlineStores" item="item" open="(" close=")" separator=",">
            #{item.clientStore,jdbcType=VARCHAR}
        </foreach>
        AND dosc.online_store_id = dos.id
    </select>

    <update id="updateStoreSetMerchant">
        UPDATE ds_online_store_config
        SET sync_price = #{syncPrice,jdbcType=INTEGER},
        sync_stock = #{syncStock,jdbcType=INTEGER}
        WHERE
        mer_code = #{merCode,jdbcType=VARCHAR}
    </update>

    <update id="updateStoreSetPlatform">
        <foreach collection="platformList" item="item" separator=";">
            UPDATE ds_online_store_config
            SET sync_price = #{item.syncPrice,jdbcType=INTEGER},
            sync_stock = #{item.syncStock,jdbcType=INTEGER}
            WHERE
            mer_code = #{merCode,jdbcType=VARCHAR}
            AND online_store_id = #{item.onlineStoreId,jdbcType=BIGINT}
        </foreach>
    </update>


    <select id="getStorePrintTemplateIdList" resultType="java.lang.Long">
        select DISTINCT print_template_id from (
            (select
                        dosc.print_template_id
                    from
                        ds_store_order_config dosc
                    join ds_online_store dos on
                        dos.mer_code = dosc.mer_code and
                        dos.id = dosc.online_store_id
                    where
                        dos.mer_code = #{merCode}
                      and dos.organization_code = #{organizationCode})
            UNION ALL
            (select
                        dosc.second_print_template_id print_template_id
                    from
                        ds_store_order_config dosc
                    join ds_online_store dos on
                        dos.mer_code = dosc.mer_code and
                        dos.id = dosc.online_store_id
                    where
                        dos.mer_code = #{merCode}
                      and dos.organization_code = #{organizationCode})
            ) t
    </select>

<!--    <select id="getStoreOrganizationCodeList" resultType="java.lang.String">-->
<!--        select-->
<!--        distinct-->
<!--            dos.organization_code-->
<!--        from-->
<!--            ds_store_order_config dosc-->
<!--        join ds_online_store dos on-->
<!--            dos.id = dosc.online_store_id-->
<!--        where-->
<!--            dos.mer_code = #{merCode}-->
<!--            and dosc.print_template_id =  #{printTemplateId}-->
<!--    </select>-->

    <select id="getStoreOrganizationCodeList" resultType="java.lang.String">
        select
            distinct
            dos.organization_code
        from
            ds_store_order_config dosc
                join ds_online_store dos on
                dos.id = dosc.online_store_id
        where
            dos.mer_code = #{merCode}
          and (dosc.print_template_id =  #{printTemplateId} or dosc.second_print_template_id =  #{printTemplateId})
    </select>
  <select id="listByOnlineStoreId" resultMap="BaseResultMap">
    SELECT <include refid="BaseColumnSql"/>
    from ds_online_store_config
    where online_store_id in
    <foreach collection="storeIds" item="storeId" index="index" open="(" close=")" separator=",">
      #{storeId}
    </foreach>

  </select>

    <select id="getStoreConfig" resultMap="BaseResultMap">
        SELECT
            dosc.*
        FROM ds_online_store dos
                 INNER JOIN ds_online_store_config dosc ON dosc.online_store_id = dos.id
        WHERE
            dos.mer_code = #{merCode}
          AND dos.platform_code = #{thirdPlatformCode}
          AND dos.online_client_code = #{onlineClientCode}
          AND dos.online_store_code = #{onlineStoreCode}
    </select>
</mapper>
