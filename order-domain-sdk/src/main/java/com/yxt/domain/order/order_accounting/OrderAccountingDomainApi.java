package com.yxt.domain.order.order_accounting;

import static com.yxt.domain.order.BusinessOrderServiceName.API;

import com.yxt.domain.order.order_accounting.req.OrderAccountingAsyncReq;
import com.yxt.lang.dto.api.ResponseBase;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;

/**
 * <AUTHOR> (moatkon)
 * @date 2024年09月12日 17:26
 * @email: <EMAIL>
 */
public interface OrderAccountingDomainApi {

  @PostMapping(API + "/order-accounting/sync")
  ResponseBase<Boolean> orderAccountingAsync(@RequestBody OrderAccountingAsyncReq req);
}
