<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
	xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 https://maven.apache.org/xsd/maven-4.0.0.xsd">
	<modelVersion>4.0.0</modelVersion>
	<parent>
		<groupId>cn.hydee.starter</groupId>
		<artifactId>hydee-spring-boot-starter-parent</artifactId>
		<version>1.2.0</version>
		<relativePath></relativePath>
	</parent>
	<groupId>cn.hydee.business</groupId>
	<artifactId>hydee-business-order</artifactId>
	<name>hydee-business-order</name>
	<description>电商云OMS平台</description>

	<properties>
		<project.build.sourceEncoding>UTF-8</project.build.sourceEncoding>
		<project.reporting.outputEncoding>UTF-8</project.reporting.outputEncoding>
		<java.version>1.8</java.version>
		<skipTests>true</skipTests> <!-- 跳过单元测试 -->
		
		<okhttp.version>4.2.2</okhttp.version> <!--$NO-MVN-MAN-VER$ -->
		<mybatis-plus.version>3.1.2</mybatis-plus.version>
		<druid.version>1.1.21</druid.version>
		<easyexcel.version>3.3.3</easyexcel.version>
		<hutool.version>5.8.16</hutool.version>
		<oms-tool.version>1.2.3-SNAPSHOT</oms-tool.version>
		<loop-cure.version>1.2.1-SNAPSHOT</loop-cure.version>
		<oms-tool-erp.version>2.3.4-SNAPSHOT</oms-tool-erp.version>
        <oms-tool-unified.version>1.4.14.RELEASE</oms-tool-unified.version>
        <oms-tool-third-o2o.version>1.0.0-SNAPSHOT</oms-tool-third-o2o.version>
		<batch-starter.version>2.1.4-RELEASE</batch-starter.version>
        <elasticsearch.version>7.14.0</elasticsearch.version>
        <poi.version>3.15</poi.version>
		<spring.cloud.sentinel.version>2.1.3.RELEASE</spring.cloud.sentinel.version>
		<sentinel.datasource.apollo.verison>1.8.0</sentinel.datasource.apollo.verison>
		<es.starter.version>1.0.0-SNAPSHOT</es.starter.version>
        <spring-amqp.version>2.2.0.RELEASE</spring-amqp.version>
		<yxt-online-store-core.version>1.0.2-SNAPSHOT</yxt-online-store-core.version>
	</properties>

	<dependencies>
         <dependency>
            <groupId>cn.hydee</groupId>
            <artifactId>erp</artifactId>
			 <version>${oms-tool-erp.version}</version>
        </dependency>
        <dependency>
            <groupId>cn.hydee</groupId>
            <artifactId>unified</artifactId>
            <version>${oms-tool-unified.version}</version>
			<exclusions>
				<exclusion>
					<groupId>cn.hydee</groupId>
					<artifactId>base</artifactId>
				</exclusion>
			</exclusions>
        </dependency>
        <dependency>
            <groupId>cn.hydee</groupId>
            <artifactId>third-o2o</artifactId>
            <version>${oms-tool-third-o2o.version}</version>
			<exclusions>
				<exclusion>
					<groupId>cn.hydee</groupId>
					<artifactId>base</artifactId>
				</exclusion>
			</exclusions>
        </dependency>

		<dependency>
			<groupId>cn.hydee.starter</groupId>
			<artifactId>grey-spring-boot-web-starter</artifactId>
			<version>3.2.0-SNAPSHOT</version>
		</dependency>

		<dependency>
			<groupId>cn.hydee.starter</groupId>
			<artifactId>hydee-spring-boot-starter</artifactId>
			<version>4.7.1</version>
			<exclusions>
				<exclusion>
					<groupId>cn.hydee.starter</groupId>
					<artifactId>grey-spring-boot-web-starter</artifactId>
				</exclusion>
				<exclusion>
					<artifactId>poi-ooxml-schemas</artifactId>
					<groupId>org.apache.poi</groupId>
				</exclusion>
				<exclusion>
					<artifactId>poi-ooxml</artifactId>
					<groupId>org.apache.poi</groupId>
				</exclusion>
				<exclusion>
					<artifactId>poi</artifactId>
					<groupId>org.apache.poi</groupId>
				</exclusion>
			</exclusions>
		</dependency>

		<!-- hippo4j -->
		<dependency>
			<groupId>cn.hydee</groupId>
			<artifactId>hydee-hippo4j-spring-boot-starter</artifactId>
			<version>3.1.0-SNAPSHOT</version>
		</dependency>

		<!-- hudit -->
		<dependency>
			<groupId>cn.hydee.hudit</groupId>
			<artifactId>hudit-sdk</artifactId>
			<version>1.0.0-SNAPSHOT</version>
		</dependency>

		<dependency>
			<groupId>org.springframework.boot</groupId>
			<artifactId>spring-boot-starter-web</artifactId>
			<exclusions>
				<exclusion>
					<artifactId>logback-classic</artifactId>
					<groupId>ch.qos.logback</groupId>
				</exclusion>
				<exclusion>
					<artifactId>log4j-to-slf4j</artifactId>
					<groupId>org.apache.logging.log4j</groupId>
				</exclusion>
			</exclusions>
		</dependency>

		<!-- WebSocket -->
		<dependency>
			<groupId>org.springframework.boot</groupId>
			<artifactId>spring-boot-starter-websocket</artifactId>
		</dependency>
		<!-- sentinel -->
		<dependency>
			<artifactId>yxt-sentinel-spring-boot-starter</artifactId>
			<groupId>com.yxt</groupId>
			<version>1.0.3</version>
		</dependency>
		<!-- OpenFeign -->
		<dependency>
			<groupId>org.springframework.cloud</groupId>
			<artifactId>spring-cloud-starter-openfeign</artifactId>
		</dependency>

		<!-- feign-okhttp -->
		<dependency>
			<groupId>io.github.openfeign</groupId>
			<artifactId>feign-okhttp</artifactId>
			<exclusions>
				<exclusion>
					<groupId>com.squareup.okhttp3</groupId>
					<artifactId>okhttp</artifactId>
				</exclusion>
			</exclusions>
		</dependency>

		<dependency>
			<groupId>io.micrometer</groupId>
			<artifactId>micrometer-core</artifactId>
		</dependency>

		<!-- 动态数据源 -->
		<dependency>
			<groupId>com.baomidou</groupId>
			<artifactId>dynamic-datasource-spring-boot-starter</artifactId>
			<version>2.5.5</version>
		</dependency>

		<!-- MyBatis-Plus -->
		<dependency>
			<groupId>com.baomidou</groupId>
			<artifactId>mybatis-plus-boot-starter</artifactId>
			<version>${mybatis-plus.version}</version>
		</dependency>
		
		<!-- MyBatis-Plus Generator -->
		<dependency>
			<groupId>com.baomidou</groupId>
			<artifactId>mybatis-plus-generator</artifactId>
			<version>${mybatis-plus.version}</version>
		</dependency>

		<dependency>
			<groupId>org.springframework.boot</groupId>
			<artifactId>spring-boot-starter-data-redis</artifactId>
			<!-- 排除lettuce -->
			<!--<exclusions>
				<exclusion>
					<groupId>io.lettuce</groupId>
					<artifactId>lettuce-core</artifactId>
				</exclusion>
				<exclusion>
					<groupId>redis.clients</groupId>
					<artifactId>jedis</artifactId>
				</exclusion>
			</exclusions>-->
		</dependency>

		<!-- 改成 jedis -->
		<!--<dependency>
			<groupId>redis.clients</groupId>
			<artifactId>jedis</artifactId>
			<version>2.9.1</version>
		</dependency>-->

		<dependency>
			<groupId>org.apache.commons</groupId>
			<artifactId>commons-pool2</artifactId>
			<version>2.6.2</version>
		</dependency>

		<dependency>
			<groupId>org.apache.velocity</groupId>
			<artifactId>velocity-engine-core</artifactId>
			<version>2.1</version>
		</dependency>

		<dependency>
			<groupId>org.mybatis</groupId>
			<artifactId>mybatis-typehandlers-jsr310</artifactId>
			<version>1.0.2</version>
		</dependency>

		<!-- RabbitMQ -->
        <dependency>
            <groupId>org.springframework.amqp</groupId>
            <artifactId>spring-rabbit</artifactId>
            <version>${spring-amqp.version}</version>
        </dependency>
        <dependency>
            <groupId>org.springframework.amqp</groupId>
            <artifactId>spring-amqp</artifactId>
            <version>${spring-amqp.version}</version>
        </dependency>

		<!-- RocketMQ -->
		<dependency>
			<groupId>org.apache.rocketmq</groupId>
			<artifactId>rocketmq-spring-boot-starter</artifactId>
			<version>2.0.3</version>
			<exclusions>
				<exclusion>
					<groupId>com.alibaba</groupId>
					<artifactId>fastjson</artifactId>
				</exclusion>
			</exclusions>
		</dependency>
		<!-- gson -->
		<dependency>
			<groupId>com.google.code.gson</groupId>
			<artifactId>gson</artifactId>
			<version>2.8.5</version>
		</dependency>
		<!-- 数据源 -->
		<dependency>
			<groupId>com.alibaba</groupId>
			<artifactId>druid-spring-boot-starter</artifactId>
			<version>${druid.version}</version>
		</dependency>

		<!-- 数据库驱动 -->
		<dependency>
			<groupId>mysql</groupId>
			<artifactId>mysql-connector-java</artifactId>
			<scope>runtime</scope>
		</dependency>

		<!-- EasyExcel -->
		<dependency>
			<groupId>com.alibaba</groupId>
			<artifactId>easyexcel</artifactId>
			<version>${easyexcel.version}</version>
			<exclusions>
				<exclusion>
					<groupId>org.apache.poi</groupId>
					<artifactId>poi</artifactId>
				</exclusion>
				<exclusion>
					<groupId>org.apache.poi</groupId>
					<artifactId>poi-ooxml</artifactId>
				</exclusion>
				<exclusion>
					<artifactId>poi-ooxml-schemas</artifactId>
					<groupId>org.apache.poi</groupId>
				</exclusion>
			</exclusions>
		</dependency>

		<!-- Hutool工具类所有模块 -->
		<dependency>
			<groupId>cn.hutool</groupId>
			<artifactId>hutool-all</artifactId>
			<version>${hutool.version}</version>
		</dependency>

		<!-- okhttp -->
		<dependency>
			<groupId>com.squareup.okhttp3</groupId>
			<artifactId>okhttp</artifactId>
			<version>${okhttp.version}</version> <!--$NO-MVN-MAN-VER$ -->
		</dependency>
		
		<dependency>
			<groupId>org.springframework.boot</groupId>
			<artifactId>spring-boot-configuration-processor</artifactId>
			<optional>true</optional>
		</dependency>

		<dependency>
			<groupId>org.projectlombok</groupId>
			<artifactId>lombok</artifactId>
			<optional>true</optional>
		</dependency>

		<dependency>
			<groupId>com.h2database</groupId>
			<artifactId>h2</artifactId>
			<scope>test</scope>
		</dependency>

		<dependency>
			<groupId>org.springframework.boot</groupId>
			<artifactId>spring-boot-starter-test</artifactId>
			<scope>test</scope>
			<exclusions>
				<exclusion>
					<groupId>org.junit.vintage</groupId>
					<artifactId>junit-vintage-engine</artifactId>
				</exclusion>
			</exclusions>
		</dependency>

		<!-- 循环治愈工具 -->
		<dependency>
			<groupId>cn.hydee</groupId>
			<artifactId>hydee-loop-cure</artifactId>
			<version>${loop-cure.version}</version>
		</dependency>
		
		<!-- 批量导入工具 -->
        <dependency>
            <groupId>cn.hydee</groupId>
            <artifactId>hydee-batch-starter</artifactId>
            <version>${batch-starter.version}</version>
            <exclusions>
	            <exclusion>
	                <groupId>org.apache.lucene</groupId>
		            <artifactId>lucene-core</artifactId>
	            </exclusion>
				<exclusion>
					<groupId>cn.hydee.starter</groupId>
					<artifactId>hydee-spring-boot-starter</artifactId>
				</exclusion>
	        </exclusions>
        </dependency>

        <!-- elasticsearch -->
        <dependency>
            <groupId>org.elasticsearch.client</groupId>
            <artifactId>elasticsearch-rest-high-level-client</artifactId>
            <version>${elasticsearch.version}</version>
        </dependency>

        <dependency>
            <groupId>org.elasticsearch</groupId>
            <artifactId>elasticsearch</artifactId>
            <version>${elasticsearch.version}</version>
        </dependency>
		<!-- mongodb -->
		<dependency>
			<groupId>org.springframework.boot</groupId>
			<artifactId>spring-boot-starter-data-mongodb</artifactId>
		</dependency>
		<!-- zip -->
		<dependency>
			<groupId>net.lingala.zip4j</groupId>
			<artifactId>zip4j</artifactId>
			<version>1.3.2</version>
		</dependency>
		<dependency>
			<groupId>cn.hydee.openapi</groupId>
			<artifactId>hydee-openapi-sdk</artifactId>
			<version>1.0.2-SNAPSHOT</version>
		</dependency>
		<dependency>
			<groupId>org.springframework.kafka</groupId>
			<artifactId>spring-kafka</artifactId>
		</dependency>
		<dependency>
			<groupId>com.alibaba.otter</groupId>
			<artifactId>canal.client</artifactId>
			<version>1.1.4</version>
			<exclusions>
				<exclusion>
					<groupId>com.google.guava</groupId>
					<artifactId>guava</artifactId>
				</exclusion>
				<exclusion>
					<artifactId>logback-classic</artifactId>
					<groupId>ch.qos.logback</groupId>
				</exclusion>
				<exclusion>
					<artifactId>logback-core</artifactId>
					<groupId>ch.qos.logback</groupId>
				</exclusion>
			</exclusions>
		</dependency>

		<dependency>
			<groupId>cn.hydee.starter</groupId>
			<artifactId>hydee-elasticsearch-spring-boot-starter</artifactId>
			<version>${es.starter.version}</version>
		</dependency>


		<dependency>
			<groupId>com.yxt</groupId>
			<artifactId>yxt-common-wechatrobot</artifactId>
			<version>4.10.3</version>
			<exclusions>
				<exclusion>
					<groupId>ch.qos.logback</groupId>
					<artifactId>logback-classic</artifactId>
				</exclusion>
				<exclusion>
					<groupId>ch.qos.logback</groupId>
					<artifactId>logback-core</artifactId>
				</exclusion>
			</exclusions>
		</dependency>

		<!-- 引入jwt -->
		<dependency>
			<groupId>io.jsonwebtoken</groupId>
			<artifactId>jjwt</artifactId>
			<version>0.9.0</version>
		</dependency>

		<dependency>
			<groupId>org.apache.pdfbox</groupId>
			<artifactId>pdfbox</artifactId>
			<version>2.0.27</version>
		</dependency>

		<dependency>
			<groupId>com.github.jai-imageio</groupId>
			<artifactId>jai-imageio-core</artifactId>
			<version>1.4.0</version>
		</dependency>

		<dependency>
			<groupId>com.github.jai-imageio</groupId>
			<artifactId>jai-imageio-jpeg2000</artifactId>
			<version>1.4.0</version>
		</dependency>

		<!-- https://mvnrepository.com/artifact/org.apache.poi/poi-ooxml -->
		<dependency>
			<groupId>org.apache.poi</groupId>
			<artifactId>poi-ooxml</artifactId>
			<version>5.0.0</version>
			<exclusions>
				<exclusion>
					<artifactId>bcprov-jdk15on</artifactId>
					<groupId>org.bouncycastle</groupId>
				</exclusion>
			</exclusions>
		</dependency>
		<!-- https://mvnrepository.com/artifact/org.apache.poi/poi -->
		<dependency>
			<groupId>org.apache.poi</groupId>
			<artifactId>poi</artifactId>
			<version>5.0.0</version>
		</dependency>

		<!-- xxljob -->
		<dependency>
			<groupId>com.yxt</groupId>
			<artifactId>yxt-xxljob-spring-boot-starter</artifactId>
			<version>4.2.0</version>
		</dependency>
		<dependency>
			<groupId>org.redisson</groupId>
			<artifactId>redisson-spring-boot-starter</artifactId>
			<version>3.21.3</version>
			<exclusions>
				<exclusion>
					<groupId>org.springframework.boot</groupId>
					<artifactId>spring-boot-starter-web</artifactId>
				</exclusion>
				<exclusion>
					<groupId>org.springframework.boot</groupId>
					<artifactId>spring-boot-starter-webflux</artifactId>
				</exclusion>
				<exclusion>
					<groupId>org.redisson</groupId>
					<artifactId>redisson-spring-data-30</artifactId>
				</exclusion>
			</exclusions>
		</dependency>
		<dependency>
			<groupId>org.redisson</groupId>
			<artifactId>redisson-spring-data-21</artifactId>
			<version>3.21.3</version>
		</dependency>
		<!--Rtree -->
		<dependency>
			<groupId>com.github.davidmoten</groupId>
			<artifactId>rtree</artifactId>
			<version>0.8.7</version>
		</dependency>
		<!--经纬度计算-->
		<dependency>
			<groupId>org.gavaghan</groupId>
			<artifactId>geodesy</artifactId>
			<version>1.1.3</version>
		</dependency>
		<dependency>
			<groupId>com.yxt.middle</groupId>
			<artifactId>yxt-middle-baseinfo-sdk</artifactId>
			<version>1.2.2-RELEASE</version>
		</dependency>
		<dependency>
			<groupId>com.yxt</groupId>
			<artifactId>yxt-third-platform-sdk-order</artifactId>
			<version>1.5.2-RELEASE</version>
		</dependency>
		<dependency>
			<groupId>com.yxt</groupId>
			<artifactId>yxt-third-platform-sdk-system</artifactId>
			<version>1.5.0-RELEASE</version>
		</dependency>
		<dependency>
			<groupId>com.yxt</groupId>
			<artifactId>yxt-third-platform-sdk-store</artifactId>
			<version>1.4.0-RELEASE</version>
		</dependency>

		<dependency>
			<groupId>cn.hydee.ydjia</groupId>
			<artifactId>hydee-middle-member-sdk</artifactId>
			<version>3.3.0-RELEASE</version>
		</dependency>
		<dependency>
			<groupId>com.yxt</groupId>
			<artifactId>yxt-third-platform-sdk-rider</artifactId>
			<version>1.0.0-RELEASE</version>
		</dependency>

		<!-- JTS Topology Suite -->
		<dependency>
			<groupId>org.locationtech.jts</groupId>
			<artifactId>jts-core</artifactId>
			<version>1.19.0</version>
		</dependency>

		<dependency>
			<groupId>com.yxt.medical</groupId>
			<artifactId>yxt-medical-prescription-sdk</artifactId>
			<version>20240924-G3</version>
		</dependency>

		<dependency>
			<groupId>com.yxt.domain.order</groupId>
			<artifactId>order-domain-sdk</artifactId>
			<version>1.1.0-RELEASE</version>
			<exclusions>
				<exclusion>
					<groupId>com.yxt</groupId>
					<artifactId>yxt-spring-boot-starter</artifactId>
				</exclusion>
			</exclusions>
		</dependency>

		<dependency>
			<groupId>com.yxt.org</groupId>
			<artifactId>yxt-org-read-open-sdk</artifactId>
			<version>1.0.5</version>
			<exclusions>
				<exclusion>
					<groupId>com.yxt</groupId>
					<artifactId>yxt-core-spring-boot-starter</artifactId>
				</exclusion>
			</exclusions>
		</dependency>
		<dependency>
			<groupId>com.yxt.login</groupId>
			<artifactId>yxt-login-open-sdk</artifactId>
			<version>1.0.2</version>
			<exclusions>
				<exclusion>
					<groupId>com.yxt</groupId>
					<artifactId>yxt-core-spring-boot-starter</artifactId>
				</exclusion>
			</exclusions>
		</dependency>

<!--		<dependency>-->
<!--			<groupId>com.yxt</groupId>-->
<!--			<artifactId>yxt-online-store-core</artifactId>-->
<!--			<version>${yxt-online-store-core.version}</version>-->
<!--		</dependency>-->
	</dependencies>

	<repositories>
		<repository>
			<id>aliyun</id>
			<name>aliyun</name>
			<url>https://maven.aliyun.com/repository/public</url>
		</repository>

		<repository>
			<id>hydee</id>
			<name>hydee</name>
			<url>https://nexus.hxyxt.com/repository/maven-public/</url>
		</repository>
	</repositories>

	<build>
		<plugins>
			<plugin>
				<groupId>org.springframework.boot</groupId>
				<artifactId>spring-boot-maven-plugin</artifactId>
			</plugin>
			<plugin>
				<groupId>org.apache.maven.plugins</groupId>
				<artifactId>maven-enforcer-plugin</artifactId>
				<executions>
					<execution>
						<id>ban-snapshot</id>
						<configuration>
							<rules>
								<requireReleaseDeps>
									<!--enforcer配置，排除指定依赖-->
									<excludes>
										<exclude>cn.hydee:hydee-loop-cure:1.2.1-SNAPSHOT</exclude>
										<exclude>cn.hydee:erp:2.3.4-SNAPSHOT</exclude>
										<exclude>cn.hydee:third-o2o:1.0.0-SNAPSHOT</exclude>
										<exclude>cn.hydee:hydee-batch-starter:2.0.1-SNAPSHOT</exclude>
										<exclude>cn.hydee.starter:hydee-elasticsearch-spring-boot-starter:1.0.0-SNAPSHOT</exclude>
										<exclude>cn.hydee.starter:grey-spring-boot-web-starter:3.2.0-SNAPSHOT</exclude>
										<exclude>cn.hydee:hydee-hippo4j-spring-boot-starter:3.1.0-SNAPSHOT</exclude>
										<exclude>cn.hydee.hudit:hudit-sdk:1.0.0-SNAPSHOT</exclude>
										<exclude>cn.hydee.openapi:hydee-openapi-sdk:1.0.2-SNAPSHOT</exclude>
										<exclude>cn.hydee.ydjia:hydee-middle-member-sdk:1.1.17-SNAPSHOT</exclude>
										<exclude>com.yxt:yxt-online-store-core:1.0.2-SNAPSHOT</exclude>
									</excludes>
								</requireReleaseDeps>
							</rules>
						</configuration>
					</execution>
				</executions>
			</plugin>
		</plugins>
	</build>

</project>
